import { Injectable } from '@angular/core';
import { Request, XHRBackend, RequestOptions, Response, Http, RequestOptionsArgs, Headers } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { Router } from '@angular/router';
import 'rxjs/add/operator/catch';
import 'rxjs/add/observable/throw';
import { BaseComponent } from '@app/base/components/base.component';

@Injectable()
export class InterceptorService extends Http {

  constructor(backend: XHRBackend, defaultOptions: RequestOptions, private router: Router) {
    super(backend, defaultOptions);
  }

  request(url: string | Request, options?: RequestOptionsArgs): Observable<Response> {
    //do whatever 
    return super.request(url, options).catch((err) => {
        //Error Response.
        if (err.status === 403 || err.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('userType');
            this.router.navigateByUrl('/');
            BaseComponent.user = undefined;
        }
        return Observable.throw(err);
    });
  }
}