#map {
    height: calc(100vh - 208px);
    width: 100%;
    }

.btn:hover{
    color: #10B8A8 !important;
}

.btn:focus{
    color: #10B8A8 !important;
}

.btn1:hover{
    color: #ffffff !important;
}
.btn1:focus{
    color: #ffffff !important;
}
.chat_message.check_event .left_side{
    width: 23% !important;
}

.event-body{
    overflow: hidden;
}
.checkin-disable{
    opacity: 0.8;
}
.signin input[type="submit"] {
    background: #10B8A8;
    color: white;
    border-radius: 20px;
    width: 82px;
    padding: 8px 2px;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
}
.filter-drop-down{
    margin-bottom: 15px !important;
    width: 130px !important;
    height: 39px !important;
}
.simple-filter-drop-down{
    margin-bottom: 15px !important;
    width: 130px !important;
    height: 39px !important;
    margin-top: -29px;
}
.dot{
    font-size: 13px;
    color: #71909d;
    padding-right: 5px;
}
.dot1{
    font-size: 13px;
    color: #37474f;
    padding-right: 5px;
}
.pmain {
    font-size: 14px !important;
    color: #8D8D8D;
    text-align: left !important;
}
.guest-btn{
    margin-left: 19px !important;
}
.with_white_button {
    border: 1px solid #FFFFFF;
    border-radius: 100px;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    padding: 7px 11px 7px 11px !important;
}
.without_white_button {
    background: #FF8F00;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    padding: 8px 11px 8px 11px !important;
    border-radius: 100px;
    margin-left: 19px;
}
.white_button {
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    border: 1px solid white;
    border-radius: 20px;
    height: 28px;
    padding: 8px 14px 24px 14px !important;
    margin-left: 30px;
    margin-top: 28px !important;
    cursor: pointer;
}
.blue_border_button {
    border: 1px solid #10B8A8;
    border-radius: 100px;
    padding: 7px 15px 6px 15px !important;
    font-size: 12px;
    color: #10B8A8;
    display: inline-block;
    cursor: pointer;
    margin-right: 0px;
}
.blue_bg_button {
    background: #10B8A8;
    cursor: pointer;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    border-radius: 100px;
    padding: 9px 20px 7px 22px !important;
    text-align: center;
    display: inline-block;
    margin-bottom: 11px;
}
.button_group {
    display: flex;
    margin-top: 0px !important;
    float: right;
}
.request-dot{
    font-size: 11px;
    padding-right: 2px;
    color: #f19632d8;
    vertical-align: super;
}
.custom-slice {
    position: absolute;
    width: 1em;
    height: 1em;
    /* clip: rect(0em, 1em, 1em, 0.5em); */
}
.custom-bar{
    position: absolute;
    border: 0.08em solid #10B8A8;
    width: 0.84em;
    height: 0.84em;
    /* clip: rect(0em, 0.5em, 1em, 0em); */
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    border-radius: 50%;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
}
.chat-image{
    padding-bottom: 9px;
}
.ls {
    font-size: 37px;
    color: #FFFFFF;
    position: relative;
    letter-spacing: -1px;
    line-height: 40px;
    background: #10B8A8;
    display: inline-block;
    padding: 10px 3px !important;
    border-radius: 40px;
    top: -15px;
    height: 60px;
    width: 61px;
    text-align: center !important;
}
.po_rel_1 {
    position: relative !important;
    top: 10px !important;
}
.chat-msg{
    padding: 0px 0px 4px 0px !important;
}
.profile-center{
    margin-left: auto !important;
}
.msg-left-padding{
    padding-bottom: 16px;
}
.msg-left-width{
    width: 80% !important;
    margin: 0px !important;
}
input.meassage_search.checkin {
    background: #FFFFFF !important;
    border: 1px solid #DBDBDB;
    border-radius: 2px;
    color: #636363 !important
}
.checked-text{
    position: absolute;
    bottom: 0;
    text-align: center;
    left: 38%;
    /* top: 21px; */
    color: #3ab8a8;
    font-weight: 600;
    border-bottom: 2px solid;
    margin-bottom: 8px;
    cursor: pointer;
}
.hide-td{
    padding-left: 34px !important;
    padding-top: 10px;
    padding-bottom: 5px;
}

.guest-list{
    padding-top: 10px;
    padding-bottom: 5px;
}
.table-position{
    /* position: absolute !important; */
}
.td-show{
    /* padding-left: 22px !important; */
    padding-left: 8px !important;
    padding-top: 10px;
    /* padding-bottom: 5px;
    width: 30%; */
}
.td-showd{
    padding-left: 12px !important;
    padding-top: 10px;
}
/* .guest-table{
    height: '-webkit-fill-available';
} */
.check-table{
    position: relative !important;
    min-height: 250px;
    /* height: 100%; */
    /* height: stretch; */
}
.guest-table{
    width: 34% !important;
    display: inline-block !important;
    margin-left: 24px !important;
    background: #FFFFFF !important;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30) !important;
    border-radius: 4px !important;
    padding: 20px 10px !important;
    margin-top: 20px !important;
}

.guest-time{
    width: 165px !important;
    text-align: center !important;
}
.guest-time-reper{
    width: 248px !important;
    text-align: right !important;
}

.check_time_mr{
    margin-right: 20px;
    float: left;
    margin-top: 6%;
}

.guest-border{
    border-bottom: 1px solid #ddd !important;
}
.guest-name{
    width: max-content !important;
    max-width: 100px;
    /* min-width: 130px; */
}
.disable-text{
    color: darkgray !important;
}
img.add_event_bg {
    height: 100.5% !important;
    width: 100%;
    object-fit: cover !important;
    border-right: 7px solid #3ab8a8 !important;
}
.small-grid{
    /* POHP2-63 */
    /* height: 984px !important; */
    height: 100% !important;
    position: absolute;
}
.add_event_image_group {
    border-right: 0px solid #10B8A8 !important;
}
.search-agent-image{
    height: 130px !important;
    width: 130px !important;
    border-radius: 85px !important;
}
.property-image-width{
    width: 100% !important;
}

.disabled {
    pointer-events: none !important;
}
.long-text-dot{
    display:inline-block !important;
    width:100% !important;
    white-space: nowrap !important;
    overflow:hidden !important;
    text-overflow: ellipsis !important;
}
.add-to-leads-btn{
    background: white;
    color: #10B8A8;
    border: #10B8A8;
    border: 1px solid #10B8A8;
    margin-right: 12px;
    cursor: pointer;
    font-size: 12px;
    letter-spacing: 0;
    border-radius: 100px;
    padding: 9px 20px 7px 22px !important;
    text-align: center;
    display: inline-block;
    margin-bottom: 11px;
}
.checkin-checkbox{
    padding-left: 25px !important;
}
.td-name{
    padding-top: 10px;
    padding-bottom: 5px;
    width: 32%;
}
.td-appointment{
    /* width: 361px !important; */
    width: 248px !important;
}
.pr-detail-btn{
    margin-top: 24px;
    margin-right: 20px;
    float: right;
}
.model-confi{
    width: 14%;
    font-size: 13px;
    font-weight: 600;
    outline: none;
}
.model-cancel{
    background: #F06292 !important;
    border: 0px !important;
    outline: none;
    width: 14%;
    font-size: 13px;
    font-weight: 600;
    outline: none;
}
.run-t-prtd{
    background: #FFFFFF;
    font-size: 13px;
    color: #727272;
    vertical-align: bottom;
    border-radius: 21px;
    letter-spacing: 0;
    padding: 8px 20px 8px 20px;
    margin-left: 5px;
    width: 100%;
    text-align: center;
    border: 1px solid #727277;
}
.export-lead-ch{
    float: right;
    background: white;
    color: #10B8A8;
    border: #10B8A8;
    border: 1px solid #10B8A8;
    margin-right: 0px;
    cursor: pointer;
    font-size: 12px;
    letter-spacing: 0;
    border-radius: 100px;
    padding: 9px 20px 9px 20px !important;
    text-align: center;
    display: inline-block;
}
.event-info-month{
    font-size: 22px;
    color: #676767;
    border: 1px;
    text-align: center;
    line-height: 8px;
    padding-bottom: 50px;
    width: 60px;
    display: inline-block;
    height: 55px;
    padding-top: 0px;
    margin-left: 0px;
    border-radius: 4px;
    cursor: pointer;
    background: white;
    margin-top: 9px;
}
.event-info-month span{
    background: #566D77;
    color: white !important;
    font-size: 12px !important;
    padding: 1px 4px 0px 4px;
    display: inline-block;
    line-height: 19px;
    width: 100%;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.event-info-month div{
    padding-top: 10px;
}
img.check_icon.back_check_2{
    height: 45px;
}

.msg_btn_run_tool{
    background: #10B8A8;
    cursor: pointer;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    border-radius: 100px;
    padding: 7px 35px 7px 35px !important;
    text-align: center;
    display: inline-block;
    margin-bottom: 11px;
}
.guest-overlay{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100.5% !important;
    width: 100%;
    opacity: 0.5;
    -webkit-transition: .3s ease;
    transition: .3s ease;
    background-color: #1bcea5e8;
}
.img-margin-left-7{
    margin-left: 5%;
}
.img-margin-left-5{
    margin-left: 5%;
}
.already-register{
    font-weight: 600;
}
.msg-in-table{
    background: #10b8a8;
    cursor: pointer;
    font-size: 12px;
    color: #fff;
    letter-spacing: 0;
    border-radius: 100px;
    padding: 9px 20px 9px 20px !important;
    text-align: center;
    margin-right: 5px;
    float: right;
}
.check-msg{
    float: right !important;
}
.guest-check{
    width: 1px;
}
.check_rating{
    width: 12%;
}
.margin-bttom{
    margin-bottom: 15px;
}
.unlock-text{
    color: #b1b2b2;
    margin-left: 20px;
    padding-top: 14px;
    font-size: 1.1em;
    width: 13em;
}
.unlock-btn-div{
    margin-left: 15px;
    padding-top: 10px;
}
@media only screen and (max-width: 767px){
    .checkin-checkbox{
        padding-left: 10px !important;
    }
    table tr th:nth-child(1), table tr td:nth-child(1){
        padding-left: 10px !important;
    }
    .export-lead-ch{
        margin-bottom: 5px !important;
        margin: auto;
    }
    .msg-in-table{
        margin: auto;
        padding: 9px 24px 9px 24px !important;
        float: right;
    }
    .run-tool-over{
        margin-left: 20px;
    }
    .check-table{
        min-height: 100%;
    }
    .row-zero{
        margin-right: 0px !important;
        margin-left: 0px !important
    }
    .chat_message.check_event .left_side.height_auto.Recipient_Chosen{
        width: 100% !important;
    }
    .recipient_group{
        margin: 17px;
    }
    .chat_session.Recipient_Chosen_chat.message_list_with_name {
        height: calc(100vh - 230px) !important;
    }
    .box_on_map.position_reletive.pull-right {
        margin-top: 10px;
        margin-bottom: 15px;
    }
}
@media (min-width: 768px) and (max-width: 1023px) {
    .td-show[_ngcontent-c1] {
        /* padding-left: 75px !important; */
        padding-top: 10px;
        padding-bottom: 5px;
        width: 51%;
    }
    .td-showd{
        padding-left: 95px !important;
        padding-top: 10px;
        padding-bottom: 5px;
        width: 54.3% !important;
    }
    .check-table{
        min-height: 100%;
    }

    .row-zero{
        margin-right: 0px !important;
        margin-left: 0px !important
    }
    .chat_message.check_event .left_side.height_auto.eventchatwidth{
        width: 100% !important;
    }
    .chat_message.check_event .left_side.height_auto.Recipient_Chosen{
        width: 100% !important;
    }
    .recipient_group{
        width: 100%;
        margin: 15px 5px 15px 5px;
    }
}

.request-tab{
    min-width: 1345px !important;
}
.display-inline{
    /* display: inline-block; */
    /* width: 100%; */
}
.chat-profile-img-center{
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0px;
}
.drop-down-chat{
    margin: auto;
    width: 100%;
}

.hide_open_chat{
    display: none !important;
    opacity: 0;
}
.margin-left{
    margin-left: 15px;
}
.add_new_list.dis_inline_disabled{
    background: #10B8A8;
    font-size: 12px;
    color: #FFFFFF;
    vertical-align: bottom;
    border-radius: 21px;
    letter-spacing: 0;
    padding: 8px 10px 6px 12px;
    margin-left: 5px;
    width: 110px;
    text-align: center;
}
.property-list-table .table tr:last-child>td .open_click_menu ul {
  margin-bottom: 0px !important
}
.col-sm-16 .box_on_map{
  width: 30% !important;
}
.col-sm-4 .box_on_map{
  width: 30% !important;
}
@media only screen and (max-width: 767px){
  .Event_manager_add {
    grid-template-columns: 30.0% 30.0%;
  }
  .Event_manager_add.small-grid .add_event_image_group {
    display: inline-block;
    width: 100%;
    height: 100%
  }
  .your_open_agent {
    position: absolute;
    top: 75%;
    left: 5%;
    width: 90%;
  }
  .img-margin-left-5{
        margin-left: 0%;
  }
  .event_address_title {
      top: 20%;
      left: 5%;
      font-size: 40px;
  }
  .event_question{
      font-size: 25px;
      color: #FFFFFF;
      position: absolute;
      top: 30%;
      left: 5%;
  }
  .event_search{
    width: 90%;
    top: 45%;
    left: 5%;
  }
  .your_open_agent {
      position: absolute;
      top: 75%;
      left: 5%;
      width: 89%;
  }
  .event_address_note {
      font-size: 16px;
      color: #FFFFFF;
      position: absolute;
      top: 41%;
      left: 5%;
  }
  .Event_manager_add.small-grid .event_agent_group{
    width: 100%;
  }
  .event_form{
    width: 95%;
    margin-left: 1%;
    margin-bottom: 5%;
  }
  img.img-responsive.footer_logo_event{
    left: 0px;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .Event_manager_add.small-grid .event_agent_group{
    width: 40%;
  }
  .Event_manager_add.small-grid .add_event_image_group{
    width: 58%;
  }
  .event_search{
    top: 45%;
    left: 0px;
  }
  .your_open_agent{
    left: 0px;
    width: 85%;
  }
  .event_address_note{
    top: 40%;
    left: 0px;
  }
  .event_address_title{
    top: 20%;
    left: 0px;
  }
  img.img-responsive.footer_logo_event{
    left: 0px;
  }
  .event_question{
    left: 0px;
  }
}
.search-agent-list-event{
  width: 100%;
  height: 200px;
  overflow-x: auto;
}


.badge-notify{
    background: #AD5FBF;
    position: relative;
    color: white !important;
    top: -15px;
    left: -15px;
    font-size: 13px !important;
   }

.badge-notify-1 {
    background: #AD5FBF;
    position: relative;
    color: white !important;
    top: -10px;
    left: 330px;
    font-size: 13px !important;
}
.badge-notify-2 {
    float: right;
    top: -5px;
    left: 84px;
    background: #AD5FBF;
    color: white;
    font-size: 13px;
    position: relative;
    margin-right: 10px;
}


.otp-info{
    border: 1px solid red;
  }

 .checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    max-width: 100%;
    margin-top: 6px;
  }

  .custom-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  .custom-checkbox input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
  }

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border: 2px solid #ccc;
    border-radius: 3px;
    cursor: pointer;
  }

  .custom-checkbox input:checked + .checkmark {
    background-color: #10B8A8;
    border-color: #10B8A8;
  }

  .checkmark::after {
    content: "";
    position: absolute;
    display: none;
  }

  .custom-checkbox input:checked + .checkmark::after {
  display: block;
left: 4px;
    top: 0px;
    width: 5px;
    height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
