import { Component, OnInit ,<PERSON><PERSON><PERSON><PERSON>} from '@angular/core';
import { AuthComponent } from '@app/auth/components/auth.component';
import { FormGroup, FormControl,Validators } from '@angular/forms';
import { NgForm } from '@angular/forms/src/directives/ng_form';

@Component({
  selector: 'forgotpassword',
  templateUrl: '../views/reset-password.html',
  styleUrls: ['../css/auth.component.css']
})
export class ResetPasswordComponent extends AuthComponent implements OnInit,OnDestroy {

  resetPasswordForm: FormGroup;
  reset_password_token:any;
  private paramsSubscription;

  constructor() {
    super();
   }

  ngOnInit() {
    this.paramsSubscription = this.route.queryParams.subscribe(params=>{
      if(params['token'] == undefined)
      {
        this.router.navigateByUrl('/');
      }
    });

    this.resetPasswordForm = new FormGroup({
      password : new FormControl('', [Validators.required, Validators.minLength(5),Validators.maxLength(15)]),
      confirm_new_password : new FormControl('', [Validators.required, Validators.minLength(5),Validators.maxLength(15)])
    }, passwordMatchValidator);

    function passwordMatchValidator(g: FormGroup) {
      return g.get('password').value === g.get('confirm_new_password').value ? null : {'mismatch': true};
    };
  }

  ResetPassword(form:FormGroup){
    this.paramsSubscription = this.route.queryParams.subscribe(params=>{
      form.value['reset_password_token']=params['token'];
      this.authService.resetPassword(form.value).subscribe(res => {        
        this.successResponse(res);
        this.router.navigateByUrl('/');
        form.reset();       
      },err => {       
        this.errorResponse(err.json());
      });
    });
  }

  ngOnDestroy(): void {
    this.paramsSubscription.unsubscribe();
  }

}
