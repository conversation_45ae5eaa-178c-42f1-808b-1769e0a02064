<div>
  <header></header>
</div>

<div  class="brokerage-profile">
  <div class="new_profiles">
    <div class="new_profile_title">
      <img src="{{fileUrl}}" class="new_symbols-avatar dis_inline" alt="">
       <div class="new_title dis_inline file-upload-title">
          {{profileName}}
           <label for="file-upload" class="file-upload-label">
             <span class="custom-file-upload">
               <span class="file-upload-text">Add Profile Image</span>
             </span>
           </label>
           <input id="file-upload" type="file" (change)="uploadProfileImage($event)"/>
        </div>
    </div>
    <div class="new_profile_group_wrap">
       <div class="new_profile_group dis_inline">
          <ul class="nav nav-pills">
             <li class="active"><a data-toggle="pill" href="#Profile">Profile</a></li>
             <li><a data-toggle="pill" href="#Password">Password</a></li>
             <li><a data-toggle="pill" href="#Billing">Billing</a></li>
             <li><a data-toggle="pill" href="#Agent">Agent</a></li>
             <!-- <li><a data-toggle="pill" href="#PreferredLender">Preferred Lender</a></li> -->
          </ul>
          <div class="tab-content"  >
             <div id="Profile" class="tab-pane fade in active" id="brokerDiv"> 
                <form [formGroup]="brokerageProfileForm" id ="brockerInfo">              
                <div class="group_1 mt-20">                  
                   <div class="title2">Basic Information</div>
                   <div class="new_form" >
                      <div class="new_form_group " formGroupName="profile">
                         <div class="group new_form_label">      
                            <input type="text" placeholder=" " required class=" width_350" formControlName="firm_name">
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>Brokerage Name*</label>
                         </div>
                      </div>
                      <div class="new_form_group" formGroupName="profile">
                          <div class="group new_form_label">      
                            <input type="text" placeholder=" " class=" width_350" formControlName="brokerage_id">
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>{{brokerageIdText}}</label>
                          </div>
                      </div>
                      <div class="new_form_group " formGroupName="profile">
                          <div class="group new_form_label">      
                            <input type="text" placeholder=" " required class=" width_350" formControlName="contact_name">
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>Broker Name*</label>
                          </div>
                      </div>
                      <div class="new_form_group ">
                          <div class="group new_form_label">      
                            <input type="text" placeholder=" " required class=" width_350" formControlName="email">
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>Contact Email*</label>
                          </div>
                      </div>
                     <!-- For formationg mobile nuber -->
                     <div class="new_form_group " formGroupName="profile">
                        <div class="group new_form_label">      
                           <input  type="text" maxlength="12" required #phone (keyup)="validateFormat(phone.value)" class="width_350" formControlName="office_phone" placeholder=" "/>
                           <span class="highlight"></span>
                           <span class="bar"></span>
                           <label>Office Phone Number*</label>
                           <div *ngIf="brokerageProfileForm['controls'].profile['controls']['office_phone'].touched">
                              <p class="form-validation" *ngIf="brokerageProfileForm.controls.profile.controls.office_phone.errors?.minlength">phone number must be 10 characters</p>
                              <p class="form-validation" *ngIf="brokerageProfileForm.controls.profile.controls.office_phone.errors?.maxlength">phone number must be 10 characters</p>
                           </div>
                        </div>
                     </div>
                      <div class="new_form_group " formGroupName="profile">
                        <div class="group new_form_label">      
                          <input type="text" placeholder=" " required class=" width_350" formControlName="address">
                          <span class="highlight"></span>
                          <span class="bar"></span>
                          <label>Street Address*</label>
                        </div>
                      </div>
                      <div class="new_form_group " formGroupName="profile">
                          <div class="group new_form_label">
                            <div class="form_group width_350">
                              <ng-select class="custom agent-dropdown" 
                                placeholder = "State*"
                                formControlName="state"
                                notFoundText="No State found" 
                                [items]="stateList"
                                bindLabel="name"
                                bindValue="id"
                                [clearable]=false
                                [searchable]=false
                                (change)="getCityList($event.id)">
                              </ng-select>
                            </div>
                          </div>
                      </div>       
                      <div class="new_form_group " formGroupName="profile">
                          <div class="group new_form_label">      
                            <div class="form_group width_350">
                              <ng-select class="custom agent-dropdown"
                                required
                                formControlName="city"
                                allowClear=true 
                                placeholder = "City*"
                                notFoundText="No City found" 
                                [items]="cityList"
                                bindLabel="name"
                                bindValue="id"
                                [clearable]=false
                                [searchable]=false
                                (change)="getZipCode($event.id)">
                              </ng-select>
                            </div>
                          </div>
                      </div>
                     <div class="new_form_group " formGroupName="profile">
                         <div class="group new_form_label">      
                            <div class="form_group width_350">
                              <ng-select class="custom agent-dropdown"
                                formControlName="zipcode" 
                                placeholder = "Zip Code*"
                                notFoundText="No Zip Code found" 
                                [items]="zipCodeList"
                                bindLabel="code"
                                bindValue="id"
                                [clearable]=false
                                [searchable]=false
                                (change)="setZipCode($event.id)">
                              </ng-select>
                            </div>
                         </div>
                      </div>
                   </div>
                   
                </div>
                <div class="group_1 mt-20">
                   <div class="title2">Contact Preferences</div>
                   <div class="new_form">
                      <div class="check_group profile_checkbox width_350 ">
                         <div class="form_group flex_none">
                            <input type="checkbox" (change)="CPStatusChange('msg')" [checked]="msgCP">   <span class="checkmark"></span>
                            <label class="width_auto">Message Notifications</label>
                         </div>
                         <div class="form_group">
                            <input type="checkbox" (change)="CPStatusChange('w_oh')" [checked]="w_ohCP">   <span class="checkmark"></span>
                            <label class="width_auto">Weekly Open House Notification</label>
                         </div>
                      </div>
                   </div>
                </div>
                  <div class="row">
                    <div class="col-sm-16 ">
                        <div class="new_form_group profile_save_button ml-88">                         
                          <input type="submit" [ngClass]="{'submit-disable':brokerageProfileForm.invalid}" class="submit_button with_bg dis_inline" [disabled]="brokerageProfileForm.invalid" value="Submit" (click)="updateBrokerageProfile(brokerageProfileForm)">
                        </div>
                    </div>
                  </div>
                </form>
              </div>
              <div id="Password" class="tab-pane fade">
                <div class="group_1 mt-20">
                   <div class="title2">Change your Password</div>
                   <div class="new_form">
                     <form [formGroup]="profilePassword">
                      <div class="new_form_group ">
                         <div class="group new_form_label">      
                            <input type="password" placeholder=" " required class=" width_350" formControlName="password">                            
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>Old Password*</label>
                            <div *ngIf="profilePassword.controls.password.touched">
                              <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.required">Enter old password</p>
                              <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.minlength">Password must be 5-15 characters</p>
                              <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.maxlength">Password must be 5-15 characters</p>
                          </div>
                         </div>
    
                      </div>
                      <div class="new_form_group ">
                         <div class="group new_form_label">      
                            <input type="password" placeholder=" " required class=" width_350" formControlName="new_password">
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>New Password*</label>
                            <div *ngIf="profilePassword.controls.new_password.touched">
                              <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.required">Enter new password</p>
                              <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.minlength">Password must be 5-15 characters</p>
                              <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.maxlength">Password must be 5-15 characters</p>
                          </div>
                         </div>
                      </div>
                      <div class="new_form_group ">
                         <div class="group new_form_label">      
                            <input type="password" placeholder=" " required class=" width_350" formControlName="confirm_new_password">
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>Confirm New Password*</label>
                            <div *ngIf="profilePassword.controls.confirm_new_password.touched">
                              <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.required">Enter confirm password</p>
                              <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.minlength">Password must be 5-15 characters</p>
                              <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.maxlength">Password must be 5-15 characters</p>
                              <p class="form-validation" *ngIf="profilePassword.hasError('mismatch')">Confirm password not match</p>
                            </div>
                         </div>
                      </div>
                      <div class="new_form_group ">
                         <input type="submit" class="submit_button with_bg" [ngClass]="{'submit-disable':profilePassword.invalid}" [disabled]="profilePassword.invalid" value="Submit" (click)="updatePassword(profilePassword)">
                      </div>
                      </form>
                    </div>
                </div>
              </div>
              <div id="Billing" class="tab-pane fade billing_ss">
                  <div class="group_1 mt-20">
                     <div class="title2">
                        Payment Method
                     </div>
                     <div class="new_form">
                      <form [formGroup]="brokeragePaymentForm">
                        <div class="new_form_group dis_inline">
                          <div class="group new_form_label" [ngClass]="{'disable_form':showextrafeild ? null : 'disabled','disable_form_blank':brokeragePaymentForm.controls.firstName.value == null && showextrafeild == false}">
                             <input type="text" placeholder=" " id="firstname" required class="" [attr.disabled] = "showextrafeild ? null : 'disabled'" formControlName="firstName">
                             <span class="highlight"></span>
                             <span class="bar"></span>
                             <label>First Name*</label>
                          </div>
                       </div>
                       <div class="new_form_group dis_inline ml-10">
                          <div class="group new_form_label" [ngClass]="{'disable_form':showextrafeild ? null : 'disabled','disable_form_blank':brokeragePaymentForm.controls.lastName.value == null && showextrafeild == false}">
                             <input type="text" placeholder=" " required class=" " [attr.disabled] = "showextrafeild ? null : 'disabled'"  formControlName="lastName">
                             <span class="highlight"></span>
                             <span class="bar"></span>
                             <label>Last Name*</label>
                          </div>
                       </div>
                       <div class="new_form_group " *ngIf="showextrafeild">
                          <div class="group new_form_label" formGroupName="creditCard">      
                             <input type="text" placeholder=" " #creditCardNumber required class=" width_350" [attr.disabled] = "showextrafeild ? null : 'disabled'"  formControlName="number" autocomplete="cc-number" id="cc-number" >
                             <span class="highlight"></span>
                             <span class="bar"></span>
                             <label>Card Number*</label>
                             <div *ngIf="brokeragePaymentForm.controls.creditCard['controls']['number'].touched">
                                <p class="form-validation" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['number'].errors?.required">Enter Card number</p>
                                <p class="form-validation" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['number'].errors?.pattern">Card number must be Digit or max length 16</p>
                              </div>                             
                          </div>
                       </div>

                       <div class="new_form_group" *ngIf="!showextrafeild">
                          <div class="group new_form_label" [ngClass]="{'disable_form':showextrafeild ? null : 'disabled','disable_form_blank':brokeragePaymentForm.controls.creditCard['controls']['number'].value == null}">
                             <input type="text" placeholder=" " class=" width_350" [attr.disabled] = "showextrafeild ? null : 'disabled'" [(ngModel)] = "defaultCardNuber"  [ngModelOptions]="{standalone: true}">
                             <span class="highlight"></span>
                             <span class="bar"></span>
                             <label>Card Number*</label>                            
                          </div>
                       </div>
                      
                       <span *ngIf="showextrafeild">
                        <div class="new_form_group ">
                          <div class="group new_form_label" formGroupName="creditCard">      
                             <input type="password" placeholder=" " required class=" width_350"  formControlName="cvv">
                             <span class="highlight"></span>
                             <span class="bar"></span>
                             <label>CVV*</label>
                             <div *ngIf="brokeragePaymentForm.controls.creditCard['controls']['cvv'].touched">
                                <p class="form-validation" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['cvv'].errors?.required">Enter cvv number</p>
                                <p class="form-validation" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['cvv'].errors?.pattern">cvv must be Digit or max length 4</p>
                              </div>
                          </div>
                       </div>
                       <label class="label-space">Expires On</label>
                       <div class="new_form_group dis_inline">
                          <div class="group new_form_label" formGroupName="creditCard">      
                             <input type="text" placeholder=" " required class=" " #date (keyup)="cardExDateV(date.value)" formControlName="expirationMonth">
                             <span class="highlight"></span>
                             <span class="bar"></span>                                     
                             <label>Month*</label>
                          </div>
                       </div>
                       <div class="new_form_group dis_inline ml-10">
                          <div class="group new_form_label " formGroupName="creditCard">      
                             <input type="text" placeholder=" " required class="smallSize-number " #year (keyup)="cardExYearV(year.value)"  formControlName="expirationYear">
                             <span class="highlight"></span>
                             <span class="bar"></span>
                             <label>Year*</label>
                          </div>
                       </div>
                       <div class="errmsg">                                                                       
                          <div class="col-md-6 err-month-paddding">
                          <span class="err-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].touched">
                            <p class="form-validation err-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].errors?.required" >Enter Month</p>
                            <p class="form-validation err-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].errors?.number">Month must be Digit or max length 2</p>
                            <p class="form-validation err-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].errors?.incorrect">Invalid Month</p>
                          </span>
                        </div>

                        <div class="col-md-6">
                          <span class="err-year-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationYear'].touched">
                            <p class="form-validation err-year-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationYear'].errors?.required">Enter Year</p>
                            <p class="form-validation err-year-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationYear'].errors?.yearNumber">Year must be Digit or max length 4</p>
                            <p class="form-validation err-year-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationYear'].errors?.maxlength">Year must be Digit or max length 4</p>
                            <p class="form-validation err-year-width" *ngIf="brokeragePaymentForm.controls.creditCard['controls']['expirationYear'].errors?.incorrect">Invalid Year</p>                                          
                          </span>
                        </div>
                        </div>
                      </span>
                        <div class="new_form_group">
                           <input type="submit"  *ngIf="!showextrafeild" (click)="enableEditPaymentMethod(true)" class="submit_button" value="Change">
                           <input type="submit" *ngIf="showextrafeild" class="submit_button with_bg" value="Update" [ngClass]="{'submit-disable':brokeragePaymentForm.invalid}" [disabled]="brokeragePaymentForm.invalid" (click)="updatePayment(brokeragePaymentForm)">
                           <input type="submit"  *ngIf="showextrafeild" (click)="enableEditPaymentMethod(false)" class="cancle_button dis_inline" value="Cancel">
                          </div>
                        <div class="new_form_group"  >
                          
                         </div>
                      </form>  
                     </div>
                  </div>

                  <div class="group_1 mt-20">
                     <div class="title2">Billing Info</div>
                     <div class="new_form">
                      <form [formGroup]="brokerageBillingInfo">  
                        <div class="new_form_group ">
                           <div class="group new_form_label">      
                              <input type="text" placeholder=" " required class=" width_350" formControlName="address_1">
                              <div *ngIf="brokerageBillingInfo.controls.address_1.touched">
                                <p class="form-validation" *ngIf="brokerageBillingInfo.controls.address_1.errors?.required">Billing address is required</p>                                
                              </div>
                              <span class="highlight"></span>
                              <span class="bar"></span>
                              <label>Billing Address*</label>
                           </div>
                        </div>
                        <div class="new_form_group ">
                           <div class="group new_form_label">      
                              <input type="text" placeholder=" " class=" width_350" formControlName="address_2">
                              <div *ngIf="brokerageBillingInfo.controls.address_2.touched">
                                <p class="form-validation" *ngIf="brokerageBillingInfo.controls.address_2.errors?.required">Address(Cont.) is required</p>                                
                              </div>
                              <span class="highlight"></span>
                              <span class="bar"></span>
                              <label>Address (Cont.)</label>
                           </div>
                        </div>
                        <div class="new_form_group ">
                           <div class="group new_form_label">      
                              <input type="text" placeholder=" " required class=" width_350" formControlName="city"  >
                              <div *ngIf="brokerageBillingInfo.controls.city.touched">
                                <p class="form-validation" *ngIf="brokerageBillingInfo.controls.city.errors?.required">City is required</p>                                
                              </div>
                              <span class="highlight"></span>
                              <span class="bar"></span>
                              <label>City*</label>
                           </div>
                        </div>
                        <div class="new_form_group ">
                           <div class="group new_form_label">      
                              <input type="text" placeholder=" " required class=" width_350" formControlName="state" >
                              <div *ngIf="brokerageBillingInfo.controls.state.touched">
                                <p class="form-validation" *ngIf="brokerageBillingInfo.controls.state.errors?.required">State is required</p>                                
                              </div>
                              <span class="highlight"></span>
                              <span class="bar"></span>
                              <label>State*</label>
                           </div>
                        </div>
                        <div class="new_form_group ">
                           <div class="group new_form_label">      
                              <input type="number" placeholder=" " id="zip"  required class=" width_350" formControlName="zipcode" >
                              <div *ngIf="brokerageBillingInfo.controls.zipcode.touched">
                                <p class="form-validation" *ngIf="brokerageBillingInfo.controls.zipcode.errors?.required">Zipcode is required</p>                                
                                <p class="form-validation" *ngIf="brokerageBillingInfo.controls.zipcode.errors?.minlength">Zipcode must be 6 characters</p>
                                <p class="form-validation" *ngIf="brokerageBillingInfo.controls.zipcode.errors?.maxlength">Zipcode must be 6 characters</p>
                              </div>
                              <span class="highlight"></span>
                              <span class="bar"></span>
                              <label>Zip*</label>
                           </div>
                        </div>
                        <div class="new_form_group ">
                           <input type="submit" class="submit_button" [ngClass]="{'submit-disable':brokerageBillingInfo.invalid}" [disabled]="brokerageBillingInfo.invalid" value="Submit" (click)="updateBillingInfo(brokerageBillingInfo)">
                        </div>
                        </form>
                     </div>
                  </div>

                <div class="group_1 mt-20">
                   <div class="title2">Invoice History</div>
                   <!-- <div class="invoice_history ">
                      <div class="invoice_group">
                         <div class="in_date dis_inline">February 11, 2018</div>
                         <div class="in_rs dis_inline">$199</div>
                         <div class="question_mark dis_inline"><i class="fa fa-download"></i></div>
                      </div>
                      <div class="invoice_group">
                         <div class="in_date dis_inline">January 11, 2018</div>
                         <div class="in_rs dis_inline">$199</div>
                         <div class="question_mark dis_inline"><i class="fa fa-download"></i></div>
                      </div>
                      <div class="invoice_group">
                         <div class="in_date dis_inline">December 11, 2017</div>
                         <div class="in_rs dis_inline">$199</div>
                         <div class="question_mark dis_inline"><i class="fa fa-download"></i></div>
                      </div>
                      <div class="invoice_group">
                         <div class="in_date dis_inline">November 11, 2017</div>
                         <div class="in_rs dis_inline">$199</div>
                         <div class="question_mark dis_inline"><i class="fa fa-download"></i></div>
                      </div>
                      <div class="invoice_group">
                         <div class="in_date dis_inline">October 11, 2017</div>
                         <div class="in_rs dis_inline">$199</div>
                         <div class="question_mark dis_inline"><i class="fa fa-download"></i></div>
                      </div>
                      <div class="show_all_invoice">Show all invoices</div>
                   </div> -->
                   <div class="invoice_history">
                    <div class="invoice_group" *ngIf="purchaseHistory.length == 0">
                       <div class="in_date dis_inline">No History</div>
                    </div>
                     <div class="invoice_group" *ngFor="let history of purchaseHistory">
                        <div class="in_date dis_inline">{{getPurchaseHistoryDate(history.transaction_date)}}</div>
                        <div class="in_rs dis_inline">${{history.price}}</div>
                        <div *ngIf="invoiceLoadingIndex == null && showInvoiceLoading == false" class="question_mark dis_inline"><i class="fa fa-download" (click)="downLoadInvoiceHistory(history)"></i></div>
                        <div *ngIf="invoiceLoadingIndex == history.id && showInvoiceLoading == true" class="question_mark dis_inline"><img class="invoice-loging" src="{{imagePrefix}}loading.gif"></div>
                     </div>
                     <div *ngIf="showAllinvoicesLink == true" class="show_all_invoice" (click)="getPurchasehistory(true)">Show all invoices</div>
                  </div>
                </div>
                <!-- <div class="group_1 mt-20">
                    <div class="new_form_group" *ngIf="accountStatus == true">
                        <input type="submit" class="Cancel-Subscription-btn" value="Cancel Subscription" data-toggle="modal" data-target="#cancelSubscriptionBroker">
                     </div>
                </div> -->
             </div>
             
             <div id="Agent" class="tab-pane fade">
                <div class="group_1 mt-20">
                   <div class="title2">Invite an agent to your brokerage</div>
                   <div class="new_form">
                      <form [formGroup]="agentInviteForm">
                      <div class="new_form_group ">
                         <div class="group new_form_label">      
                            <input type="text" placeholder=" " formControlName="email" required class=" width_350">
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>Agent Email*</label>
                         </div>
                         <div *ngIf="agentInviteForm.controls['email'].untouched">
                            <span></span>
                        </div>
                        <div *ngIf="agentInviteForm.controls['email'].touched && agentInviteForm.controls.email.errors?.email">
                            <span class="form-validation">Enter valid email address</span>
                        </div>
                        <div class="new_form_group dis_inline">
                          <input type="submit" [ngClass]="{'submit-disable':agentInviteForm.invalid}" [disabled]="agentInviteForm.invalid" class="submit_button with_bg" (click)="newAgentInvite(agentInviteForm)" value="Send">
                        </div>
                      </div>
                      </form>
                </div>
                </div>

                
                <div class="group_1 mt-20">
                   <div class="title2 titile-space">Your brokerage</div>
                  <div class="my_client_table_group">
                      <div class="myclient_navbar">
                          <ul>
                            <li class="active" data-toggle="pill" href="#Verified">Verified</li>
                            <li  data-toggle="pill" href="#Unverified" >Unverified</li>
                          </ul>
                      </div>
                  <div class="tab-content table-content">

                   <div id="Verified" class="tab-pane fade in active table-responsive selected_saved">
                   <table class="table">
                      <thead>
                         <tr>
                            <th (click)="agentSorting('V','NA')" class="th-width">Name <img id="V_NA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                            <th (click)="agentSorting('V','AM')">Agent ID<img id="V_AM" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                         </tr>
                      </thead>
                      <tbody>
                          <tr *ngFor="let agent of invitedVerifyAgentList">
                           <td *ngIf="agent.invite_status == 'A'">
                              <img *ngIf="agent.agent.profile.profile_photo ==''" src="{{imagePrefix}}default-placeholder.png" height="50px" class="agent-image dis_inline" alt=""> 
                              <img *ngIf="agent.agent.profile.profile_photo !=''" src="{{agent.agent.profile.profile_photo}}" height="50px" class="agent-image dis_inline" alt=""> 
                              <div class="dis_inline agent-name"><span class="dark">{{agent.agent.profile.name}}<br></span></div>
                              </td>
                              <td *ngIf="agent.invite_status == 'A'"><div class="bold_font">{{agent.agent.profile.mls_agent_id}}</div> </td>
                          </tr>
                      </tbody>                      
                   </table>
                   </div>
                  
                   <div id="Unverified" class="tab-pane table-responsive selected_saved">
                      <table class="table">
                         <thead>
                            <tr>
                               <th (click)="agentSorting('U','NA')" class="th-width">Name <img id="U_NA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                               <th (click)="agentSorting('U','AM')" colspan="4">Agent ID<img id="U_AM" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                            </tr>
                         </thead>
                         <tbody>
                             <tr *ngFor="let agent of invitedUnVerifyAgentList">
                              <td *ngIf="agent.invite_status =='P'">
                                  <img *ngIf="agent.agent.profile.profile_photo ==''" src="{{imagePrefix}}default-placeholder.png" height="50px" class="agent-image dis_inline" alt=""> 
                                  <img *ngIf="agent.agent.profile.profile_photo !=''" src="{{agent.agent.profile.profile_photo}}" height="50px" class="agent-image dis_inline" alt=""> 
                                  <div class="dis_inline agent-name"><span class="dark">{{agent.agent.profile.name}}<br></span></div>
                              </td>
                              <td *ngIf="agent.invite_status =='P'"><div class="bold_font">{{agent.agent.profile.mls_agent_id}}</div> </td>
                              <td *ngIf="agent.invite_status =='P'" class="accept">
                                <div class="deny-btn-size margin_zero accept-css"><span class="accept-space" (click)="acceptAgentRequest(agent.id,'A',agent)">Accept</span></div>
                              </td>
                              <td *ngIf="agent.invite_status =='P'" class="deny"><div class="deny-btn-size margin_zero deny_css"><span class="btn-space" (click)="acceptAgentRequest(agent.id,'D',agent)">Deny</span></div></td>
                            </tr>
                         </tbody>                      
                      </table>
                      </div>
                      </div>
                    </div>
                  </div>
             </div>
             <!-- </div> -->
             <!-- <div id="PreferredLender" class="tab-pane fade new_profile_details">
                <div class="group_1 mt-20" *ngIf="(myLender | json) != '{}'">
                   <div class="title2">Your preferred lender</div>
                   <div class="new_form">
                      <div class="new_form_group">
                        <span *ngIf="myLender?.profile_photo == ''">
                            <img src="{{imagePrefix}}default-placeholder.png" class="new_symbols-avatar searched-agent-image dis_inline" alt="">
                        </span>
                        <span *ngIf="myLender?.profile_photo != ''">
                            <img src="{{myLender?.profile_photo}}" class="new_symbols-avatar searched-agent-image dis_inline" alt="">
                        </span>
                        <div class="new_details dis_inline">
                            <div class="title3">
                              {{myLender?.name}}
                            </div>
                            <div class="sub_title">
                              {{myLender?.lending_company}}
                            </div>
                         </div>
                         <div class="lender dis_inline">
                            <input type="submit" (click)="manageBrokerLender(0,'remove')" class="remove-lender" value="Remove">
                        </div>
                      </div>
                   </div>
                </div>
                <div class="group_1 mt-20">
                   <div class="title2">Change your preferred lender</div>
                   <div class="new_form">
                      <div class="new_form_group ">
                         <div class="group new_form_label ">      
                            <input id="searchLenderTxt" #brokerLender type="text" placeholder=" " required class=" width_350" (keyup)="searchLenderUser(brokerLender.value)">
                            <i class="fa fa-search search_button"></i>
                            <span class="highlight"></span>
                            <span class="bar"></span>
                            <label>Lender Name</label>
                         </div>
                      </div>
                      <div *ngIf="showLenderList != false">
                        <div class="agent_found">
                            {{searchLenderList.length}} agent found
                        </div>
                        <div [ngClass]="{'search-agent-list': searchLenderList.length > 3}">
                           <div class="remove_agent search-agent-padding" *ngFor="let lender of searchLenderList" >
                               <span *ngIf="lender.profile_photo == null || lender.profile_photo == '' || lender.profile_photo == undefined" [ngClass]="{ 'remove-span' : lender.profile_photo == null || lender.profile_photo == '' || lender.profile_photo == undefined}"><img src="{{imagePrefix}}testmonial-default (1).png" class="img-responsive" alt=""></span>
                               <span *ngIf="lender.profile_photo != null || lender.profile_photo != undefined || lender.profile_photo != ''"><img src="{{lender.profile_photo}}" width="130px" height="130px" class="search-agent-image" alt=""></span>
                               <div class="remove_details">
                                   <div *ngIf="lender.name != null" class="name">{{lender.name}}</div>
                                   <div *ngIf="lender.lending_company != null" class="name">{{lender.lending_company}}</div>
                                   <div class="remove_button select" (click)="manageBrokerLender(lender.id,'add')">Add Lender</div>
                               </div>
                           </div>
                        </div>
                       </div>
                   </div>
                </div>
                <div class="pl_detail">
                   <div class="title2">My lender isn’t showing up</div>
                   <div class="sub_title pref-lender-sub-title">Send your preferred lender this link so they can set up a profile on Open to Close</div>
                   <div class="new_form_group">
                      <input type="text" id="inviteLender" placeholder="Lender Name" disabled class="width_350 input_new">
                   </div>
                </div>
             </div> -->
          </div>
       </div>

       <div *ngIf="accountStatus != false" class="artboard6_sidebar side_bar_height">
          <div class="Account account_mtp">Account Status</div>
          <span class="agent-status">Premium</span>
          <p class="Account_text">You have full access to all features.</p>
          <!-- <div class="border"></div> -->
          <!-- <input type="submit" class="submit_button button_with_bg" (click)="openPlansModal()" value="Upgrade Now"> -->
          <div class="new_form_group" *ngIf="accountStatus == true && permanentPremium != true">
            <input type="submit" class="Cancel-Subscription-btn" value="Cancel Subscription" data-toggle="modal" data-target="#cancelSubscriptionBroker">
         </div>
          <!-- <div class="a_10">10 <span>seats available</span></div>
          <div class="Manage">Manage Seats</div> -->
       </div>

       <div *ngIf="accountStatus == false" class="artboard6_sidebar artboard7_side dis_inline">
        <div class="Account">Account Status <br><span>Free</span></div>
        <p class="Account_text">Upgrade your account today to take advantage of all the features available in Open Houses Direct.</p>
        <div *ngIf="permanentPremium == false"><input type="submit" class="submit_button button_with_bg" (click)="openPlansModal()" value="Upgrade Now"></div>
       </div>

    </div>
 </div>
</div>

<div>
  <div id="cancelSubscriptionBroker" class="modal fade" role="dialog">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
           <h4 class="modal-title">Cancel Subscription</h4>
          </div>
          <div class="modal-body">
            <p>Are you sure to cancel subscription?</p>
          </div>
          <div class="modal-footer">
            <input type="submit" [disabled]="disablecancelSubsBtn == true" value="Yes" class="submit_button with_bg" data-dismiss="modal" (click)="cancelSubscriptionBroker()">
            <input type="submit" class="Cancel-Subscription-btn" data-dismiss="modal" value="No">
          </div>
        </div>      
      </div>
    </div>
</div>

<div class="footer">
<footer></footer>
</div>