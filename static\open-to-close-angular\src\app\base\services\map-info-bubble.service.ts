import { Injectable } from '@angular/core';
import { Http, Request } from '@angular/http';
import { IMAGEPATH, PATH } from '@app/base/components/base.constants';
import {CurrencyPipe} from '@angular/common'

import 'rxjs/add/operator/map'
import { BaseComponent } from '@app/base/components/base.component';

declare var InfoBubble;

@Injectable()
export class MapInfoBubbleService{

    public infoBubble: any;
    html:any = "";
    fileUrl: any = IMAGEPATH + "symbols-map-hover.png";
    houseImage = IMAGEPATH+ "symbols-map-hover.png";

    constructor(public http:Http, public baseService: BaseComponent,private cp: CurrencyPipe){}

    mapInfoBubble(htmlCode, x:number,y:number){
      this.infoBubble = new InfoBubble({
        content: htmlCode,
        backgroundColor: 'none',
        borderColor: 'none',
        borderStyle: 'none',
        borderRadius: 5,
        borderWidth: 0,
        padding: 0,
        maxWidth : 200,
        maxHeight :240,
        arrowSize:0,
        hideCloseButton: true,
        shadowStyle: 0,
        disableAnimation : true,
        pixelOffset: [x,y]
      });
      return this.infoBubble;
    }

    changeHTML(imageUrl, price, bds, baths, sqft, address, data){
      console.log('MapData - ', PATH)
      //first_event_type: "AO"

      if(imageUrl != ''){
        this.fileUrl = imageUrl;
      }else{
        this.fileUrl = IMAGEPATH + "symbols-map-hover.png";
      }
      price = this.cp.transform(price,"",'symbol','1.0')
      this.html = ""
      this.html += " <div class='box_on_map cursor-pointer'>";
      this.html += "<div class='map_group'>";
      if(data && data.first_event_type== "AO"){
        this.html += "<div class=''><img class='brokerImage' style='width: 110px !important;  position:absolute; margin-top: 5px; margin-left: 5px;' src='static/open-to-close-angular/dist/assets/images/72_tag.png'> </div>";
      } else if (data && data.event_list && Object.keys(data.event_list)) {
          if(data.event_list[0] && data.event_list[0].event_type == 'AO') {
            this.html += "<div class=''><img class='brokerImage' style='width: 110px !important;  position:absolute; margin-top: 5px; margin-left: 5px;' src='static/open-to-close-angular/dist/assets/images/72_tag.png'> </div>";
          }
      }
      this.html += "<img src='"+this.fileUrl+"' class='box_on_image_image' alt=''>";
      this.html += "<div class='on_map_details'>";
      this.html += "<div class='on_map_price title'>"+price+"</div>";
      this.html += "<div class='on_map_detail'>"+bds+'bds' +' | '+ baths+'bths'+' | '+sqft+' sqft'+"</div>";
      this.html += "</div>";
      this.html += "</div>";
      this.html += "<div class='on_map_address'>"+address+"</div>";
      this.html += "</div>";
      return this.html;

      // return this.html = " <div class='box_on_map cursor-pointer'>"+
      //         "<div class='map_group'>"+
      //         // "<div class=''><img class='brokerImage' style='width: 110px !important;  position:absolute; margin-top: 5px; margin-left: 5px;' src='assets/images/72_tag.png'> </div>"+
      //           "<img src='"+this.fileUrl+"' class='box_on_image_image' alt=''>"+
      //           "<div class='on_map_details'>"+
      //               "<div class='on_map_price title'>"+price+"</div>"+
      //               "<div class='on_map_detail'>"+bds+'bds' +' | '+ baths+'bths'+' | '+sqft+' sqft'+"</div>"+
      //           "</div>"+
      //         "</div>"+
      //         "<div class='on_map_address'>"+address+"</div>"+
      //       "</div>";
      }


    setHTML(timeList){
      let html = " <div id='map_info_bubble' class='box_on_map_event cursor-pointer'>";
      for(let i=0;i<timeList.length;i++){
        if(timeList[i]['property_file'] != ''){
          this.fileUrl = timeList[i]['property_file'];
        }else{
          this.fileUrl = IMAGEPATH + "symbols-map-hover.png";
        }
        html += "<div class='map_group event-list-map'>"+
                  "<img src='"+this.fileUrl+"' class='small-image' alt=''>"+
                  "<div class='event-map-detail' style='overflow:hidden;'>"+
                    "<div class='on_map_price street-title' style='overflow:scroll;'>"+timeList[i]['street_address']+"</div>"+
                    "<div class='on_map_detail event-bds'>"+timeList[i]['bedroom']+'bds' +' | '+ timeList[i]['full_bath']+'bths'+' | '+timeList[i]['living_area']+' sqft'+"</div>"+
                    "<div class='on_map_detail event-map-date'>"+this.baseService.getStringEventDate(timeList[i]['date'],timeList[i]['start_time'],timeList[i]['is_listhub_event'])+"</div>"+
                    "<div class='on_map_detail event-map-time'>"+this.baseService.getTimeTypes(timeList[i]['start_time'],'',timeList[i]['date'],timeList[i]['is_listhub_event']) +" - " + this.baseService.getTimeTypes(timeList[i]['end_time'],'',timeList[i]['date'],timeList[i]['is_listhub_event'])+"</div>"+
                  "</div>"+
                "</div>"
      }
      html+="</div>";
      return html;
    }

}
