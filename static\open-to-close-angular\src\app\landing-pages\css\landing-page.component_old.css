body
{
     margin: 0px;
     padding:0px;
    font-family: 'Source Sans Pro', sans-serif;

}
 #banner_page_1
{
    background:linear-gradient(to right, #9F9F9F 0%, #EBEBEB 100%);
    padding-bottom: 50px;
}
#banner_landing_page
{
    background: #ffffff;
    padding-bottom: 50px;
}
.space_1
{
    text-align: center;
    padding-top: 40px;
}
.space_1_h1
{
    font-size: 70px;
    line-height: 70px;
    color: #7a7a7a;

}

.space_1_p {
    font-size: 22px;
    color: #808080;
    margin-top: 50px;
    margin-bottom: 35px;
    max-width: 50%;
    margin: 50px auto 35px auto;
}


#space_2
{
    background-color: #eceff1;
    padding:50px 0px 50px 0px;
}
.space_2_h1
{
    font-weight: 600;
    font-size: 50px;
    line-height: 60px;
    color: #7d7d7d;
    margin-top: 10px;
}

.space_2_h3
{
    font-weight: 400;
    color: #7d7d7d;
    margin-top: 10px;
    font-style: italic;
}
.space_2_p
{
    font-size: 24px;
    font-weight: 500;
    line-height: 34px;
    color: #8d8d8d;
    margin-top: 15px;
    margin-bottom: 0px;
}
.max_width {
    max-width: 60%;
}
.space_2_b
{
    font-weight:800;
    color: #5A5A5A;
}
.circle_img
{
    /* background-color: #d8d8d8; */
    /* border-radius: 50%; */
    /* border: 1px solid #979797; */
    /* width: 406px; */
    /*height: 406px;*/
}
#space_3
{
    background-color: #fff;
    padding: 50px 0px 50px 0px;
}

#space_4
{
    background-color: #eceff1;
    padding:50px 0px 50px 0px;
}

#space_5
{
    background-color: #eceff1;
    padding: 50px 0px 50px 0px;
}

#space_6
{
    background-color: #fff;
    padding:50px 0px 50px 0px;
}

.space_pricing
{
    background-color: #fff;
    padding:44px 0px 50px 0px;

}
.specing_h2
{
    font-size: 57px;
    color: #595959;
    font-weight: 400;
    margin-bottom: 15px;
}
.space_4_p
{
    font-size: 25px;
    color: #595959;
    margin-bottom: 60px;
}
.Pricing_heading
{
    background:#f0f2f4;
    padding: 10px;
    font-size: 30px;
    color: #676767;
}


.btn-pricing_1 {
     width: 128px;
    border-radius: 4px;
    border: 1px solid #00c2b7;
    /* background: #ffffff; */
    color: #4cb8a7 !important;
    font-size: 16px;
    /* outline: 0; */
    margin: 10px 20px 48px 20px;
    display: inline-block;
    padding: 10px;
    text-decoration: none !important;
    cursor: pointer !important;
}

.premium-price-text {
    color: #10b8a8;
    font-size: 18px
}


.btn-pricing_2 {
    background-color: #10b8a8;
    border-radius: 4px;
    width: 145px;
    display: inline-block;
    padding: 10px;
    color: #fff !important;
    text-decoration: none !important;
    margin: 10px 20px 10px 20px;
    cursor: pointer !important;
}

.space-4-pricing-h2 {
    font-size: 56px;
    padding-top: 0px;
    color: #737373;
}

.space-4-pricing-p
{
    font-size: 16px;
    color: #8d8d8d;;
    font-weight: 400;
    /*padding-top: 10px;*/
    line-height: 27px;
}


.font-check.text-right
{
    padding:0px;
    position: relative;
    top: 4px;
}
.advice-p
{
    padding:0px;
}
.font-check
{
    color: #4cb8a7;

}
.advice
{
    padding-top: 20px;
    padding-bottom: 20px;
}
.advice ul
{
    padding: 0px
}
.advice-p
{
    font-size: 14px;
    padding-bottom: 18px;
    font-weight: 500;
    color: #dbdbdb;
}
.advice-b
{
    color:#7a7a7a;
}
.advice-c
{
    color:#10b8a8;
}
.advice-d
{
    color: #8d8d8d;
    font-size: 12px;
    color: #8d8d8d;
}


.Pricing_heading_premium {
    background: #10b8a8;
    padding: 10px;
    font-size: 30px;
    color: #ffffff;
}

.primunm_btn
{
    color: #ffff !important;
    background-color: #10b8a8 !important;
    border-radius: 4px;
    width: 145px;
    /*height: 83px;*/
}


a.sign_up_button.space_1_btn {
    background-color: #10b8a8;
    border-radius: 23.5px;
    width: 122px;
    padding: 10px;
    display: inline-block;
    color: #fff;
    text-decoration: none;
    cursor: pointer !important;
    font-weight: 600;
}


.show_border {
    border: 1px solid #c2c2c2;
    border-radius: 4px;
    float: left;
    width: 100%;
    margin-bottom: 20px;
}

.advice {
    width: 90%;
    margin: auto;
}

.advice li {
    font-size: 15px;
    color: #7a7a7a;
    list-style-type: none;
    margin-bottom: 7px;
    font-weight: 600;
    margin-left: 5px;
}

.advice li i {
    margin-right: 10px;
    color: #00c2b7;
    width: 12px;
    height: 9px;
}


.advice li.theme_color {
    color: #10b8a8;
}

.advice li.theme_color_disable , .advice li.theme_color_disable i {
    color: #dbdbdb;
}


.pricing-advise {
    border-top: .5px solid #c2c2c2;
    margin-top: 20px;
    padding-top: 20px;
}

h2.space-4-pricing-h2 sup {
    font-size: 28px;
    color: #737373;
}


body {
    background: #fff !important;
}


.section5_landing_page .sec_5_text {
    color: #9c9c9c;
    font-size: 23px;
    font-weight: 400;
    text-align: center;
    margin-bottom: 60px;
    font-style: oblique;
}




/*.space_wrapper
{
    display: flex;
    vertical-align: middle;
    height: 100%;
    flex-direction: column;
    justify-content: center;
}

.circle_img {
    display: flex;
    height: 100%;
    vertical-align: middle;
    align-items: center;
    justify-content: center;
}*/

.circle_img img {
    height: 100%;
    width: 100%;
    object-fit: fill;
    background-color: #d8d8d8;
    border-radius: 50%;
    border: 1px solid #979797;
    width: 406px;
    height: 406px;
}

.full_img img{
  height: auto !important;
  width: 100% !important;
  -o-object-fit: fill;
  object-fit: fill;
}

.pricing_max_width {
    max-width: 80%;
    margin: auto;
}

.duration-m-d{
  top: 0px !important;
  font-size: medium;
  text-align: start;
  vertical-align: text-top;
  margin: 15px 0px 0px 15px;
  display: inline-block;
  font-weight: 600;
}
.duration-m-s{
  vertical-align: sub;
  font-weight: normal;
}

.selected-plan-p {
  color: #ffffff !important;
}

.selected-plan-price sup {
  font-size: 28px;
  color: #737373;
}


.banner_p1 {
    font-size: 22px;
    font-weight: 700;
    color: #777777;
}

.banner_p2 {
    font-size: 22px;
    color: #808080;
}

.banner_p3 {
    font-size: 22px;
    color: #808080;
}

.line{
    width: 85.5em;
    border-width: 0.17em;
    border-color: #d3d3d3;
    margin-top: 5%;
    margin-bottom: 5%;
}

.pricing-introduction-title {
    font-size: 50px;
    padding-top: 1.5%;
    font-weight: 600;
    color: #7d7d7d;
    margin-bottom: 15px;
}

.pricing-introduction-description {
    font-size: 25px;
    color: #6d6d6d;
    padding-top: 15px;
}

.copyright_text {
    font-size: 1.5em;
    color: #676767;
    padding-top: 3.1em;
    margin-bottom: -0.2em;
    padding-left: 11%;
    padding-right: 11%;
}


@media only screen and (max-width: 1023px) {


    .pricing_max_width {
        max-width: 100%;
        margin: auto;
        float: left;
        width: 100%;
    }

    .pricing_max_width .col-sm-8 {
        float: left;
        width: 100%;
        margin-bottom: 20px;
    }


    p.space_1_p {
        max-width: 90%;
    }

    /*section#space_2 .row {
        display: block;
    }
    */

    .space_2_h1 {
        font-size: 40px;
        line-height: 40px;
    }

    .circle_img img {
        width: 306px;
        margin: auto;
        height: 306px;
        display: table;
    }

    .space_2_p {
      font-size: 16px;
      line-height: 25px !important;
      margin-top: 20px;
      margin-bottom: 20px;
    }

    .dis_flex_mobile
    {
        display: flex;
        flex-direction: column
    }

    .roder_1
    {
        order: 1
    }


    .roder_2
    {
        order: 2
    }


    .advice li {
        display: flex;
    }

    .advice li i {
        position: relative;
        top: 5px;
        right: 5px;
    }

    .max_width{
        max-width: 100%;
        text-align: center;
    }

    .line{
        width: 90%;
    }

    .pricing-introduction-title {
        font-size: 31px !important;
    }

    .pricing-introduction-description {
        font-size: 23px;
    }

}



@media only screen and (min-width: 1024px) {
    section#space_2 .row {
        display: flex;
    }
}

.banner_title_agent{
  color: #ffffff;
  font-size: 70px;
  font-weight: 400;
  line-height: 70px;
  text-align: center;
  position: relative;
  top: -80px;
}

#space_4
{
    background-color: #eceff1;
    padding:50px 0px 50px 0px;
}

#space_5
{
    background-color: #fff;
    padding: 50px 0px 50px 0px;
}

#space_6
{
    background-color: #eceff1;
    padding:50px 0px 50px 0px;
}

@media only screen and (max-width: 767px){
  .open_landing_page {
    padding-top: 30px;
  }

  .banner_p1{
    font-size: 18px;
  }

  .banner_p2{
    font-size: 18px;
  }

  .banner_p2_1{
    margin-left: -1%;
  }

  .banner_p3{
    font-size: 18px;
  }

  #space_3{
    padding: 50px 2% 50px 2%;
  }

  #space_2{
    padding: 50px 2% 50px 2%;
  }
  #space_3{
    padding: 50px 2% 50px 2%;
  }
  #space_5{
    padding: 50px 2% 50px 2%;
  }
  #space_6{
    padding: 50px 2% 50px 2%;
  }

  .space_2_p{
    font-size: 18px;
  }

  .specing_h2{
    font-size: 35px;
  }
  .space_4_p{
    margin-left: 5%;
    margin-right: 5%;
    font-size: 18px;
  }

  /* .pricing-mo{ */
    /* padding-left: 0px !important; */
    /* padding-right: 0px !important; */
  /* } */

  .show_border{
    border: 0px solid #c2c2c2;
  }

  .advice{
    width: 85%;
  }
  .banner_title_agent{
    color: #ffffff;
    font-size: 40px;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    position: relative;
    top: 15px;
  }

  .duration-m-s{
    font-size: 20px;
  }
  .duration-m-d{
    margin: 5px 0px 0px 0px;
    font-weight: 200;
  }
  .price-text-title{
    font-size: 18px;
    margin-left: 10px;
    font-weight: 500;
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
  }
  .space-4-pricing-p{
    /* line-height: 15px; */
  }
  .btn-pricing_2{
    margin: 20px 20px 10px 20px;
  }
  .pricing-mo{
    border-bottom: .5px solid #c1c2c1 !important;
  }
}

.price-col{
  color: #8d8d8d;
}


.duration-m-s{
    font-size: 20px;
  }
  .duration-m-d{
    margin: 5px 0px 0px 0px;
    font-weight: 200;
  }
.price-text-title{
    font-size: 18px;
    margin-left: 10px;
    font-weight: 500;
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
}
.free-pricing_1{
    padding-bottom: 14%;
}
.free-pricing_2{
  padding-bottom: 17%;
}
.main-title{
    display: inline-block;
    float: none;
}
