
<div class="header">
  <nav class="navbar navbar-inverse hidden-xs">
    <div class="container-fluid">
      <div class="navbar-header">
        <a class="navbar-brand cursor-pointer"><img src="{{imagePrefix}}logo.png" class="img-responsive"   alt="logo"></a>
      </div>
        <div id="menu_group" *ngIf="showOption">
            <ul class="nav navbar-nav menu_new cursor-pointer">
                <!-- <li [ngClass]="{'active2': selectedHeader=='admin-users' }"><a (click)="routeOnUrl('admin/admin-users')">User Management</a></li> -->
                <li [ngClass]="{'active2': selectedHeader=='home-buyer' }"><a (click)="routeOnUrl('admin/home-buyer')">Home Buyers</a></li>
                <li [ngClass]="{'active2': selectedHeader=='listing-agent' }"><a (click)="routeOnUrl('admin/listing-agent')">Listing Agents</a></li>
                <li [ngClass]="{'active2': selectedHeader=='brokerage' }"><a (click)="routeOnUrl('admin/brokerage')">Brokerages</a></li>
                <!-- <li [ngClass]="{'active2': selectedHeader=='admin-plans' }"><a (click)="routeOnUrl('admin/admin-plans')">Billing Management</a></li> -->
                <li [ngClass]="{'active2': selectedHeader=='listing-management' }"><a (click)="routeOnUrl('admin/listing-management')">Listing Management</a></li>
            </ul>
            </div>
            <div *ngIf="showOption">
                <ul class="nav navbar-nav navbar-right cursor-pointer">
                <li><a (click)="logOutAdmin()">Log Out</a></li>
                </ul>
            </div>
      </div>
   </nav>
</div>
