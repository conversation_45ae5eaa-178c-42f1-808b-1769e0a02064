<div>
    <header></header>
</div>
<div class="profile_page_1 artboard2_page">
        <div class="container">
           <div class="artboard2 payment_checkout_group">
              <div class="payment_checkout">  
                <div class="pt-88">  
                    <div class="title2">Your Order</div>
                    <div class="border"></div>
                    <div class="group payment_form mt-20 type">      
                        <label class="type_lable">{{selectedPlans.name}}</label>
                        <div class="cycle_price abs_text font-40 ">{{selectedPlans.price}} <span>{{selectedPlans.currency_iso_code}}</span></div>
                        <span class="type_text">Premium</span>
                    </div>
                    <div class="total_price">  Total:  <div class="total_price_">{{selectedPlans.price}}</div> </div>
                    <div class="title2 mt-70">Your payment method</div>
                    <div class="border"></div>                    
                    <div class="check_group payment_pathod mt-10" >
                        <div class="form_group text-left"  *ngIf="showNewCard == false || cardPresent == true">
                            <!-- <span class="checkmark"></span> -->
                            <label for="" class="pay_end_date">Your saved credit card ending in <span>{{savedCreditCard}}</span></label>
                        </div>                    
                        <div class="form_group text-left" *ngIf="showNewCard == true && cardPresent == false">
                        <label class="credit-icon"><i class="fa fa-cc-visa"></i></label>
                        <label class="credit-icon"><i class="fa fa-cc-mastercard"></i></label>
                        <label class="credit-icon"><i class="fa fa-cc-amex"></i></label>
                        <label class="credit-icon"><i class="fa fa-cc-discover"></i></label>
                        </div>                    
                    </div> 
                </div>  
                <span *ngIf="showNewCard != false">
                    
                      <form [formGroup]="purchaseCardInfoForm">
                    <div *ngIf="showCarditCardInfo == true">
                    <div class="group_1 mt-20 form_width_350">
                          <div class="title2">Card Information</div>
                        <div class="new_form">
                            <div class="row">  
                                <div class="col-sm-16">
                                    <div class="new_form_group dis_inline pull-left">
                                        <div class="group new_form_label">      
                                        <input type="text" placeholder=" " required="" class=" " formControlName="first_Name" required>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>First Name*</label>
                                        <div *ngIf="purchaseCardInfoForm.controls.first_Name.touched">
                                            <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.first_Name.errors?.required">Enter first name</p>
                                        </div> 
                                        </div>
                                    </div>
        
                                    <div class="new_form_group dis_inline ml-10 pull-left">
                                        <div class="group new_form_label">      
                                        <input type="text" placeholder=" " required="" class=" " formControlName="last_Name" required>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Last Name*</label>
                                        <div *ngIf="purchaseCardInfoForm.controls.last_Name.touched">
                                            <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.last_Name.errors?.required">Enter last name</p>
                                        </div> 
                                        </div>
                                    </div>  
                                </div>  
                            </div>                             
                              <div class="new_form_group ">
                                <div class="group new_form_label" formGroupName="creditCard">      
                                   <input type="text" placeholder=" " required="" class=" width_350" formControlName="number" required>
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>Card Number*</label>
                                   <div *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['number'].touched">
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['number'].errors?.required">Enter Card number</p>
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['number'].errors?.pattern">Card number must be Digit or max length 16</p>
                                  </div> 
                                </div>
                             </div>
                             <div class="new_form_group ">
                                <div class="group new_form_label" formGroupName="creditCard">      
                                   <input type="password" placeholder=" " required="" class=" width_350" formControlName="cvv" required>
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>CVV*</label>
                                   <div *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['cvv'].touched">
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['cvv'].errors?.required">Enter cvv number</p>
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['cvv'].errors?.pattern">cvv must be Digit or max length 4</p>
                                  </div>
                                </div>
                             </div>
                             <div class="new_form_group dis_inline pull-left" formGroupName="creditCard">
                                <div class="group new_form_label">      
                                   <input type="text" placeholder=" " required="" class=" " #date (keyup)="cardExDateV(date.value)"  formControlName="expirationMonth" required>
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>Month*</label>                                                                                                         
                                    <div>
                                    <span *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].touched">
                                      <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].errors?.required" >Enter Month</p>
                                      <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].errors?.number">Month must be Digit or max length 2</p>
                                      <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationMonth'].errors?.incorrect">Invalid Month</p>
                                    </span>
                                  </div>
                                </div>
                             </div>
  
                             <div class="new_form_group dis_inline ml-10 pull-left" >
                                <div class="group new_form_label" formGroupName="creditCard">      
                                   <input type="text" placeholder=" " required="" class=" "  #year (keyup)="cardExYearV(year.value)" formControlName="expirationYear" required>
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>Year*</label>
                                  <div>
                                    <span *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].touched">
                                      <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].errors?.required">Enter Year</p>
                                      <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].errors?.yearNumber">Year must be Digit or max length 4</p>
                                      <p class="form-validation err-year-width" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].errors?.maxlength">Year must be Digit or max length 4</p>
                                      <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['expirationYear'].errors?.incorrect">Invalid Year</p>                                          
                                    </span>
                                  </div>
                                 
                                </div>
                             </div>                             
                        </div>
                       </div>
                    </div>
                    <div *ngIf="showBillingInfo == true">
                     <div class="group_1 mt-20 form_width_350" formGroupName="creditCard">
                          <div class="title2">Billing Information</div>
                          <div class="new_form">
                             <div class="new_form_group ">
                                <div class="group new_form_label" formGroupName="billingAddress">      
                                   <input type="text" placeholder=" " required="" class=" width_350" formControlName="address_1" required>
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>Billing Address*</label>
                                   <div *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['address_1'].touched">
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['address_1'].errors?.required">Billing address is required</p>                                
                                  </div>
                                </div>
                             </div>
                             <div class="new_form_group ">
                                <div class="group new_form_label" formGroupName="billingAddress">      
                                   <input type="text" placeholder=" " class=" width_350" formControlName="address_2">
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>Address (Cont.)</label>
                                   <div *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['address_2'].touched">
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['address_2'].errors?.required">Address(Cont.) is required</p>                                
                                  </div>
                                </div>
                             </div>
                             <div class="new_form_group ">
                                <div class="group new_form_label" formGroupName="billingAddress">      
                                   <input type="text" placeholder=" " required="" class=" width_350" formControlName="city" required>
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>City*</label>
                                   <div *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['city'].touched">
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['city'].errors?.required">City is required</p>                                
                                  </div>
                                </div>
                             </div>
                             <div class="new_form_group ">
                                <div class="group new_form_label" formGroupName="billingAddress">      
                                   <input type="text" placeholder=" " required="" class=" width_350" formControlName="state" required>
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>State*</label>
                                   <div *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['state'].touched">
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['state'].errors?.required">State is required</p>                                
                                  </div>
                                </div>
                             </div>
                             <div class="new_form_group ">
                                <div class="group new_form_label" formGroupName="billingAddress">      
                                   <input type="text" placeholder=" " required="" class=" width_350" formControlName="zipcode" required>
                                   <span class="highlight"></span>
                                   <span class="bar"></span>
                                   <label>Zip*</label>
                                   <div *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['zipcode'].touched">
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['zipcode'].errors?.required">Zipcode is required</p>                                
                                    <p class="form-validation err-text-align" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['zipcode'].errors?.minlength">Zipcode must be 5 characters</p>
                                    <p class="form-validation" *ngIf="purchaseCardInfoForm.controls.creditCard['controls']['billingAddress']['controls']['zipcode'].errors?.maxlength">Zipcode must be 5 characters</p>
                                  </div>
                                </div>
                             </div>
                                                        
                          </div>
                       </div>
                    </div>
                      </form>
                      <div class="new_form_group ">
                            <input type="submit" class="submit_button button_with_bg" [ngClass]="{'submit-disable':purchaseCardInfoForm.invalid}" [disabled]="purchaseCardInfoForm.invalid || !buttonEnable" value="Complete Order" (click)="completeOrder(purchaseCardInfoForm)">
                         </div>  
                    </span>
                    <div *ngIf="showNewCard == false " class="new_form_group">
                        <input type="submit" class="submit_button button_with_bg" value="Complete Order" (click)="completeOrderByCard()">
                    </div>
              </div>
           </div>
        </div>
     </div>
<div>
    <footer></footer>
</div>