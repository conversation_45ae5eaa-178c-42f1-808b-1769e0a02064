import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ProfileService } from '@app/profile/service/profile.service';
import { FormGroup, FormControl,Validators, ValidatorFn } from '@angular/forms';
import { BrokerageDetail } from '@app/profile/models/brokerage-detail';
import { HomeBuyerDetail } from '@app/profile/models/home-buyer-detail';
import { AgentDetail } from '@app/profile/models/agent-detail';
import { AuthService } from '@app/auth/services/auth.service';
import { Plans } from '@app/profile/models/plan';
import { PurchaseService } from '@app/purchase/service/purchase.service';
import { Lender } from '@app/profile/models/lender';

@Component({
  selector: 'app-profile',
  templateUrl: '../views/profile.component.html',
  styleUrls: ['../css/profile.component.css']
})
export class ProfileComponent extends BaseComponent implements OnInit {

  profilePassword:FormGroup;
  profileBillingForm:FormGroup;

  profileService: ProfileService;
  authService: AuthService;
  purchaseService : PurchaseService;

  public static brokerageUser: BrokerageDetail = new BrokerageDetail();
  public static homeBuyerUser: HomeBuyerDetail = new HomeBuyerDetail();
  public static mortgageLender: Lender = new Lender();
  public static listingAgent: AgentDetail = new AgentDetail();
  

  constructor() {
    super();    
    this.defineProfileForms();
    this.profileService = ServiceLocator.injector.get(ProfileService);
    this.authService = ServiceLocator.injector.get(AuthService);
    this.purchaseService =ServiceLocator.injector.get(PurchaseService);

    if(BaseComponent.user == undefined || Object.keys(BaseComponent.user).length == 0){
      this.authService.getUserDetails().subscribe(res => {
        BaseComponent.user = res.result;
      });
    }
  }
  
  ngOnInit(){
    if(this.getPreviousScreen() != '/profile'){
      this.clearLocalStorageSearch();
    }
  }

  defineProfileForms(){
    this.profilePassword = new FormGroup({
      password : new FormControl('',[Validators.required, Validators.minLength(5),Validators.maxLength(15)]),
      new_password : new FormControl('', [Validators.required, Validators.minLength(5),Validators.maxLength(15)]),
      confirm_new_password : new FormControl('', [Validators.required, Validators.minLength(5),Validators.maxLength(15)])
    }, passwordMatchValidator);

    function passwordMatchValidator(g: FormGroup) {
      return g.get('new_password').value === g.get('confirm_new_password').value ? null : {'mismatch': true};
    }

    this.profileBillingForm = new FormGroup({
      billing_adddres: new FormControl('',Validators.required),
      address_cont: new FormControl('',Validators.required),
      city : new FormControl('',Validators.required),
      state: new FormControl('',Validators.required),
      zip: new FormControl('',Validators.required)
    });
  }
}
