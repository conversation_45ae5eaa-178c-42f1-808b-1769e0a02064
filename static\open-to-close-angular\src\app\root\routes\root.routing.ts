import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthComponent } from '@app/auth/components/auth.component';
import { AdminPlansComponent } from '@app/admin/adminPlans/component/admin-plans.component';
import { UsersComponent } from '@app/admin/users/component/users.component';

const routes: Routes = [
  // { path: "join-us", component: AuthComponent },
  { path: '', component: AuthComponent },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})

export class RootRoutingModule { }