import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ApiService } from '@app/base/services/api.service';
import { BaseRoutingModule } from '@app/base/routes/base-routing.module';
import { RouterModule } from '@angular/router';
import { BaseComponent } from '@app/base/components/base.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { HttpClientModule } from '@angular/common/http';
import { HeaderComponent } from '@app/root/components/header.component';
import { FooterComponent } from '@app/root/components/footer.component';
import { ToastService } from '@app/base/services/toast.service';
import {ToastyModule} from 'ng2-toasty';
import { AuthGuard } from '@app/base/services/auth.guard.service';
import { HBAuthGuard } from '@app/base/services/auth.HB.service';
import { LAauthguard } from '@app/base/services/auth.LA.service';
import { LPauthguard } from "@app/base/services/auth.LP.service";
import { MLAuthGuard } from '@app/base/services/auth.ML.service';
import { eventManagerAuthguard } from '@app/base/services/auth.event-manager.service';
import { BRAuthGuard } from '@app/base/services/auth.BR.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { SearchBarComponent } from '@app/searchBar/component/search-bar.component';
import { MapInfoBubbleService } from '@app/base/services/map-info-bubble.service';
import { AngularFireModule } from 'angularfire2';
import { environment } from '@env/environment';
import { AngularFireDatabaseModule } from 'angularfire2/database';
import { AngularFireAuthModule } from 'angularfire2/auth';
import { MessagingService } from '@app/messaging.service';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { Http } from '@angular/http';
import {DndModule} from 'ng2-dnd';
import {NgxPaginationModule} from 'ngx-pagination';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { AuthService } from '@app/auth/services/auth.service';
import { EventModalService } from '@app/event-modal/service/event-service';
import { SearchBarFilterComponent } from '@app/searchBar/component/filter.component';
import { AddEventComponent } from '@app/add-event/components/add-event.component';
import { AddEventService } from '@app/add-event/service/add-event-services';
import { CurrencyPipe } from '@angular/common';
import { GetPlansService } from '@app/base/services/get.plans.service';
import { UserAccountStatusAuthGuard } from '@app/base/services/user.account.status.service';
import { ClientLeadsAuthGuard } from '@app/base/services/client-leads.auth.service';
import { ChatAuthguard } from '@app/base/services/chat.auth.service';
import { AdminHeaderComponent } from '@app/admin/admin-header/component/admin-header.component';
import { PhonePipe } from '@app/myLeads/pipes/phone-number-formating.pipe';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { SafeHtmlPipe } from '@app/safe-html.pipe';
import { AutoCompleteDirective } from '@app/directives/auto-complete.directive';


@NgModule({
  declarations: [HeaderComponent,FooterComponent,SearchBarComponent,EventModalComponent,SearchBarFilterComponent,AddEventComponent,AdminHeaderComponent,PhonePipe,SafeHtmlPipe,AutoCompleteDirective],

  imports: [
    CommonModule,
    BaseRoutingModule,
    BrowserAnimationsModule,
    HttpModule,
    HttpClientModule,
    FormsModule,   
    ReactiveFormsModule,
    ToastyModule.forRoot(),
    RouterModule.forRoot([]),
    AngularFireDatabaseModule,
    AngularFireAuthModule,
    NgSelectModule,
    DndModule.forRoot(),
    NgxPaginationModule,
    CKEditorModule
],
  
  exports: [RouterModule,FormsModule,ReactiveFormsModule,HeaderComponent,FooterComponent, ToastyModule, NgSelectModule ,SearchBarComponent,DndModule,EventModalComponent,NgxPaginationModule,AddEventComponent,AdminHeaderComponent,PhonePipe,SafeHtmlPipe,AutoCompleteDirective],
  providers: [BaseComponent,ApiService,ToastService,AuthGuard,HBAuthGuard,LAauthguard,BRAuthGuard,MLAuthGuard,eventManagerAuthguard,MapInfoBubbleService,MessagingService,AuthService,EventModalService,AddEventService,CurrencyPipe,GetPlansService,UserAccountStatusAuthGuard, ClientLeadsAuthGuard, eventManagerAuthguard,ChatAuthguard, LPauthguard]
})
export class BaseModule { }
