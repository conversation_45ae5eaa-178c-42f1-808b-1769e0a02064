<admin-header></admin-header>
  
  <div class="col-sm-16 top-bar">
    <div class="log-mt">
    </div>
  </div>
  
  <div class="user-box-pd">
    <div class="user-box">
      <div class="col-sm-16 box-title">
        <span>Find a listing</span>
      </div>
      
      <div class="container-fluid">
        <div class="row">
          <div class="col-sm-16 in-pdt">
            <div class="col-sm-2">
              <input type="text" class="mls-input" placeholder="MLS ID" #mlsid>
            </div>

            <div class="col-sm-2">
              <input type="text" class="mls-input" placeholder="Parcel ID" #parcelId>
            </div>

            <div class="col-sm-2">
              <ng-select class="custom listing-mg-status cursor_poiner"
                placeholder="Status"
                [items] = "ListingStatus"
                bindValue="value"
                bindLabel="value"
                [clearable]=false
                [searchable]=false
                [virtualScroll]=true
                (change)="setListingStatus($event)"
                >
              </ng-select>
            </div>
  
            <div class="col-sm-3">
              <input type="text" class="city-input" placeholder="Address, City, State" #address>
            </div>
  
            <div class="col-sm-3">
              <input class="search-input" [(ngModel)]="selectedListingAgent" type="text" #agentName placeholder="Listing Agent" (keyup)="searchListingAgent(agentName.value)">
              <div *ngIf="showAgentList != false">
                <div class="search-agent-pos" [ngClass]="{'search-agent-box': searchAgentList.length > 3}">
                    <ul class="search-agent-ul">
                        <li class="search-agent-div" *ngFor="let agent of searchAgentList" (click)="setListingAgent(agent)">                                                           
                         {{agent.name}}
                        </li>
                      </ul>
                </div>
              </div>
            </div>
  
            <div class="col-sm-2">
                <input class="search-input" [(ngModel)]="selectedBroker" type="text" #brokerName placeholder="Brokerage" (keyup)="searchBrokerage(brokerName.value)">  
                <div *ngIf="showBrokeragetList != false">
                    <div class="search-agent-BR-pos" [ngClass]="{'search-agent-BR-box': searchBrokeragetList.length > 3}">
                        <ul class="search-agent-ul">
                            <li class="search-agent-div" *ngFor="let broker of searchBrokeragetList" (click)="setBrokerage(broker)">                                                           
                             {{broker.firm_name}}
                            </li>
                          </ul>
                    </div>
                  </div>
            </div>
            <div class="col-sm-2">
              <input type="submit" value="Search" class="ad-search-btn btn submit_button with_bg" (click)="searchListing(mlsid.value,address.value,parcelId.value)">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



<div class="table-top">
    <table class="table">
      <tr>
          <th>
            MLS ID
          </th>
          <th>
            Parcel ID
          </th>
          <th>
            Status
          </th>
          <th>
            Address
          </th>
          <th>
            Listing Agent
          </th>
          <th>
            Brokerage
          </th>
          <th>
            Date Added
          </th>
      </tr>
      <tbody>
          <tr *ngFor="let listing of listingList | paginate: { itemsPerPage: itemsPerPage, currentPage: pageCount,totalItems :totalRecordsCount};let i = index">
              <td class="cursor-pointer">
                <span *ngIf="listing.mls_id == ''" class="dark">N/A</span>
                <span *ngIf="listing.mls_id != ''" class="dark">{{listing.mls_id}}</span>
              </td>
              <td class="cursor-pointer">
                <span class="dark">{{listing.parcel_id}}</span>
              </td>
              <td>
                  <span class="dark">{{listing.status}}</span>
              </td>
              <td>
                  <span class="dark">{{listing.street}}<br>{{listing.location}}</span>
              </td>
              <td>
                  <span class="dark">{{listing.listing_agent}}</span>
              </td>
              <td>
                  <span class="dark">{{listing.brokerage}}</span>
              </td>
              <td>
                  <span class="dark">{{setDateFormat(listing.date_added)}}</span> 
              </td>
              <td>
                <div class="open_click_menu" (click)="openMenu(i,listing.id)">
                    <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more" alt="">
                    <ul id="user_{{i}}_{{listing.id}}" class="click_menu_open events">
                      <li *ngIf="listing.is_edit == true" (click)="impersonateUser(listing.listing_agent_email,listing.id)" class="cursor-pointer option-menu">Edit Listing</li>
                      <li (click)="deleteListing()" class="cursor-pointer option-menu">Delete Listing</li>
                    </ul>
                </div>
              </td>
          </tr>
        </tbody>
        <div class="pagination-center" *ngIf="totalRecordsCount > itemsPerPage">
            <span *ngIf="pageCount !=1">
                <pagination-controls  (pageChange)="getPageChange($event)"
                maxSize="6"
                previousLabel="PREVIOUS"
                nextLabel="NEXT">
                </pagination-controls>
            </span>
    
            <span *ngIf="pageCount ==1">
                <pagination-controls  (pageChange)="getPageChange($event)"
                previousLabel=""
                maxSize="6"
                nextLabel="NEXT">
                </pagination-controls>
            </span>
        </div>
      </table>
    </div>
            

<div>
  <div id="deleteListingModal" class="modal fade" role="dialog">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">Delete Listing</h4>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete this listing?</p>
          </div>
          <div class="modal-footer">
            <input type="submit" value="Confirm" class="submit_button with_bg model-confi" data-dismiss="modal">
            <input type="submit" class="submit_button with_bg model-cancel" data-dismiss="modal" value="Cancel">
          </div>
        </div>      
      </div>
    </div>
</div>