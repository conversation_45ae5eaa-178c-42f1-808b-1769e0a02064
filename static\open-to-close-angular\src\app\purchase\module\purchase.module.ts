import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PurchaseComponent } from '@app/purchase/component/purchase.component';
import { BaseModule } from '@app/base/modules/base.module';
import { PurchaseService } from '@app/purchase/service/purchase.service';
import { PurchaseSuccessComponent} from '@app/purchase/component/purchase-success.component';

@NgModule({
  imports: [
    CommonModule,
    BaseModule,
    
  ],
  declarations: [PurchaseComponent,PurchaseSuccessComponent],
  providers: [PurchaseService]
})

export class PurchaseModule { }
