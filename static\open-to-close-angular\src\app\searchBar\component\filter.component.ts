import { <PERSON>mpo<PERSON>,<PERSON><PERSON><PERSON><PERSON>, NgZone,OnInit,EventEmitter,Input,Output, OnChanges, SimpleChanges } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';

import * as moment from 'moment';
import { SearchBarComponent } from '@app/searchBar/component/search-bar.component';
import { FilterModel } from '@app/searchBar/models/filter-model';
import { NavigationEnd } from '@angular/router';
import { Location } from '@angular/common';
declare var $;

@Component({
  selector: 'search-bar-filter',
  templateUrl: '../views/filter.html',
  styleUrls: ['../css/search-bar.css']
})

export class SearchBarFilterComponent extends SearchBarComponent implements OnInit{

  public filterModel = new FilterModel();
  private isSearched: boolean = false;
  public selectedBaths;
  public selectedBeds;
  public selectedSuggestions = "";
  public filterSearchList: any[] = [];
  public filterSearchFrom = '';
  public isFavSearch : Boolean = false;

  // private homeTypes: any[] = [
  //   {name:"Single Family Detached", checked: false},
  //   {name:"Townhouse", checked: false},
  //   {name:"Apartment", checked: false},
  //   {name:"Condominium", checked: false},
  //   {name:"Duplex", checked: false},
  //   {name:"Manufactured Home", checked: false},
  //   {name:"Mobile Home", checked: false},
  //   {name:"Quadruplex", checked: false},
  //   {name:"Single Family Attached", checked: false},
  //   {name:"Triplex", checked: false}
  // ]

  private homeTypes: any[] = [
    {name:"Single Family - Detached", checked: false},
    {name:"Loft Style", checked: false},
    {name:"Moduler/Pre-Fab", checked: false},
    {name:"Mfg/Mobile Housing", checked: false},
    {name:"Gemini/Twin Home", checked: false},
    {name:"Apartment Style/Flat", checked: false},
    {name:"Townhouse", checked: false},
    {name:"Patio Home", checked: false},
  ]

  private eventTypes: any[] = [
    {name:"Open House", value:"OH", checked: false, image: this.imagePrefix + "OH.png", css: "marker-legend-dropdown-mobile"},
    // {name:"72 Hour Home Sale",value:"AO", checked: false, image: this.imagePrefix + "AO.png", css: "marker-legend-dropdown-mobile"},
    {name:"Broker Open",value:"BO", checked: false, image: this.imagePrefix + "BO.png", css: "marker-dropdown-bo-icon-mobile"},
    {name:"No Events",value:"NO", checked: false, image: this.imagePrefix + "no-event.png", css: "marker-dropdown-no-event-icon-mobile"},
  ]

  private openHouseTypeFilter: any[] = [
    {name:"SHOW ALL Open Houses", value:"0", checked: false},
    {name:"SHOW ONLY PRE-MLS /Coming Soon", value:"1", checked: false},
    {name:"Active Listings - No Open Houses", value:"2", checked: false},
  ]

  private statusTypes: any[] = [
    {name:"Active", checked: false, image: this.imagePrefix + "active.png"},
    {name:"Pending", checked: false, image: this.imagePrefix + "pending.png"},
    {name:"PRE-MLS/Coming Soon", checked: false, image: this.imagePrefix + "preMLS.png"}
  ]

  constructor(public zone: NgZone,private location: Location) {
    super(zone);
  }

  ngOnInit(){
    this.initData();
  }

  initData(){
    if(this.searchService.getCurrentListType() != undefined){
      this.listType = this.searchService.getCurrentListType();
    }else{
      this.location.back();
    }

    if(this.searchService.getFavoriteScreen() != undefined){
      this.isFavSearch = this.searchService.getFavoriteScreen();
    }

    if(this.searchService.getFilterForm != undefined){
      this.filterSearchFrom = this.searchService.getFilterForm();
    }

    let searchProperty;

    if(localStorage.getItem('recentSearches')){
      if(localStorage.getItem('recentSearches') != null){
        let localStorageSearch = JSON.parse(localStorage.getItem('recentSearches'));
        searchProperty = localStorageSearch[0];
        for(let key of Object.keys(searchProperty)){
          this.filterModel[key] = searchProperty[key];
        }
      }
    }

    if(this.filterModel.property_type.length != 0){
      for(let i=0;i<this.filterModel.property_type.length;i++){
        this.homeTypes.forEach(homeType => {
          if(homeType.name == this.filterModel.property_type[i]){
            homeType.checked = true;
          }
        });
      }
    }

    if(this.filterModel.property_status.length != 0){
      for(let i=0;i<this.filterModel.property_status.length;i++){
        this.statusTypes.forEach(statusType => {
          if(statusType.name == this.filterModel.property_status[i]){
            statusType.checked = true;
          }
        });
      }
    }

    if(this.filterModel.event_type.length != 0){
      for(let i=0;i<this.filterModel.event_type.length;i++){
        this.eventTypes.forEach(eventType => {
          if(eventType.value == this.filterModel.event_type[i]){
            eventType.checked = true;
          }
        });
      }
    }

    if(this.filterModel.open_house_type.length != 0){
      for(let i=0;i<this.filterModel.open_house_type.length;i++){
        this.openHouseTypeFilter.forEach(openHouse => {
          if(openHouse.value == this.filterModel.open_house_type[i]){
            openHouse.checked = true;
          }
        });
      }
    }

    if(this.filterModel.bathroom != undefined){
      this.selectedBaths = this.filterModel.bathroom +'+';
    }

    if(this.filterModel.bedroom != undefined){
      this.selectedBeds = this.filterModel.bedroom +'+';
    }
    if(this.filterModel['suggestion'] != undefined){
      this.selectedSuggestions = this.filterModel['suggestion'];
    }
  }

  onselectLocation(searchLocation){
    $(".search-result").fadeOut(500);
    if(searchLocation != undefined){
      this.filterModel['search'] = searchLocation;
      this.selectedSuggestions = searchLocation['suggestion'];
      this.pageNo = 1;
    }
  }

  filterSearch(){
    this.filterModel.list_type = this.listType;
    var searchObj = {
      'filterModel' : this.searchProperty,
      'isSearched' : this.isSearched,
      'listType' : this.listType
    }
    this.filterSearchList[0] = this.filterModel;
    localStorage.setItem('recentSearches',JSON.stringify(this.filterSearchList));
    this.searchService.setFilterSearch(searchObj);
    this.location.back();
  }


  setFilterBeds(value){
    this.selectedBeds = value;
    this.filterModel.bedroom = value.replace("+", "");
    this.isSearched = true;
  }

  setFilterBaths(value){
    this.selectedBaths = value;
    this.filterModel.bathroom = value.replace("+", "");
    this.isSearched = true;
  }

  setFilterStatus(statusEvent){
    if(statusEvent.target.checked){
      this.filterModel.property_status.push(statusEvent.target.value);
    }
    else{
      let propertyStatusValue = this.filterModel.property_status.filter(propertyStatus => propertyStatus == statusEvent.target.value);
      if(propertyStatusValue.length != 0){
        let propertyStatusIndex = this.filterModel.property_status.indexOf(propertyStatusValue[0]);
        this.filterModel.property_status.splice(propertyStatusIndex, 1);
      }
    }
  }

  setFilterHomeTypes(homeTypeEvent){
    if(homeTypeEvent.target.checked){
      this.filterModel.property_type.push(homeTypeEvent.target.value);
    }
    else{
      let propertyTypeValue = this.filterModel.property_type.filter(propertyType => propertyType == homeTypeEvent.target.value);
      if(propertyTypeValue.length != 0){
        let propertyTypeIndex = this.filterModel.property_type.indexOf(propertyTypeValue[0]);
        this.filterModel.property_type.splice(propertyTypeIndex, 1);
      }
    }
  }

  setFilterEventTypes(eventTypeEvent){
    if(eventTypeEvent.target.checked){
      this.filterModel.event_type.push(eventTypeEvent.target.value);
    }
    else{
      let eventTypeValue = this.filterModel.event_type.filter(propertyType => propertyType == eventTypeEvent.target.value);
      if(eventTypeValue.length != 0){
        let eventTypeIndex = this.filterModel.event_type.indexOf(eventTypeValue[0]);
        this.filterModel.event_type.splice(eventTypeIndex, 1);
      }
    }
  }

  setFilterOpenHouseTypes(openHouseType){
    if(openHouseType.target.checked){
      this.filterModel.open_house_type.push(openHouseType.target.value);
    }
    else{
      let openHouseTypeValue = this.filterModel.open_house_type.filter(propertyType => propertyType == openHouseType.target.value);
      if(openHouseTypeValue.length != 0){
        let TypeIndex = this.filterModel.open_house_type.indexOf(openHouseTypeValue[0]);
        this.filterModel.open_house_type.splice(TypeIndex, 1);
      }
    }
  }

  cancelFilterSearch(){
    this.location.back();
  }
}
