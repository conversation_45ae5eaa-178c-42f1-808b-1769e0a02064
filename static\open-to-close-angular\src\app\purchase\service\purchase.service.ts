import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ApiService } from '@app/base/services/api.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { CreditCard } from '@app/purchase/model/credit-card';
import { Message } from '@app/base/model/message';
import { CreditCardList } from '@app/purchase/model/credit-card-list';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { Plans } from '@app/profile/models/plan';
import { Purchase } from '@app/purchase/model/purchase';
import { ApiResponse } from '@app/auth/models/api-response';

@Injectable()
export class PurchaseService {
  public purchasePlan:Plans;
  baseComponent:BaseComponent;
  apiService:ApiService;

  constructor() { 
    this.baseComponent=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
  }

  public setPlan(plan: Plans){
    this.purchasePlan=plan;
  }

  public getPlan(): Plans{
    return this.purchasePlan;
  }

  public creditCard(creditCard:CreditCard): Observable<ApiResponse>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['purchase']['creditCard'],creditCard);
    return this.apiService.apiCall(options);  
  }

  public creditCardList(): Observable<Purchase>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['purchase']['creditCardList'],{});
    return this.apiService.apiCall(options);  
  }

  public purchase(id): Observable <ApiResponse>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['purchase']['purchasePlanId'],+id);
    return this.apiService.apiCall(options);  
  }

  public subscriptionClientToken(): Observable<ApiResponse>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['purchase']['subscriptionToken'],{});
    return this.apiService.apiCall(options);
  }

  public subsciptionPlan(purchaseDetail:Purchase): Observable<Purchase>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['purchase']['purchasePlan'],purchaseDetail);
    return this.apiService.apiCall(options);
  }
  
}
