<div>
  <div class="header">
    <nav class="navbar navbar-inverse">
       <div class="container-fluid">
          <div class="navbar-header">              
             <a class="navbar-brand cursor-pointer"><img src="{{imagePrefix}}logo.png" class="img-responsive"  (click)="routeOnUrl('/')"  alt="logo"></a>
          </div>            
       </div>
    </nav>
 </div>
</div>

<div>
    <div class="new_profiles header_fix">
        <div class="new_profile_group_wrap">
           <div class="new_profile_group dis_inline">
              <div class="tab-content resetPwdPosition">                
                 <div id="menu1" class="tab-pane fade in active new_profile_details">
                  <div class="resetPwdText">
                    <h3>Reset Password</h3>
                  </div>
                  <div class="group_1 mt-20 resetPwd">
                        <div class="title2"></div>
                        <div class="new_form">
                          <form [formGroup]="resetPasswordForm">
                           <div class="new_form_group ">
                              <div class="group new_form_label">      
                                 <input type="password" required class=" width_350" formControlName="password" placeholder=" " required>
                                 <span class="highlight"></span>
                                 <span class="bar"></span>
                                 <label>New Password*</label>
                              </div>
                              <div *ngIf="resetPasswordForm.controls.password.touched">
                                  <p class="form-validation" *ngIf="resetPasswordForm.controls.password.errors?.required">Enter new password</p>
                                  <p class="form-validation" *ngIf="resetPasswordForm.controls.password.errors?.minlength">Password must be 5-15 characters</p>
                                  <p class="form-validation" *ngIf="resetPasswordForm.controls.password.errors?.maxlength">Password must be 5-15 characters</p>
                              </div>
                           </div>
                           <div class="new_form_group ">
                              <div class="group new_form_label">      
                                 <input type="password" required class=" width_350"formControlName="confirm_new_password" placeholder=" " required>
                                 <span class="highlight"></span>
                                 <span class="bar"></span>
                                 <label>Confirm New Password*</label>
                              </div>
                              <div *ngIf="resetPasswordForm.controls.confirm_new_password.touched">
                                  <p class="form-validation" *ngIf="resetPasswordForm.controls.confirm_new_password.errors?.required">Enter confirm password</p>
                                  <p class="form-validation" *ngIf="resetPasswordForm.controls.confirm_new_password.errors?.minlength">Password must be 5-15 characters</p>
                                  <p class="form-validation" *ngIf="resetPasswordForm.controls.confirm_new_password.errors?.maxlength">Password must be 5-15 characters</p>
                              </div>
                              <div *ngIf="resetPasswordForm.controls.confirm_new_password.touched">
                                  <p class="form-validation" *ngIf="resetPasswordForm.hasError('mismatch')">Confirm password not match</p>
                              </div>
                           </div>
                           <div class="new_form_group ">
                              <input type="submit" class="submit_button with_bg" [ngClass]="{'submit-disable':resetPasswordForm.invalid}" [disabled]="resetPasswordForm.invalid" value="Submit" (click)="ResetPassword(resetPasswordForm)">
                           </div>
                          </form>
                        </div>
                 </div>
              </div>
           </div>
        </div>
     </div>
</div>

<div>
<footer [currentPage]="'resetPassword'"></footer>
</div>