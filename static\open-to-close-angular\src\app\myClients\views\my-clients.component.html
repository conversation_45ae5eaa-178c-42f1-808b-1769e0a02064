<div>
<header></header>
</div>

<div>
  <div class="myclient_page header_fix">
    <div class="container">
      <div class="ls_group my_client_new mt-20">
        <div class="title_group ml_zero">
          <div class="title">My Clients</div>
        </div>
        <div class="invite-block" *ngIf="!isTabletScreen">
          <div class="sub-block">
            <div class="invite-contain">
                Invite clients<br>
                <span>Share this link with your clients to get them signed up with Open Houses Direct and linked to your account.</span>
            </div><div class="invite-link">
                    <input type="text" placeholder="Invite link" class="form-control" [(ngModel)]="myInviteLink">
                  </div>
          </div>
        </div>
        <div class="pull-right mt-5 m-top">
          <div class="search_button dis_inline">
            <div style="margin-right: 12px;" class="input-group stylish-input-group">
              <input type="text" class="form-control margin_zero" placeholder="Search by name" #search (keyup)="searchClient(search.value)">
              <span style="padding: 5px; right: 1rem; top: 0rem;" class="glyphicon glyphicon-search"></span>
            </div>
          </div>
          <div (click)="clientCSV()" class="save_notes export_csv" style="    margin-right: 13px;
          margin-top: 17px;
          position: relative;
           left: 0px;">Export CSV</div>
        </div>
        <div class="invite-block" *ngIf="isTabletScreen">
            <div class="sub-block">
              <div class="invite-contain">
                  Invite clients<br>
                  <span>Share this link with your clients to get them signed up with Open Houses Direct and linked to your account.</span>
              </div>
              <div class="invite-link">
                <input type="text" placeholder="Invite link" [(ngModel)]="myInviteLink" class="form-control">
              </div>
            </div>
          </div>
      </div>
    <div class="my_client_table_group">
      <div class="myclient_navbar">
        <ul>
        <li class="active" data-toggle="pill" href="#home" >Active</li>
        </ul>
      </div>
      <div class="tab-content">
        <div id="home"  class="event-list tab-pane fade in active table-responsive selected_saved">
            <div class="No_matches" *ngIf="showClientLoader == true">
              <div class="loader">
              <div class="message">Loading...</div>
              <div class="dots"><div class="center"></div></div>
              </div>
            </div>
          <div *ngIf="clientList.length == 0 && showClientLoader == false" class="No_matches">
            <div class="title">No Clients</div>
            <div class="text">You have no active clients linked to your account. To add new clients,<br>request your client to to add you as their agent in their profile.</div>
          </div>
          <div *ngIf="clientList.length != 0" class="property-list-table">
            <table class="table">
              <thead>
                <tr>
                  <th (click)="clientSorting('NM')">Name <img id="CA_NM" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                  <th (click)="clientSorting('TC')">Time as Client <img id="CA_TC" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                  <th (click)="clientSorting('SA')">Saved Searches <img id="CA_SA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                  <th (click)="clientSorting('CH')">Check Ins <img id="CA_CH" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                  <th (click)="clientSorting('NT')" colspan="4"> Notes <img id="CA_NT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let client of clientList;let i = index">
                    <td>
                    <a (click)="checkIns('my-clients/check-ins',client,'')" class="a_tag_color cursor-pointer">
                      <span *ngIf="client.profile_image == ''">
                        <div class="ls dis_inline po_rel_1 font_semibold">{{clientNameImage(client.name)}}</div>
                      </span>
                      <span *ngIf="client.profile_image != ''">
                        <img [src]="client.profile_image" class="client-profile-pic search-agent-event symbols-property-image dis_inline" alt="">
                      </span>
                      <div class="dis_inline ml_10 font_semibold my-client-font">{{client.name}}</div>
                    </a>
                    </td>
                    <td class="font_semibold my-client-font">{{getClientTime(client.time_as_client)}}</td>
                    <td class="font_semibold my-client-font">{{client.save_search_count}}</td>
                    <td class="font_semibold my-client-font">{{client.checkin_count}}</td>
                    <td class="font_semibold my-client-font">{{client.note_count}}</td>
                    <td class="action-view action-client-view">
                      <a (click)="messageClient(client)">
                        <div class="save_notes margin_zero">Message</div>
                      </a>
                    </td>
                    <td class="action-option">
                      <div class="open_click_menu"  (click)="openMenu(i,client.client_id)">
                        <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                        <ul id="mc_{{i}}_{{client.client_id}}" class="click_menu_open events">
                          <li class="cursor-pointer option-menu" (click)="checkIns('my-clients/check-ins',client,'addNote')">Add Note</li>
                        </ul>
                      </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <div *ngIf="clientTotalCount > clientItemPerPage && clientTotalCount != clientList.length" class="new_form_group load_more_btn">
              <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadClients('AC', clientIndex)" value="Load More">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div>
  <footer></footer>
</div>
