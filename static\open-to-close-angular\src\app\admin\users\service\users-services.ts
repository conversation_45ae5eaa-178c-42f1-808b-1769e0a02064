import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ApiService } from '@app/base/services/api.service';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiResponse } from '@app/auth/models/api-response';

@Injectable()
export class UsersService {

  public baseservice:BaseComponent;
  public apiService:ApiService;

  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
  }

  getUsersList():Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['getAllUsers'],{});
    return this.apiService.apiCall(options);
  }

  manageUserAccount(usetStatus):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['manageUserAccountStatus'],usetStatus.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public sendforgotPasswordEmail(email):Observable<ApiResponse>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['auth']['forgotPassword'],email);
    return this.apiService.apiCall(options);
  }

  public filterUserSearch(filterParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['getAllUsers'],filterParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public getImpersonateUserToken(email):Observable<ApiResponse>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['impersonateUser'],email.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public reSendUserConfiEmail(email):Observable<ApiResponse>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['reSendConfimEmail'],email.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public downloadCSV(filterParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['downloadCSV'],filterParams.toString(), null, null, null, false, true);
    return this.apiService.downloadFile(options);
  }

  getHBUsersList():Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['getHBUser'],{});
    return this.apiService.apiCall(options);
  }

  public HBfilterUserSearch(filterParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['getHBUser'],filterParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public LAfilterUserSearch(filterParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['getLAUser'],filterParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public BRfilterUserSearch(filterParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['getBRUser'],filterParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public DowngradeUser(filterParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['downgradeUser'],filterParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
  public UpgradeUser(filterParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['upgradeUser'],filterParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
}
