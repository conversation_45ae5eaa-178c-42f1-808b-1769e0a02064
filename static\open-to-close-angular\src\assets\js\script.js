$(document).ready(function($) {

// var ans10 = 0;

// $(".search_location.header_fix").click(function(){
//     if(ans10 == 1)
//     {
//         $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);    
//     }
// });


// $("footer").click(function(){
//     $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
// });

// $(".google_map").click(function(){
//     $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
// });

// $("input.select_box_price").click(function(){
//     $(".form_group.col-sm-2.price_select .square_footage").fadeIn(500);
// });

 // $(".max").on("click", function(){
 //        $(".baths_group.price_ul li").click(function(){
 //             ans = $(this).html();
 //            $("input.max").val(ans);
 //          //  $(".form_group.col-sm-2.price_select .square_footage").toggle();
 //        });
 // });

 // $(".minimum").on("click", function(){
 //        $(".baths_group.price_ul li").click(function(){
 //             ans = $(this).html();
 //            $("input.minimum").val(ans);
 //        });
 // });


 //  $("input.minimum").focus(function(){
 //     if( $("input.minimum").is(":focus") ) {
 //            $(".baths_group.price_ul li").click(function(){
 //                 ans = $(this).html();
 //                $("input.minimum").val(ans);

 //                $("input.minimum").blur(function(){
 //                   $(this).css("background-color", "#ffffff");
 //                 });

 //            });

    

 //    }
    
 //    $(this).css("background-color", "#cccccc");

 //  });

    

    


//   $(".minimum").on("click", function(){
//     $(".minimum").focus(function(){
//         $(".baths_group.price_ul li").click(function(){
//              ans = $(this).html();
//             $("input.minimum").val(ans);
//         });

//     });
   
// });

// $(".max").on("click", function(){
//     $(".max").focus(function(){
//         $(".baths_group.price_ul li").click(function(){
//              ans = $(this).html();
//             $("input.max").val(ans);
//         });
      
//     });
// });
// var clicks = 0;
// var price = '';
// $(".baths_group.price_ul li").click(function(){

//  if (clicks == 0){
//         ans = $(this).html();
//     $("input.minimum").val(ans);
//         ++clicks;
//         $("input.select_box_price").val(ans);
//         price = ans;
//     } else{
//           ans = $(this).html();
//         $("input.max").val(ans);
//         $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
//         clicks = 0;
//         price = price +' to '+ ans;
//         $( "input.select_box_price" ).val(price);
        
//     }
// });



    $(".map_ele_group").mouseover(function(){
       $(this).find(".map_house_abo.hidden-xs").fadeIn( 10 );
    });


    $(".map_ele_1").mouseout(function(){
       $(".map_house_abo.hidden-xs").fadeOut(10);
    });

    $(".notification_icon").click(function() {
        $(".notification").toggle();
    });
    $(".remove_notification").click(function() {
        $(".notification").css('display', 'none');
    });
    if ($(window).width() >= 1000) {
        $('.panel-collapse').addClass("in");
        $('h2.bg_title').off("click");

        $("h2.bg_title").click(function(event) {
            event.stopPropagation();
        });
    }
    
    // $('#example3').sliderPro({
    //     width: 960,
    //     height: 500,
    //     fade: true,
    //     arrows: true,
    //     buttons: false,
    //     fullScreen: true,
    //     shuffle: true,
    //     smallSize: 500,
    //     mediumSize: 1000,
    //     largeSize: 3000,
    //     thumbnailArrows: true,
    //     autoplay: true
    // });

    // var slider = $( '#example3' ).data( 'sliderPro' );
    // var total_ans = slider.getTotalSlides();
    // var total_ans2 = slider.getSelectedSlide();
    // total_ans2++;
    // var total_slider = total_ans2 + "/" + total_ans;
    // $(".manage3").html(total_slider);


// $( '#example3' ).on( 'gotoSlide', function( event ) {
//             var slider = $( '#example3' ).data( 'sliderPro' );
//             var total_ans = slider.getTotalSlides();
//             var total_ans2 = slider.getSelectedSlide();
//             total_ans2++;
//             var total_slider = total_ans2 + "/" + total_ans;
//             $(".manage3").html(total_slider);
// });

});

$(window).bind("load", function() {
    $("ul.cont_select_int li").click(function() {
        var ans = $(this).attr("data-value");

        $('li[href="#' + ans + '"]').tab('show');

    });
});