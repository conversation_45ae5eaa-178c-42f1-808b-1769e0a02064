.cycle_1-three-box{
    margin-left: 22px !important;
    width: 117px !important;
}
.cycle_1 .title2{
    padding-right: 21px !important;
}
/* .cycle_upgrade{
    margin-right: 11px !important;
} */
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}
.input-backg{
    background-color: #FFFFFF !important;
}
.disable_form label {
    top: -13px;
    font-size: 12px;
}

.disable_form_blank label {
    top: 10px !important;
    font-size: 16px !important;
}

.disable_form input {
    background: transparent;
}

.smallSize-number{
    padding: 9px 10px 6px 5px !important;
    border: none !important;
    border-bottom: 1px solid #C2C2C2 !important;
    width: 166px !important;
}
.plans-time{
font-size: 12px;
color: #8D8D8D;
}

.remove-box-shadow{
    box-shadow: 0px 0px 0px 0px #F0F2F4 !important;
    background: none !important;
}

.ng-select .ng-control{
    border: none !important;
    border-bottom: 1px solid #bbbaba !important;
}

.Cancel-Subscription-btn{
    font-size: 12px;
    color: #F06292;;
    border: 1px solid #F06292;;
    border-radius: 100px;
    background: white;
    margin-top: 20px;
    padding: 0px;
    padding: 5px 15px 5px 15px;
    outline: none;
}

.hb-profile-img{
    margin-left: 307px !important;
}

.search-agent-image{
    height: 130px !important;
    width: 130px !important;
    border-radius: 85px !important;
}

.errmsg{
    margin-top: -36px !important;
    padding-bottom: 46px !important;
}
.err-width{
    float: left !important;
}
.err-month-paddding{
    padding: 0 !important;
}
.err{
    margin-bottom: -37px !important;
}
.year-err{
    text-align: left !important;
}
.err-year-width{
    float: left !important;
    margin-left: 8px !important;
}
.upgrade_agent_title.title2{
    padding: 20px 21px !important;
}
.th-width{
    width: 50% !important
}
.agent-name{
    position: relative;
    top: 2px !important;
    left: 5px;
}
.table-content{
    background: #FFFFFF;
    border-radius: 4px;
    padding: 0px !important
}
.deny-btn-size{
    font-size: 12px;
    letter-spacing: 0;
    cursor: pointer;
    padding: 5px 37px 7px 18px;
    display: inline-block;
    border-radius: 22px;
    float: right;
    margin-top: 7px;
}
.btn-space{
    color: #f06292 !important;
    padding-left: 13px !important;
}
.accept-space{
    color: #10B8A8 !important;
    padding-left: 10px !important;
}
.accept-css{
    border: 1px solid #10B8A8;
    background: transparent;
    color: #10B8A8;
}
.accept{
    padding-left: 52px !important;
}
.deny{
    padding-left: 0px !important;
}
.titile-space{
    padding-bottom: 7px !important
}
.agent-image{
    height: 55px !important;
    width: 55px;
    border-radius: 50% !important;
}
.agent-status{
    font-size: 24px;
    color: #10B8A8;
    line-height: 20px;
    font-weight: 600;
}
.lender-title{
    padding-left: 12%;
}
.lender-submit{
    float: right;
}
.pref-lender-sub-title{
    width: auto !important;
}
.lender.dis_inline{
    margin-left: 20px;
    margin-top: 5px;
    vertical-align: top;
}
.remove-lender{
    font-size: 13px;
    color: #F06292;
    border: 1px solid #F06292;
    border-radius: 100px;
    background: white;
    margin-top: 12px;
    padding: 0px;
    text-align: center;
    padding: 5px 24px 5px 24px;
}
.br-profile-pic{
    width: 47%;
    display: flex;
}
.my-market-text{
    margin-right: 4%;
    max-width: 159px;
    float: left;
    margin-top: 5px;
    color: #8D8D8D;
}
.my-market-div{
    float: left;
    margin-right: 9%;
}
.email-confirm-p{
    font-size: 16px;
    width: 90%;
    color: #8D8D8D;
    opacity: 0.95;
}
.email-confirm-border{
    border-bottom: 4px solid #3ab8a8;
    border-radius: 4px;
    margin-bottom: 15px;
}
.email-confirm-email{
    color: #8D8D8D;
    padding-top: 15px;
    padding-bottom: 11px;
    font-size: 16px;
    font-weight: 700;
}
.email-confirm-cancel{
    padding-left: 27px;
    color: #3ab8a8;
    font-weight: 600;
}
.email-confirm-email-title{
    font-weight: 600;
}
.invoice-loging{
    height: 17px;
    width: 17px;
}
.account-text{
    width: 100%;
}
.broker-info{
    box-shadow: 0 0 0 99999px rgb(167 163 167 / 63%);
    position: relative;
    z-index: 9999;
    pointer-events:  auto;
    transition: all 0.5s ease;
  }

.btn-wiseagent{
    background-color: #00549a;
    box-shadow: 0px 3px 4px rgba(0, 84, 154, 0.1);
    border-radius: 4px;
    position: absolute;
    margin-top: 4.8rem;
    width: 345px;
    margin-left: -2.8rem;
    padding: 2rem 0;
    
}

.wiseagent-title{
    font-size: 16px;
    font-weight: bold;
    margin-top: 3rem;
}

.wiseagent-auth-text{
    font-size: 14px;
    text-align: center;
    font-weight: 400;
    margin: 2rem;
}

.btn-new-wiseagent{
    background-color: #00549a;
    box-shadow: 0px 3px 4px rgba(0, 84, 154, 0.1);
    border-radius: 4px;
    padding: 1rem 0;
    width: 100%;
    text-align: center;
    color: #fff !important;
    font-size: 18px;
}

.wiseagent-img{
    width: 50%;
}

.btn-wiseagent-dashboard{
    background-color: #41AD9D !important;
}

.btn-wiseagent span{
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    color: #fff;
}

.btn-wiseagent img{
    width: 190px;
}

#connectWithWiseAgent .modal-header, #connectWithWiseAgent .modal-footer{
    border: none;
}

#connectWithWiseAgent .modal-footer div span a {
    text-decoration: none;
    font-size: 12px;
    color: #10B8A8;
    line-height: 15px;
}

#connectWithWiseAgent .modal-title{
    font-weight: bold;
    font-size: 20px;
    line-height: 23px;
    color: #000000;
    padding-top: 1rem;
}

.wiseagent-form label{
    font-size: 14px;
    color: #6C6C6C;
    font-weight: 600;
}

.wiseagent-form input, .wiseagent-form select{
    border: 1px solid #DBDBDB;
    box-sizing: border-box;
    border-radius: 4px;
    height: 45px;
    box-shadow: none;
    color: #000;
}

.phone-wrapper{
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.pb-0{
    padding-bottom: 0;
}

.mb-3{
    margin-bottom: 17px ;
}

.mt-4{
    margin-top: 2rem;
}

.text-center{
    text-align: center;
}

.px-0{
    padding-left: 0;
    padding-right: 0;
}

.pt-0{
    padding-top: 0;
}

.p-4{
    padding: 2rem;
}

.btn-agent{
    background: #43B7AD;
    border-radius: 35px;
    font-weight: 600;
    font-size: 16px;
    line-height: 23px;
    text-align: center;
    color: #FFFFFF;
    padding: 8px 24px;
    box-shadow: 0px 4px 4px rgba(67, 183, 173, 0.1);
}