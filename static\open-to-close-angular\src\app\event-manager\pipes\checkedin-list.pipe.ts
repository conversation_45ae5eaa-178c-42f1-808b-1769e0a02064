import { PipeTransform, Pipe } from '@angular/core';

@Pipe({name: 'checkedInList'})

export class CheckedInListPipe implements PipeTransform {
    transform(guestList, isCheckin: boolean) : any {     
        var checkedInList = [];
        for (let i=0;i<guestList.length;i++) {
            if(guestList[i]['is_checkin'] == isCheckin){
                checkedInList.push(guestList[i]);
            }
        }      
        return checkedInList;
    }
}