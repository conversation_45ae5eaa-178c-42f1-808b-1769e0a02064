<div class="notification-detail-header">
  <header></header>
</div>

<div class="notification-detail-body">

<div class="notification_page header_fix ">
  <div class="container">
    <div class="title mt-20">
      Notifications
    </div>
      <div class="notification_list_2 mt-20">
        <span *ngFor="let message of notificationList">
          <div (click)="readNotification(message.notification_id,message)" [ngClass]="{'un-readed-noti':message.is_notification_read == true,'readed-noti':message.is_notification_read == false}" class="notification_div">
              <span *ngIf="message.notification_data.property_file != ''">
                <img src="{{message.notification_data.property_file}}" class="noti_image search-agent-event symbols-property-image dis_inline" alt="">
              </span>
              <span *ngIf="message.notification_data.property_file == ''">
                <img src="{{imagePrefix}}symbols-map-hover.png" class="noti_image search-agent-event symbols-property-image dis_inline" alt="">
              </span>
              <div class="msg-title">
                <span class="msg-title-text">{{message.notification_title}}</span>
                <span class="notification-time">{{getNotificationTime(message.notification_time)}}</span>
              </div>
                <div class="dis_inline noti_text"><span class="dark">{{message.notification_message}}</span>  </div>
            </div>
          </span>
        </div>
          <div *ngIf="totalNotificationCount > itemPerPage &&  totalNotificationCount != notificationList.length" class="new_form_group load_more_btn">
            <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreNotification(notificationIndex)" value="Load More">
          </div>
    </div>
  </div>
</div>

<div class="notification-detail-footer">
  <footer [ngClass]="{'footer-class': notificationList.length == 0}"></footer>
</div>