<admin-header></admin-header>

<div class="login-box">
  <div class="center-title">Reset Password</div>
  <div class="center-sub-title">Enter new password and confirm it to set your<br>new password</div>

  <form [formGroup]="adminResetPasswordForm">
      <div class="input-div">
        <input type="password" formControlName="password" placeholder="Enter new password*" class="input-textbox">
        <div class="error-align" *ngIf="adminResetPasswordForm.controls.password.touched">
          <p class="form-error" *ngIf="adminResetPasswordForm.controls.password.errors?.required">Enter new password</p>
          <p class="form-error" *ngIf="adminResetPasswordForm.controls.password.errors?.minlength">Password must be 5-15 characters</p>
          <p class="form-error" *ngIf="adminResetPasswordForm.controls.password.errors?.maxlength">Password must be 5-15 characters</p>
      </div>
      </div>
      <div class="input-div">
        <input type="password" formControlName="confirm_new_password" placeholder="Confirm password*" class="input-textbox"><br>
        <div class="error-align" *ngIf="adminResetPasswordForm.controls.confirm_new_password.touched">
          <p class="form-error" *ngIf="adminResetPasswordForm.controls.confirm_new_password.errors?.required">Enter confirm password</p>
          <p class="form-error" *ngIf="adminResetPasswordForm.controls.confirm_new_password.errors?.minlength">Password must be 5-15 characters</p>
          <p class="form-error" *ngIf="adminResetPasswordForm.controls.confirm_new_password.errors?.maxlength">Password must be 5-15 characters</p>
        </div>
        <div class="error-align" *ngIf="adminResetPasswordForm.controls.confirm_new_password.touched">
            <p class="form-error" *ngIf="adminResetPasswordForm.hasError('mismatch')">Confirm password not match</p>
        </div>
      </div>

      <div class="input-div">
        <input type="submit" value="Set Password" class="login-btn" [ngClass]="{'submit-disable':adminResetPasswordForm.invalid}" [disabled]="adminResetPasswordForm.invalid" (click)="ResetPassword(adminResetPasswordForm)">
      </div>
  </form>
</div>
