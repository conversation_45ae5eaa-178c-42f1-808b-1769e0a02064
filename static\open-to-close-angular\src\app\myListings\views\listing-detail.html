<div class="listing-detail-header">
    <header></header>
</div>
<div class="listing-detail-body">
  
      <search-bar (searchObjEvent)="getSearchObj($event)" [searchFrom]="'myListingListView'" [currentPage]="'listingAgent'" [isListViewScreen]='true'></search-bar>
      <div class="myclient_page My_Listings">       
       <div class="container">
          <div class="ls_group ">
             <div class="title_group ml_zero">
                <div class="title pull-left">Listings</div>
                <div *ngIf="isPaidAccount" (click)="routeOnUrl('my-listing/add-new-property')" class="save_notes pull-left ml-10 hidden-xs">Add Manually</div>
                &nbsp;
                <!--<div *ngIf="isPaidAccount" class="pull-left ml-10" style='margin-top: 5px;'><strong> OR </strong> </div>&nbsp;-->
                <!-- <div *ngIf="isPaidAccount && !isArmlsView" (click)="showArmls()" class="save_notes pull-left ml-10 hidden-xs">Add ARMLS</div> -->
                <!-- <input *ngIf="isPaidAccount" type="text" class="pull-left ml-10 hidden-xs form-control" [(ngModel)]="armlsId"  style='width:250px; border-radius: 25px' aria-label="State" type="text"  placeholder="Enter Property MLSID..." />
                <div *ngIf="isPaidAccount" (click)="addArmls()" class="save_notes pull-left ml-10 hidden-xs" style ="    margin-left: -25px !important;margin-top: -1px;height: 35px;background-color: #ce69aa; border: none;">Syndicate from ARMLS</div>-->
                <!-- <div *ngIf="isArmlsView" (click)="closeArmls()" 
                  class="cancel_notes pull-left ml-10 hidden-xs">Cancel</div>-->
                  
              </div>
              <img (click)="routeOnUrl('my-listing')" src="{{imagePrefix}}Icon.png" class="Icon_png cursor-pointer" alt="">
          </div>
          <div class="loader"  *ngIf="showArmlLoader == true">
          <div class="dots"><div class="center"></div></div>
          </div>

          <div class="my_client_table_group">
              <!-- <div class="myclient_navbar myclient_nav_mobile visible-xs">
              <div class="select_mate" data-mate-select="active" >
              <select name="" id="" class="select-nav drop_down_icon"  >
                <option value="menu1">On the market</option>
                <option value="menu3">Coming soon</option>
                <option value="menu4">Off market</option>
              </select>
                <p class="selecionado_opcion"  onclick="open_select(this)" ></p>
                <span onclick="open_select(this)" class="icon_select_mate" >
                <span class="drop_down_icon drp_soan"></span>
                </span>
                <div class="cont_list_select_mate">
                   <ul class="cont_select_int">  </ul>
                </div>
             </div>
          </div> -->

             <div class="myclient_navbar">
                <ul>
                  <li class="active" (click)="currentTab('onTheMarketList')" data-toggle="pill" href="#menu1">My Listings</li>
                  <li data-toggle="pill" (click)="currentTab('commingSoonPropertyList')" href="#menu3">Other Listings</li>
                  <li data-toggle="pill" href="#menu4">Off market</li>
                </ul>
             </div>

             <div class="tab-content">
                <div id="menu1" class="event-list tab-pane fade in active table-responsive selected_saved">
                  <div class="No_matches" *ngIf="showONMLoader == true">
                    <div class="loader">
                    <div class="message">Loading...</div>
                    <div class="dots"><div class="center"></div></div>
                    </div>
                  </div>
                  <div *ngIf="onTheMarketList.length == 0 && showONMLoader == false" class="No_matches">
                    <div class="title">No Listings</div>
                    <div class="text">You have no MLS listings active matching your search criteria</div>
                  </div>
                    <div *ngIf="onTheMarketList.length != 0" class="property-list-table">
                      <table class="table">
                        <thead>
                          <tr>
                              <th (click)="listingSort('ONM', 'PR')">Property <img id="ONM_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                              <th (click)="listingSort('ONM', 'HP')">Price <img id="ONM_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                              <th (click)="listingSort('ONM', 'BD')">Beds <img id="ONM_BD" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                              <th (click)="listingSort('ONM', 'BT')">Baths<img id="ONM_BT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                              <th (click)="listingSort('ONM', 'SF')">Sqft<img id="ONM_SF" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                              <th (click)="listingSort('ONM', 'DT')" colspan="4">Events <img id="ONM_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                          </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let property of onTheMarketList;let ONM = index">
                              <td (click)="gotToPropertyDetail('my-listing/property-detail',property.id)" class="cursor-pointer">
                                <span class="vertical-align-top"><img *ngIf="property.property_file != ''" src="{{property.property_file}}"  class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                                <span class="vertical-align-top"><img *ngIf="property.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                                <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{property.street}}<br></span><span>{{property.location}}</span></div>
                              </td>

                              <td class="font_semibold list-table-digit">{{property.home_price | currency:"":symbol:"1.0"}}</td>
                              <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bedroom.png" alt=""> {{property.bedroom}} beds</td>
                              <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bathroom.png" alt=""> {{property.full_bath}} baths</td>
                              <td class="font_semibold list-table-digit">{{property.living_area}}</td>
                              <td>
                                <span *ngFor="let event of property.event_list">
                                  <span *ngIf="event.event_type == 'BO'" (click)="openEventModel('brokerOpen',event)">
                                    <div class="month"> <span class="bgcolor1 font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                  </span>
                                  <span *ngIf="event.event_type == 'AO'" (click)="openEventModel('appointmentOnly',event)">
                                    <div class="month"> <span class="month_2_color font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                  </span>
                                  <span *ngIf="event.event_type == 'OH'" (click)="openEventModel('openHouse',event)">
                                    <div class="month"> <span class="month_3_color font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                  </span>
                                </span>
                              </td>
                              <td>
                                <span *ngIf="property.total_event_count != 0" class="pluse-more">
                                  + {{property.total_event_count}} more
                                  </span>
                              </td>
                              <td class="action-view">
                                <a (click)="showNewPropertyDialog(property,'onTheMarketList')"><div class="save_notes margin_zero">Add Event</div> </a>
                              </td>
                              <td class="action-option">
                                <div class="open_click_menu" (click)="openMenu(ONM,property.id)">
                                  <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                  <ul id="ml_{{ONM}}_{{property.id}}" class="click_menu_open events">
                                    <li *ngIf="isPaidAccount" class="cursor-pointer option-menu" (click)="showLisingDetail(property.id)">Edit Listing</li>
                                    <li class="cursor-pointer option-menu" (click)="gotToPropertyDetail('my-listing/property-detail',property.id)">Property Details</li>
                                    <li (click)="openSharePropertyModal(property.id)" class="cursor-pointer option-menu" >Share</li>
                                  </ul>
                                </div>
                              </td>
                          </tr>
                        </tbody>
                      </table>
                      <div *ngIf="ONMCount > ONMItemsPerPage && ONMCount != onTheMarketList.length" class="new_form_group load_more_btn">
                        <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreListings(ONMIndex , 'ONM')" value="Load More">
                      </div>
                    </div>
                </div>

                <div id="menu3" class="tab-pane fade in  table-responsive selected_saved">
                    <div class="No_matches" *ngIf="showCSLoader == true">
                      <div class="loader">
                      <div class="message">Loading...</div>
                      <div class="dots"><div class="center"></div></div>
                      </div>
                    </div>
                  <div *ngIf="commingSoonPropertyList.length == 0 && showCSLoader == false" class="No_matches">
                    <div class="title">No Listings</div>
                    <div class="text">You have no PRE-MLS / Coming Soon listings matching your search criteria. To add a new PRE-MLS / Coming Soon listing,<br>click the ‘Add Listing’ button above.</div>
                  </div>
                  <div *ngIf="commingSoonPropertyList.length != 0" class="property-list-table">
                  <table class="table">
                    <thead>
                      <tr>
                        <th (click)="listingSort('CS', 'PR')">Property <img id="CS_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                        <th (click)="listingSort('CS', 'HP')">Price <img id="CS_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                        <th (click)="listingSort('CS', 'BD')">Beds <img id="CS_BD" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                        <th (click)="listingSort('CS', 'BT')">Baths<img id="CS_BT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                        <th (click)="listingSort('CS', 'SF')">Sqft <img id="CS_SF" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                        <th (click)="listingSort('CS', 'DT')" colspan="4">Events <img id="CS_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""></th>
                      </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let property of commingSoonPropertyList;let CS = index">
                          <td (click)="gotToPropertyDetail('my-listing/property-detail',property.id)" class="cursor-pointer">
                            <span class="vertical-align-top"><img *ngIf="property.property_file != ''" src="{{property.property_file}}"  class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                            <span class="vertical-align-top"><img *ngIf="property.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                            <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{property.street}}<br></span>{{property.location}}</div>
                          </td>
                          <td class="font_semibold list-table-digit">{{property.home_price | currency:"":symbol:"1.0"}}</td>
                          <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bedroom.png" alt=""> {{property.bedroom}} beds</td>
                          <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bathroom.png" alt=""> {{property.full_bath}} baths</td>
                          <td class="font_semibold list-table-digit">{{property.living_area}}</td>
                          <td>
                            <span *ngFor="let event of property.event_list">
                              <span *ngIf="event.event_type == 'BO'" (click)="openEventModel('brokerOpen',event)">
                                <div class="month"> <span class="bgcolor1 font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                              </span>
                              <span *ngIf="event.event_type == 'AO'" (click)="openEventModel('appointmentOnly',event)">
                                <div class="month"> <span class="month_2_color font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                              </span>
                              <span *ngIf="event.event_type == 'OH'" (click)="openEventModel('openHouse',event)">
                                <div class="month"> <span class="month_3_color font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                              </span>
                            </span>
                          </td>
                          <td>
                            <span *ngIf="property.total_event_count != 0" class="pluse-more">
                              + {{property.total_event_count}} more
                              </span>
                          </td>
                          <td class="action-view">
                            <a (click)="showNewPropertyDialog(property,'commingSoonPropertyList')"><div class="save_notes margin_zero">Add Event</div> </a>
                          </td>
                          <td class="action-option">
                            <div class="open_click_menu" (click)="openMenu(CS,property.id)">
                              <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                              <ul id="ml_{{CS}}_{{property.id}}" class="click_menu_open events">
                                <li *ngIf="isPaidAccount" class="cursor-pointer option-menu" (click)="showLisingDetail(property.id)">Edit Listing</li>
                                <li class="cursor-pointer option-menu" (click)="gotToPropertyDetail('my-listing/property-detail',property.id)">Property Details</li>
                                <li (click)="openSharePropertyModal(property.id)" class="cursor-pointer option-menu" >Share</li>
                                <li class="cursor-pointer option-menu" (click)="removeListingDetail(property)">Remove listing</li>
                              </ul>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <div *ngIf="CSCount > CSItemsPerPage && CSCount != commingSoonPropertyList.length" class="new_form_group load_more_btn">
                      <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreListings(CSIndex , 'CS')" value="Load More">
                    </div>
                  </div>
                </div>

              <div id="menu4" class="tab-pane fade in  table-responsive selected_saved">
                  <div class="No_matches" *ngIf="showOFMLoader == true">
                    <div class="loader">
                    <div class="message">Loading...</div>
                    <div class="dots"><div class="center"></div></div>
                    </div>
                  </div>
                  <div *ngIf="offMarketPropertyList.length == 0 && showOFMLoader == false" class="No_matches">
                    <div class="title">No Listings</div>
                    <div class="text">You have no off market listings matching your search criteria.</div>
                  </div>
                  <div *ngIf="offMarketPropertyList.length != 0" class="property-list-table">
                <table class="table">
                  <thead>
                    <tr>
                      <th (click)="listingSort('OFM', 'PR')">Property <img id="OFM_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                      <th (click)="listingSort('OFM', 'HP')">Price <img id="OFM_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                      <th (click)="listingSort('OFM', 'BD')">Beds <img id="OFM_BD" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                      <th (click)="listingSort('OFM', 'BT')">Baths<img id="OFM_BT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                      <th (click)="listingSort('OFM', 'SF')">Sqft <img id="OFM_SF" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                      <!-- <th (click)="listingSort('OFM', 'DT')" colspan="4">Events <img id="OFM_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th> -->
                      <th> </th>
                    </tr>
                  </thead>
                  <tbody>
                      <tr *ngFor="let property of offMarketPropertyList;let OFM = index">
                        <td (click)="gotToPropertyDetail('my-listing/property-detail',property.id)" class="cursor-pointer">
                          <span class="vertical-align-top"><img *ngIf="property.property_file != ''" src="{{property.property_file}}"  class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                          <span class="vertical-align-top"><img *ngIf="property.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""> </span>
                          <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{property.street}}<br></span>{{property.location}}</div>
                        </td>
                          <td class="font_semibold list-table-digit">{{property.home_price | currency:"":symbol:"1.0"}}</td>
                          <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bedroom.png" alt=""> {{property.bedroom}} beds</td>
                          <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bathroom.png" alt=""> {{property.full_bath}} baths</td>
                          <td class="font_semibold list-table-digit">{{property.living_area}}</td>
                          <!-- <td>
                            <span *ngFor="let event of property.event_list">
                                <span *ngIf="event.event_type == 'BO'" (click)="openEventModel('brokerOpen',event)">
                                  <div class="month"> <span class="bgcolor1 font_semibold">{{getDayName(event.date,event.start_time)}}</span><div>{{getDay(event.date,event.start_time)}}</div> </div>
                                </span>
                                <span *ngIf="event.event_type == 'AO'" (click)="openEventModel('appointmentOnly',event)">
                                  <div class="month"> <span class="month_2_color font_semibold">{{getDayName(event.date,event.start_time)}}</span><div>{{getDay(event.date,event.start_time)}}</div> </div>
                                </span>
                                <span *ngIf="event.event_type == 'OH'" (click)="openEventModel('openHouse',event)">
                                  <div class="month"> <span class="month_3_color font_semibold">{{getDayName(event.date,event.start_time)}}</span><div>{{getDay(event.date,event.start_time)}}</div> </div>
                                </span>
                              </span>
                          </td> -->
                          <td class="action-view">
                            <a (click)="gotToPropertyDetail('my-listing/property-detail',property.id)"><div class="save_notes margin_zero">Property Details</div> </a>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div *ngIf="OFMCount > OFMItemsPerPage && OFMCount != offMarketPropertyList.length" class="new_form_group load_more_btn">
                    <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreListings(OFMIndex , 'OFM')" value="Load More">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


<div class="modal fade share-property event-card-responsive" id="shareProperty" role="dialog">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
          <div class="modal-body">
            <div class="heading">Share property</div>
            <label>Email</label>
            <form [formGroup]="sharPropertyFormGroup">
              <div>
                <input formControlName="email" class="email-input" type="text" placeholder="Email">
                <div *ngIf="sharPropertyFormGroup.controls['email'].touched && sharPropertyFormGroup.controls.email.errors?.email">
                  <span class="form-validation">Enter valid email address</span>
                </div>
                <div class="line">
                  <div class="or_line">
                    <hr/>
                    <div class="or">OR</div>
                  </div>
                </div>
              </div>

              <label class="cilent-label">Select your client</label>
              <div>
                <ng-select class="custom share-property-email"
                  placeholder="Client name"
                  formControlName="client"
                  notFoundText="No Client Name found"
                  [items]="clientList"
                  bindValue="client_id"
                  bindLabel="client_name"
                  (change)="onClientChange($event)"
                  [clearable]=false
                  [searchable]=false
                  >
                </ng-select>
              </div>
            <label>Message</label>
            <textarea formControlName="message" placeholder="Write a message to your client" class="message_share_property" cols="30" rows="10"></textarea>

            <span *ngIf="showShareErrorMsg" class="form-validation">{{sharePropertyErrorMSg}}</span>

            <div class="form_group send">
              <input type="button" value="Send" class="new_form new_text_css" placeholder="Send" (click)="shareProperty(sharPropertyFormGroup)"/>
            </div>
          </form>
            <div class="share_property_icons">
              <i (click)="shareFBProperty($event)" class="fa fa-facebook-square"></i>
              <a class="twitter popup" href="http://twitter.com/share?url=https://share.openhousesdirect.com/share/p/{{sharePropertyId}}"><i class="fa fa-twitter"></i></a>
            </div>
          </div>
        </div>
    </div>
</div>

<event-modal (setPropertyLatestInfo)="UpdatePropertyInfo($event)"></event-modal>
<add-event (addEventResponse)="addEvent($event)"></add-event>

<div class="listing-detail-footer">
    <footer></footer>
</div>
