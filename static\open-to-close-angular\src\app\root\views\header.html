<div class="header">
  <!-- <div class="note" *ngIf="selectedHeader=='search'">
                You are currently running an open house event. Tap this banner to <a href="#" class="banner">go back to your run tool. </a> <span> <i class="fa fa-close"></i> hide </span>
           </div> -->
  <nav class="navbar navbar-inverse hidden-xs hidden-md">
    <div class="container-fluid">
      <div class="navbar-header">
        <a class="navbar-brand cursor-pointer"><img src="{{imagePrefix}}logo.png" class="img-responsive"
            (click)="routeOnUrl('/')" alt="logo"></a>
        <button type="button" class="btn btn-info menu_button hidden-sm hidden-md hidden-lg" data-toggle="collapse"
          data-target="#menu_group"><i class="fa fa-bars"></i></button>
      </div>
      <!-- <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1"> -->
      <div id="menu_group ">
        <ul class="nav navbar-nav menu_new cursor-pointer" *ngIf="isLogin">

          <li *ngIf="checkUserValid('search')" [ngClass]="{'active2': selectedHeader=='search' }"><a
              (click)="routeOnUrl('search')">Search</a></li>
          <li *ngIf="checkUserValid('myOpenHouse')" [ngClass]="{'active2': selectedHeader=='myOpenHouse' }"><a
              (click)="routeOnUrl('my-open-houses/my-open-houses-list')">My List</a></li>
          <li *ngIf="currentUserType == 'listingAgent' || currentUserType == 'brokerage'"
            [ngClass]="{'active2': selectedHeader=='my-list' }"><a
              (click)="routeOnUrl('my-list/myList-open-house-list')">My List</a></li>
          <li *ngIf="checkUserValid('event-manager')" [ngClass]="{'active2': selectedHeader=='event-manager' }"><a
              (click)="checkUserStatus('event-manager/event-list','event-manager')">Guest Book</a></li>
          <li *ngIf="checkUserValid('my-listing')" [ngClass]="{'active2': selectedHeader=='my-listing' }"><a
              (click)="checkUserStatus('my-listing/listing-detail','myListings')">My Listings</a></li>
          <li *ngIf="checkUserValid('my-clients')" [ngClass]="{'active2': selectedHeader=='my-clients' }"><a
              (click)="checkUserStatus('my-clients','my-clients')">My Clients</a></li>
          <li *ngIf="checkUserValid('my-leads')" [ngClass]="{'active2': selectedHeader=='my-leads' }"><a
              (click)="checkUserStatus('my-leads','my-leads')">My Leads</a></li>
        </ul>
        <ul class="nav navbar-nav navbar-right cursor-pointer logout" *ngIf="isLogin">
          <!-- <li *ngIf="checkUserValid('tranning')"><a href="https://openhousesdirect.blog/" target="_blank"><img src="{{imagePrefix}}tranning.png" style="height: 23px !important;" class="img-responsive header_icon header_icon-sm" alt=""></a></li> -->
          <li *ngIf="checkAccountSubscription()">
            <a (click)="checkUserStatus('', 'upgrade')">
              <div class="save_notes margin-upgrade-button-web header-upgrade">Upgrade</div>
            </a>
          </li>

          <li [ngClass]="{'active2': selectedHeader=='favorites' }"><a
              (click)="routeOnUrl('favorites/favorites-list')"><img
                src="{{imagePrefix}}symbols-glyph-nav-favorite-head.png"
                class="img-responsive header_icon header_icon-sm" alt=""></a></li>
          <li *ngIf="checkUserValid('messaging')" [ngClass]="{'active2': selectedHeader=='messaging' }"><a
              (click)="checkUserStatus('messaging','messaging')"><i *ngIf="isUnreadMessage == true"
                class="fa fa-circle header-orange-dot"></i><img src="{{imagePrefix}}symbols-glyph-nav-message.png"
                class="img-responsive header_icon header_icon-sm" alt=""></a></li>
          <li *ngIf="checkUserValid('all-notification')" [ngClass]="{'active2': selectedHeader=='all-notification' }"
            class="cursor-pointer">
            <a (click)="showNotificationMenu()" class="notification_icon">
              <i *ngIf="isUnreadNotification == true" class="fa fa-circle header-orange-dot"></i>
              <img id="notification" src="{{imagePrefix}}symbols-glyph-nav-notification.png"
                class="img-responsive header_icon header_icon-sm-no" alt="">

              <div class="notification">
                <div class="title">Notifications</div>
                <div *ngFor="let notifi of notificationList;let i = index">
                  <div (click)="readHeaderNotification(notifi.notification_id,notifi)" *ngIf="i < 3"
                    class="notification_list">
                    <span *ngIf="notifi.notification_data.property_file !=''">
                      <img src={{notifi.notification_data.property_file}}
                        class="noti-img-width noti_image symbols-property-image rounded-img dis_inline list-pro-img"
                        alt="">
                    </span>
                    <span *ngIf="notifi.notification_data.property_file == ''">
                      <img src="{{imagePrefix}}symbols-property-image.png"
                        class="noti-img-width noti_image symbols-property-image rounded-img dis_inline list-pro-img"
                        alt="">
                    </span>
                    <span>
                      <span class="notifi-title">{{notifi.notification_title}}</span>
                      <span class="notifi-time">{{getNotificationTime(notifi.notification_time)}}</span>
                    </span>
                    <span class="dis_inline noti_text"><span class="dark">{{notifi.notification_message}}</span></span>
                  </div>
                </div>
                <div (click)="seeAllNotifications()" class="see_all_noti">See all notifications</div>
              </div>
            </a>
          </li>

          <li (click)="checkUserType()" [ngClass]="{'active2': selectedHeader=='profile' }"><a><img
                src="{{imagePrefix}}symbols-glyph-nav-profile.png" class="img-responsive header_icon" alt=""></a></li>
          <li><a (click)="logout()">Log Out</a></li>
          <!-- <li><a href="https://blog.openhousesdirect.com" target="_blank"> Blog</a></li> -->

          <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true"
              aria-expanded="false">Resources <span class="caret"></span></a>
            <ul class="dropdown-menu">
              <li><a href="https://blog.openhousesdirect.com">Blog</a></li>
              <li><a *ngIf="checkUserValid('tranning')" href="https://www.openhousemasters.com"
                  target="_blank">Training</a></li>
            </ul>
          </li>
        </ul>

      </div>
      <div *ngIf="!isLogin">
        <ul class="nav navbar-nav navbar-right cursor-pointer">
          <li><a data-toggle="modal" data-target="#authModal" (click)="OpenAuthModal('signIn')">Sign In</a></li>
          <li><a data-toggle="modal" data-target="#authModal" (click)="OpenAuthModal('signUp')">Join</a></li>
          <li><a href="https://blog.openhousesdirect.com" target="_blank">Blog</a></li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- mobile menu button  -->
  <span>
    <li class="visible-xs visible-md mobile_logo_menu back_white_menu">
      <img src="{{imagePrefix}}symbols-logo-icon.png" (click)="routeOnUrl('/')"
        class="img-responsive mobile_white_logo symbols-logo-icon" alt="">
      <button type="button" class="click_mobile_menu btn btn-info menu_button hidden-sm hidden-md hidden-lg"
        data-toggle="collapse" data-target="#menu_group_mobile"><i class="fa fa-bars"></i></button>
      <div class="dis_mo_none mobile_close"><i class="fa fa-close"></i></div>
      <ul *ngIf="isLogin" class="mobile_icon">
        <!-- <li *ngIf="checkUserValid('tranning')"><a href="https://openhousesdirect.blog/" target="_blank"><img src="{{imagePrefix}}tranning.png" style="height: 23px !important;" class="img-responsive header_icon header_icon-sm" alt=""></a></li> -->
        <li *ngIf="checkAccountSubscription()">
          <a (click)="checkUserStatus('', 'upgrade')">
            <div class="save_notes margin-upgrade-button">Upgrade</div>
          </a>
        </li>
        <li [ngClass]="{'active2': selectedHeader=='favorites' }"><a
            (click)="routeOnUrl('favorites/favorites-list')"><img
              src="{{imagePrefix}}symbols-glyph-nav-favorite-head.png" class="img-responsive header_icon header_icon-sm"
              alt=""></a></li>
        <li *ngIf="checkUserValid('messaging')" [ngClass]="{'active2': selectedHeader=='messaging' }"><a
            (click)="checkUserStatus('messaging','messaging')"><i *ngIf="isUnreadMessage == true"
              class="fa fa-circle header-orange-dot"></i><img src="{{imagePrefix}}symbols-glyph-nav-message.png"
              class="img-responsive header_icon header_icon-sm" alt=""></a></li>
        <li *ngIf="checkUserValid('all-notification')" [ngClass]="{'active2': selectedHeader=='all-notification' }"
          class="cursor-pointer">
          <a (click)="seeAllNotifications()" class="notification_icon">
            <i *ngIf="isUnreadNotification == true" class="fa fa-circle header-orange-dot"></i>
            <img id="notification" src="{{imagePrefix}}symbols-glyph-nav-notification.png"
              class="img-responsive header_icon header_icon-sm-no" alt="">
          </a>
        </li>
        <li (click)="checkUserType()" [ngClass]="{'active2': selectedHeader=='profile' }"><a><img
              src="{{imagePrefix}}symbols-glyph-nav-profile.png" class="img-responsive header_icon" alt=""></a></li>
      </ul>
    </li>

  </span>
  <!-- <span *ngIf="selectedHeader == '/' || selectedHeader == ''">
        <li class="visible-xs mobile_logo_menu on_scroll_change">
            <img src="{{imagePrefix}}White_logo.png" class="img-responsive mobile_white_logo " alt="">
            <button  type="button" class="click_mobile_menu btn btn-info menu_button hidden-sm hidden-md hidden-lg" data-toggle="collapse" data-target="#menu_group_mobile"><i class="fa fa-bars"></i></button>

            <div class="dis_mo_none mobile_close"><i class="fa fa-close"></i></div>
        </li>
    </span> -->

  <!-- mobile menu  -->
  <nav class="mobile_new_menu collapse" id="menu_group_mobile">
    <ul>
      <!-- <li class="mobile_new_search_menu">
                <div class="mobile_search_menu_2">
                    <input type="text" value="Phoenix, AZ" class="mobile_search_menu_image">
                </div>
            </li> -->
      <li *ngIf="checkUserValid('search')" [ngClass]="{'active2': selectedHeader=='search' }"><a
          (click)="routeOnUrl('search')">Search</a></li>
      <li *ngIf="checkUserValid('myOpenHouse')" [ngClass]="{'active2': selectedHeader=='myOpenHouse' }"><a
          (click)="routeOnUrl('my-open-houses/my-open-houses-list')">My List</a></li>
      <li *ngIf="checkUserValid('event-manager')" [ngClass]="{'active2': selectedHeader=='event-manager' }"><a
          (click)="checkUserStatus('event-manager/event-list','event-manager')"></a></li>
      <li *ngIf="checkUserValid('my-listing')" [ngClass]="{'active2': selectedHeader=='my-listing' }"><a
          (click)="checkUserStatus('my-listing/listing-detail','myListings')">My Listings</a></li>
      <li *ngIf="checkUserValid('my-clients')" [ngClass]="{'active2': selectedHeader=='my-clients' }"><a
          (click)="checkUserStatus('my-clients','my-clients')">My Clients</a></li>
      <li *ngIf="checkUserValid('my-leads')" [ngClass]="{'active2': selectedHeader=='my-leads' }"><a
          (click)="checkUserStatus('my-leads','my-leads')">My Leads</a></li>
      <li *ngIf="currentUserType == 'listingAgent' || currentUserType == 'brokerage'"
        [ngClass]="{'active2': selectedHeader=='my-list' }"><a (click)="routeOnUrl('my-list/myList-open-house-list')">My
          List</a></li>
      <!-- <li *ngIf="isLogin"><a href="https://blog.openhousesdirect.com" target="_blank">Blog</a></li> -->
      <li class="dropdown">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true"
          aria-expanded="false">Resources <span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li><a href="https://blog.openhousesdirect.com">Blog</a></li>
          <li><a *ngIf="checkUserValid('tranning')" href="https://www.openhousemasters.com" target="_blank">Training</a>
          </li>
        </ul>
      </li>
      <li *ngIf="isLogin"><a (click)="logout()">Log Out</a></li>
      <div *ngIf="!isLogin">
        <!-- <ul class="nav navbar-nav navbar-right cursor-pointer"> -->
        <li><a data-toggle="modal" data-target="#authModal" (click)="OpenAuthModal('signIn')">Sign In</a></li>
        <li><a data-toggle="modal" data-target="#authModal" (click)="OpenAuthModal('signUp')">Join</a></li>
        <li><a href="https://blog.openhousesdirect.com" target="_blank">Blog</a></li>
        <li>
          <div class="btn-group">
            <button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true"
              aria-expanded="false">
              Right-aligned menu
            </button>
            <div class="dropdown-menu dropdown-menu-right">
              <button class="dropdown-item" type="button">Action</button>
              <button class="dropdown-item" type="button">Another action</button>
              <button class="dropdown-item" type="button">Something else here</button>
            </div>
          </div>
        </li>
        <!-- </ul> -->
      </div>
    </ul>
    <ul class="mobile_icon">
      <!-- <div *ngIf="isLogin && (selectedHeader == '/' || selectedHeader == '')">
                <li [ngClass]="{'active2': selectedHeader=='favorites' }"><a (click)="routeOnUrl('favorites')"><img src="{{imagePrefix}}symbols-glyph-nav-favorite.png" class="img-responsive header_icon" alt=""></a></li>
                <li *ngIf="checkUserValid('messaging')" [ngClass]="{'active2': selectedHeader=='messaging' }"><a (click)="routeOnUrl('messaging')"><i *ngIf="isUnreadMessage == true" class="fa fa-circle header-orange-dot"></i><img src="{{imagePrefix}}symbols-glyph-nav-message.png" class="img-responsive header_icon" alt=""></a></li>
                <li *ngIf="checkUserValid('all-notification')" [ngClass]="{'active2': selectedHeader=='all-notification' }" class="cursor-pointer">
                    <a  (click)="seeAllNotifications()" class="notification_icon">
                        <i *ngIf="isUnreadNotification == true" class="fa fa-circle header-orange-dot"></i>
                        <img id="notification" src="{{imagePrefix}}symbols-glyph-nav-notification.png" class="img-responsive header_icon " alt="">
                    </a>
                </li>
                <li (click)="checkUserType()" [ngClass]="{'active2': selectedHeader=='profile' }"><a><img src="{{imagePrefix}}symbols-glyph-nav-profile.png" class="img-responsive header_icon" alt=""></a></li>
                <li><a href="https://blog.openhousesdirect.com" target="_blank"> Blog</a></li>
            </div> -->
    </ul>
  </nav>
</div>

<div class="modal fade sign_modal" id="authModal" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
        <div class="modal_content">
          <ul class="nav nav-pills">
            <li><a data-toggle="pill" href="#signIn">Sign In</a></li>
            <li><a data-toggle="pill" href="#signUp">New Account</a></li>
          </ul>

          <div class="tab-content">
            <div id="signIn" class="tab-pane new_account fade in active">
              <div class="title">Sign in to find open houses near you</div>
              <form [formGroup]="loginForm">
                <div class="form_group">
                  <input type="text" class="new_form" formControlName="email" placeholder="Enter your email address*"
                    required />
                  <div *ngIf="loginForm.controls['email'].untouched">
                    <span></span>
                  </div>
                  <div *ngIf="loginForm.controls['email'].touched && loginForm.controls.email.errors?.email">
                    <span class="form-validation">Enter valid email address</span>
                  </div>
                </div>
                <div class="form_group">
                  <input type="password" class="new_form" formControlName="password" placeholder="Enter your password*"
                    required />
                  <div *ngIf="loginForm.controls.password.touched">
                    <p class="form-validation" *ngIf="loginForm.controls.password.errors?.required">Enter password</p>
                  </div>
                  <label class="cursor-pointer for_get_pass" data-toggle="modal" data-target="#forgetPasswordModal"
                    data-dismiss="modal">Forgot password?</label>
                  <label class="cursor-pointer resend-email" data-toggle="modal" data-target="#resendEmailConfirm"
                    data-dismiss="modal">Resend confirmation email?</label>
                </div>
                <div class="form_group cursor-pointer">
                  <input type="submit" [ngClass]="{'submit-disable': loginForm.invalid }" class="new_form"
                    [disabled]="loginForm.invalid" value="Submit" (click)="loginUser(loginForm)" />
                </div>
              </form>
              <!-- <div class="or_line">
                                    <hr/>
                                    <div class="or">OR</div>
                                </div>
                                <div class="Connect new_text_css cursor_poiner">
                                    <i class="fa fa-facebook"></i>
                                    Connect with Facebook
                                </div> -->

            </div>

            <div id="signUp" class="tab-pane new_account fade">
              <div class="title">
                <span>Register As</span>
                <!-- <span *ngIf="homeBuyerRegistration && !estateProfessionalRegistration">Register here for the easiest way to Open House</span> -->
              </div>
              <div *ngIf="!agentSignup && !brokerSignup" class="registration-options">
                <div class="registration-option">
                  <input type="radio" id="estateProfessional" name="registrationType"
                    (click)="estateProfessionalSignUp()" (change)="selectRegistrationType('estateProfessional')"
                    [checked]="selectedRegistrationType === 'estateProfessional'" for="estateProfessional" />
                  <label class="labels" for="estateProfessional">Real Estate Professional</label>
                </div>
                <div style="margin-left: 20px;" class="registration-option">
                  <input type="radio" id="homeBuyer" name="registrationType" (click)="homeBuyerSignUp()"
                    (change)="selectRegistrationType('homeBuyer')"
                    [checked]="selectedRegistrationType === 'homeBuyer'" />
                  <label style="margin-bottom: 0px;" for="homeBuyer">Home Buyer</label>
                </div>
              </div>

              <div *ngIf="showLenderSignUp == true">
                <div class="title">You have been invited to be a preferred lender on Open Houses Direct.</div>
                <div class="menu51_text">Set up an account to be shown as a preferred lender and start collecting leads
                </div>
              </div>

              <form [formGroup]="registerForm">
                <div class="form_group" formGroupName="profile">
                  <input type="text" class="new_form" formControlName="first_name" placeholder="Enter first name*"
                    required />
                  <div *ngIf="registerForm['controls'].profile['controls']['first_name'].untouched">
                    <span></span>
                  </div>
                  <div
                    *ngIf="registerForm['controls'].profile['controls']['first_name'].touched && registerForm['controls'].profile['controls'].first_name.errors?.required">
                    <span class="form-validation">Enter first name</span>
                  </div>
                </div>
                <div class="form_group" formGroupName="profile">
                  <input type="text" class="new_form" formControlName="last_name" placeholder="Enter last name*"
                    required />
                  <div *ngIf="registerForm['controls'].profile['controls']['last_name'].untouched">
                    <span></span>
                  </div>
                  <div
                    *ngIf="registerForm['controls'].profile['controls']['last_name'].touched && registerForm['controls'].profile['controls']['last_name'].errors?.required">
                    <span class="form-validation">Enter last name</span>
                  </div>
                </div>
                <div *ngIf="estateProfessionalRegistration">
                  <div *ngIf="!brokerSignup && !agentSignup && estateProfessionalRegistration" class="form_group">
                    <ng-select class="custom signup-dropdown" placeholder="Role" notFoundText="No Agent found"
                      [items]="AgentTypeList" bindLabel="value" bindValue="name" [(ngModel)]="selectedUserTypeDropdown"
                      [ngModelOptions]="{standalone: true}" [clearable]=false [searchable]=false
                      (change)="getAgentType($event.name)">
                    </ng-select>
                  </div>

                  <!-- <div *ngIf="estateProfessionalRegistration" class="form_group">
                                            <div  formGroupName="profile">
                                                <div class="form-check">
                                                  <input class="form-check-input position-static" type="checkbox" id="blankCheckbox"
                                                  (change)="checkAgentId('')"
                                                  value="is_not_member_ARMLS" aria-label="..." formControlName="is_not_member_ARMLS">
                                                  <label class="form-check-label" for="blankCheckbox"> &nbsp;&nbsp;I am not a member of ARMLS</label>
                                                </div>
                                              </div>
                                        </div> -->

                  <!-- <div *ngIf="isTitleSelected !=false && agentType=='Broker' && !registerForm['controls'].profile['controls']['is_not_member_ARMLS'].value" class="form_group"
                                            [ngClass]="{'has-success has-feedback': estateProfessionalRegistration && agentIdInPreDirectory && showResponseIcon, 'has-error has-feedback': estateProfessionalRegistration && !agentIdInPreDirectory && showResponseIcon}">
                                            <input type="text" #agentId class="new_form" placeholder="Enter your brokerage ID*" (keyup)="checkAgentId(agentId.value)"
                                                [ngClass]="{'success-border-box': estateProfessionalRegistration && agentIdInPreDirectory && showResponseIcon, 'error-border-box': estateProfessionalRegistration && !agentIdInPreDirectory && showResponseIcon}"/>
                                            <span *ngIf="agentIdInPreDirectory && estateProfessionalRegistration && showResponseIcon"
                                                class="glyphicon glyphicon-ok form-control-feedback"></span>
                                            <span *ngIf="!agentIdInPreDirectory && estateProfessionalRegistration && showResponseIcon"
                                                class="glyphicon glyphicon-remove form-control-feedback"></span>
                                        </div>


                                        <div *ngIf="isTitleSelected !=false && agentType=='Real Estate Agent' && !registerForm['controls'].profile['controls']['is_not_member_ARMLS'].value " class="form_group"
                                            [ngClass]="{'has-success has-feedback': estateProfessionalRegistration && agentIdInPreDirectory && showResponseIcon, 'has-error has-feedback': estateProfessionalRegistration && !agentIdInPreDirectory && showResponseIcon}">
                                            <input type="text" #agentId class="new_form" placeholder="Enter your ARMLS ID*" (keyup)="checkAgentId(agentId.value)"
                                            [ngClass]="{'success-border-box': estateProfessionalRegistration && agentIdInPreDirectory && showResponseIcon, 'error-border-box': estateProfessionalRegistration && !agentIdInPreDirectory && showResponseIcon}"/>
                                            <span *ngIf="agentIdInPreDirectory && estateProfessionalRegistration && showResponseIcon"
                                                class="glyphicon glyphicon-ok form-control-feedback"></span>
                                            <span *ngIf="!agentIdInPreDirectory && estateProfessionalRegistration && showResponseIcon"
                                                class="glyphicon glyphicon-remove form-control-feedback"></span>
                                        </div>

                                        <div *ngIf="isTitleSelected !=false && agentType=='Mortgage Lender' " class="form_group">
                                            <input type="text" #company class="new_form" placeholder="Enter Company Name*" (keyup)="setLenderCompanyName(company.value)"/>
                                        </div>

                                        <div *ngIf="!agentIdInPreDirectory && showResponseIcon">
                                            <span class="form-validation">{{agentIdErrorMessage}}</span>
                                        </div> -->

                </div>

                <div class="form_group"
                  [ngClass]="{'has-success has-feedback': estateProfessionalRegistration && emailInPreDirectory && showResponseIcon, 'has-error has-feedback': estateProfessionalRegistration && !emailInPreDirectory && showResponseIcon}">
                  <input type="text" class="new_form"
                    [ngClass]="{'success-border-box': estateProfessionalRegistration && emailInPreDirectory && showResponseIcon, 'error-border-box': estateProfessionalRegistration && !emailInPreDirectory && showResponseIcon}"
                    formControlName="email" placeholder="Enter your email address*" required />

                  <span *ngIf="emailInPreDirectory && estateProfessionalRegistration && showResponseIcon"
                    class="glyphicon glyphicon-ok form-control-feedback"></span>
                  <span *ngIf="!emailInPreDirectory && estateProfessionalRegistration && showResponseIcon"
                    class="glyphicon glyphicon-remove form-control-feedback"></span>

                  <div *ngIf="registerForm.controls['email'].untouched">
                    <span></span>
                  </div>
                  <div *ngIf="registerForm.controls['email'].touched && registerForm.controls.email.errors?.email">
                    <span class="form-validation">Enter valid email address</span>
                  </div>
                  <div *ngIf="!emailInPreDirectory && estateProfessionalRegistration && showResponseIcon">
                    <span class="form-validation">{{emailErrorMessage}}</span>
                  </div>
                </div>
                <div class="form_group" formGroupName="passwordForm">
                  <input type="password" class="new_form" formControlName="password" placeholder="Choose a password*"
                    required />
                  <label>Password must be 5-15 characters</label>
                  <div *ngIf="registerForm['controls'].passwordForm['controls'].password.touched">
                    <p class="form-validation"
                      *ngIf="registerForm['controls'].passwordForm['controls'].password.errors?.required">Enter password
                    </p>
                    <p class="form-validation"
                      *ngIf="registerForm['controls'].passwordForm['controls'].password.errors?.minlength">Password must
                      be 5-15 characters</p>
                    <p class="form-validation"
                      *ngIf="registerForm['controls'].passwordForm['controls'].password.errors?.maxlength">Password must
                      be 5-15 characters</p>
                  </div>
                </div>
                <div class="form_group from-md-10" formGroupName="passwordForm">
                  <input type="password" class="new_form" formControlName="confirm_password"
                    placeholder="Confirm password*" required />
                  <label>Confirm password must be 5-15 characters</label>
                  <div *ngIf="registerForm['controls'].passwordForm['controls'].confirm_password.touched">
                    <p class="form-validation" *ngIf="registerForm['controls'].passwordForm['controls'].confirm_password.errors?.required &&
                                            registerForm['controls'].passwordForm.hasError('mismatch') == false">Enter
                      confirm password</p>
                    <p class="form-validation"
                      *ngIf="registerForm['controls'].passwordForm['controls'].confirm_password.errors?.minlength">
                      Confirm password must be 5-15 characters</p>
                    <p class="form-validation"
                      *ngIf="registerForm['controls'].passwordForm['controls'].confirm_password.errors?.maxlength">
                      Confirm password must be 5-15 characters</p>
                    <p class="form-validation" *ngIf="registerForm['controls'].passwordForm.errors?.mismatch">Confirm
                      password not match</p>
                  </div>
                </div>

                <div class="form_group from-md-10" formGroupName="profile">
                  <ng-select class="custom signup-dropdown" placeholder="Country" notFoundText="No Country found"
                    [items]="countryList" bindLabel="value" bindValue="name" [(ngModel)]="selectedCountry"
                    [ngModelOptions]="{standalone: true}" [clearable]=false [searchable]=false>
                  </ng-select>
                </div>
                <!-- For formationg mobile nuber -->
                <div class="form_group from-md-10" formGroupName="profile">
                  <input type="text" maxlength="12" #phone (keyup)="validateFormat(phone.value)" class="new_form"
                    formControlName="phone" placeholder="Phone Number" />
                  <div *ngIf="registerForm['controls'].profile['controls']['phone'].touched">
                    <p class="form-validation" *ngIf="registerForm.controls.profile.controls.phone.errors?.minlength">
                      phone number must be digits and 10 characters</p>
                    <p class="form-validation" *ngIf="registerForm.controls.profile.controls.phone.errors?.maxlength">
                      phone number must be digits and 10 characters</p>
                  </div>
                </div>
                <!-- For unformatted mobile number -->
                <div class="form_group from-md-10" formGroupName="profile" *ngIf="showOtp"
                  style="border: 1px solid red; padding: 10px!important;">
                  <input type="text" maxlength="4" #otp class="new_form" formControlName="otp"
                    placeholder="Enter Verification Code" (keyup)="validateOtpFormat(otp.value)" />
                  <br />
                  <!-- <p>Haven't Received Any Code</p> -->
                  <a class="registration-link"><span (click)="sendOtp(registerForm)">Resend OTP</span></a>
                </div>
                <div class="check_group" *ngIf="showLenderSignUp == false">
                  <span *ngIf="isClientInviteSignUp == false">
                    <div *ngIf="!brokerSignup && !agentSignup && homeBuyerRegistration" class="form_group">
                      <input type="checkbox" [checked]="agent" (change)="viewAgentId()" /> <span
                        class="checkmark"></span>
                      <label class="width_auto">I am working with an agent</label>
                    </div>
                    <div *ngIf="showAgent != false">
                      <div class="form_group">
                        <input type="text" #name class="new_form " placeholder="Type agent name here*"
                          (keyup)="searchAgent(name.value)" />
                        <img src="{{imagePrefix}}symbols-glyph-openhouse.png" class="img-responsive search" alt="">
                      </div>
                      <div *ngIf="showSearching != false">
                        <div class="agent_found">
                          Searching...
                        </div>
                      </div>
                      <div *ngIf="agentList != false">
                        <div class="agent_found">
                          {{searchAgentList.length}} agent found
                        </div>
                        <div [ngClass]="{'search-agent-list': searchAgentList.length > 3}">
                          <div class="remove_agent search-agent-padding" *ngFor="let agent of searchAgentList">
                            <span
                              *ngIf="agent.profile_photo == null || agent.profile_photo == '' || agent.profile_photo == undefined"
                              [ngClass]="{ 'remove-span' : agent.profile_photo == null || agent.profile_photo == '' || agent.profile_photo == undefined}"><img
                                src="{{imagePrefix}}testmonial-default (1).png" width="130px" height="130px"
                                class="search-agent-image" alt=""></span>
                            <span
                              *ngIf="agent.profile_photo != null || agent.profile_photo != undefined || agent.profile_photo != ''"><img
                                src="{{agent.profile_photo}}" width="130px" height="130px" class="search-agent-image"
                                alt=""></span>
                            <div class="remove_details">
                              <div *ngIf="agent.name != null" class="name">{{agent.name}}</div>
                              <div *ngIf="agent.name == null" class="name">{{agent.mls_agent_id}}</div>
                              <div class="sub_name"> {{agent.brokerage_name}}</div>
                              <div *ngIf="getAgentId == agent.id" (click)="removeAgent()" class="remove_button">Remove
                                agent</div>
                              <div *ngIf="getAgentId != agent.id" (click)="showMyAgent(agent.id)"
                                class="remove_button select">This is my agent</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- <div *ngIf="!brokerSignup && !agentSignup && estateProfessionalRegistration" class="form_group">
                                                <input type="checkbox" [checked]="estateAgent" (change)="viewEstateAgent()" />    <span class="checkmark"></span>
                                                <label>I am an agent or real estate professional</label>
                                            </div> -->

                    <!-- <div *ngIf="showEstateAgent !=false"> -->
                    <!-- <div *ngIf="estateProfessionalRegistration">
                                                <div *ngIf="!brokerSignup && !agentSignup && estateProfessionalRegistration" class="form_group">
                                                    <ng-select class="custom signup-dropdown"
                                                        placeholder = "Role"
                                                        notFoundText="No Agent found"
                                                        [items]="AgentTypeList"
                                                        bindLabel="name"registerForm
                                                        bindValue="name"
                                                        [clearable]=false
                                                        [searchable]=false
                                                        (change)="getAgentType($event.name)">
                                                    </ng-select>
                                                </div>

                                                <div *ngIf="isTitleSelected !=false && agentType=='Broker' " class="form_group">
                                                    <input type="text" #agentId class="new_form" placeholder="Enter your brokerage ID*" (keyup)="checkAgentId(agentId.value)"/>
                                                </div>

                                                <div *ngIf="isTitleSelected !=false && agentType=='Real Estate Agent' " class="form_group">
                                                    <input type="text" #agentId class="new_form" placeholder="Enter your MLS ID*" (keyup)="checkAgentId(agentId.value)"/>
                                                </div>

                                                <div *ngIf="isTitleSelected !=false && agentType=='Mortgage Lender' " class="form_group">
                                                    <input type="text" #company class="new_form" placeholder="Enter Company Name*" (keyup)="setLenderCompanyName(company.value)"/>
                                                </div>
                                            </div> -->
                  </span>
                </div>
                <div class="check_group" *ngIf="isClientInviteSignUp == true && homeBuyerRegistration == true">
                  <div class="form_group">
                    <input type="checkbox" disabled checked="true" /> <span class="checkmark"></span>
                    <label class="width_auto">I am working with an agent</label>
                  </div>
                  <div>
                    <div class="remove_agent search-agent-padding">
                      <span
                        *ngIf="invitedAgentResponse.profile_photo == null || invitedAgentResponse.profile_photo == '' || invitedAgentResponse.profile_photo == undefined"
                        [ngClass]="{ 'remove-span' : invitedAgentResponse.profile_photo == null || invitedAgentResponse.profile_photo == '' || invitedAgentResponse.profile_photo == undefined}"><img
                          src="{{imagePrefix}}testmonial-default (1).png" width="130px" height="130px"
                          class="search-agent-image" alt=""></span>
                      <span
                        *ngIf="invitedAgentResponse.profile_photo != null || invitedAgentResponse.profile_photo != undefined || invitedAgentResponse.profile_photo != ''"><img
                          src="{{invitedAgentResponse.profile_photo}}" width="130px" height="130px"
                          class="search-agent-image" alt=""></span>
                      <div class="remove_details">
                        <div *ngIf="invitedAgentResponse.name != null" class="name">{{invitedAgentResponse.name}}</div>
                        <div *ngIf="invitedAgentResponse.name == null" class="name">
                          {{invitedAgentResponse.mls_agent_id}}</div>
                        <div class="sub_name"> {{invitedAgentResponse.brokerage_name}}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="checkbox-container">
                  <label class="custom-checkbox" style="margin-top: 3px;">
                    <input type="checkbox" formControlName="agree_terms" />
                    <span class="checkmark"></span>
                  </label>
                  <span style="font-size: 13px; line-height: 1.4;">
                    I agree to the <a href="/terms-of-use" target="_blank">Terms of Use</a> and
                    <a href="/privacy-policy" target="_blank">Privacy Policy</a>, and I am opting in to receive
                    communications via email, phone calls, and SMS/MMS messages. Message frequency may vary.
                    Standard message and data rates may apply. Reply STOP to unsubscribe or HELP for assistance.
                  </span>
                </div>


                <div class="form_group" *ngIf="showLenderSignUp == true">
                  <input type="text" #company class="new_form" placeholder="Enter Company Name*"
                    (keyup)="setLenderCompanyName(company.value)" />
                </div>

                <div class="form_group cursor-pointer">
                  <input type="button" *ngIf="!showOtp" style="width: 150px !important;"
                    [ngClass]="{'submit-disable':registerForm.invalid || !isFormValid}"
                    [disabled]="registerForm.invalid || !isFormValid" class="new_form" value="Send Verification Code"
                    (click)="sendOtp(registerForm)" />
                  <input type="button" *ngIf="showOtp" style="width: 150px !important;"
                    [ngClass]="{'submit-disable':registerForm.invalid || !isFormValid}"
                    [disabled]="registerForm.invalid || !isFormValid" class="new_form" value="Register"
                    (click)="verifyOtp(registerForm)" />
                  <!-- <input type="button"  [ngClass]="{'submit-disable':registerForm.invalid || !isFormValid}" [disabled]="registerForm.invalid || !isFormValid" class="new_form" value="Submit" (click)="registerUser(registerForm)" /> -->
                </div>
              </form>
              <!-- <div *ngIf="showLenderSignUp == false">
                                    <div class="or_line">
                                        <hr/>
                                        <div class="or">OR</div>
                                    </div>
                                    <div class="Connect new_text_css cursor_poiner">
                                        <i class="fa fa-facebook"></i>
                                        Connect with Facebook
                                    </div>
                                </div> -->
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</div>

<div class="modal fade sign_modal" id="forgetPasswordModal" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
        <div class="modal_content">
          <div class=" new_account">
            <div class="title">Forgot your password?</div>
            <div class="menu51_text">Enter your email address and we’ll send you a link to set your password.</div>
            <form [formGroup]="forgotPasswordForm">
              <div class="form_group">
                <input type="text" class="new_form" placeholder="Enter your email address*" formControlName="email"
                  required />

                <div *ngIf="forgotPasswordForm.controls['email'].untouched">
                  <span></span>
                </div>

                <div
                  *ngIf="forgotPasswordForm.controls['email'].touched && forgotPasswordForm.controls.email.errors?.email">
                  <span class="form-validation">Enter valid email address</span>
                </div>

              </div>

              <div class="form_group">
                <input type="submit" class="new_form  new_text_css"
                  [ngClass]="{'submit-disable':forgotPasswordForm.invalid}" [disabled]="forgotPasswordForm.invalid"
                  value="Submit" (click)="ForgotPassword(forgotPasswordForm)" />
              </div>

            </form>

            <div class="know_u_pass"> Know your password? <span><a class="cursor_poiner" data-toggle="modal"
                  data-target="#authModal" data-dismiss="modal"> Sign In </a></span></div>
            <!-- <div class="or_line">
                          <hr/>
                          <div class="or">OR</div>
                       </div>
                       <div class="Connect new_text_css cursor_poiner">
                          <i class="fa fa-facebook"></i>
                          Connect with Facebook
                       </div> -->
          </div>
        </div>
        <div class="modal_footer">
          <p>By creating an account you agree to the Open Houses Direct <a href="/terms-of-use" target="_blank"> Terms
              of Use </a> and <a href="/privacy-policy" target="_blank">Privacy Policy</a></p>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="modal fade sign_modal" id="thankYouModal" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
        <div class="modal_content">
          <div class=" new_account">
            <div class="title">Thank you for signing up!</div>
            <!-- <p class="thankyou-p">We have sent you an email to confirm your account. In order to complete the sign-up process, please click the activation link.</p> -->
            <p class="thankyou-p">Your account has been successfully created. You can now log in and start exploring our
              services.</p>
          </div>
          <div class="form_group cursor-pointer">
            <input type="button" data-dismiss="modal" class="new_form" value="Close" />
          </div>
        </div>
        <div class="modal_footer">
          <p>By creating an account you agree to the Open Houses Direct <a href="/terms-of-use" target="_blank"> Terms
              of Use </a> and <a href="/privacy-policy" target="_blank">Privacy Policy</a></p>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="modal fade sign_modal" id="emailUpdateModal" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
        <div class="modal_content">
          <div class=" new_account">
            <div class="title upddate-email">Thank you for updating your email.</div>
            <div class="upddate-email-border"></div>
            <p class="upddate-email-p">We have sent you an email to confirm your account. In order to complete the
              update process, please click the activation link.</p>
          </div>
          <div class="form_group cursor-pointer">
            <input type="button" data-dismiss="modal" class="new_form" value="Close" />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade sign_modal" id="resendEmailConfirm" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
        <div class="modal_content">
          <div class=" new_account">
            <div class="title">Resend Confirmation Email?</div>
            <div class="menu51_text">Enter your email address and we’ll send you a link to confirm your email.</div>
            <form [formGroup]="reSendEmailConfirmationForm">
              <div class="form_group">
                <input type="text" class="new_form" placeholder="Enter your email address*" formControlName="email"
                  required />
                <div
                  *ngIf="reSendEmailConfirmationForm.controls['email'].touched && reSendEmailConfirmationForm.controls.email.errors?.email">
                  <span class="form-validation">Enter valid email address</span>
                </div>
              </div>
              <div class="form_group">
                <input type="submit" class="new_form  new_text_css"
                  [ngClass]="{'submit-disable':reSendEmailConfirmationForm.invalid}"
                  [disabled]="reSendEmailConfirmationForm.invalid" value="Submit"
                  (click)="reSendConfirmationEmail(reSendEmailConfirmationForm)" />
              </div>
            </form>
          </div>
        </div>
        <div class="modal_footer">
          <p>By creating an account you agree to the Open Houses Direct <a href="terms-of-use" target="_blank"> Terms of
              Use </a> and <a href="/privacy-policy" target="_blank">Privacy Policy</a></p>
        </div>
      </div>
    </div>
  </div>
</div>



<div class="modal fade " id="upgradeModal" role="dialog">
  <div class="agent_plan_upgrade upgrade-contain">
    <div class="modal-dialog modal-lg Upgrade-Agent">
      <div class="modal-content">
        <div class="modal-body">
          <div class="upgrade_agent">
            <div class="upgrade_agent_title title2">
              Upgrade account
            </div>
            <div class="banifit">
              <div class="title2">
                Membership Benefits
              </div>
              <ul *ngIf="listingAgent">
                <li> <span> <i class="fa fa-check"></i> </span> Unlimited PRE-MLS / Coming Soon listings</li>
                <li> <span> <i class="fa fa-check"></i> </span> In-app messaging with home buyers</li>
                <li> <span> <i class="fa fa-check"></i> </span> Access to open house leads</li>
                <li> <span> <i class="fa fa-check"></i> </span> Seller open house reporting</li>
                <li> <span> <i class="fa fa-check"></i> </span> Client list management</li>
                <!-- <li> <span> <i class="fa fa-check"></i> </span> Unlimited Open House events</li>
                            <li> <span> <i class="fa fa-check"></i> </span> Full access to open house and property leads</li>
                            <li> <span> <i class="fa fa-check"></i> </span> Inter-brokerage Open House scheduling</li> -->
              </ul>
              <ul *ngIf="brokerage">
                <li> <span> <i class="fa fa-check"></i> </span> Inter-brokerage open house scheduling</li>
                <li> <span> <i class="fa fa-check"></i> </span> Inter-brokerage open house event trading</li>
                <li> <span> <i class="fa fa-check"></i> </span> Inter-brokerage access to event manager</li>
              </ul>
            </div>
            <div class="billing_cycle">
              <div class="upgrade-account-sub-title">Start your FREE 7 day trial</div>
              <div class="upgrade-text">Upgrade your account today to unlock premium features.</div>
              <div *ngFor="let plan of plansList" class="cycle_1">
                <div [ngClass]="{'selected-plan': selectedPlan == plan.id}">
                  <div class="title2" [ngClass]="{'selected-plan': selectedPlan == plan.id}"><span
                      *ngIf="plan.id.includes('M');then Monthly else Yearly"></span></div>
                  <ng-template #Monthly>Monthly</ng-template>
                  <ng-template #Yearly>Yearly</ng-template>
                  <div *ngIf="plan.id.includes('M');" [ngClass]="{'selected-plan': selectedPlan == plan.id}"
                    class="cycle_price">${{plan.price}}<span
                      [ngClass]="{'selected-plan': selectedPlan == plan.id}">/mo</span></div>
                  <div *ngIf="plan.id.includes('Y');" [ngClass]="{'selected-plan': selectedPlan == plan.id}"
                    class="cycle_price">${{plan.price}}<span
                      [ngClass]="{'selected-plan': selectedPlan == plan.id}">/year</span></div>
                  <span class="cycle_upgrade cursor-pointer" *ngIf="selectedPlan != plan.id"
                    (click)="upgradeAccount(plan)">Upgrade</span>
                  <div class="cycle_upgrade " *ngIf="selectedPlan == plan.id">Your current plan</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade sign_modal" id="lockedFeatureModal" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
        <div class="modal_content">
          <div class=" new_account">
            <div class="title font-weight-title upddate-email">Locked Feature</div>
            <div class="upddate-email-border lock-border"></div>
            <p class="upddate-email-p-lock text-color">To unlock inter-brokerage event assignment, your brokerage must
              have a <b>premium</b> Open Houses Direct account.</p><br>
            <p class="upddate-email-p-lock text-color">Talk to your brokerage about upgrading their account to give you
              team access event management tools.</p>
          </div>
          <div class="form_group cursor-pointer">
            <input type="button" data-dismiss="modal" class="new_form" value="Close" />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="modal fade sign_modal" id="agentLockedModal" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
        <div class="modal_content">
          <div class=" new_account">
            <div class="title font-weight-title upddate-email">Restricted Feature</div>
            <div class="upddate-email-border lock-border"></div>
            <p class="upddate-email-p-lock text-color">This feature is only available to Premium accounts. </p>
            <p class="upddate-email-p-lock text-color">Learn more about the benefits of upgrading to a <b>premium</b>
              Open Houses Direct account.</p><br>
          </div>
          <div class="form_group cursor-pointer">
            <input (click)="onLocaModelClose()" type="button" data-dismiss="modal" class="new_form"
              value="Learn more" />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
