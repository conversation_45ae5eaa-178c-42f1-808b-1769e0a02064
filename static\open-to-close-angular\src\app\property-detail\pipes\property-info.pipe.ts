import { PipeTransform, Pi<PERSON> } from '@angular/core';
import { water } from '@app/property-detail/module/property.constants';
import { accessibility } from '@app/property-detail/module/property.constants';
import { security } from '@app/property-detail/module/property.constants';
import { parking } from '@app/property-detail/module/property.constants';
import { view_Types } from '@app/property-detail/module/property.constants';
import { otherBuildingCon } from '@app/property-detail/module/property.constants';

@Pipe({name: 'propertyInfo'})

export class PropertyInfoPipe implements PipeTransform {
    transform(value,type,args:string[]) : any {
        if(type == 'water'){
            for (let keyValue of water) {    
                for (let key in keyValue) {
                    if(key == value){
                        return keyValue[value]
                    }
                }
            }
        }
        else if(type == 'accessability'){
            for (let keyValue of accessibility) {    
                for (let key in keyValue) {
                    if(key == value){
                        return keyValue[value]
                    }
                }
            }
        }
        else if(type == 'security'){
            for (let keyValue of security) {    
                for (let key in keyValue) {
                    if(key == value){
                        return keyValue[value]
                    }
                }
            }
        }
        else if(type == 'parking'){
            for (let keyValue of parking) {    
                for (let key in keyValue) {
                    if(key == value){
                        return keyValue[value]
                    }
                }
            }
        }
        else if(type == 'viewType'){
            for (let keyValue of view_Types) {    
                for (let key in keyValue) {
                    if(key == value){
                        return keyValue[value]
                    }
                }
            }
        }
        else if(type == 'buildingConstruction'){
            for (let keyValue of otherBuildingCon) {    
                for (let key in keyValue) {
                    if(key == value){
                        return keyValue[value]
                    }
                }
            }
        }
    }
  }
