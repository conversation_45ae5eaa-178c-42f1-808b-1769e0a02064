import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FavoriteComponent } from '@app/favorite/component/favorite.component';
import { FavoriteListComponent } from '@app/favorite/component/favorite-list';
import { BaseModule } from '@app/base/modules/base.module';
import { FavoriteService } from '@app/favorite/service/favorite-service'

@NgModule({
  imports: [
    BaseModule,
    CommonModule
  ],
  declarations: [FavoriteComponent,FavoriteListComponent],
  providers: [FavoriteService]
})
export class FavoriteModule { }
