.search-agent-image{
    height: 130px !important;
    width: 130px !important;
    border-radius: 85px !important;
}

.title3{
    font-size: 12px;
    color: #8D8D8D;
    margin-top: 14px !important;
    padding-bottom: 25px !important;
}
.title2{
    font-size: 15px;
    color: #8D8D8D;
}
.notifi-time{
    float: right;
    margin-top: 23px;
    font-size: 13px;
    margin-right: 10px;
    color: #8d8d8df5;
    font-weight: bold;
}

img.noti_image.symbols-property-image.dis_inline {
    position: relative;
    top: 19px !important;
    margin-left: 5px !important;
}
.notifi-title{
    margin-left: 7px !important;
    color: #727272 !important;
    margin-left: 7px!important;
    min-height: 10%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-inline-box;
    line-height: 19px;
    max-width: 50%;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
.noti_text {
    font-size: 12px;
    color: #8D8D8D;
    margin-left: 82px !important;
    margin-top: -20px !important;
    margin-right: 15px;
}

.notification span.dark {
    min-height: 37px;
    font-size: 16px;
    color: #67676791 !important;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-height: 19px;
    max-height: 32px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.header-orange-dot{
    font-size: 11px !important;
    position: absolute;
    margin-left: 19px;
    margin-top: -9px;
    color: #f19632d8;
}
.noti-img-width{
    width: 66px !important;
    height: 64px !important;
}

img.noti_image.symbols-property-image.dis_inline {
    position: relative;
    top: 27px !important;
    margin-left: 11px;
}

.notification .title {
    font-size: 17px !important;
    color: #727272 !important;
    margin-bottom: 13px;
    margin-left: 25px;
    line-height: 10px;
    font-family: 'Source Sans Pro', sans-serif;
    font-weight: bold;
}
.from-md-10{
    margin-top: 10px !important;
}
.upgrade_agent_title.title2{
    padding: 20px 21px !important;
}
.upgrade_agent_title.title2 {
    font-size: 25px !important;
    background: #10B8A8 !important;
    color: #d9edf7 !important;
    font-weight: 600;
}
.upgrade-contain{
    position: absolute;
    margin: auto;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 30%;
    height: 80%;
}
.thankyou-p{
    color: #8D8D8D;
}
.upddate-email{
    width: 90%;
}
.upddate-email-border{
    border-bottom: 4px solid #3ab8a8;
    border-radius: 4px;
    margin-bottom: 15px;
}
.upddate-email-p{
    font-size: 16px;
    width: 90%;
    color: #8D8D8D;
    opacity: 0.95;
}
.upddate-email-p-lock{
    font-size: 16px;
    color: #8D8D8D;
    opacity: 0.95;
}
.lock-border{
    border-radius: 0px !important;
}
.margin-upgrade-button{
    margin-top: -15% !important;
}
.margin-upgrade-button-web{
    margin-top: -8% !important;
}

.estate-professional-text{
    font-size: 16px;
    color: #676767;
    margin-bottom: 15px;
    margin-top: 15px;
    font-weight: 600;
}

.registration-link{
    color: #10B8A8 !important;
    cursor: pointer !important;
    border-bottom: 1px solid #10B8A8;
}

.has-success .form-control-feedback {
    padding-top: 5px;
}

.has-error .form-control-feedback {
    padding-top: 5px;
}

.success-border-box{
    border-color: #3c763d;
}

.error-border-box{
    border-color: #a93442;
}
label.resend-email{
  margin-top: 16px;
  margin-left: 2px;
  float: right;
}


.dropdown > a:hover {
    background-color: #10B8A8;
    color: white;
}


.dropdown-toggle:active, .open .dropdown-toggle {
    background:#10B8A8 !important;
    color:white !important;
}
.registration-options {
  display: flex;
  justify-content: flex-start; /* Align to the left */
  margin-left: 0px;
}

.registration-option {
  display: flex;
  align-items: center; /* Align items properly */
  margin-left: 0px; /* Add spacing between options */
}

.registration-option input[type="radio"] {
  appearance: none; /* Remove default radio button style */
  margin: 0 6px 0 0; /* Adjust margin and space with label */
  width: 16px;
  height: 16px;
  border: 2px solid #10B8A8; /* Outer circle border */
  border-radius: 50%; /* Make it circular */
  outline: none;
  position: relative;
  cursor: pointer;
}

.registration-option input[type="radio"]::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px; /* Size of inner check circle */
  height: 8px;
  background-color: transparent; /* Default: transparent */
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: background-color 0.3s ease; /* Smooth transition */
}

.registration-option input[type="radio"]:checked::before {
  background-color: #10B8A8; /* Inner circle color when checked */
}
.labels{
  margin-bottom: 0px;
}
.registration-option label {
  font-size: 14px;
  color: #333;
  cursor: pointer;
  line-height: 1; /* Ensure consistent alignment */
}
  .checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    max-width: 100%;
    margin-top: 6px;
  }

  .custom-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  .custom-checkbox input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
  }

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border: 2px solid #ccc;
    border-radius: 3px;
    cursor: pointer;
  }

  .custom-checkbox input:checked + .checkmark {
    background-color: #10B8A8;
    border-color: #10B8A8;
  }

  .checkmark::after {
    content: "";
    position: absolute;
    display: none;
  }

  .custom-checkbox input:checked + .checkmark::after {
  display: block;
left: 4px;
    top: 0px;
    width: 5px;
    height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
