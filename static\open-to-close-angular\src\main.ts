import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import 'hammerjs';

import { RootModule } from '@root/modules/root.module';
import { environment } from './environments/environment';

if (environment.production) {
  enableProdMode();
}

// platformBrowserDynamic().bootstrapModule(RootModule).catch(err => console.log(err));

platformBrowserDynamic().bootstrapModule(RootModule)
  .catch(err => console.log(err));