import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ApiService } from '@app/base/services/api.service';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiResponse } from '@app/auth/models/api-response';
import { AddProperty,GetPropertyResponse} from '@app/myListings/models/add-property';
import { ResponseContentType } from '@angular/http';

@Injectable()
export class MyListingService {

  public baseservice:BaseComponent;
  public apiService:ApiService;
  
  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
   }

   addNewProperty(propertyDetails:AddProperty): Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addProperty'],propertyDetails);
    return this.apiService.apiCall(options);   
   }

   updateProperty(propertyDetails):Observable<GetPropertyResponse>{
     let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['property']['updateProperty'],propertyDetails);
     return this.apiService.apiCall(options);
   }

   getAllProperty(pageNumber):Observable<GetPropertyResponse[]>{
     let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['property']['getAllProperty']+"?page_no="+pageNumber,{});
     return this.apiService.apiCall(options);
   }

   getAllPropertyList(urlParams):Observable<GetPropertyResponse[]>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['property']['getAllProperty'],{}, urlParams.toString());
    return this.apiService.apiCall(options);
  }

  getAllPropertyListSearch(urlParams):Observable<GetPropertyResponse[]>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['getAllProperty'],urlParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

   getAllPropertyWithPagination(start,end):Observable<GetPropertyResponse[]>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['property']['getAllProperty']+'?'+start+'='+start+'&'+end+'='+end,{});
    return this.apiService.apiCall(options);
  }

   getProperty(id):Observable<any>{
     let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['property']['getProperty']+id,'');
     return this.apiService.apiCall(options);
   }

   addPropertyExternal(external):Observable<any>{
     let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addPropertyExternal'],external)
     return this.apiService.apiCall(options);
  }

  addPropertyBuildingInfo(building):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addPropertyBuilding'],building)
    return this.apiService.apiCall(options);
  }

  addPropertyListingById(urlParams):Observable<GetPropertyResponse[]>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addPropertyListingById'],urlParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  addPropertyLocation(location):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addPropertyLocation'],location)
    return this.apiService.apiCall(options);
  }

  addPropertyTaxExpsenses(taxExpsenses):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addPropertyTaxeExpsenses'],taxExpsenses)
    return this.apiService.apiCall(options);
  }

  addPropertyImage(formData):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['propertyImages'],formData, null, null, null, true);
    return this.apiService.apiCall(options);
  }
  addPropertyToFavorite(property):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['saveToFavorite'],property.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
  addPropertyEvent(eventUrlParams):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addPropertyEvent'],eventUrlParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  contactListingAgent(message):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['contactListingAgent'],message.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  getPropertyOverview(urlParams):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['property']['getPropertyOverview'],{}, urlParams.toString());
    return this.apiService.apiCall(options);
  }

  getPropertyEvents(event):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['getPropertyEvent'],event.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  runEvent(event):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['runEvent'],event.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  searchCity(cityName):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['searchCity'],{},cityName.toString());
    return this.apiService.apiCall(options);
  }

  searchZipCode(zipCode):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['searchZipCode'],{},zipCode.toString());
    return this.apiService.apiCall(options);
  }

  getClientListForShareProperty():Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['myClient']['getClientListSP'],{});
    return this.apiService.apiCall(options);
  }

  shareProperty(sharePropertyParms):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['shareProperty'],sharePropertyParms.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  getPropertyOverViewPdf(property):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['oveviewPdf'],property.toString(), null,ResponseContentType.Blob, null, false, true);
    return this.apiService.downloadFile(options);
  }

  removeListing(sharePropertyParms):Observable<any>{    
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['removeListing'],sharePropertyParms.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
}