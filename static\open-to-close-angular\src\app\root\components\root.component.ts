import { Component,OnInit } from '@angular/core';
import { MessagingService } from '@app/messaging.service';
import * as firebase from 'firebase';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
import { Observable } from 'rxjs/Rx';
import { ChatService } from '@app/messaging/service/chat-service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { EventManagerService } from '@app/event-manager/services/event-manager.service';
import { BaseComponent } from '@app/base/components/base.component';
import { NotificationService } from '@app/notification/service/notification-service';
import { Notification } from 'rxjs/Notification';
import { environment } from "../../../environments/environment";


@Component({
  selector: 'app-root',
  templateUrl: '../views/root.component.html',
  styleUrls: ['../css/root.component.css']
})

export class RootComponent implements OnInit{

  public message:any;
  messaging = firebase.messaging();
  currentMessage = new BehaviorSubject(null);
  chatService : ChatService;
  eventMangerService : EventManagerService;
  notificationService : NotificationService;

  constructor(private msgService: MessagingService) {
    this.chatService =ServiceLocator.injector.get(ChatService);
    this.eventMangerService =ServiceLocator.injector.get(EventManagerService);
    this.notificationService =ServiceLocator.injector.get(NotificationService);
  }

  ngOnInit() {
    let self = this;
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register(environment.firebaseServiceWorkerPath).then(function(registration) {
          // Registration was successful

          self.msgService.getPermission();
          self.msgService.messaging.onMessage(function(payload){
            self.notificationService.setNotificatioDotIcon.emit(true);
            if(payload['data']['notification_type'] == 'event_run_tool'){
              self.eventMangerService.updateRunningEvent.emit(payload);
            }
            else if(payload['data']['notification_type'] == 'chat'){
              self.notificationService.setMessageDotIcon.emit(true);

              if(BaseComponent.baseselectedHeader == 'messaging'){
                self.chatService.onFirebase.emit(payload);
              }
              else if(BaseComponent.baseselectedHeader == 'run-event-manager'){
                self.chatService.onRunToolFirebase.emit(payload);
              }
            }
          });
        },function(err) {
          // registration failed :(
          console.log('SW reg failed: ', err);
        });
      });
    }
  }
}
