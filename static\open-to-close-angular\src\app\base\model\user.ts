export class User{
    id:any;
    name:String;
    email:String;
    user_type:String;
    profile_photo:String;
    is_paid_account: boolean;
    is_broker_paid_account: boolean;
    is_connected_with_broker: boolean;
    is_open_registration: boolean;
    is_agent_have_broker: boolean;
    is_broker_has_updated: boolean;
    is_permanent_premium: boolean;
}

export class UserDetailResponse{
    message:String;
    status:any;
    result:User;
    statusCode:any;
}

export class SignUpResponse{
    message:String;
    status:any;
    result:Result;
    statusCode:any;
}

class Result{
    mls_verification: VerificationResponse;
    email_verification: VerificationResponse;
    id: number;
    email: string;
    is_guest: boolean;
    mortgage_rate: boolean;
}

class VerificationResponse{
    msg: string;
    status: boolean;
}