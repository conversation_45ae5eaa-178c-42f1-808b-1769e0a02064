import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ApiService } from '@app/base/services/api.service';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiResponse } from '@app/auth/models/api-response';

@Injectable()
export class MyClientService {

  public baseservice:BaseComponent;
  public apiService:ApiService;
  public clientObject;
 
  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
   }

   public getClients(clientUrlParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['myClient']['getMyClients'],clientUrlParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public getClientActivity(params):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['myClient']['getClientActivity'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public addNewNote(params):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['myClient']['addNewNote'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public updateNote(params):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['myClient']['updateNote'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
  
  public deleteNote(params):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['DELETE'],API_REFERENCE['myClient']['deleteNote'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public getSingleClient(clientParas):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['myClient']['getSingleClientInfo'], {}, clientParas.toString());
    return this.apiService.apiCall(options);
  }

  public searchClient(clientNameParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['myClient']['searchClients'], {}, clientNameParams.toString());
    return this.apiService.apiCall(options);
  }

  public exportClientCSV():Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['myClient']['exportCSV'],null, null, null, null, false, true);
    return this.apiService.downloadFile(options);
  }

  public getInviteLink():Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['myClient']['getInviteLink'], {});
    return this.apiService.apiCall(options);
  }

  public setClientObj(clientDetail){
    this.clientObject = clientDetail;
  }

  public getClientObj(){
    return this.clientObject;
  }
}