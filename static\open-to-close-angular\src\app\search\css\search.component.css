#map {
    height: calc(100vh - 208px);
    width: 100%;
}
#markerLayer img {
    border: 2px solid red !important;
    width: 85% !important;
    height: 90% !important;
    border-radius: 5px;
  }
  
  #mypopup {
    display:none;
}

.search-body{
    overflow: hidden;
}
.search-count{
    float: right;
    margin-top: 5px;
    margin-right: 5px;
    color: #8D8D8D;
}

#legend {
    font-family: Arial, sans-serif;
    background: #fff;
    padding: 10px;
    margin: 10px;
    border-radius: 4px;
    opacity: 0.85;
}
#legend h3 {
    margin-top: 0;
}
#legend img {
    vertical-align: middle;
}
.legend-span{
    margin-left: 10px;
}
.legend-text{
    margin-top: 0px;
    font-weight: 600;
    margin-left:4px;
}
.brokerImageDiv {
    position: absolute;
    top: 0px;
    z-index: 1;
    margin: 10px;
}
.brokerImage{
    width: 100px;  
    -webkit-filter: drop-shadow(1px 1px 0 white)
    drop-shadow(-1px -1px 0 white);
    filter: drop-shadow(1px 1px 0 white) 
    drop-shadow(-1px -1px 0 white);
}

