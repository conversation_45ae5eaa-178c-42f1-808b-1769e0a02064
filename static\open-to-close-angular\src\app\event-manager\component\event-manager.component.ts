import { Component, OnInit, ViewChild,<PERSON><PERSON><PERSON> } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { MapInfoBubbleService } from '@app/base/services/map-info-bubble.service';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { EventManagerService } from '@app/event-manager/services/event-manager.service';
import { FavoriteService } from '@app/favorite/service/favorite-service';
import { SearchService } from '@app/search/service/search.service';
import { SearchBarComponent } from '@app/searchBar/component/search-bar.component';
import * as moment from 'moment';

declare var google;
declare var $;
declare var MarkerClusterer;

@Component({
  selector: 'event-search',
  templateUrl: '../views/event-manager.component.html',
  styleUrls: ['../css/event-manager.component.css']
})
export class EventManaegerComponent extends BaseComponent implements OnInit {

  @ViewChild(EventModalComponent) eventModal: EventModalComponent;
  @ViewChild(SearchBarComponent) searchBarComponent: SearchBarComponent;

  markerSet =[];
  public mapPolygons: any;
  public map;
  public poly;
  public bermudaTriangle = [];
  public freeHandPolygons = [];
  public matchMarker = [];
  public markers = [];
  public lat=40.730610;
  public lng=-73.935242;
  public currentLatLng;
  public infoBubble: any;
  infoBubbleService : MapInfoBubbleService;
  public positionStatus:Boolean =true;
  public geolocationPosition;
  houseImage = this.imagePrefix+ "symbols-map-hover.png";
  html:any = "";
  pageCount: number = 1;
  public totalPorpertyCount;
  public lengthOfList = 0;
  public currentEventType = "UP";
  public itemsPerPage:any;
  public markerCluster;
  currentEvent: any;
  public searchListType = 5;
  public searchPageNo = 1;
  public selectedSDate = '';
  public selectedEDate = '';
  public propertyFilterSubscription: any;


  upcomingEventList: any[] = [];
  availableEventList: any[] = [];
  requestsEventList: any[] = [];
  pastEventList: any[] = [];
  customEventList: any[] = [];
  currentEventList: any[] = [];

  eventMangerService: EventManagerService;
  favoriteService :FavoriteService;
  searchService : SearchService;
  eventTypeParams = new URLSearchParams();

  public autoMapPosition : Boolean = true;
  public showCancelDraw: Boolean = false;
  public showMapLoading: Boolean = false;
  public addLoaderClass: Boolean = true;
  public allowAddGeoJson : Boolean = true;
  public mobileReZoom : Boolean = true;
  public isMobileListView  : Boolean = true;

  public eventManagerMapGeoJson;
  public eventManagerViewPort;

  public eventSearchSubscription;

  eventType = ['Upcoming','Available','Requests','Past Events','Custom'];
  public selectedEventType = 'Upcoming';

  constructor(public zone: NgZone) {
    super();
    this.infoBubbleService = ServiceLocator.injector.get(MapInfoBubbleService);
    this.eventMangerService = ServiceLocator.injector.get(EventManagerService);
    this.favoriteService = ServiceLocator.injector.get(FavoriteService);
    this.searchService = ServiceLocator.injector.get(SearchService);
  }

  ngOnInit() {

    $('#lockedFeatureModal').on('hidden.bs.modal', function () {
      self.zone.run(() => {
        self.selectedEventType = 'Upcoming';
        self.currentEventType = 'UP';
        self.searchBarComponent.eventType = 'UP';
        self.map.setOptions({draggable: true, zoomControl: false, scrollwheel: true, disableDoubleClickZoom: false});
      })
    });
    this.searchBarComponent.showopenHouseFilter = false;
    this.searchBarComponent.mapListView = true;
    var self = this;
    this.searchBarComponent.openSearch = true;
    if(this.getPreviousScreen() != '/event-manager'){
      this.clearLocalStorageSearch();
    }
    if(this.searchBarComponent.selectedOHAId == ''){
      this.searchBarComponent.selectedOHAId = null;
    }

    if($(window).width() < 767){
      this.searchBarComponent.mapListView = false;
    }

    this.setPreviousScreen('/event-manager');

    this.searchBarComponent.searchFrom = 'eventManager';
    this.initMap();

    if(localStorage.getItem('recentSearches') == null){
      // this.setEventManagerType();
      this.searchBarComponent.eventType = 'UP';
    }

    $(document).ready(function(){
      $(this).scrollTop(0);
    });

    $(".show_map").click(function(){
      $(".display_none_map").addClass("show_map_mobile");
      $(".map_side_bar").addClass("hide_map_mobile");
    });

    $(".show_list").click(function(){
      $(".map_side_bar").removeClass("hide_map_mobile");
      $(".display_none_map").removeClass("show_map_mobile");
      self.zone.run(() => {
        self.isMobileListView = true;
        self.searchBarComponent.mapListView = false;
      });
    });
  }

  initMap(){

    $(document).ready(function () {
      $("#showDatePicker").hide();
      $('#datePickerCustom').daterangepicker({
        "opens": "left",
        autoUpdateInput: false
        }, function(start, end, label) {

          self.searchBarComponent.searchProperty['local_event_start_date'] = start.format('YYYY-MM-DD');
          self.searchBarComponent.searchProperty['local_event_end_date'] = end.format('YYYY-MM-DD');

          self.eventTypeParams.set('event_start_date',self.getLocalToUtcDate(start.format('YYYY-MM-DD')));
          self.eventTypeParams.set('event_end_date',self.getLocalToUtcDate(end.format('YYYY-MM-DD')));

          self.searchBarComponent.searchProperty['event_start_date'] = self.getLocalToUtcDate(start.format('YYYY-MM-DD'));
          self.searchBarComponent.searchProperty['event_end_date'] = self.getLocalToUtcDate(end.format('YYYY-MM-DD'));
          self.selectedSDate = self.getLocalToUtcDate(start.format('YYYY-MM-DD'))
          self.selectedEDate = self.getLocalToUtcDate(end.format('YYYY-MM-DD'));
          $('#datePickerCustom').val(start.format('MM/DD/YYYY')+' - '+end.format('MM/DD/YYYY'));
          self.searchBarComponent.filterProperty();
          // self.getEventManagerList(self.eventTypeParams, "CU");
        });
    });

    if(localStorage.getItem('recentSearches')){
      this.searchBarComponent.allowMapIdle = false;
      this.autoMapPosition = false;
    }
    if(BaseComponent.currentUserLatitude != undefined && BaseComponent.currentUserLongitude != undefined){
      this.autoMapPosition = false;
      this.currentLatLng = new google.maps.LatLng(BaseComponent.currentUserLatitude,BaseComponent.currentUserLongitude);
    }
    else{
      this.currentLatLng = new google.maps.LatLng(this.lat,this.lng);
    }
    var mapOptions = {
      zoom:8,
      center: this.currentLatLng,
      zoomControl: false,
      streetViewControl: false,
      fullscreenControl: false,
    };

    var marker = new google.maps.Marker({
      position: this.currentLatLng,
      map: this.map,
      optimized:false
    });
    this.map = new google.maps.Map(document.getElementById('map'),mapOptions);

    if(localStorage.getItem('recentSearches')){
      this.setPreviousMapPositon();
    }

    this.map.addListener('idle', function() {
    var lat0 = self.map.getBounds().getNorthEast().lat();
    var lng0 = self.map.getBounds().getNorthEast().lng();
    var lat1 = self.map.getBounds().getSouthWest().lat();
    var lng1 = self.map.getBounds().getSouthWest().lng();
    self.eventManagerViewPort ={
      "location": {
        "top_right": {"lat":lat0,"lon": lng0},
        "bottom_left": {"lat": lat1,"lon": lng1}
      }
    };

    var southWest = new google.maps.LatLng(lat1,lng1);
    var northEast = new google.maps.LatLng(lat0,lng0);
    var bounds = new google.maps.LatLngBounds(southWest,northEast);
    self.searchBarComponent.searchProperty['gmap_bounds'] = bounds;

    var idleLatLng = {
      "lat" : self.map.data.map.center.lat(),
      "lng" : self.map.data.map.center.lng()
    }
    self.searchBarComponent.searchProperty['idleLatLng'] = idleLatLng;

    if($(window).width() < 767 && self.searchBarComponent.mapListView == false){
      if(localStorage.getItem('recentSearches') == null){
        self.searchBarComponent.searchProperty['geo_bounding_box'] = "{}";
      }
      else{
        let recentSearches = JSON.parse(localStorage.getItem('recentSearches'));
        self.searchBarComponent.searchProperty['geo_bounding_box'] = recentSearches[0]['geo_bounding_box'];
      }
      self.searchBarComponent.searchProperty['is_map_list'] = false;
      self.searchBarComponent.mapListView = false;
    }
    else{
      self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.eventManagerViewPort);

      if($(window).width() < 767 && self.isMobileListView == true){
        self.searchBarComponent.searchProperty['geo_bounding_box'] = "{}";
      }
      self.searchBarComponent.searchProperty['is_map_list'] = true;
      self.searchBarComponent.mapListView = true;
    }

    self.searchBarComponent.searchProperty['request_type'] = 'WEB'
    self.searchBarComponent.pageNo = 0;

    self.zone.run(() => {
      self.showMapLoading = true;
    });
    self.disable();

    if(self.searchBarComponent.allowMapIdle == true){
      self.searchBarComponent.filterProperty();
      localStorage.setItem('zoomLevel',self.map.getZoom());
      self.searchBarComponent.allowMapIdle = false;
    }
    if(self.infoBubble.isOpen() == true){
      self.infoBubble.close();
    }
    });

    var self=this;
    if(navigator.geolocation){
      /*
        * @Desc: Find current position
        * @Param:
        * @return:display infowindow on map with given string
        *
      */

    navigator.geolocation.getCurrentPosition((position)=>{
      var currentPosition
      BaseComponent.currentUserLatitude = position.coords.latitude;
      BaseComponent.currentUserLongitude = position.coords.longitude;
        currentPosition= {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        self.geolocationPosition=currentPosition;

        if(this.positionStatus == true && this.autoMapPosition == true){
          self.map.setCenter(currentPosition);
        }
      },
      ()=>{
        this.handleLocationError(true,this.map.getCenter());
      });

    }
    else{
        this.handleLocationError(false,this.map.getCenter());
    }


    var self=this;
      $("#draw a").click((e)=>{
        if(self.showMapLoading == false){
        /*
          * @Desc: Allow to draw polygon
          * @Param:
          * @return:
          *
        */
        this.clearGoogleMap(true);
        self.showCancelDraw = true;
        delete self.searchBarComponent.searchProperty['polygon'];
        self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.eventManagerViewPort);
        self.searchBarComponent.searchProperty['is_map_list'] = true;
        self.searchBarComponent.searchProperty['request_type'] = 'WEB'

        self.map.setOptions({ draggableCursor:'default'});
        e.preventDefault();
        self.disable();
        google.maps.event.addDomListener(self.map.getDiv(),'mousedown',(e)=>{
        self.drawFreeHand();
        });
      }
      else{
        e.preventDefault();
      }
    });

    $("#cancelDraw a").click((e)=>{
        self.showCancelDraw = false;
        self.clearGoogleMap(true);
        self.allowAddGeoJson = true;
        if(self.freeHandPolygons.length != 0){
          self.freeHandPolygons = [];
          delete this.searchBarComponent.searchProperty['polygon'];
          self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.eventManagerViewPort);
          self.searchBarComponent.searchProperty['is_map_list'] = true;
          self.searchBarComponent.searchProperty['request_type'] = 'WEB'
          self.searchBarComponent.filterProperty();
          self.zone.run(() => {
            self.showMapLoading = true;
          });
        }
        self.enable();
    });

    this.html = this.infoBubbleService.changeHTML("symbols-map-hover.png", "120000", "", "", "", "", {});
    this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);

    if($(window).width() < 767){
      google.maps.event.addListener(self.map,'click', function() {
        if(self.infoBubble.isOpen() == true){
          self.infoBubble.close();
          $("#property_info").remove();
        }
      });
      this.addLoaderClass = false;
      this.searchBarComponent.mapListView = false;
    }
    else{
      this.addLoaderClass = true;
      this.searchBarComponent.mapListView = true;
    }
  }

  clearGoogleMap(clearMarker){
    if(clearMarker == true){
      if(this.poly != undefined){
        this.poly.setMap(null);
      }
      if(this.mapPolygons != undefined){
        this.mapPolygons.setMap(null);
      }
      if(this.markerSet.length !=0){
        this.markerCluster.clearMarkers();
      }
      for (var i = 0; i < this.markers.length; i++) {
        this.markers[i].setMap(null);
      }
      this.markers = [];
      this.markerSet = [];
    }

    if(this.eventManagerMapGeoJson != undefined){
      for (var i = 0; i < this.eventManagerMapGeoJson.length; i++){
        this.map.data.remove(this.eventManagerMapGeoJson[i])
      }
    }
    // delete this.searchBarComponent.searchProperty['polygon'];
  }

  handleLocationError(browserHasGeolocation,pos){
    /*
      * @Desc:showing error message in infowindow if read location permission id block by user
      * @Param:
      * @return:error message.
      *
    */
  // infoWindow.setPosition(pos);
  // infoWindow.setContent(browserHasGeolocation ?'Error: The Geolocation service failed.' :'Error: Your browser doesn\'t support geolocation.');
  // infoWindow.open(this.gmap);
  }

  addMarkerCluster(){
    var cluster;
    let self = this;
    let j=1;
    var markers =this.markerSet.map(function(location, i){
      var markerColorType = 'no-event';
      var markerColor = "#fffffff7";
      if(location['first_event_type'] == ''){
        markerColorType = 'no-event';
        markerColor = "#fffffff7";
      }
      else{
        if(location['listing_status']=="PRE-MLS/Coming Soon"){
          if(location['first_event_type']=="OH"){
            markerColorType = "AO";
          }
          else if(location['first_event_type']=="BO"){
            markerColorType = "BO";
          }
          else if(location['first_event_type']=="AO"){
            markerColorType = "AO";
          }
          else if(location['first_event_type']==""){
            markerColorType = "no-event";
          }
          else {
            markerColorType = location['first_event_type'];
          }
        }
        else{
          if(location['first_event_type']=="AO"){
            markerColorType = "OH";
          }
          else if(location['first_event_type']=="OH"){
            markerColorType = "OH";
          }
          else{
            markerColorType = location['first_event_type'];
          }
        }
      }

      let iconImage = self.getIconImage(google, markerColorType)

      var priceLabel = self.priceFormat(self.markerSet[i]["home_price"]);
      cluster = new google.maps.Marker({
        position: {lat :self.markerSet[i]["latitude"],lng: self.markerSet[i]["longitude"]},
        // icon : self.imagePrefix+markerColorType+".png",
        icon: iconImage,
        map: self.map,
        label: {
          text: '$'+priceLabel,
          background: '#AD5FBF',
          color: markerColor,
          align: 'center',
          padding: '0',
          fontSize: "14px",
          fontWeight: "600"
        },
        title:self.markerSet[i]["street"],
        id:self.markerSet[i]["id"]
      });
      self.markers.push(cluster);
      //hide all markers when map load
      //this.setMapHideAll(i,this.markerSet[i]["id"] -1 ,null);
      if($(window).width() < 767){
        google.maps.event.addListener(cluster, 'click',((marker,event)=>{
          return function(){
            if(self.markerSet[i]['property_file'] == ''){
              var property = self.currentEventList['records'].filter((propertyId) => propertyId.property_id == self.markerSet[i]['id']);
              if(property.length !=0){
                if(self.markerSet[i] != undefined){
                  self.markerSet[i]['property_file'] = property[0]['property_file'];
                  self.openMobileEventPropertyInfoBubble(i,marker);
                }
              }else{
                let url = new URLSearchParams();
                url.set('property_id',self.markerSet[i]['id'].toString());
                self.propertyFilterSubscription = self.favoriteService.getPropertyFile(url).subscribe(res =>{
                  self.zone.run(()=>{
                    if(self.markerSet[i] != undefined){
                      self.markerSet[i]['property_file'] = res['result']['property_file'];
                      self.openMobileEventPropertyInfoBubble(i,marker);
                    }
                  });
                },err=>{
                  console.log(err.json())
                });
              }
            }
            else{
              self.openMobileEventPropertyInfoBubble(i,marker);
            }
          }
        })(cluster));
        return cluster;

      }else{
        google.maps.event.addListener(cluster, 'mouseover',((marker,event)=>{
          return function(){
            if(self.markerSet[i]['property_file'] == ''){
              var property = self.currentEventList['records'].filter((propertyId) => propertyId.property_id == self.markerSet[i]['id']);
              if(property.length !=0){
                if(self.markerSet[i] != undefined){
                  self.markerSet[i]['property_file'] = property[0]['property_file'];
                  self.openWebEventPropertyInfoBubble(i,marker);
                }
              }else{
                let url = new URLSearchParams();
                url.set('property_id',self.markerSet[i]['id'].toString());
                self.propertyFilterSubscription = self.favoriteService.getPropertyFile(url).subscribe(res =>{
                  self.zone.run(()=>{
                    if(self.markerSet[i] != undefined){
                      self.markerSet[i]['property_file'] = res['result']['property_file'];
                      self.openWebEventPropertyInfoBubble(i,marker)
                    }
                  });
                },err=>{
                  console.log(err.json())
                });
              }
            }
            else{
              self.openWebEventPropertyInfoBubble(i,marker);
            }
          }
        })(cluster));

       google.maps.event.addListener(cluster, 'mouseout',((marker,event)=>{
          return function(){
            self.infoBubble.close();
            $("div.box_on_map", self.infoBubble.bubble_).on("click",function(){
                self.gotToPropertyDetail('event-manager/property-detail',marker['id']);
            });
          }
        })(cluster));
        return cluster;
      }
    });

    this.markerCluster = new MarkerClusterer(this.map, markers,
      {
        maxZoom: 12,
        styles: this.mapMarkerCluster[0]
      });

      google.maps.event.addListener(this.markerCluster, 'clusterclick', function(clust) {
        self.mobileReZoom = false;
        if(self.eventSearchSubscription){
          self.searchBarComponent.allowMapIdle = true;
          self.eventSearchSubscription.unsubscribe();
        }
      });
  }

  openMobileEventPropertyInfoBubble(i,marker){
    let self = this;
    if(this.infoBubble.isOpen() == false){
      var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);
      this.html = this.infoBubbleService.changeHTML(this.markerSet[i]['property_file'], this.markerSet[i]['home_price'], this.markerSet[i]['bedroom'], this.markerSet[i]['full_bath'], this.markerSet[i]['living_area'],this.markerSet[i]['street'], this.markerSet[i]);
      if(pixelOffsetY != undefined && pixelOffsetY < 260){
        this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
      }else{
        this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
      }
      this.infoBubble.open(this.map, marker);
      setTimeout(() => {
        $(".box_on_map").parent().parent().parent().addClass('pop_div');
        $(".box_on_map").parent().parent().parent().attr('id','property_info');
        $('div.box_on_map', self.infoBubble.bubble_).on('click',function(){
          self.gotToPropertyDetail('event-manager/property-detail',marker['id']);
        });
      }, 20);
    }else{
      self.infoBubble.close();
      $("#property_info").remove();
      self.openMobileEventPropertyInfoBubble(i,marker);
    }
  }

  openWebEventPropertyInfoBubble(index,marker){
    if(this.infoBubble.isOpen() == true){
      this.infoBubble.close();
    }
    var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);
    this.html = this.infoBubbleService.changeHTML(this.markerSet[index]['property_file'], this.markerSet[index]['home_price'], this.markerSet[index]['bedroom'], this.markerSet[index]['full_bath'], this.markerSet[index]['living_area'],this.markerSet[index]['street'], this.markerSet[index]);
    if(pixelOffsetY != undefined && pixelOffsetY < 260){
      this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
    }else{
      this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
    }
    this.infoBubble.open(this.map, marker);
    setTimeout(() => {
      $(".box_on_map").parent().parent().parent().addClass('pop_div');
      $(".box_on_map").parent().parent().parent().attr('id','property_info');
    },20);
  }

  mapZoomOut(){
    if(this.showMapLoading == false){
      var getZoom = this.map.getZoom();
      this.map.setZoom(getZoom - 1);
    }
  }

  mapZoomIn(){
    if(this.showMapLoading == false){
      var getZoom = this.map.getZoom();
      this.map.setZoom(getZoom + 1);
    }
  }

  disable(){
    /*
      * @Desc:disable map controls.
    */
    this.map.setOptions({
      draggable: false,
      zoomControl: false,
      scrollwheel: false,
      disableDoubleClickZoom: false,
      clickable:false
    });
  }

  drawFreeHand(){
    if(this.showMapLoading == false){
    //the polygon
    var self=this;
    self.freeHandPolygons = [];
    this.poly=new google.maps.Polyline({map:this.map,clickable:false,strokeColor: "#10B8A8"});

    //move-listener
    var move=google.maps.event.addListener(this.map,'mousemove',(e)=>{
      self.poly.getPath().push(e.latLng);
    });

    //mouseup-listener
    google.maps.event.addListenerOnce(this.map,'mouseup',(e)=>{
      google.maps.event.removeListener(move);
      var path=self.poly.getPath();
      self.poly.setMap(null);
      self.poly=new google.maps.Polygon({map:self.map,path:path, strokeColor: "#10B8A8",
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: "#10B8A8",
      fillOpacity: 0
    });
      var len = path.getLength();
      var latlist = [];
      for (var i = 0; i < len; i++) {
        latlist.push("new google.maps.LatLng(" + path.getAt(i).toUrlValue(5) + "), ");
        self.freeHandPolygons.push({lat:path.getAt(i).lat(), lon:path.getAt(i).lng()});
      }

      this.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.eventManagerViewPort);
      if(self.freeHandPolygons.length == 0){
        delete self.searchBarComponent.searchProperty['polygon'];
      }else{
        this.searchBarComponent.searchProperty['polygon'] = JSON.stringify(self.freeHandPolygons);
      }
      this.searchBarComponent.searchProperty['is_map_list'] = true;
      this.searchBarComponent.searchProperty['request_type'] = 'WEB'
      this.searchBarComponent.filterProperty();

      self.zone.run(() => {
        self.showMapLoading = true;
      });
      self.disable();

      google.maps.event.clearListeners(self.map.getDiv(), 'mousedown');
      self.enable();

        setTimeout(function(){
          for (var j = 0; j <self.markerSet.length; j++){
            var currentMarkerPosition=new google.maps.LatLng(self.markerSet[j]["latitude"],self.markerSet[j]["longitude"]);
            var resultColor = google.maps.geometry.poly.containsLocation(currentMarkerPosition,self.poly)
              if(resultColor){
                 /*
                  * @desc:if marker available
                  */
                  self.matchMarker.push({lat:self.markerSet[j]["latitude"],lng:self.markerSet[j]["longitude"],id:self.markerSet[j]["property_id"]});

                  // self.setMapOnAll(j,self.markerSet[j]["id"] - 1,self.map);
              }
              else{
                self.setMapHideAll(j,self.markerSet[j]["property_id"],null);
              }
          }
          for(let i=0; i< self.matchMarker.length; i++){
            self.setMapOnAll(i,self.matchMarker[i]['id'],self.map);
          }
        });
      });
    }
  }

  enable(){
    /*
      * @Desc:enable map controls.
    */
    this.map.setOptions({
      draggable: true,
      zoomControl: false,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      clickable:false
    });
  }

  setMapHideAll(index,id,map){
    /*
      * @Desc: set markers hide in map
      * @Param:
                index:number
                map:current map
      * @return:
      *
    */
    let markerCluster = this.markerCluster.getMarkers().filter(marker => marker['id'] == id);
    this.markerCluster.removeMarker(markerCluster[0])
    let marker = this.markers.filter(marker => marker['id'] == id);
    marker[0].setMap(map);
    // this.markers[id].setMap(map)
  }

  setMapOnAll(index,id,map){
    /*
      * @Desc: Set markers show in map
      * @Param:
                index:number
                map:current map
      * @return:
      *
    */

    let marker = this.markers.filter(marker => marker['id'] == id);
    marker[0].setMap(map);
    // let markerCluster = this.markerCluster.getMarkers().filter(marker => marker['id'] == id);
    // this.markerCluster.setMap(map)
    // this.markers[id].setMap(map);
  }

  selectedEvent(type,event){
    this.eventModal.openEventModal(type, event,false);
  };

  setEventManagerType(){
    this.clearLocalStorageSearch();
    setTimeout(() => {
      this.searchBarComponent.filterProperty();
    }, 100);
  }

  getEventManagerList(eventTypeParams, type){
   this.eventSearchSubscription = this.searchService.getEventSearch(eventTypeParams).subscribe(res => {
      this.removeInfoBubble();
      this.searchBarComponent.allowMapIdle = true;
      if(type == "UP"){
        this.upcomingEventList = res['result'];
        this.currentEventList =  this.upcomingEventList;
        this.totalPorpertyCount = this.upcomingEventList['total_records_count']
        this.lengthOfList = this.upcomingEventList['records'].length;
        this.itemsPerPage = res['result']['items_per_page'];
      }else if(type == "AV"){
        this.availableEventList = res['result'];
        this.currentEventList =  this.availableEventList;
        this.totalPorpertyCount = this.availableEventList['total_records_count'];
        this.lengthOfList = this.availableEventList['records'].length;
        this.itemsPerPage = res['result']['items_per_page'];
        // this.deleteMarkers();
      }else if(type == "RE"){
        this.requestsEventList = res['result'];
        this.currentEventList =  this.requestsEventList;
        this.totalPorpertyCount = this.requestsEventList['total_records_count'];
        this.lengthOfList = this.requestsEventList['records'].length;
        this.itemsPerPage = res['result']['items_per_page'];
        // this.deleteMarkers();
      }else if(type == "PA"){
        this.pastEventList = res['result'];
        this.currentEventList =  this.pastEventList;
        this.totalPorpertyCount = this.pastEventList['total_records_count'];
        this.lengthOfList = this.pastEventList['records'].length;
        this.itemsPerPage = res['result']['items_per_page'];
        // this.deleteMarkers();
      }
      else if(type == "CU"){
        this.customEventList = res['result'];
        this.currentEventList =  this.customEventList;
        this.totalPorpertyCount = this.customEventList['total_records_count'];
        this.lengthOfList = this.customEventList['records'].length;
        this.itemsPerPage = res['result']['items_per_page'];
        // this.deleteMarkers();
      }
      if(this.searchBarComponent.searchProperty['is_map_list'] == true || event['currentPageNumber'] == 0){
        this.deleteMarkers(res['result']['map_records_list']);
        this.zone.run(() => {
          this.showMapLoading = false;
        });
        this.map.setOptions({draggable: true, zoomControl: false, scrollwheel: true, disableDoubleClickZoom: false});
      }
      var setsScroll = document.getElementById('scroll');
      setsScroll.scrollTop = 0;
    }, err => {
      this.errorResponse(err.json());
    });
  }

  filterEventType(type){
    this.searchBarComponent.mapListView = true;
    $("#showDatePicker").hide();
    this.zone.run(() => {
      this.showMapLoading = true;
    });
    this.disable();
    this.searchBarComponent.searchProperty['event_view'] = "MV";
    this.pageCount = 1;
    this.searchBarComponent.pageNo = 1
    if(type == 'Upcoming'){
      this.currentEventType = "UP";
      this.searchBarComponent.eventType = this.currentEventType;
      this.searchBarComponent.searchProperty['type'] = "UP";
      this.searchBarComponent.filterProperty();
    }
    if(type == 'Available'){
      if(BaseComponent.user != undefined){
        if(BaseComponent.user.user_type == "LA"){
          // if(BaseComponent.user.is_paid_account && BaseComponent.user.is_connected_with_broker && BaseComponent.user.is_broker_paid_account){
          if(BaseComponent.user.is_paid_account && BaseComponent.user.is_connected_with_broker){  
            this.currentEventType = "AV";
            this.searchBarComponent.eventType = this.currentEventType;
            this.searchBarComponent.searchProperty['type'] = "AV";
            this.eventTypeParams.set("type", "AV");
            this.searchBarComponent.filterProperty();
          }
          else{
            this.zone.run(() => {
              this.showMapLoading = false;
            });
            $('#lockedFeatureModal').modal('show');
          }
        }
        else if(BaseComponent.user.user_type == "BR" && BaseComponent.user.is_paid_account){
          this.currentEventType = "AV";
          this.searchBarComponent.eventType = this.currentEventType;
          this.searchBarComponent.searchProperty['type'] = "AV";
          this.eventTypeParams.set("type", "AV");
          this.searchBarComponent.filterProperty();
        }
      }
      // this.getEventManagerList(this.eventTypeParams, "AV");
    }
    if(type == 'Requests'){
      if(BaseComponent.user != undefined){
        if(BaseComponent.user.user_type == "LA"){
          // if(BaseComponent.user.is_paid_account && BaseComponent.user.is_connected_with_broker && BaseComponent.user.is_broker_paid_account){
          if(BaseComponent.user.is_paid_account && BaseComponent.user.is_connected_with_broker){ 
            this.currentEventType = "RE";
            this.searchBarComponent.eventType = this.currentEventType;
            this.searchBarComponent.searchProperty['type'] = "RE";
            this.searchBarComponent.filterProperty();
          }
          else{
            this.zone.run(() => {
              this.showMapLoading = false;
            });
            $('#lockedFeatureModal').modal('show');
          }
        }
        else if(BaseComponent.user.user_type == "BR" && BaseComponent.user.is_paid_account){
          this.currentEventType = "RE";
          this.searchBarComponent.eventType = this.currentEventType;
          this.searchBarComponent.searchProperty['type'] = "RE";
          this.searchBarComponent.filterProperty();
        }
      }
      // this.getEventManagerList(this.eventTypeParams, "RE");
    }
    if(type == 'Past Events'){
      this.currentEventType = "PA";
      this.searchBarComponent.eventType = this.currentEventType;
      this.searchBarComponent.searchProperty['type'] = "PA";
      this.searchBarComponent.filterProperty();
      // this.getEventManagerList(this.eventTypeParams, "PA");
    }
    if(type == 'Custom'){
      this.currentEventType = "CU";
      this.searchBarComponent.eventType = 'CU';
      this.searchBarComponent.searchProperty['type'] = "CU";

      let self = this;
      $("#showDatePicker").show();
      if(this.selectedSDate == '' && this.selectedEDate == ''){
        $(document).ready(function(){
          $("#datePickerCustom").trigger("click");
        });
      }else{
        self.searchBarComponent.searchProperty['event_start_date'] = this.selectedSDate;
        self.searchBarComponent.searchProperty['event_end_date'] = this.selectedEDate;
        self.searchBarComponent.filterProperty();
        // self.getEventManagerList(self.eventTypeParams, "CU");
      }
    }
  }

  getpage(pageNumber :number){
    let eventTypeParams = new URLSearchParams();
    this.pageCount = pageNumber;
    this.searchBarComponent.searchProperty['is_map_list'] = false;
    if(this.currentEventType == 'CU'){
      this.searchBarComponent.openSearch = true;
      this.searchBarComponent.searchProperty['event_start_date'] = this.selectedSDate;
      this.searchBarComponent.searchProperty['event_end_date'] = this.selectedEDate;
    }
    this.eventTypeParams.set("page_no", pageNumber.toString());
    this.eventTypeParams.set("type",this.currentEventType);
    this.searchBarComponent.pageNo =pageNumber;
    this.searchBarComponent.eventType = this.currentEventType;
    if(pageNumber != 1){
      this.searchBarComponent.mapListView = false;
      this.searchBarComponent.searchProperty['is_map_list'] = false;
    }
    else{
      this.searchBarComponent.mapListView = true;
    }
    this.searchBarComponent.filterProperty();
    var setScroll = document.getElementById('scroll');
    setScroll.scrollTop = 0;
  }

  addToFavorite(id,item){
    let index = this.currentEventList['records'].indexOf(item);
    this.currentEventList['records'][index]['is_favourite'] = this.favoriteService.setFavourite(!this.currentEventList['records'][index]['is_favourite'], id, this.currentEventList['records'], index);
  }

  propertyOnMap(property){
    for(let i=0;i<property.length;i++){
      if(property[i]['latitude'] != 0 && property[i]['longitude'] !=0){
        property[i]['property_file'] = ''
        this.markerSet.push(property[i]);
      }
    }
    this.addMarkerCluster();
  }

  removeInfoBubble(){
    var infowin = document.getElementsByClassName("pop_div");
    for(var i=0;i<infowin.length;i++)
    {
      infowin[i].innerHTML = ''
      $("div.pop_div").remove();
    }
  }


  deleteMarkers(property){
    if(this.markerSet.length !=0){
      this.markerCluster.clearMarkers();
    }
    for(var i = 0; i < this.markers.length; i++){
        this.markers[i].setMap(null);
    }
    this.markers = [];
    this.markerSet = [];
    this.propertyOnMap(property);
  };

  getSearchObj(event){
    this.zone.run(
    () => {
      this.eventTypeParams = event;
      this.eventTypeParams.set('event_view','MV');
      this.eventTypeParams.set('type', this.currentEventType);
      this.getEventManagerList(this.eventTypeParams,this.currentEventType);
      this.pageCount = this.searchBarComponent.pageNo;
    });
  }

  isIntrested(value){
    var property = this.currentEventList['records'].filter((propertyId) => propertyId.property_id == value['propertyId']);
    var propertyIndex = this.currentEventList['records'].indexOf(property[0]);
    if(property.length !=0){
      var event = property[0]['events'].filter((eventId) => eventId.id == value['eventId']);
      var eventIndex = this.currentEventList['records'][propertyIndex]['events'].indexOf(event[0]);
      if(event.length !=0){
        this.currentEventList['records'][propertyIndex]['events'][eventIndex]['is_interested'] = value['isIntrested'];
      }
    }
  }

  UpdatePropertyInfo(propertyInfo){
    var property = this.currentEventList['records'].filter((propertyId) => propertyId.property_id == propertyInfo['property']);
    var propertyIndex = this.currentEventList['records'].indexOf(property[0]);
    if(property.length !=0){
      let updatedPropertyParams = new URLSearchParams();
      updatedPropertyParams.set('property_id',propertyInfo['property']);
      updatedPropertyParams.set('list_type','4');
      updatedPropertyParams.set('type',this.currentEventType);
      updatedPropertyParams.set('open_house_request_id',propertyInfo['openHouseAgent']);

      for(let i=0;i<this.searchBarComponent.eventTypeList.length;i++){
        updatedPropertyParams.set('event_type['+[i]+']',this.searchBarComponent.eventTypeList[i]);
      }

      this.favoriteService.getUpdatedPropertyInfo(updatedPropertyParams).subscribe(res =>{
        this.currentEventList['records'][propertyIndex] = res['result'];
      },err=>{
        this.errorResponse(err.json());
      });
    }
  }

  setMapPosition(position){
    this.searchBarComponent.allowMapIdle = false;
    if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
      this.map.setCenter(new google.maps.LatLng(position['lat'],position['lng']));
      this.currentLatLng = new google.maps.LatLng(position['lat'],position['lng']);
      this.autoMapPosition = false;
    }
    this.autoMapPosition = false;
    this.allowAddGeoJson = true;
  }


  drawPolygons(polygonObj){
    this.clearGoogleMap(false);
    this.mobileReZoom = true;
    if(polygonObj['isError'] == true){
      if(this.mapPolygons != undefined){
        this.mapPolygons.setMap(null);
      }
    }
    else{
      if(this.eventManagerMapGeoJson != undefined){
        for (var i = 0; i < this.eventManagerMapGeoJson.length; i++){
          this.map.data.remove(this.eventManagerMapGeoJson[i])
        }
      }

      if(this.allowAddGeoJson == true){
        this.eventManagerMapGeoJson = this.map.data.addGeoJson(polygonObj);
        this.map.data.setStyle({
          strokeColor: "#10B8A8",
          strokeWeight: 2,
          strokeOpacity: 0.8,
          fillColor: "#10B8A8",
          fillOpacity: 0
        });
      }
      if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
        this.zoom(this.map);
        delete this.searchBarComponent.searchProperty['polygon'];
        if(this.poly != undefined){
          this.poly.setMap(null);
        }
        if(this.mapPolygons != undefined){
          this.mapPolygons.setMap(null);
        }
        this.showCancelDraw = false;
      }
      this.searchBarComponent.allowMapIdle = true;
    }
  }

  setPreviousMapPositon(){
    var bound = new google.maps.LatLngBounds();
    var filterList = JSON.parse(localStorage.getItem("recentSearches"))[0];
    if(filterList['type'] == 'UP'){
      this.selectedEventType = 'Upcoming';
      this.currentEventType = 'UP';
      this.searchBarComponent.eventType = 'UP';
    }
    else if(filterList['type'] == 'AV'){
      this.selectedEventType = 'Available';
      this.currentEventType = 'AV';
      this.searchBarComponent.eventType = 'AV';
    }
    else if(filterList['type'] == 'RE'){
      this.selectedEventType = 'Requests';
      this.currentEventType = 'RE';
      this.searchBarComponent.eventType = 'RE';
    }
    else if(filterList['type'] == 'PA'){
      this.selectedEventType = 'Past Events';
      this.currentEventType = 'PA';
      this.searchBarComponent.eventType = 'PA';
    }
    else if(filterList['type'] == 'CU'){
      this.selectedEventType = 'Custom';
      this.currentEventType = 'CU';
      this.searchBarComponent.eventType = 'CU';

      let intStartDate = new Date(filterList['local_event_start_date']);
      let inteEndDate = new Date(filterList['local_event_end_date']);

      this.eventTypeParams.set('event_start_date',filterList['event_start_date']);
      this.eventTypeParams.set('event_end_date',filterList['event_end_date']);
      var startDate = moment(intStartDate).format('MM/DD/YYYY');
      var endDate = moment(inteEndDate).format('MM/DD/YYYY');

      setTimeout(() => {
        $("#showDatePicker").show();
        this.zone.run(()=>{
        if($("#datePickerCustom").data('daterangepicker') != undefined){
          $('#datePickerCustom').val(startDate+' - '+endDate);
          $("#datePickerCustom").data('daterangepicker').setStartDate(startDate);
          $("#datePickerCustom").data('daterangepicker').setEndDate(endDate);
        }
        });
      },200);
    }

    if(JSON.parse(localStorage.getItem("recentSearches"))[0]['polygon'] != undefined){
      this.drawCustomPolyline(JSON.parse(localStorage.getItem("recentSearches"))[0]['polygon']);
      this.showCancelDraw = true;
      this.allowAddGeoJson = false;
      this.searchBarComponent.allowCallGetMapPolygons = false;
    }
    if(JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'] != undefined){
      let bounds = JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'];
      var center = new google.maps.LatLng(bounds['lat'],bounds['lng'])
      this.map.setCenter(new google.maps.LatLng(bounds['lat'],bounds['lng']));
      if(localStorage.getItem('zoomLevel')){
        this.map.setZoom(parseInt(localStorage.getItem('zoomLevel')));
      }
      else{
        this.map.setZoom(8)
      }
      localStorage.setItem('boundryZoom','false');
      if($(window).width() < 767){
        this.searchBarComponent.mapListView = false;
      }
      else{
        this.searchBarComponent.mapListView = true;
      }
      this.searchBarComponent.allowLocalStorageSearch();
      // this.searchBarComponent.allowMapIdle = true;
    }
  }

  drawCustomPolyline(polygonsList){
    var polygons = [];
    if(polygonsList.length != 0){
      for(let i=0;i<JSON.parse(polygonsList).length;i++){
        let obj = {lng: JSON.parse(polygonsList)[i]['lon'], lat: JSON.parse(polygonsList)[i]['lat']}
        polygons.push(obj);
      }
    }

    if(polygons.length != 0){
      this.freeHandPolygons = polygons;
      this.mapPolygons = new google.maps.Polygon({
          paths: polygons,
          strokeColor: "#10B8A8",
          strokeOpacity: 0.8,
          strokeWeight: 3,
          fillOpacity: 0
      });
      const bounds = new google.maps.LatLngBounds();
      for (var i=0; i<this.mapPolygons.getPath().length; i++) {
        var point = new google.maps.LatLng(polygons[i]['lat'], polygons[i]['lng']);
        bounds.extend(point);
      }
      this.mapPolygons.setMap(this.map);
    }
  }

  zoom(map) {
    var self = this;
    var bounds = new google.maps.LatLngBounds();
    map.data.forEach(function(feature) {
      self.processPoints(feature.getGeometry(), bounds.extend, bounds);
    });
    map.fitBounds(bounds);
  }

   processPoints(geometry, callback, thisArg) {
    var self = this;
    if(geometry instanceof google.maps.LatLng) {
      callback.call(thisArg, geometry);
    }else if (geometry instanceof google.maps.Data.Point) {
      callback.call(thisArg, geometry.get());
    }else{
      geometry.getArray().forEach(function(g) {
        self.processPoints(g, callback, thisArg);
      });
    }
  }

  getCurrentLocation(){
    if(this.showMapLoading == false){
      if(this.geolocationPosition != undefined && this.geolocationPosition != null){
        this.map.setCenter(this.geolocationPosition);
        this.map.setZoom(8);
      }
    }
  }

  showMap(){
    $(".display_none_map").addClass("show_map_mobile");
    $(".map_side_bar").addClass("hide_map_mobile");
    this.isMobileListView = false;

    if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
      if(this.mobileReZoom == true){
        this.mobileReZoom = false;
        if(this.eventManagerMapGeoJson != undefined){
          this.zoom(this.map);
        }
      }
    }
    // if(this.searchBarComponent.searchLocation != undefined){
    //   this.zoom(this.map);
    // }
    this.searchBarComponent.mapListView = true;
  }

  showPropertyMarker(propertyObj){
    this.markers.filter((marker) => {
      if(marker.id == propertyObj.property_id){
        if(this.infoBubble.isOpen()){
          this.infoBubble.close();
        }
        var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);
        this.html = this.infoBubbleService.changeHTML(propertyObj.property_file, propertyObj.home_price, propertyObj.bedroom, propertyObj.full_bath, propertyObj.living_area,propertyObj.address, propertyObj);
        if(pixelOffsetY != undefined && pixelOffsetY < 260){
          this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
        }else{
          this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
        }
        this.infoBubble.open(this.map, marker);
        setTimeout(() => {
          $(".box_on_map").parent().parent().parent().addClass('pop_div');
          $(".box_on_map").parent().parent().parent().attr('id','property_info');
        }, 20);
      }
    });
    var markerSet = this.markerSet.filter((propertyId) => propertyId.id == propertyObj.id);
    if(markerSet.length !=0){
      if(markerSet[0]['property_file'] == ''){
        markerSet[0]['property_file'] = propertyObj.property_file;
      }
    }
  }

  closeAllPorpertyMarkers(){
    if(this.infoBubble.isOpen()){
      this.infoBubble.close();
      var infowin = document.getElementsByClassName("pop_div");
      for(var i=0;i<infowin.length;i++)
      {
        infowin[i].innerHTML = ''
        $("#property_info").remove();
      }
    }
  }

  polygonErrorHandling(){
    if(this.searchBarComponent.handalMaptechError == true){
      this.clearGoogleMap(true);
      delete this.searchBarComponent.searchProperty['polygon'];
      if(this.searchBarComponent.searchProperty['geo_bounding_box'] != undefined){
        this.searchBarComponent.filterProperty();
        this.searchBarComponent.handalMaptechError = true;
      }
    }
  }

  removeSearchValue(){
    this.searchBarComponent.removeLocationValue();
  }
}
