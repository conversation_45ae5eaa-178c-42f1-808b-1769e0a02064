import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { UsersService } from '@app/admin/users/service/users-services';
import { ServiceLocator } from '@app/base/components/service-locator';

declare var $;
@Component({
  selector: 'app-listing-agent',
  templateUrl: '../view/listing-agent.component.html',
  styleUrls: ['../css/listing-agent.component.css', '../../users/css/users.component.css']
})
export class ListingAgentComponent extends BaseComponent implements OnInit {
  usersService: UsersService;

  public LAUserList = [];
  public totalRecordsCount: number;
  public itemsPerPage: number;
  public pageCount: number = 1;

  public dialogTitile = '';
  public dialogMessage = '';
  public selectedUserType = '';
  public selectedUserEmail = '';
  public selectedUserStatus = '';
  public disableConfirmBtn: boolean = false;
  public selecteduserIndex;
  public currentMenuId;
  public currentMenuIndex;

  public accountStatusList = ['Unregistered', 'Free', 'Premium', 'Non ARMLS']
  public selectedAccountStatus = '';

  public searchUserParams = new URLSearchParams;
  public downloadCSVSubscription: any;

  constructor() {
    super();
    this.usersService = ServiceLocator.injector.get(UsersService);
  }
  ngOnInit() {
    this.initLAUserList();
    let self = this;
    $("body").addClass("admin-body");
    $('#deactiveUser').on('hidden.bs.modal', function () {
      self.selectedUserEmail = '';
      self.selectedUserStatus = '';
      self.disableConfirmBtn = false;
      self.selecteduserIndex = undefined;
    });

    $(document).ready(function () {
      $(document).mouseup(function (e) {
        if (self.currentMenuId != undefined && self.currentMenuIndex != undefined) {
          $("#user_" + self.currentMenuIndex + "_" + self.currentMenuId).hide();
          self.currentMenuId = undefined;
          self.currentMenuIndex = undefined;
        }
      });
    });
  }

  public setAccountStatus(agentAccountStatus: string): void {
    this.selectedAccountStatus = agentAccountStatus;
  }

  openMenu(index, id) {
    this.currentMenuIndex = index;
    this.currentMenuId = id;
    $("#user_" + index + "_" + id).toggle();
  }

  initLAUserList() {
    this.searchUserParams = new URLSearchParams();
    this.usersService.LAfilterUserSearch(this.searchUserParams).subscribe(
      res => {
        this.LAUserList = res["result"]["records"];
        this.LAUserList.forEach(user => {
          user.sign_up_date = this.getAdminDateFormat(user.sign_up_date, 'MM/DD/YYYY')
          if (user.last_login_date != '') {
            user.last_login_date = this.getAdminDateFormat(user.last_login_date, 'MM/DD/YYYY')
          } else {
            user.last_login_date = "-";
          }

          if (user.last_account_status_change != '') {
            user.last_account_status_change = this.getAdminDateFormat(user.last_account_status_change, 'MM/DD/YYYY')
          } else {
            user.last_account_status_change = "-";
          }
        });
        this.totalRecordsCount = res["result"]["total_records_count"];
        this.itemsPerPage = res["result"]["items_per_page"];
      },
      err => this.errorResponse(err.json())
    );
  }

  public getPageChange(pageNo) {
    this.pageCount = pageNo;
    this.searchUserParams.set('page_no', pageNo);
    this.usersService.LAfilterUserSearch(this.searchUserParams).subscribe(res => {
      this.LAUserList = res['result']['records'];
      this.LAUserList.forEach(user => {
        user.sign_up_date = this.getAdminDateFormat(user.sign_up_date, 'MM/DD/YYYY')
        if (user.last_login_date != '') {
          user.last_login_date = this.getAdminDateFormat(user.last_login_date, 'MM/DD/YYYY')
        } else {
          user.last_login_date = "-";
        }
        if (user.last_account_status_change != '') {
          user.last_account_status_change = this.getAdminDateFormat(user.last_account_status_change, 'MM/DD/YYYY')
        } else {
          user.last_account_status_change = "-";
        }
      })
      this.totalRecordsCount = res['result']['total_records_count'];
      this.itemsPerPage = res['result']['items_per_page'];
    }, err => {
      this.errorResponse(err.josn());
    });
  }

  public searchAgentUser(userName: string, email: string, mlsId: string, brokerageName: string): void {
    if (userName != '') {
      this.searchUserParams.set('user_name', userName);
    }
    else {
      this.searchUserParams.delete('user_name');
    }
    if (email != '') {
      this.searchUserParams.set('user_email', email.toString());
    }
    else {
      this.searchUserParams.delete('user_email');
    }
    if (mlsId != '') {
      this.searchUserParams.set('mls_id', mlsId.toString());
    }
    else {
      this.searchUserParams.delete('mls_id');
    }
    if (brokerageName != '') {
      this.searchUserParams.set('brokerage_name', brokerageName.toString());
    }
    else {
      this.searchUserParams.delete('brokerage_name');
    }

    if (this.selectedAccountStatus != '') {
      let account_status = this.selectedAccountStatus
      this.searchUserParams.set('account_status', account_status);
    }
    else {
      this.searchUserParams.delete('account_status');
    }

    this.getPageChange(1);
  }


  public resetUserPassword(userEmail: string): void {
    var email = {
      "email": userEmail
    }

    this.usersService.sendforgotPasswordEmail(email).subscribe(res => {
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    });
  }

  public impersonateUser(userEmail: string): void {
    let impersonateParams = new URLSearchParams;
    impersonateParams.set("email", userEmail)
    this.usersService.getImpersonateUserToken(impersonateParams).subscribe(res => {
      this.setUserToken(res['result']['token']);
      let userType = this.userTypes.filter(item => item.code === res['result']['user_type']);
      localStorage.setItem('userType', userType[0]['userType']);
      window.open(window.location.origin);
    }, err => {
      this.errorResponse(err.json());
    });
  }

  downloadCSV(userName: string, email: string, mlsId: string, brokerageName: string) {
    this.searchUserParams.set('user_role', 'LA');
    if (userName != '') {
      this.searchUserParams.set('user_name', userName);
    }
    else {
      this.searchUserParams.delete('user_name');
    }
    if (email != '') {
      this.searchUserParams.set('user_email', email.toString());
    }
    else {
      this.searchUserParams.delete('user_email');
    }

    if (this.selectedAccountStatus != '') {
      let account_status = this.selectedAccountStatus
      this.searchUserParams.set('account_status', account_status);
    }
    else {
      this.searchUserParams.delete('account_status');
    }

    if (mlsId != '') {
      this.searchUserParams.set('mls_id', mlsId.toString());
    }
    else {
      this.searchUserParams.delete('mls_id');
    }
    if (brokerageName != '') {
      this.searchUserParams.set('agent_brokerage_name', brokerageName.toString());
    }
    else {
      this.searchUserParams.delete('agent_brokerage_name');
    }

    this.downloadCSVFile();
  }

  downloadCSVFile() {
    if (this.downloadCSVSubscription) {
      this.downloadCSVSubscription.unsubscribe();
    }
    this.downloadCSVSubscription = this.usersService.downloadCSV(this.searchUserParams).subscribe(res => {
      this.downloadFile(res, "Listing-Agent.csv");
    }, err => this.errorResponse(err.json()));
  }

  resendUserConfirmationEmail(userEmail) {
    let userParams = new URLSearchParams;
    userParams.set("email", userEmail);
    this.usersService.reSendUserConfiEmail(userParams).subscribe(res => {
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    });
  }

  manageUserModal(type, userId, user) {
    this.selecteduserIndex = this.LAUserList.indexOf(user);
    this.selectedUserEmail = userId;
    $("#deactiveUser").modal("show");
    if (type == 'active') {
      this.dialogTitile = 'Activate user';
      this.dialogMessage = 'Are you sure you want to Activate this user?';
      this.selectedUserStatus = 'active';
    }
    else if (type == 'deActive') {
      this.dialogTitile = 'Deactivate user';
      this.dialogMessage = 'Are you sure you want to Deactivate this user?';
      this.selectedUserStatus = 'deActive';
    }
  }

  manageUser() {
    if (this.selectedUserEmail != '' && this.selectedUserStatus != '') {
      let manageUserParams = new URLSearchParams;
      if (this.selectedUserStatus == 'deActive') {
        manageUserParams.set('is_active', 'false');
      }
      else if (this.selectedUserStatus == 'active') {
        manageUserParams.set('is_active', 'true');
      }
      manageUserParams.set('email', this.selectedUserEmail);
      this.disableConfirmBtn = true;
      this.usersService.manageUserAccount(manageUserParams).subscribe(res => {
        this.successResponse(res);
        this.disableConfirmBtn = false;
        $("#deactiveUser").modal("hide");
        if (this.selecteduserIndex != undefined) {
          if (this.LAUserList[this.selecteduserIndex]['is_active'] == false) {
            this.LAUserList[this.selecteduserIndex]['is_active'] = true;
          }
          else if (this.LAUserList[this.selecteduserIndex]['is_active'] == true) {
            this.LAUserList[this.selecteduserIndex]['is_active'] = false;
          }
        }
      }, err => {
        this.errorResponse(err.json());
      });
    }
  }

  downgradeUser(user) {
    let manageUserParams = new URLSearchParams;
    manageUserParams.set('user_type', user.user_type);
    manageUserParams.set('email', user.email);
    this.usersService.DowngradeUser(manageUserParams).subscribe(res => {
      user.is_paid_account = false
      user.is_permanent_premium = false
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    });
  }

  upgradeUser(user) {
    let manageUserParams = new URLSearchParams;
    manageUserParams.set('user_type', user.user_type);
    manageUserParams.set('email', user.email);
    this.usersService.UpgradeUser(manageUserParams).subscribe(res => {
      user.is_paid_account = true
      user.is_permanent_premium = true
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    });
  }


}
