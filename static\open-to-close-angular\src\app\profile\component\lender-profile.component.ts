import { Component, OnInit } from '@angular/core';
import { ProfileComponent } from '@app/profile/component/profile.component';
import { NgForm } from '@angular/forms/src/directives/ng_form';
import { FormGroup, FormControl,Validators,FormArray } from '@angular/forms';

@Component({
  selector: 'app-lender-profile',
  templateUrl: '../views/lender-profile.html',
  styleUrls: ['../css/profile.component.css']
})
export class LenderProfileComponent extends ProfileComponent implements OnInit {

 lenderProfileForm:FormGroup;
 fileUrl: any = this.imagePrefix + "default-placeholder.png";
 profileName: string;
 public msgCP : boolean = false;
 public w_ohCP : boolean = false;
    
  constructor() {
    super();
  }

  ngOnInit() {
    if(this.getPreviousScreen() != '/profile'){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/profile/mortgageLender');
    this.initData();
  }

  initForm(){
    this.lenderProfileForm = new FormGroup({
      email : new FormControl('',[Validators.required,Validators.email]),
      profile: new FormGroup({
        name : new FormControl('',Validators.required),
        cell_phone: new FormControl('',[Validators.required, Validators.minLength(10),Validators.maxLength(10)]),
        office_phone: new FormControl('',[Validators.required, Validators.minLength(10),Validators.maxLength(10)]),
        lending_company : new FormControl('',Validators.required),
        email_preferences: new FormArray([])
      }),
    });
  }

  async initData(): Promise<any>{
    await this.initForm();
      if(Object.keys(ProfileComponent.mortgageLender).length == 0){
        this.profileService.lenderDetails().subscribe(res => {
          this.profileName = res.result.profile.name;
          ProfileComponent.mortgageLender = res.result;
          
          if(res.result.profile.profile_photo != ""){
            this.fileUrl = res.result.profile.profile_photo;
          }
          this.lenderProfileForm.patchValue(res['result']);
          this.checkCP(res.result.profile.email_preferences);
      });
    }else{
      this.lenderProfileForm.patchValue(ProfileComponent.mortgageLender);
      this.profileName = ProfileComponent.mortgageLender.profile.name;
      this.checkCP(ProfileComponent.mortgageLender.profile.email_preferences);
      if(ProfileComponent.mortgageLender.profile.profile_photo != ""){
        this.fileUrl = ProfileComponent.mortgageLender.profile.profile_photo
      }
    }
  }

  updatePassword(form:FormGroup){
    this.authService.changePassword(form.value).subscribe(res => {
      form.reset();
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    });
  }

  updateProfile(form:FormGroup){
    var email_preferencesList=[];
    if(this.msgCP == true){
      email_preferencesList.push('msg');   
    }
    if(this.w_ohCP == true){
      email_preferencesList.push('w_oh');    
    }
    form.value['profile']['email_preferences'] = email_preferencesList;
    this.profileService.lenderUpdate(form.value).subscribe(res =>{
      this.successResponse(res);
      ProfileComponent.mortgageLender = res['result'];
    },err=>this.errorResponse(err.json()));
  }

  checkCP(res){
    for(let i=0; i< res.length; i++){
      if(res[i]== 'msg')
      {
        this.msgCP = true;
      }
      if(res[i]== 'w_oh')
      {
        this.w_ohCP = true;
      }
    }
  }

  CPStatusChange(status){
    if(status == 'msg'){
      if(this.msgCP == false)
      {
        this.msgCP = true;       
      }
      else
      {
        this.msgCP = false;
      }
    }
    if(status == 'w_oh'){
      if(this.w_ohCP == false)
      {
        this.w_ohCP = true;
      }
      else
      {
        this.w_ohCP = false;
      }
    }
  }

    /*
  * @desc: upload/change profile image
  */
  uploadProfileImage(event){
    let formData = new FormData();
    formData.append('profile_photo', event.srcElement.files[0]);
    this.profileService.uploadUserImage(formData).subscribe(res => {
      this.fileUrl = res['result'];
      ProfileComponent.mortgageLender.profile.profile_photo = res['result'];
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    })
  }
}
