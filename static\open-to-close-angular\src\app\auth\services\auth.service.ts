import { Injectable } from '@angular/core';
import { BaseComponent} from '@app/base/components/base.component';
import { SignIn } from '@auth/models/signin';
import { SignUp } from '@auth/models/signup'
import { User,UserDetailResponse, SignUpResponse} from '@base/model/user';
import { Observable } from 'rxjs/Observable';
import { Message } from '@app/base/model/message';
import { ResetPassword } from '@auth/models/reset-password';
import { ApiResponse } from '@auth/models/api-response';
import { UserResponse,UserAPIResponse} from '@app/auth/models/user-response';
import { ServiceLocator } from '@app/base/components/service-locator';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiService } from '@app/base/services/api.service';
import { AgentResponse } from '@app/profile/models/agent-detail';
import { BrokerageResponse } from '@app/profile/models/brokerage-detail';

@Injectable()
export class AuthService{

 public baseComponent:BaseComponent;
 public apiService:ApiService;

  constructor(){
    this.baseComponent=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService)
  }

  public singin(signin:SignIn): Observable<UserAPIResponse>{
    let options =this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['auth']['signin'],signin);
    return this.apiService.apiCall(options);
  }

  public singup(signup:SignUp,userType): Observable<SignUpResponse>{
    let options =this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['auth']['signup']+'/'+userType,signup);
    return this.apiService.apiCall(options);
  }

  public sendOtp(param: any): Observable<any> {
    console.log(param);
    let options = this.baseComponent.getRequestOptions(
        METHOD_REFERENCE['POST'],
        API_REFERENCE['auth']['sendOtp'],
        JSON.stringify(param), // Send as JSON string
        null,
        null,
        null,
        false,
        false // Set isUrlParams to false
    );
    return this.apiService.apiCall(options);
}

public verifyOtp(param: any): Observable<any> {
    let options = this.baseComponent.getRequestOptions(
        METHOD_REFERENCE['POST'],
        API_REFERENCE['auth']['verifyOtp'],
        JSON.stringify(param), // Send as JSON string
        null,
        null,
        null,
        false,
        false // Set isUrlParams to false
    );
    return this.apiService.apiCall(options);
}


  public changePassword(passwordValues): Observable<ApiResponse>{
    let options =this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['auth']['password'],passwordValues);
    return this.apiService.apiCall(options);
  }

  public logout(logoutObj):Observable<any>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['auth']['logout'],logoutObj);
    return this.apiService.apiCall(options);
  }

  public verification(token) : Observable<any>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['auth']['verification'],{'verification_token':token});
    return this.apiService.apiCall(options);
  }

  public searchAgent(name): Observable<any>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['auth']['agentSearch']+'?mls_agent_id='+name,{});
    return this.apiService.apiCall(options);
  }

  public searchBrokerageAgent(searchUrlParams): Observable<any>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['auth']['brokerageAgentSearch'],{}, searchUrlParams.toString());
    return this.apiService.apiCall(options);
  }

  public getUserDetails():Observable<UserDetailResponse>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['auth']['getUserDetail'],{});
    return this.apiService.apiCall(options);
  }

  public forgotPassword(email):Observable<ApiResponse>{
    let options = this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['auth']['forgotPassword'],email);
    return this.apiService.apiCall(options);
  }

  public resetPassword(password:ResetPassword):Observable<ApiResponse>{
    let options = this.baseComponent.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['auth']['resetPassword'],password);
    return this.apiService.apiCall(options);
  }

  public searchSuggestions(search):Observable<any>{
    let options = this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['searchSuggestions']+'?search='+search,{});
    return this.apiService.apiCall(options);
  }

  public getAgentDetail(user):Observable<any>{
    let options = this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['auth']['getAgentDetails'],user.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public agentDetail():Observable <AgentResponse>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['update'],{});
    return this.apiService.apiCall(options);
  }

  public brokerageDetails(): Observable <BrokerageResponse>{
    let options=this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['update'],{});
    return this.apiService.apiCall(options);
  }

  public reSendConfirmationEmail(email: URLSearchParams):Observable<ApiResponse>{
    let options = this.baseComponent.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['reSendConfimEmail'],email.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
  public getGuestUserInfobyEmail(email): Observable<any> {
    let options = this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'], API_REFERENCE['profile']['getGuestUser'] + '?guest_email=' + email, {});
    return this.apiService.apiCall(options);
  }
  public getGuestUserInfobyPhone(phone): Observable<any> {
    let options = this.baseComponent.getRequestOptions(METHOD_REFERENCE['GET'], API_REFERENCE['profile']['getGuestUser'] + '?phone=' + phone, {});
    return this.apiService.apiCall(options);
  }

}
