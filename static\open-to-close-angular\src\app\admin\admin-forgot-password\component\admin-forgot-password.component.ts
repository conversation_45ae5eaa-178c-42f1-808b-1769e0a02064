import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { AuthService } from '@app/auth/services/auth.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { FormGroup, FormControl,Validators } from '@angular/forms';

@Component({
  selector: 'app-admin-forgot-password',
  templateUrl: '../views/admin-forgot-password.component.html',
  styleUrls: ['../css/admin-forgot-password.component.css','../../admin-login/css/admin-login.component.css']
})
export class AdminForgotPasswordComponent extends BaseComponent implements OnInit {

  public authService:AuthService;
  adminForgotPasswordForm : FormGroup;

  constructor() {
    super();
    this.authService=ServiceLocator.injector.get(AuthService);
   }

  ngOnInit() {
    this.adminForgotPasswordForm = new FormGroup({
      email: new FormControl('',[Validators.email, Validators.required]),
    });
  }

  ForgotPassword(form:FormGroup){
    form.value['is_admin'] = true;
    this.authService.forgotPassword(form.value).subscribe(res =>{
      this.successResponse(res);
    },err =>{
      this.errorResponse(err.json());
    });    
  }
}
