import { Component, OnInit,On<PERSON><PERSON>roy } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { BaseComponent } from '@app/base/components/base.component';
import { AuthService } from '@app/auth/services/auth.service';
import { ServiceLocator } from '@app/base/components/service-locator';

@Component({
  selector: 'app-admin-reset-password',
  templateUrl: '../views/admin-reset-password.component.html',
  styleUrls: ['../css/admin-reset-password.component.css','../../admin-login/css/admin-login.component.css']
})
export class AdminResetPasswordComponent extends BaseComponent implements OnInit,OnDestroy {

  adminResetPasswordForm: FormGroup;
  private paramsSubscription;
  public authService:AuthService;

  constructor() {
    super();
    this.authService=ServiceLocator.injector.get(AuthService);
  }

  ngOnInit() {
    this.paramsSubscription = this.route.queryParams.subscribe(params=>{
      if(params['token'] == undefined)
      {
        this.router.navigateByUrl('admin');
      }
    });

    this.adminResetPasswordForm = new FormGroup({
      password : new FormControl('', [Validators.required, Validators.minLength(5),Validators.maxLength(15)]),
      confirm_new_password : new FormControl('', [Validators.required, Validators.minLength(5),Validators.maxLength(15)])
    }, passwordMatchValidator);

    function passwordMatchValidator(g: FormGroup) {
      return g.get('password').value === g.get('confirm_new_password').value ? null : {'mismatch': true};
    };
  }

  ResetPassword(form:FormGroup){
    this.paramsSubscription = this.route.queryParams.subscribe(params=>{
      form.value['reset_password_token']=params['token'];
      form.value['is_admin'] = true;
      this.authService.resetPassword(form.value).subscribe(res => {        
        this.successResponse(res);
        this.router.navigateByUrl('admin');
        form.reset();       
      },err => {       
        this.errorResponse(err.json());
      });
    });
  }

  ngOnDestroy(): void {
    this.paramsSubscription.unsubscribe();
  }

}
