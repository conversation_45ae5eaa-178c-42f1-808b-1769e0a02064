import { Component, OnInit } from '@angular/core';
import { ProfileComponent } from '@app/profile/component/profile.component';
import { NgForm,FormGroup,FormControl,Validators, FormArray } from '@angular/forms';
import { BaseComponent } from '@app/base/components/base.component';
import { User } from '@app/base/model/user';
import { Profile } from 'selenium-webdriver/firefox';
import { HomeBuyerDetail } from '@app/profile/models/home-buyer-detail';
import { Agent } from '@app/profile/models/profile';
import { ChatService } from '@app/messaging/service/chat-service';
import { ServiceLocator } from '@app/base/components/service-locator';


declare var $;

@Component({
  selector: 'app-home-buyer-profile',
  templateUrl: '../views/home-buyer-profile.html',
  styleUrls: ['../css/profile.component.css']
})
export class HomeBuyerProfileComponent extends ProfileComponent implements OnInit {

  homeBuyerProfileForm:FormGroup;

  fileUrl: any = this.imagePrefix + "testmonial-default (1).png";
  agentImageUrl: any = this.imagePrefix + "testmonial-default (1).png"

  public searchAgentList=[];
  public emailPreferencesList=[];
  public custFormOjt :  FormGroup;
  public agentList:boolean = false;

  public msgCP : boolean = false;
  public w_ohCP : boolean = false;
  public disableAcheckBox : boolean =false;
  profileName: string;
  agetID : Boolean = false;
  public status: Boolean = false;
  public statusFalse :Boolean = false;
  showMyAgent : Boolean =false;
  selectedAgentId : String;
  selectedAgentUserId : String;
  agentName : String;
  brokerName : String;

  //email confirm
  public oldEmailAddress : String = '';
  public NewEmailAddress : String = '';
  public tempUpdatedProfileInfo : FormGroup;

  public agentSearchSubscription: any;

  public chatService : ChatService;

  constructor() {
    super();
    this.chatService = ServiceLocator.injector.get(ChatService);
  }

  ngOnInit() {
    if(this.getPreviousScreen() != '/profile'){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/profile/homeBuyer');
    this.initData();
  }

  defineForms(){
    this.homeBuyerProfileForm = new FormGroup({
      email: new FormControl('',[Validators.required,Validators.email]),
      profile: new FormGroup({
        name: new FormControl(""),
        phone : new FormControl('',[Validators.required, Validators.minLength(12),Validators.maxLength(12)]),
        email_preferences: new FormArray([]),
        agent: new FormGroup({
          id: new FormControl(''),
        }),
      }),
    });
  }

  async initData(){
    await this.defineForms();
    if(ProfileComponent.homeBuyerUser.profile.agent !=undefined && ProfileComponent.homeBuyerUser.profile.agent !=null && Object.keys(ProfileComponent.homeBuyerUser.profile.agent).length == 0){
      if(Object.keys(ProfileComponent.homeBuyerUser.profile.agent).length == 0){
        this.profileService.homeBuyerDetail().subscribe(res => {
          
          if(res.result.profile.profile_photo != ""){
            this.fileUrl = res.result.profile.profile_photo;
          }
          this.homeBuyerProfileForm.patchValue(res.result);
          this.homeBuyerProfileForm.controls.profile['controls']['phone'].setValue(this.validateNumber(res.result.profile.phone));
          this.homeBuyerProfileForm.controls.profile['controls']['agent']['controls']['id'].setValue(res.result.profile.agent_id);
          ProfileComponent.homeBuyerUser = res.result;
          ProfileComponent.homeBuyerUser.profile.agent = new Agent();
          ProfileComponent.homeBuyerUser.profile.agent_id = res.result.profile.agent_id;
          ProfileComponent.homeBuyerUser.profile.agent_user_id = res.result.profile.agent_user_id
          ProfileComponent.homeBuyerUser.profile.agent = res.result.profile.agent;
          
          this.checkCP(res.result.profile.email_preferences);
          this.profileName = res.result.profile.name;
          this.selectedAgentId = res.result.profile.agent_id;
          this.agentName = res.result.profile.agent_name;
          this.brokerName = res.result.profile.agent_brokerage_name;
          this.selectedAgentUserId = res.result.profile.agent_user_id

          if(res.result.profile.agent_profile_photo != ""){
            this.agentImageUrl = res.result.profile.agent_profile_photo;
          }
         
          this.status = ProfileComponent.homeBuyerUser.profile.agent_id;         
          if(ProfileComponent.homeBuyerUser.profile.agent_id != 0){
            this.checkUserStatus(1);
          }
          else
          {
            this.checkUserStatus('');   
          }
          this.setOldEmailAddress(res.result.email);
        }, err => {
          // this.errorResponse(err.json());
        });
      } 
    }
    else{
      if(ProfileComponent.homeBuyerUser.profile.profile_photo != ""){
        this.fileUrl = ProfileComponent.homeBuyerUser.profile.profile_photo;
      }

      if(ProfileComponent.homeBuyerUser.profile.agent_id != 0){
       this.checkUserStatus(1);
      }
      else
      {
        this.checkUserStatus('');
      }
      
      if(ProfileComponent.homeBuyerUser.profile.agent_profile_photo != ""){
        this.agentImageUrl = ProfileComponent.homeBuyerUser.profile.agent_profile_photo;
      }
      this.selectedAgentId = ProfileComponent.homeBuyerUser.profile.agent_id;
      this.agentName = ProfileComponent.homeBuyerUser.profile.agent_name;
      this.selectedAgentUserId = ProfileComponent.homeBuyerUser.profile.agent_user_id;
      this.homeBuyerProfileForm.controls.profile['controls']['phone'].setValue(this.validateNumber(ProfileComponent.homeBuyerUser.profile.phone));
      this.brokerName = ProfileComponent.homeBuyerUser.profile.agent_brokerage_name;
      this.checkCP(ProfileComponent.homeBuyerUser.profile.email_preferences);
      this.homeBuyerProfileForm.patchValue(ProfileComponent.homeBuyerUser);
      this.profileName = ProfileComponent.homeBuyerUser.profile.name;
      this.setOldEmailAddress(ProfileComponent.homeBuyerUser.email);
    }
  }

  updatePassword(form:FormGroup){
    this.authService.changePassword(form.value).subscribe(res => {
      form.reset();
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    });
  }

  checkEmailIsChanged(form:FormGroup){
    this.NewEmailAddress = form.value['email'];
    if(this.oldEmailAddress.toLowerCase() != this.NewEmailAddress.toLowerCase()){
      this.tempUpdatedProfileInfo = form;
      $("#confirmEmailChangePopupHB").modal("show");
    }
    else{
      this.updateProfile(form);
    }
  }

  onConfirmEmailClick(){
    this.updateProfile(this.tempUpdatedProfileInfo);
  }

  updateProfile(form:FormGroup){
    var email_preferencesList=[];
    if(this.msgCP == true){
      email_preferencesList.push('msg');
    }

    if(this.w_ohCP == true){
      email_preferencesList.push('w_oh');
    }   
          
    if(this.homeBuyerProfileForm.controls.profile['controls']['agent']['controls']['id'].value == '' || this.homeBuyerProfileForm.controls.profile['controls']['agent']['controls']['id'].value == null){
      form.value['profile']['agent']['id'] = 0;
    }
    form.value['profile']['email_preferences']=email_preferencesList;
    this.profileService.homeBuyerUpdate(form.value).subscribe(res => {
    $("#confirmEmailChangePopupHB").modal("hide");
    this.oldEmailAddress = res.result.email;
    this.profileSuccessResponse();
      if(res.result['is_logout'] == true){
        ProfileComponent.homeBuyerUser = new HomeBuyerDetail();
        this.refreshBasePropertys();
        this.router.navigate(['/'],{queryParams:{email_confirm:true}});
      }
      else{
        this.homeBuyerProfileForm.patchValue(res.result);
        ProfileComponent.homeBuyerUser.profile.agent_id = res['result']['profile']['agent_id'];
        ProfileComponent.homeBuyerUser = res.result;
        this.checkCP(res.result.profile.email_preferences);
        this.profileName = res.result.profile.name;      
        this.selectedAgentId = res.result.profile.agent_id;
        this.agentName = res.result.profile.agent_name;
        this.brokerName = res.result.profile.agent_brokerage_name;
      }
    },err => {       
      this.errorResponse(err.json());
    });   
  }
    
  changeStatus(value){
    if(value == 'yes'){
      this.status = true;
      this.statusFalse = false;
    }
    else if(value == 'no'){
      if(ProfileComponent.homeBuyerUser.profile.agent_id != 0){
        this.addRemoveAgent(0,'remove');
        this.statusFalse = true;
        this.status = false;
      }
      else{
        this.statusFalse = true;
        this.status = false;
      }
    }
  }

  searchAgent(name){
    if(name == ''){
      this.searchAgentList=[];
      this.agentList=false;
    }
    if(name.trim() != ""){
      if(this.agentSearchSubscription){
        this.agentSearchSubscription.unsubscribe();
      }
      this.agentSearchSubscription = this.authService.searchAgent(name).subscribe(res =>{
        this.searchAgentList=res['result'];       
        this.agentList=true;
      });
    }
  }

  addRemoveAgent(id,status){
    if(status == 'add'){
      let agent = this.searchAgentList.filter(agent => agent.id == id)[0];
      
      if(agent['profile_photo'] != null || agent['profile_photo'] != undefined){
        this.agentImageUrl = agent['profile_photo'];
        ProfileComponent.homeBuyerUser.profile.agent_profile_photo = agent['profile_photo'];
      }
      else{
        this.agentImageUrl = this.imagePrefix + "testmonial-default (1).png";
        ProfileComponent.homeBuyerUser.profile.agent_profile_photo = this.imagePrefix + "testmonial-default (1).png";
      }
    }
    
    let manageLender = new URLSearchParams();
    manageLender.set('attach_user_id',id);
    this.profileService.addRemoveLender(manageLender).subscribe(res=>{
      this.disableAcheckBox= false;
      ProfileComponent.homeBuyerUser = res['result'];
      this.homeBuyerProfileForm.patchValue(res['result']);
      ProfileComponent.homeBuyerUser.profile.agent = new Agent();
      this.successResponse(res);
      this.homeBuyerProfileForm.controls.profile['controls']['agent']['controls']['id'].setValue(res['result']['profile']['agent_id']);
      if(status == 'add'){
        this.searchAgentList=[];
        this.agentList=false;
        if(res['result']['profile']['agent_id'] != 0){
          this.checkUserStatus(1);
        }
        else
        {
          this.checkUserStatus('');
        }
        this.status=true;
        ProfileComponent.homeBuyerUser.profile.agent_id = res['result']['profile']['agent_id'];
        this.selectedAgentId = res['result']['profile']['agent_id'];
        this.selectedAgentUserId = res['result']['profile']['agent_user_id'];
        this.agentName = res['result']['profile']['agent_name'];
        this.brokerName = res['result']['profile']['agent_brokerage_name'];
        
      }
      else if(status == 'remove'){
        if(res['result']['profile']['agent_id'] != 0){
          this.checkUserStatus(1);
        }
        else
        {
          this.checkUserStatus('');
        }
        this.statusFalse=true;
        ProfileComponent.homeBuyerUser.profile.agent_id = res['result']['profile']['agent_id'];
        this.selectedAgentId = res['result']['profile']['agent_id'];
        this.agentName = res['result']['profile']['agent_name'];
        this.brokerName = res['result']['profile']['agent_brokerage_name'];
        this.selectedAgentUserId = res['result']['profile']['agent_user_id'];
      }
    },err=>{
      this.errorResponse(err.json());
    });
  }

  validateFormat(number:String){
    this.homeBuyerProfileForm.controls.profile['controls']['phone'].setValue(this.validateNumber(number));
  }

  checkCP(res){
    for(let i=0; i< res.length; i++){
      if(res[i]== 'msg')
      {
        this.msgCP = true;
      }
      if(res[i]== 'w_oh')
      {
        this.w_ohCP = true;
      }
    }
  }

  CPStatusChange(status){    
    if(status == 'msg'){
      if(this.msgCP == false)
      {
        this.msgCP = true;       
      }
      else
      {
        this.msgCP = false;
      }
    }
    if(status == 'w_oh'){
      if(this.w_ohCP == false)
      {
        this.w_ohCP = true;
      }
      else
      {
        this.w_ohCP = false;
      }
    }
  }

  checkUserStatus(status){
    if(status == 1){
      this.status=true;
      this.showMyAgent=true;
      this.statusFalse=false;
      this.agetID=true;
    }
    if(status ==  '')
    {
      this.status=false;
      this.showMyAgent=false;
      this.statusFalse=true;
    }
  }

  /*
  * @desc: upload/change profile image
  */
  uploadProfileImage(event){
    let formData = new FormData();
    formData.append('profile_photo', event.srcElement.files[0]);
    this.profileService.uploadUserImage(formData).subscribe(res => {
      this.fileUrl = res['result'];
      ProfileComponent.homeBuyerUser.profile.profile_photo = res['result'];
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    })
  }

  setOldEmailAddress(email){
    this.oldEmailAddress = email;
  }

  contactAgent(){
    if(this.selectedAgentUserId != undefined){
      var client = {};
      client['user_name'] = this.agentName;
      client['profile_image'] = this.agentImageUrl;
      client['chat_thread_id'] = this.selectedAgentUserId;
      client['receiver_id'] = this.selectedAgentUserId;
      client['last_message_time'] = "";
      client['last_message'] = '';
      this.chatService.setClientChatThread(client);
      this.router.navigate(['messaging']);
    }
  }
}
