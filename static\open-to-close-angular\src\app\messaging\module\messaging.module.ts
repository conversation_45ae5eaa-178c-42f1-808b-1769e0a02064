import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MessagingComponent } from '@app/messaging/components/messaging.component';
import { NewChatComponent } from '@app/messaging/components/new-chat.component';
import { ChatComponent } from '@app/messaging/components/chat.component';
import { BaseModule } from '@app/base/modules/base.module';
import { ChatService } from '@app/messaging/service/chat-service.ts';
import { ReversePipe } from '@app/messaging/pipes/chat-reverse.pipe';

@NgModule({
  imports: [
    CommonModule,
    BaseModule
  ],
  declarations: [MessagingComponent,NewChatComponent,ChatComponent, ReversePipe],
  providers: [ChatService]
})
export class MessagingModule { }
