.btnhover:hover{
    color: white !important;
}

.btnhover:focus{
    color: white !important;
}

.chat-image{
    padding-bottom: 9px;
}
.ls {
    font-size: 37px;
    color: #FFFFFF;
    position: relative;
    letter-spacing: -1px;
    line-height: 40px;
    background: #10B8A8;
    display: inline-block;
    padding: 10px 3px !important;
    border-radius: 40px;
    top: -15px;
    height: 60px;
    width: 61px;
    text-align: center !important;
}
.po_rel_1 {
    position: relative !important;
    top: 10px !important;
}
.chat-msg{
    padding: 0px 0px 4px 0px !important;
}
.chat-co-sm-4{
    float: left;
    padding-right: 24px;
}
.pd-r-10{
    padding-right: 10px;
}
.new-left-msg{
    margin-left: 20px;
}
.full-right{
    width: calc(100vw - 439px) !important;
}

.chat-box-file{
  display: inline;
  position: relative;
}

.box_on_map{
  left: 2% !important;
  width: 210px !important;
}

.col-sm-16 .box_on_map{
  left: 3% !important;
}

@media only screen and (max-width: 767px){
	/* .box_on_map{
    left: 0% !important;
  } */
  .col-sm-16 .box_on_map{
    left: 10% !important;
  }
  .col-sm-4 .box_on_map{
    left: -11% !important;
  }
}

@media only screen and (max-width: 375px){
  .col-sm-4 .box_on_map{
    left: -10% !important;
  }
}

@media only screen and (max-width: 768px){
	/* .col-sm-4 .box_on_map{
    left: 80% !important;
  } */
  .col-sm-16 .box_on_map{
    left: 12% !important;
  }
}
@media (max-width: 1023px) and (min-width: 768px) {
  .col-sm-4 .box_on_map{
    left: 84% !important;
  }
}

@media only screen and (min-width: 1024px) {
  /* .col-sm-4 .box_on_map{
    left: 79% !important;
  } */
}
