import { Component, OnInit,Output, Input, EventEmitter} from '@angular/core';
import { ServiceLocator } from '@app/base/components/service-locator';
import { FormGroup, FormControl,NgForm,Validators} from '@angular/forms';
import { AuthService } from '@app/auth/services/auth.service';
import { SearchService } from '@app/search/service/search.service';
import { SearchSuggestions } from '@app/search/model/search-suggestions';
import { BaseComponent } from '@app/base/components/base.component';
import { Observable, Subject } from 'rxjs';
import { Http, Headers } from '@angular/http';

declare var $;
declare function crear_select(): any;

@Component({
  selector: 'app-auth',
  templateUrl: '../views/auth.component.html',
  styleUrls: ['../css/auth.component.css','../../landing-pages/css/landing-page.component.css']
})

export class AuthComponent extends BaseComponent implements OnInit{

  public flag: boolean = true;
  public suggestionsList = [];
  public searchAgentList=[];
  public authService:AuthService;
  public searchService :SearchService;

  public lat;
  public lng;
  public zipCode;
  public city;

  public searchSubscription: any;

  constructor() {
    super();
    this.authService=ServiceLocator.injector.get(AuthService);
    this.searchService=ServiceLocator.injector.get(SearchService);
  }

  ngOnInit(){
    if(this.getPreviousScreen() != ''){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/');

    if ($(window).width() < 767) {
      $('#menu_group').addClass('collapse');
    }

    $(".search-input-layout").click(function(){
      $(".search-result").fadeIn(100);
    });
  }

  suggestions(keyword){
    if(keyword.trim().length >2){
      if(this.searchSubscription){
        this.searchSubscription.unsubscribe();
      }
      this.searchSubscription = this.authService.searchSuggestions(keyword).subscribe(res =>{
        this.suggestionsList = res['result'];
      });
    }
    else{
      this.suggestionsList = [];
    }
  }

  redirectToApp(url){
    window.open(url);
  }

  onselectSuggestion(search){
    this.searchService.setSearchSuggestions(search);
    this.router.navigate(['search']);
    localStorage.setItem('boundryZoom',"true");
  }

  getLocation(){
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        if (position) {
          console.log("Latitude: " + position.coords.latitude +
            "Longitude: " + position.coords.longitude);
          this.lat = position.coords.latitude;
          this.lng = position.coords.longitude;
          this.fetchLocation(this.lat, this.lng);
        }
      },
        (error) => console.log(error));
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  }

  openSignUpModal(){
    $("#authModal").modal("show");
    $('#authModal .modal-body .nav-pills a:eq(' + 1 + ')').tab('show');
  }
  OpenAuthModal(modalType) {
    var index = 1;
    if (modalType == "signIn") {
      index = 0;
    }
    $('#authModal .modal-body .nav-pills a:eq(' + index + ')').tab('show');
  }
  fetchLocation(lat, lng){
    fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`)
    .then(res => res.json())
    .then(resJson => {
      const { postcode, locality } = resJson;
      this.zipCode = postcode;
      this.city = locality;
      postcode !== "" ? this.suggestions(postcode) : this.suggestions(locality);
    })
  }

}
