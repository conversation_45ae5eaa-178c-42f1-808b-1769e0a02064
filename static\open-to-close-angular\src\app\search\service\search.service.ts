import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ApiService } from '@app/base/services/api.service';
import { Observable } from 'rxjs/Observable';
import { SearchSuggestions } from '@app/search/model/search-suggestions';
import { SearchSuggestionsResponse } from '@app/search/model/search-suggestions-response';
import { SearchResults } from '@app/search/model/search-results-response';
import { SearchResultsByFilters } from '@app/search/model/search-results-by-filters';
import { SearchFiltersResponse } from '@app/search/model/search-results-by-filters-response';
import { SearchByCustomShape } from '@app/search/model/search-by-custom-shape';
import { SearchCustomShapeResponse } from '@app/search/model/search-by-custom-shape-response';
import { SaveSearch } from '@app/search/model/save-search';
import { Message } from '@app/base/model/message';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { Http, RequestOptions, Headers } from '@angular/http';
import { FilterModel } from '@app/searchBar/models/filter-model';

@Injectable()
export class SearchService {
  public searchSuggestionsresult;
  public searchProperty = new FilterModel();
  public myClientSearch;
  private http:Http;
  public searchObj;
  public currentPage;
  public currentListType;
  public filterForm = '';
  public isFavScreen :  Boolean = false;

  public baseservice:BaseComponent;
  apiService:ApiService;

  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
    this.http = ServiceLocator.injector.get(Http);
  }

  public setFavoriteScreen(fav){
    this.isFavScreen = fav;
  }

  public getFavoriteScreen(){
    return this.isFavScreen;
  }

  public setFilterForm(screenName){
    this.filterForm = screenName;
  }

  public getFilterForm(){
    return this.filterForm;
  }

  public setCurrentListType(listType){
    this.currentListType = listType;
  }

  public getCurrentListType(){
    return this.currentListType;
  }

  public setSearchSuggestions(searchSuggestion){
    this.searchSuggestionsresult = searchSuggestion;
  }

  public getSearchSuggestions(){
    return this.searchSuggestionsresult;
  }

  public setFilterSearch(filterModelObj){
    this.searchObj = filterModelObj;
  }

  public getFilterSearch(){
    return this.searchObj;
  }

  public setSearchProperty(searchProperty){
    this.searchProperty = searchProperty;
  }

  public getSearchProperty(){
    return this.searchProperty;
  }

  public setMyClientSearch(search){
    this.myClientSearch = search;
  }

  public getMyClientSearch(){
    return this.myClientSearch;
  }

  public searchSuggestions(searchSuggestionParams):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['searchProperty'],searchSuggestionParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public searchOsmId(searchString):Observable<any>{
    return this.http.get("https://nominatim.openstreetmap.org/search?q="+searchString+"&polygon_geojson=1&format=json&viewbox=").map(res => res.json() , err => err.json());
  }

  public getPolygonList(osmId):Observable<any>{
    return this.http.get("http://polygons.openstreetmap.fr/get_geojson.py?id="+osmId+"&format=json&params=0").map(res => res.json(), err => err.json());
  }

  public getSearchPolygonList(searchString):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['property']['searchPolygons']+'?search='+searchString, {});
    return this.apiService.apiCall(options);
  }

  public searchLocation(search):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['searchSuggestions']+'?search='+search,{});
    return this.apiService.apiCall(options);
  }

  public searchResults(search_criteria,search_id):Observable<SearchResults>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['searchResults']+'/'+search_criteria+'/'+search_id,{});
    return this.apiService.apiCall(options);
  }

  public searchPropertiesByFilters(search_criteria,search_id,searchPropertiesByFilters:SearchResultsByFilters): Observable<SearchFiltersResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['search']['searchPropertiesByFilters']+'/'+search_criteria+'/'+search_id+'/filters',searchPropertiesByFilters);
    return this.apiService.apiCall(options);
  }

  public searchByMapCustomShape(searchByMapCustomShape:SearchByCustomShape):Observable<SearchCustomShapeResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['search']['searchByMapCustomShape'],searchByMapCustomShape);
    return this.apiService.apiCall(options);
  }

  public saveSearch(saveSearchParams):Observable<Message>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['search']['saveSearch'],saveSearchParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public getSavedSearch(listTypeParams):Observable<Message>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['getSavedSearch'],{}, listTypeParams.toString());
    return this.apiService.apiCall(options);
  }

  addPropertyToFavorite(property):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['saveToFavorite'],property.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  getOpenHouseAgent():Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['getOpenHouseAgent'],{});
    return this.apiService.apiCall(options);
  }

  getEventSearch(eventParams):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['search']['eventSearch'],eventParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  getMapLatLng(location,abrivation):Observable<any>{
    return this.http.get("https://maps.googleapis.com/maps/api/geocode/json?address="+location+','+abrivation+'&key=AIzaSyBhuVc1V9fAtU7KftUoHrsVuIZH0Y7E6ow').map(res => res.json(), err => err.json());
  }

  getMapBoundryUsingZip(zipcode):Observable<any>{
    // return this.http.get('https://api.maptechnica.com/v1/zip5/bounds?zip5='+zipcode+'&key=API_DEMO_KEY').map(res => res.json(), err => err.json());
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['mapBoundary']['MapBoundryUsingZipcodeId'],{}, zipcode.toString());
    return this.apiService.apiCall(options);
  }

  getMapBoundryUsingState(abrivation):Observable<any>{
    // return this.http.get('https://api.maptechnica.com/v1/state/bounds?stusps='+abrivation+'&key=API_DEMO_KEY').map(res => res.json(), err => err.json());
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['mapBoundary']['MapBoundryUsingState'],{}, abrivation.toString());
    return this.apiService.apiCall(options);
  }

  getMapBoundryUsingGoId(GeoId):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['mapBoundary']['MapBoundryUsingGoId'],{}, GeoId.toString());
    return this.apiService.apiCall(options);
    // return this.http.get('https://api.maptechnica.com/v1/place/bounds?geoid='+GoId+'&key=API_DEMO_KEY').map(res => res.json(), err => err.json());
  }
}
