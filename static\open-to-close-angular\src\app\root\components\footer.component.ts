import { Component, OnInit, Input, <PERSON><PERSON>hang<PERSON>, <PERSON><PERSON>he<PERSON> } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { } from '@angular/core/src/metadata/lifecycle_hooks';
import { Router } from '@angular/router';

@Component({
  selector: 'footer',
  templateUrl: '../views/footer.html'
})

export class FooterComponent extends BaseComponent implements OnInit, DoCheck {

  @Input()
  currentPage: String = "";

  public isLogin: Boolean = false;
  router: Router
  constructor() {
    super();
  }

  ngOnInit() {
    if (BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0) {
      this.isLogin = true;
    }
  }

  ngDoCheck() {
    if (BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0) {
      this.isLogin = true;
    }
    else {
      this.isLogin = false;
    }
  }

  checkUserType() {
    this.routeOnUrl('/profile/' + BaseComponent.userType);
  }

  homePage() {
    this.router.navigate(['/']);
    window.scroll(0, 0);
  }

}
