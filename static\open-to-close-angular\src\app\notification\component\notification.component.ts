import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { NotificationService } from '@app/notification/service/notification-service';
import { ServiceLocator } from '@app/base/components/service-locator';

@Component({
  selector: 'notification',
  templateUrl: '../views/notification.component.html',
  styleUrls: ['../css/notification.component.css']
})
export class NotificationComponent extends BaseComponent implements OnInit {

public notificationList = [];
public totalNotificationCount = 0;
public notificationIndex = 2;
public itemPerPage:any;
disableLoadMore : Boolean = false;

notificationService : NotificationService;

  constructor() {
    super();
    this.notificationService = ServiceLocator.injector.get(NotificationService);
  }

  ngOnInit() {
    if(this.getPreviousScreen() != '/all-notification'){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/all-notification');

    this.notificationService.getNotification().subscribe(res =>{
      this.notificationList = res['result']['records'];
      this.totalNotificationCount = res['result']['total_records_count'];
      this.itemPerPage = res['result']['items_per_page'];
    },err=>{
      this.errorResponse(err.json());
    });
  }

  loadMoreNotification(index){
    this.disableLoadMore = true;
    this.notificationService.getMoreNotification(index).subscribe(res =>{
      this.disableLoadMore = false;
      this.notificationIndex += 1;
      res['result']['records'].forEach(record => {
        this.notificationList.push(record);
      });
    },err=>{
      this.errorResponse(err.json());
    });
  }

  readNotification(id,notification){
    let notificationIndex = this.notificationList.indexOf(notification);
    let idParams = new URLSearchParams();
    this.checkNotificationType(notification);
    idParams.set('notification_id',id);
    if(notification['is_notification_read'] == false){
      this.notificationService.readNotification(idParams).subscribe(res =>{
        this.notificationList[notificationIndex]['is_notification_read'] = true;
        this.notificationService.setNotificatioDotIcon.emit(true);
      },err=>{
        console.log(err)
      });
    }
  }
}
