import { Injectable }          from '@angular/core';
import * as firebase from 'firebase';
import 'rxjs/add/operator/take';
import { BehaviorSubject } from 'rxjs/BehaviorSubject'
import { BaseComponent } from '@app/base/components/base.component';


@Injectable()
export class MessagingService {

  messaging = firebase.messaging()
  currentMessage = new BehaviorSubject(null)

  constructor() {
   }

   getPermission() {
    this.messaging.requestPermission()
    .then(() => {
      return this.messaging.getToken()
    })
    .then(token => {
      BaseComponent.fcm_token = token;
      // this.updateToken(token)
    })
    .catch((err) => {
      console.log('Unable to get permission to notify.', err);
    });
  }

}
