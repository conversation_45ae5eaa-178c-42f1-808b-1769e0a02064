{"name": "open-to-close-angular", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --prod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^5.2.5", "@angular/cdk": "^5.2.1", "@angular/common": "^5.2.0", "@angular/compiler": "^5.2.0", "@angular/core": "^5.2.11", "@angular/forms": "^5.2.0", "@angular/http": "^5.2.0", "@angular/material": "^5.2.1", "@angular/platform-browser": "^5.2.0", "@angular/platform-browser-dynamic": "^5.2.0", "@angular/router": "^5.2.0", "@angular/service-worker": "^5.2.11", "@ckeditor/ckeditor5-angular": "^1.2.3", "@ckeditor/ckeditor5-build-classic": "^19.0.0", "@ng-select/ng-select": "^0.30.1", "angularfire2": "^5.0.0-rc.6", "braintree-web": "^3.31.0", "core-js": "^2.4.1", "file-saver": "^1.3.8", "firebase": "^4.12.1", "hammerjs": "^2.0.8", "moment": "^2.22.1", "ng2-dnd": "^5.0.2", "ng2-meta": "^4.0.0", "ng2-toastr": "^4.1.2", "ng2-toasty": "^4.0.3", "ngx-chips": "^1.6.7", "ngx-clipboard": "^10.0.0", "ngx-pagination": "^3.1.1", "rxjs": "^5.5.6", "zone.js": "^0.8.19"}, "devDependencies": {"@angular/cli": "^1.7.0", "@angular/compiler-cli": "^5.2.0", "@angular/language-service": "^5.2.0", "@types/jasmine": "~2.8.3", "@types/jasminewd2": "~2.0.2", "@types/node": "~6.0.60", "codelyzer": "^4.0.1", "jasmine-core": "~2.8.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~2.0.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "^1.2.1", "karma-jasmine": "~1.1.0", "karma-jasmine-html-reporter": "^0.2.2", "protractor": "~5.1.2", "ts-node": "~4.1.0", "tslint": "~5.9.1", "typescript": "~2.5.3"}}