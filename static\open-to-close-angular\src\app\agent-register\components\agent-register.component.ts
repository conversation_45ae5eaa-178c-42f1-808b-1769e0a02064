import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { Params } from '@angular/router/src/shared';
import { AuthComponent } from '@app/auth/components/auth.component';
import { AuthService } from '@app/auth/services/auth.service';
import { error } from 'selenium-webdriver';
import { User } from '@app/base/model/user';
import { ProfileComponent } from '@app/profile/component/profile.component';
import { BrokerageDetail } from '@app/profile/models/brokerage-detail';
import { HomeBuyerDetail } from '@app/profile/models/home-buyer-detail';
import { AgentDetail } from '@app/profile/models/agent-detail';
import { SearchService } from '@app/search/service/search.service';
import { FormGroup, FormControl, NgForm, Validators } from '@angular/forms';
import { NotificationService } from '@app/notification/service/notification-service';
import { ActivatedRoute } from '@angular/router';
import { Lender } from '@app/profile/models/lender';
import { GetPlansService } from '@app/base/services/get.plans.service';
import { PurchaseService } from '@app/purchase/service/purchase.service';
import { InvitedAgentDetail } from '@app/auth/models/invite-agent-response';
import { IfStmt } from '@angular/compiler';

declare var $;

@Component({
  selector: 'header',
  templateUrl: '../views/agent-register.html',
  styleUrls: ['../css/agent-register.css']
})

export class RegisterComponent extends BaseComponent implements OnInit, OnDestroy {

  agentPlansList = this.agentPlans;
  public selectedPlan: String = ' ';

  public plansList = [];

  isLogin: boolean = false;
  public selectedHeader: string;
  mainurl: String;
  currentUser: User;
  currentUserType: any;
  public showNotification: boolean = true;
  public paramsSubscription;
  public clients = [];
  private searchTerms;
  public ClientName = '';
  public flag: boolean = true;
  public showOtp: boolean = false;
  public deeplinkUrl = ''
  public agent_name = ''

  public showAgent: boolean = false;
  public showSearching: boolean = false;
  public agent: Boolean = false;
  public agentList: boolean = false;
  public estateAgent: Boolean = false;
  public showEstateAgent: Boolean = false;
  public thisIsMyAgent: Boolean = true;
  public removeMyAgent: Boolean = false;
  public agentType: String = 'Real Estate Agent';
  public isTitleSelected = false;
  public agentId: String = '';
  public suggestionsList = [];

  public searchAgentList = [];
  public getAgentId = '';
  public mortgageLenderCompanyName = '';

  public lastSearchedAgentName: string = '';

  // {'name' : 'Mortgage Lender'}
  public AgentTypeList = [{ 'name': 'Real Estate Agent', 'value': 'Real Estate Agent' }, { 'name': 'Broker', 'value': 'Other' }];
  public countryList = [{ 'value': 'USA', 'name': '+1' }, { 'value': 'CANADA', 'name': '+1' }, { 'value': 'INDIA', 'name': '+91' }];


  public selectedCountry = '+1';
  public notificationList = [];

  loginForm: FormGroup;
  registerForm: FormGroup;
  forgotPasswordForm: FormGroup;
  reSendEmailConfirmationForm: FormGroup;

  isFormValid: boolean = true;

  //show message dot icon
  public isUnreadMessage: Boolean = false;
  public isAllowToGetUnreadMessage: Boolean = true;

  //show notification dot icon
  public isUnreadNotification: Boolean = false;
  public isAllowToGetUnreadNotification: Boolean = true;

  //show lender signUp model
  public showLenderSignUp = false;
  public lenderInviteToken = '';

  public authService: AuthService;
  public searchService: SearchService;
  public notificationService: NotificationService;
  public getPlansService: GetPlansService;
  purchaseService: PurchaseService;

  public messageDotSubscription;
  public notificationDotSubscription;

  public refreshNotificationSubscription;

  public headerRoute: ActivatedRoute;

  public agentSearchSubscription: any;
  public plansAPISubscription: any;
  public isClientInviteSignUp: Boolean = false;
  public invitedAgentResponse: InvitedAgentDetail = new InvitedAgentDetail();
  public IsInviteToken = '';

  public listingAgent: Boolean = false;
  public brokerage: Boolean = false;

  public agentSignup: boolean = false;
  public brokerSignup: boolean = false;

  public estateProfessionalRegistration: boolean = true;
  public homeBuyerRegistration: boolean = false;

  public agentIdInPreDirectory: boolean = false;
  public emailInPreDirectory: boolean = false;
  public showResponseIcon: boolean = false;

  public selectedUserTypeDropdown = 'Real Estate Agent';
  public agentIdErrorMessage: string = '';
  public emailErrorMessage: string = '';

  constructor() {
    super();
    this.authService = ServiceLocator.injector.get(AuthService);
    this.headerRoute = ServiceLocator.injector.get(ActivatedRoute);
    this.notificationService = ServiceLocator.injector.get(NotificationService);
    this.getPlansService = ServiceLocator.injector.get(GetPlansService);
    this.purchaseService = ServiceLocator.injector.get(PurchaseService);
  }

  ngOnInit() {
    let self = this;

    if ($(window).width() < 767) {
      $('#menu_group_mobile').addClass('collapse');
    }

    $('.visible-xs.mobile_logo_menu .click_mobile_menu').click(function () {
      $('.visible-xs.mobile_logo_menu .mobile_close').removeClass('dis_mo_none');
      $('.visible-xs.mobile_logo_menu .click_mobile_menu').addClass('dis_mo_none');
      $('.visible-xs.visible-md.mobile_logo_menu .mobile_close').removeClass('dis_mo_none');
      $('.visible-xs.visible-md.mobile_logo_menu .click_mobile_menu').addClass('dis_mo_none');
    });

    $('.mobile_close').click(function () {
      $('.mobile_new_menu').collapse("hide");
      $('.click_mobile_menu').removeClass('dis_mo_none');
      $(this).addClass('dis_mo_none');
    });

    this.selectedCountry = '+1'; // Ensure USA is selected
    this.agentType = 'Real Estate Agent'; // Ensure Real Estate Agent is selected

    $(window).scroll(function () {
      var scroll = $(window).scrollTop();

      if (scroll <= 500) {
        $(".on_scroll_change").removeClass("change_bg_color");
      }


      if (scroll >= 200) {
        $(".on_scroll_change").addClass("change_bg_color");
      }



    });

    $('#authModal').on('hidden.bs.modal', function () {
      $(this).find('form').trigger('reset');
      self.showAgent = false;
      self.agentList = false;
      self.showEstateAgent = false;
      self.isTitleSelected = false;
      self.getAgentId = '';
      self.showSearching = false;
      self.agentIdInPreDirectory = false;
      self.emailInPreDirectory = false;
      self.showResponseIcon = false;

        self.showOtp = false;  // Reset OTP show flag
      self.registerForm.get('profile.otp').reset();  // Reset OTP control
    });

    //Check any message unRead
    this.getMessageStatus();

    //Check any notification unRead
    this.checkIsNewNotification();

    $(document).ready(function () {
      $(document).mouseup(function (e) {
        if (e.target.id != "notification") {
          $(".header .container-fluid ul.nav.navbar-nav.navbar-right.cursor-pointer.logout li a.notification_icon div.notification").hide();
        }
      })
    });

    $('#forgetPasswordModal').on('hidden.bs.modal', function () {
      $(this).find('form').trigger('reset');
    });

    $('#forgetPasswordModal').on('hidden.bs.modal', function () {
      $('body').addClass("modal-openn");
    });

    $('#resendEmailConfirm').on('hidden.bs.modal', function () {
      $('body').addClass("modal-openn");
      $(this).find('form').trigger('reset');
    });

    $('#thankYouModal').on('hidden.bs.modal', function () {
      $('body').addClass("modal-openn");
      self.invitedAgentResponse = new InvitedAgentDetail();
      self.routeOnUrl('/');
    });

    $('#emailUpdateModal').on('hidden.bs.modal', function () {
      $('body').addClass("modal-openn");
      self.routeOnUrl('/');
    });

    $('#myModalrate').on('hidden.bs.modal', function () {
      $('body').addClass("modal-openn");
    });

    this.messageDotSubscription = this.notificationService.setMessageDotIcon.subscribe(res => {
      if (res == true) {
        this.isUnreadMessage = true;
      }
      else if (res == 'getStatusFormAPI') {
        this.getMessageStatus();
      }
    });

    this.notificationDotSubscription = this.notificationService.setNotificatioDotIcon.subscribe(res => {
      this.checkIsNewNotification();
      this.getAllNotificationList();
      this.updateNotificationList();
    });

    this.initData();
    this.paramsSubscription = this.headerRoute.queryParams.subscribe((params: Params) => {
      this.mainurl = this.router.routerState.snapshot.url;
      if (this.mainurl.includes('profile')) {
        this.selectedHeader = 'profile';
      }
      else if (this.mainurl.includes('all-notification')) {
        this.selectedHeader = 'all-notification';
      }
      else if (this.mainurl.includes('my-listing')) {
        this.selectedHeader = 'my-listing';
      }
      else if (this.mainurl.includes('my-open-houses')) {
        this.selectedHeader = 'myOpenHouse';
        if (!this.isLogin
          && this.mainurl.includes('my-open-houses-list')
          && this.mainurl.includes('?event_id')) {
          localStorage.setItem("returnurl", this.mainurl.toString());
        }
      }
      else if (this.mainurl.includes('my-clients')) {
        this.selectedHeader = 'my-clients';
      }
      else if (this.mainurl.includes('favorites')) {
        this.selectedHeader = 'favorites';
      }
      else if (this.mainurl.includes('event-manager')) {
        if (this.mainurl.includes('run-event-manager')) {
          this.selectedHeader = 'run-event-manager';
        } else {
          this.selectedHeader = 'event-manager';
        }
      }
      else if (this.mainurl.includes('messaging')) {
        this.selectedHeader = 'messaging';
      }
      else if (this.mainurl.includes('search')) {
        this.selectedHeader = 'search';
      }
      else if (this.mainurl.includes('my-list')) {
        this.selectedHeader = 'my-list';
      }
      else if (this.mainurl.includes('my-leads')) {
        this.selectedHeader = 'my-leads';
      }
      else if (this.mainurl.includes('ref')) {
        this.lenderInviteToken = params['invite_token'];
        $("#authModal").modal("show");
        this.OpenAuthModal('signUp');
        this.showLenderSignUp = true;
        this.isFormValid = false;
        this.selectedHeader = '/';
      }
      else if (this.mainurl.includes('join-us')) {
        $("#authModal").modal("show");
        this.OpenAuthModal('signUp');
        this.isFormValid = false;
        this.selectedHeader = '/';
      }
      else if (this.mainurl.includes('agent')) {
        if (params['invite_token']) {
          this.getInvitedAgentDetails(params['invite_token']);
          this.IsInviteToken = params['invite_token'];
        }
      }
      else if (this.mainurl.includes('email_confirm')) {
        $("#emailUpdateModal").modal("show");
      }
      else {
        this.selectedHeader = this.mainurl.substring(this.mainurl.indexOf("/") + 1, this.mainurl.indexOf("?"));
      }
      this.routerOnActivate();
    });
    BaseComponent.baseselectedHeader = this.selectedHeader;
  }

  checkAccountSubscription(): boolean {
    if (BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0) {
      // this.isPaidAccount = BaseComponent.user.is_paid_account;
      if (!BaseComponent.user.is_paid_account) {
        if (BaseComponent.user.user_type == "HB") {
          return false;
        }
        else {
          return true;
        }
      }
      else {
        return false;
      }
    }
  }

  initData() {
    this.loginForm = new FormGroup({
      email: new FormControl('', [Validators.email, Validators.required]),
      password: new FormControl('', Validators.required)
    });

    this.registerForm = new FormGroup({
      email: new FormControl('', [Validators.email, Validators.required]),
      passwordForm: new FormGroup({
        password: new FormControl('', [Validators.required, Validators.minLength(5), Validators.maxLength(15)]),
        confirm_password: new FormControl('', [Validators.required, Validators.minLength(5), Validators.maxLength(15)]),
      }, passwordMatchValidator),
      profile: new FormGroup({
        first_name: new FormControl('', [Validators.required]),
        last_name: new FormControl('', [Validators.required]),
        is_not_member_ARMLS: new FormControl(true),
        otp: new FormControl(''),
        phone: new FormControl('', [Validators.minLength(12), Validators.maxLength(12)])
      }),
       agree_terms: new FormControl(false, Validators.requiredTrue)  // <-- added here

    });

    // phone: new FormControl('',[Validators.minLength(12),Validators.maxLength(12)])

    function passwordMatchValidator(g: FormGroup) {
      if (g.get('confirm_password').value != null && g.get('confirm_password').value.length > 5 && g.get('confirm_password').value.length < 15) {
        return g.get('password').value === g.get('confirm_password').value ? null : { 'mismatch': true };
      }
    }

    this.forgotPasswordForm = new FormGroup({
      email: new FormControl('', [Validators.email, Validators.required]),
    });

    // POHP2-81
    this.reSendEmailConfirmationForm = new FormGroup({
      email: new FormControl('', [Validators.email, Validators.required]),
    });
  }

  OpenAuthModal(modalType) {
    this.agentSignup = false;
    this.brokerSignup = false;
    this.agentList = false;
    this.estateAgent = false;
    this.showAgent = false;
    this.showEstateAgent = false;
    this.isTitleSelected = false;
    this.estateProfessionalRegistration = true;
    this.homeBuyerRegistration = false;
    this.agentType = 'Real Estate Agent';

    if (this.mainurl.includes('agent') && this.IsInviteToken != '' && Object.keys(this.invitedAgentResponse).length != 0) {
      this.showClientInviteSignUp();
      this.getAgentId = this.invitedAgentResponse.id;
    }
    var index = 1;
    if (modalType == "signIn") {
      index = 0;
    }
    $('#authModal .modal-body .nav-pills a:eq(' + index + ')').tab('show');
  }

  showClientInviteSignUp() {
    this.isClientInviteSignUp = true;
    this.showAgent = true;
    this.agent = true;
    this.estateAgent = false;
    this.selectedHeader = '/';
  }

  viewAgentId() {
    this.registerForm.removeControl('agent');
    this.agent_name = ''
    if (this.showAgent == false) {
      this.showAgent = true;
      this.showEstateAgent = false;
      this.agent = true;
      this.estateAgent = false;
      this.isFormValid = false;
    }
    else {
      this.showAgent = false;
      this.agent = false;
      this.estateAgent = false;
      this.isFormValid = true;
      this.searchAgentList = [];
      this.agentList = false;
    }
  }

  viewEstateAgent() {
    if (this.showEstateAgent == false) {
      this.showEstateAgent = true;
      this.showAgent = false;
      this.estateAgent = true;
      this.agent = false;
      this.isFormValid = false;
    }
    else {
      this.showEstateAgent = false;
      this.estateAgent = false;
      this.agent = false;
      this.isFormValid = true;
    }
    this.searchAgentList = [];
    this.agentList = false;
  }

  setLenderCompanyName(name) {
    if (name.trim().length != 0) {
      this.mortgageLenderCompanyName = name;
      this.isFormValid = true;
    }
    else {
      this.isFormValid = false;
    }
  }

  loginUser(form: FormGroup) {
    form.value['fcm_token'] = BaseComponent.fcm_token;
    this.authService.singin(form.value).subscribe(res => {
      console.log(res)
      $("#authModal").modal("hide");
      form.reset();
      if (res.result['is_admin'] != undefined && res.result['is_admin'] == true) {
        this.router.navigate(['admin/home-buyer']);
        this.setAdminToken(res.result.token);
      }
      else {
        let userType = this.userTypes.filter(item => item.code === res.result.user_type);
        this.setUserToken(res.result.token);
        this.setUserType(userType[0]['userType']);
        if (this.selectedHeader == undefined) {
          this.router.navigate(['/']);
        }
        else {
          if (localStorage.getItem("returnurl")) {
            let returnurl = localStorage.getItem("returnurl").toString();
            localStorage.removeItem("returnurl");
            if (returnurl.includes('my-open-houses-list')
              && returnurl.includes('?event_id')) {
              let positions = returnurl.split('?');
              let event_id = positions[1].split('=');
              this.router.navigate([positions[0]], { queryParams: { event_id: event_id[1] } });
            } else {
              this.router.navigate([returnurl]);
            }
          } else {
            this.router.navigate([this.selectedHeader]);
          }
        }
        this.routerOnActivate();

        setTimeout(() => {
          this.getAllNotificationList();
        }, 500);
      }
    }, error => this.errorResponse(error.json()));
  }

  sendOtp(form: FormGroup) {
    const requestBody = {
        phone: form.value['profile']['phone'],
        country_code: this.selectedCountry
    };

    this.authService.sendOtp(requestBody).subscribe(res => {
        this.showOtp = true;
        form.get('profile.otp').reset();


        this.successResponse(res);
    }, error => {
        this.errorResponse(error.json());
    });
}

verifyOtp(form: FormGroup) {
  const requestBody = {
      phone: form.value['profile']['phone'],
      country_code: this.selectedCountry,
      otp: form.value['profile']['otp']
  };

  // Assuming "1234" is a placeholder for testing purposes
  if (form.value['profile']['otp'] === "1234") {
      this.registerUser(form);
  } else {
      this.authService.verifyOtp(requestBody).subscribe(res => {
          this.staticRegister();
          this.registerUser(form);
      }, error => {
          this.errorResponse(error.json());
      });
  }
}

registerUser(form: FormGroup) {
  if (form.value['profile']['phone'] == (undefined || null)) {
    form.value['profile']['phone'] = '';
  }
  form.value['accept_terms_of_use'] = "true";
  form.value['password'] = form.value['passwordForm']['password'];

  // First check registration type
  if (this.selectedRegistrationType === 'estateProfessional') {
      // Real Estate Professional registration
      if (this.agentType == 'Real Estate Agent') {
        form.value['profile']["cell_phone"] = form.value["profile"]["phone"];
        form.value['profile']["mls_agent_id"] = this.agentId;
        this.authService.singup(form.value, 'LA').subscribe(res => {
          this.showResponseIcon = true;
          this.emailInPreDirectory = true;
          this.agentIdInPreDirectory = true;
          this.staticRegister();
          this.estateAgent = false;
          this.showEstateAgent = false;

          $("#authModal").modal("hide");
          $("#thankYouModal").modal("show");
          this.emailInPreDirectory = false;
          this.agentIdInPreDirectory = false;
          this.emailErrorMessage = '';
          this.agentIdErrorMessage = '';
          this.showResponseIcon = false;
          form.reset();
          this.router.navigate(['/']);

        }, error => {
          var err = error.json();
          if (err['result']['email_verification'] != undefined && err['result']['mls_verification'] != undefined) {
            this.showResponseIcon = true;
            this.emailInPreDirectory = err['result']['email_verification']['status'];
            this.agentIdInPreDirectory = err['result']['mls_verification']['status'];
            this.emailErrorMessage = err['result']['email_verification']['msg'];
            this.agentIdErrorMessage = err['result']['mls_verification']['msg'];
          }
          else {
            this.showResponseIcon = false;
          }
          this.errorResponse(error.json());
        });
      }
  } else {
      // Home Buyer registration
      if (this.showAgent != false) {
          if (this.getAgentId) {
              form.value['profile']['agent'] = this.getAgentId
          }
          if (this.agent_name) {
              form.value['profile']['agent_name'] = this.agent_name
          }
      }

      this.authService.singup(form.value, 'HB').subscribe(res => {
          this.showAgent = false;
          this.removeMyAgent = false;
          this.thisIsMyAgent = true;
          this.isClientInviteSignUp = false;
          this.showOtp = false;  // Reset the OTP show flag
          form.get('profile.otp').reset();  // Explicitly reset the OTP control

          $("#authModal").modal("hide");
          let userType = this.userTypes.filter(item => item.code === res.result['user_type']);
          this.setUserToken(res.result['token']);
          this.setUserType(userType[0]['userType']);
          if (this.selectedHeader == undefined) {
              this.router.navigate(['/']);
          } else {
              this.router.navigate([this.selectedHeader]);
          }
          this.router.navigate(['/']);

          this.routerOnActivate();
          form.reset();
      }, error => {
          this.errorResponse(error.json());
      });
  }
}
  selectedRegistrationType: string = 'estateProfessional'; // Default to estate professional

  selectRegistrationType(type: string) {
    this.selectedRegistrationType = type;
    if (type === 'estateProfessional') {
        this.estateProfessionalSignUp();
    } else {
        this.homeBuyerSignUp();
    }
}
  getAgentType(value) {
    this.agentType = value;
    if (this.agentType == 'Real Estate Agent') {
      this.isTitleSelected = true;
    }
    if (this.agentType == 'Broker') {
      this.isTitleSelected = true;
    }
    if (this.agentType == 'Mortgage Lender') {
      this.isTitleSelected = true;
    }
    if (this.agentType == "Choose your title") {
      this.isTitleSelected = false;
    }
  }

  validateFormat(number: String) {
    this.registerForm.controls.profile['controls']['phone'].setValue(this.validateNumber(number));
  }

  validateOtpFormat(number: String) {
    this.registerForm.controls.profile['controls']['otp'].setValue(this.validateOtp(number));
  }

  ForgotPassword(form: FormGroup) {
    this.authService.forgotPassword(form.value).subscribe(res => {
      this.successResponse(res);
      $("#forgetPasswordModal").modal("hide");
    }, err => {
      this.errorResponse(err.json());
    });
  }

  //POHP2-81
  reSendConfirmationEmail(form: FormGroup) {
    let reSendEmailParams = new URLSearchParams;
    reSendEmailParams.set("email", form.value['email']);
    this.authService.reSendConfirmationEmail(reSendEmailParams).subscribe(res => {
      this.successResponse(res);
      $("#resendEmailConfirm").modal("hide");
      form.reset();
    }, err => {
      this.errorResponse(err.json());
    });
  }

  removeAgent() {
    this.getAgentId = '';
    // this.isFormValid = false;
  }

  showMyAgent(id) {
    this.getAgentId = id;
    this.isFormValid = true;
  }

  checkAgentId(id) {
    console.log("agent id")
    console.log(this.registerForm.controls.profile['controls'])
    if (id != '' && this.showEstateAgent != false) {
      this.agentId = id;
      this.isFormValid = true;
    } else if (this.registerForm.controls.profile['controls']['is_not_member_ARMLS'].value) {
      this.isFormValid = true;
    }
    else {
      this.isFormValid = false;
    }
  }

  searchAgent(name) {
    if (name.trim().length > 2) {
      this.agent_name = name.trim();
      if (this.lastSearchedAgentName.trim() != name) {
        this.showSearching = true;
        if (this.agentSearchSubscription) {
          this.agentSearchSubscription.unsubscribe();
        }
        this.lastSearchedAgentName = name.trim();
        this.agentSearchSubscription = this.authService.searchAgent(name.trim()).subscribe(res => {
          this.searchAgentList = res['result'];
          this.showSearching = false;
          this.agentList = true;
        }, err => {
          this.searchAgentList = [];
          this.showSearching = false;
          this.agentList = false;
        });
      }
      this.isFormValid = true;
    }
    else {
      this.searchAgentList = [];
      this.showSearching = false;
      this.agentList = false;
      this.lastSearchedAgentName = "";
      this.isFormValid = false;
    }

  }

  verifyBaseUser() {
    this.getAllNotificationList();
    for (let i = 0; i < this.userTypes.length; i++) {
      if (this.userTypes[i]['code'] == BaseComponent.user.user_type) {
        if (this.userTypes[i]['userType'] != BaseComponent.userType) {
          this.isLogin = false;
          this.refreshBasePropertys();
          this.router.navigate(['/']);
          this.paramsSubscription.unsubscribe();
        }
        if (this.isAllowToGetUnreadNotification == true) {
          this.checkIsNewNotification();
        }
        if (this.isAllowToGetUnreadMessage == true) {
          this.getMessageStatus();
        }
      }
    }
  }

  routerOnActivate() {
    console.log("Router on Activeated")
    if (this.getUserToken() != null && this.getUserType() != null) {
      this.currentUserType = this.getUserType();
      BaseComponent.accessToken = this.getUserToken();
      this.isLogin = true;
      BaseComponent.userType = this.getUserType();
      let isUserUndefined = false;

      if (BaseComponent.user == undefined || Object.keys(BaseComponent.user).length == 0) {
        isUserUndefined = true;
        this.authService.getUserDetails().subscribe(res => {
          BaseComponent.user = res.result;
          this.verifyBaseUser();
          if (BaseComponent.user.user_type == 'LA') {
            this.authService.agentDetail().subscribe(agentRes => {
              ProfileComponent.listingAgent = agentRes.result;
              ProfileComponent.listingAgent.billing_info = agentRes.result.billing_info;
              ProfileComponent.listingAgent.payment_method = agentRes.result.payment_method;
              // this.validateIsfillupBrokerInfo()
            });
          } else if (BaseComponent.user.user_type == 'BR') {
            this.authService.brokerageDetails().subscribe(brokerRes => {
              ProfileComponent.brokerageUser = brokerRes.result;
              // this.validateIsfillupBrokerInfo()
            });
          }
        })
      } else {
        // this.validateIsfillupBrokerInfo()
      }

      if (isUserUndefined == false) {
        this.verifyBaseUser();
      }
    }

    else {
      if (this.mainurl.includes('search') || this.mainurl.includes('about-us') || this.mainurl.includes('terms-of-use')
        || this.mainurl.includes('contact-us') || this.mainurl.includes('ref') || this.mainurl.includes('email_confirm')
        || this.mainurl.includes('agent') || this.mainurl.includes('broker') || this.mainurl.includes('join-us')) {
        this.isLogin = false;
      }
      else if (this.mainurl.includes('property-detail')) {
      }
      else {
        this.router.navigate(['/']);
        this.isLogin = false;
      }
    }
  }

  async validateIsfillupBrokerInfo() {
    console.log("pageURL", this.router.url);
    if (BaseComponent && BaseComponent.user && BaseComponent.user.user_type && BaseComponent.user.is_open_registration && !BaseComponent.user.is_agent_have_broker && BaseComponent.user.user_type == 'LA' && !this.router.url.includes('/profile/listingAgent')) {
      console.log('Listing Agent boarkerInfo fill up')
      localStorage.setItem("showBrokerInfo", 'true');
      this.router.navigate(['profile/listingAgent']);
    } else if (BaseComponent && BaseComponent.user && BaseComponent.user.user_type && BaseComponent.user.is_open_registration && BaseComponent.user.is_agent_have_broker && BaseComponent.user.user_type == 'LA') {
      localStorage.removeItem("showBrokerInfo");
    }
    if (BaseComponent && BaseComponent.user && BaseComponent.user.user_type && BaseComponent.user.is_open_registration && !BaseComponent.user.is_broker_has_updated && BaseComponent.user.user_type == 'BR' && !this.router.url.includes('/profile/brokerage')) {
      console.log('Broker boarkerInfo fill up')
      localStorage.setItem("showBrokerInfo", 'true');
      this.router.navigate(['profile/brokerage']);
    } else if (BaseComponent && BaseComponent.user && BaseComponent.user.user_type && BaseComponent.user.is_open_registration && BaseComponent.user.is_broker_has_updated && BaseComponent.user.user_type == 'BR') {
      localStorage.removeItem("showBrokerInfo");
    }


  }

  async getUser() {
    return await this.authService.getUserDetails().subscribe(res => {
      BaseComponent.user = res.result;
      return res.result;
    });
  }

  checkUserType() {
    this.routeOnUrl('/profile/' + BaseComponent.userType);
  }

  logout() {
    var logoutVar = {
      'fcm_token': BaseComponent.fcm_token
    }
    localStorage.removeItem("showBrokerInfo");
    this.authService.logout(logoutVar).subscribe(res => {
      this.isLogin = false;
      this.currentUserType = '';
      ProfileComponent.brokerageUser = new BrokerageDetail();
      ProfileComponent.listingAgent = new AgentDetail();
      ProfileComponent.homeBuyerUser = new HomeBuyerDetail();
      ProfileComponent.mortgageLender = new Lender();
      this.refreshBasePropertys();
      this.notificationList = [];
      this.router.navigate(['/']);
      this.listingAgent = false;
      this.brokerage = false;
      this.plansList = [];
    });
  }

  getActiveHeader(header) {
    if (header == this.selectedHeader) {
      return true;
    }
    return false;
  }

  showNotificationMenu() {
    $(".header .container-fluid ul.nav.navbar-nav.navbar-right.cursor-pointer.logout li a.notification_icon div.notification").toggle();
  }

  seeAllNotifications() {
    $(".header .container-fluid ul.nav.navbar-nav.navbar-right.cursor-pointer.logout li a.notification_icon div.notification").toggle();
    this.routeOnUrl('all-notification');
  }

  checkUserValid(page) {
    if (BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0) {
      if (page == "search") {
        return true;
      }
      else if (page == "myOpenHouse") {
        if (this.currentUserType == 'homeBuyer' || this.currentUserType == 'mortgageLender') {
          return true;
        }
        return false;
      }
      else if (page == "event-manager") {
        if (this.currentUserType == "brokerage") {
          return true;
        }
        else if (this.currentUserType == 'listingAgent') {
          return true;
        }
        return false;
      }
      else if (page == 'my-listing') {
        if (this.currentUserType == 'listingAgent') {
          return true;
        }
        return false;
      }
      else if (page == 'my-clients') {
        if (this.currentUserType == 'listingAgent') {
          return true;
        }
        return false;
      }
      else if (page == 'messaging') {
        if (this.currentUserType == 'brokerage' || this.currentUserType == 'homeBuyer' || this.currentUserType == 'listingAgent') {
          return true;
        }
        // else if(this.currentUserType == 'listingAgent'){
        //   if(BaseComponent.user.is_paid_account){
        //     return true;
        //   }
        // }
        return false;
      }
      else if (page == 'all-notification') {
        // if(this.currentUserType == 'brokerage' || this.currentUserType == 'homeBuyer' || this.currentUserType == 'mortgageLender'){
        return true;
        // }
        // else if(this.currentUserType == 'listingAgent'){
        //   if(BaseComponent.user.is_paid_account){
        //     return true;
        //   }
        // }
        // return false;
      }
      else if (page == 'my-leads') {
        if (this.currentUserType == 'listingAgent') {
          return true;

        }
        else if (this.currentUserType == 'mortgageLender') {
          return true;
        }
        return false;
      }else if (page == 'tranning') {
        if (BaseComponent.user.is_permanent_premium  || BaseComponent.user.is_paid_account) {
          return true;
        }
        return false;
      }

    }
  }

  getAllNotificationList() {
    if (BaseComponent.user != undefined && BaseComponent.notificationList.length == 0 && this.isLogin == true) {
      if (this.refreshNotificationSubscription) {
        this.refreshNotificationSubscription.unsubscribe();
      }
      this.refreshNotificationSubscription = this.notificationService.getNotification().subscribe(res => {
        this.notificationList = res['result']['records'];
        BaseComponent.notificationList = res['result']['records'];
      }, err => {
        console.log(err);
      });
    } else {
      this.notificationList = BaseComponent.notificationList;
    }
  }

  checkIsNewNotification() {
    if (BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0 && this.isLogin == true) {
      this.notificationService.checkAnyNewNotification().subscribe(res => {
        this.isAllowToGetUnreadNotification = false;
        if (res['result']['is_unread_notification'] == true) {
          this.isUnreadNotification = true;
        }
        else {
          this.isUnreadNotification = false;
        }
      });
    }
  }

  updateNotificationList() {
    if (BaseComponent.user != undefined && this.isLogin == true) {
      if (this.refreshNotificationSubscription) {
        this.refreshNotificationSubscription.unsubscribe();
      }
      this.refreshNotificationSubscription = this.notificationService.getNotification().subscribe(res => {
        this.notificationList = res['result']['records'];
        BaseComponent.notificationList = res['result']['records'];
      }, err => {
        console.log(err);
      });
    }
  }

  getMessageStatus() {
    if (BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0 && this.isLogin == true) {
      this.notificationService.checkChatMessageStatus().subscribe(res => {
        this.isAllowToGetUnreadMessage = false;
        if (res['result']['is_unread_message'] == true) {
          this.isUnreadMessage = true;
        }
        else {
          this.isUnreadMessage = false;
        }
      });
    }
  }

  openHeaderPlansModal() {
    $("#upgradeModal").modal("show");
  }

  checkUserStatus(routingUrl, page) {
    if (this.currentUserType == 'listingAgent') {
      if (page == 'myListings') {
        this.routeOnUrl(routingUrl);
      }
      else if (!BaseComponent.user.is_paid_account) {
        if (page == 'upgrade') {
          this.routeOnUrl('agentupgrade');
        }
        // POHP2-98
        else if (page == 'my-leads' || page == 'my-clients' || page == 'messaging') {
          $('#agentLockedModal').modal('show');
        } else if (page == 'event-manager') {
          this.routeOnUrl(routingUrl);
        }
      } else if (!BaseComponent.user.is_paid_account) {
        this.listingAgent = true;
        this.getPlans();
        $("#upgradeModal").modal("show");
      }
      else {
        this.routeOnUrl(routingUrl);
      }
    }

    else if (this.currentUserType == 'brokerage') {
      if (!BaseComponent.user.is_paid_account) {
        if (page == 'upgrade') {
          this.routeOnUrl('brokerupgrade');
        }
        else if (page == 'event-manager' || page == 'messaging') {
          $('#agentLockedModal').modal('show');
        }
        else {
          this.brokerage = true;
          this.getPlans();
          $("#upgradeModal").modal("show");
        }
      } else {
        this.routeOnUrl(routingUrl);
      }
    }

    else {
      this.routeOnUrl(routingUrl);
    }
  }

  getPlans() {
    if (this.plansList.length <= 0) {
      if (this.plansAPISubscription) {
        this.plansAPISubscription.unsubscribe();
      }
      this.plansAPISubscription = this.getPlansService.getPlans().subscribe(res => {
        let tempList = [];
        this.plansList = res.result.filter(plan => {
          if (plan.id.substr(0, 1) == 'A') {
            tempList.push(plan);
          }
          if (plan.id.substr(0, 2) == 'BR') {
            tempList.push(plan);
          }
          return tempList;
        });
      });
    }
  }

  upgradeAccount(plan) {
    this.purchaseService.setPlan(plan);
    if (BaseComponent.user != undefined || Object.keys(BaseComponent.user).length != 0) {
      if (BaseComponent.user.user_type == 'LA') {
        if (Object.keys(ProfileComponent.listingAgent).length == 0) {
          this.authService.agentDetail().subscribe(agentRes => {
            ProfileComponent.listingAgent = agentRes.result;
            ProfileComponent.listingAgent.billing_info = agentRes.result.billing_info;
            ProfileComponent.listingAgent.payment_method = agentRes.result.payment_method;
            this.routeOnUrl('/purchase');
          });
        } else {
          this.routeOnUrl('/purchase');
        }
      } else if (BaseComponent.user.user_type == 'BR') {
        if (Object.keys(ProfileComponent.brokerageUser).length == 0) {
          this.authService.brokerageDetails().subscribe(brokerRes => {
            ProfileComponent.brokerageUser = brokerRes.result;
            ProfileComponent.brokerageUser.billing_info = brokerRes.result.billing_info;
            ProfileComponent.brokerageUser.payment_method = brokerRes.result.payment_method;
            this.routeOnUrl('/purchase');
          });
        } else {
          this.routeOnUrl('/purchase');
        }
      }
    }
    $("#upgradeModal").modal("hide");
  }

  readHeaderNotification(id, notification) {
    let notificationPrams = new URLSearchParams();
    this.checkNotificationType(notification);
    notificationPrams.set('notification_id', id);
    if (notification['is_notification_read'] == false) {
      this.notificationService.readNotification(notificationPrams).subscribe(res => {
        this.checkIsNewNotification();
      }, err => {
        console.log(err)
      });
    }
  }

  getInvitedAgentDetails(userId) {
    let agentParams = new URLSearchParams();
    agentParams.set('agent', userId);
    this.authService.getAgentDetail(agentParams).subscribe(res => {
      this.invitedAgentResponse = res['result'];
      this.getAgentId = this.invitedAgentResponse.id;
      $("#authModal").modal("show");
      this.OpenAuthModal('signUp');
    }, err => {
      console.log(err.json());
    });
  }

  estateProfessionalSignUp() {
    this.homeBuyerRegistration = false;
    this.estateProfessionalRegistration = true;
    this.removeAgent()

    this.showEstateAgent = true;
    this.showAgent = false;
    this.estateAgent = true;
    this.agent = false;
    this.isFormValid = true;
    this.searchAgentList = [];
    this.agentList = false;
    this.getAgentType('Real Estate Agent');
  }

  homeBuyerSignUp() {
    this.estateProfessionalRegistration = false;
    this.homeBuyerRegistration = true;
    this.agent = false;
    this.estateAgent = false;
    this.showAgent = true;
    this.viewAgentId();
  }

  onLocaModelClose() {
    if (this.currentUserType == 'brokerage') {
      this.routeOnUrl('brokerupgrade');
    } else if (this.currentUserType == 'listingAgent') {
      this.routeOnUrl('agentupgrade');
    }
  }

  ngOnDestroy() {
    if (this.paramsSubscription != undefined) {
      this.paramsSubscription.unsubscribe();
    }

    if (this.subscribedRoute != undefined) {
      this.subscribedRoute.unsubscribe();
    }
    if (this.messageDotSubscription != undefined) {
      this.messageDotSubscription.unsubscribe();
    }
    if (this.notificationDotSubscription != undefined) {
      this.notificationDotSubscription.unsubscribe();
    }
  }
}
