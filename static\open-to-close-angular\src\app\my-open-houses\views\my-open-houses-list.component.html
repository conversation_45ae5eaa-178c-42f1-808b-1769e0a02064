<div>
    <header></header>
</div>

<div>
    <search-bar (searchObjEvent)="getSearchObj($event)" [searchFrom]="'openHouseListView'" [pageNo]='searchPageNo' [currentPage]="'listingAgent'" [isListViewScreen]='true'></search-bar>
    <div class="myclient_page My_Listings">
            <div class="container">
                <div class="ls_group Favorites_page">
                    <div class="title_group ml_zero">
                        <div class="title pull-left">My List</div>
                        <a class="cursor-pointer" (click)="mapView()"><img src="{{imagePrefix}}Icon.png" class="Icon_png" alt=""></a>
                    </div>

                </div>
                <div id="MyListTable" class="my_client_table_group">
                    <div class="myclient_navbar">
                        <ul>
                        <li (click)="onTabChange('upcoming')" class="active" data-toggle="pill" href="#menu1" >Upcoming</li>
                        <li (click)="onTabChange('checkIns')"  data-toggle="pill" href="#menu3" >Past</li>
                        </ul>
                    </div>
                    <div class="tab-content">
                    <div id="menu1" class="event-list tab-pane fade in active table-responsive selected_saved">
                        <div class="No_matches" *ngIf="showUPLoader == true">
                            <div class="loader">
                            <div class="message">Loading...</div>
                            <div class="dots"><div class="center"></div></div>
                            </div>
                        </div>
                        <div *ngIf="upcomingEventList.length == 0 && showUPLoader == false" class="No_matches">
                            <div class="title">No Events</div>
                            <div class="text">You have no upcoming events in your list matching your search criteria.</div>
                        </div>
                        <div *ngIf="upcomingEventList.length != 0" class="property-list-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th (click)="eventSortting('UP', 'PR')">Property <img id="UP_PR"src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="eventSortting('UP', 'ET')">Type <img id="UP_ET" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="eventSortting('UP', 'DT')">Date <img id="UP_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="eventSortting('UP', 'OA')">Open House Agent<img id="UP_OA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="eventSortting('UP', 'OS')" colspan="4">Response <img id="UP_OS" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let event of upcomingEventList;let i = index">
                                <td (click)="propertyDetail(event.property_id)" class="cursor-pointer">
                                    <span class="vertical-align-top"><img *ngIf="event.property_file != ''" src="{{event.property_file}}"  class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""></span>
                                    <span class="vertical-align-top"><img *ngIf="event.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""></span>
                                    <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{event.address}}<br></span>{{event.location}}</div>
                                </td>

                                <td>
                                    <div [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">{{event?.event_type_msg}}</div>
                                </td>

                                    <td><span class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}} <br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>

                                    <td>
                                    <span *ngIf="event.open_house_agent_image != ''"><img [src]="event.open_house_agent_image" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                    <span *ngIf="event.open_house_agent_image == ''"><img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                    <div class="dis_inline po_rel"><span class="dark font_semibold u-name-table">{{event.open_house_agent_name}} <br></span></div>
                                </td>

                                    <td>
                                    <span class="font_semibold my-list-status">
                                        {{event.open_house_status_msg}}
                                    </span>
                                </td>

                                    <td class="action-view">
                                    <div class="save_notes margin_zero" (click)="openEventModel(event)">Event Details</div>
                                    </td>

                                    <td class="action-option">
                                    <div class="open_click_menu"  (click)="openMenu(i,event.event_id)">
                                        <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                        <ul id="oh_{{i}}_{{event.event_id}}" class="click_menu_open events">
                                            <li class="cursor-pointer option-menu" (click)="mapDirections(event.latitude,event.longitude)">Directions</li>
                                            <li class="cursor-pointer option-menu" (click)="propertyDetail(event.property_id)">Property Detail</li>
                                            <li *ngIf="currentUserId != event.open_house_agent_id"  (click)="contactOpenHouseAgent(event)" class="cursor-pointer option-menu" >Contact Open House Agent</li>
                                        </ul>
                                    </div>
                                </td>
                                </tr>
                            </tbody>
                        </table>
                        <div *ngIf="upcTotalCount > upItemPerPage &&  upcTotalCount != upcomingEventList.length" class="new_form_group load_more_btn">
                            <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreOpenHouseEventList('UP', upIndex)" value="Load More">
                        </div>
                        </div>
                    </div>

                    <div id="menu3" class="tab-pane table-responsive selected_saved">
                        <div class="No_matches" *ngIf="showCKLoader == true">
                            <div class="loader">
                            <div class="message">Loading...</div>
                            <div class="dots"><div class="center"></div></div>
                            </div>
                        </div>
                        <div *ngIf="checkedInEventList.length == 0 && showCKLoader == false" class="No_matches">
                            <div class="title">No Events</div>
                            <div class="text">You have no past events in your list matching your search criteria.</div>
                        </div>
                        <div *ngIf="checkedInEventList.length != 0" class="property-list-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th (click)="eventSortting('CH', 'PR')">Property <img id="CH_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="eventSortting('CH', 'ET')">Type <img id="CH_ET" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="eventSortting('CH', 'DT')">Date <img id="CH_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="eventSortting('CH', 'RA')">Rating<img id="CH_RA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="eventSortting('CH', 'LS')" colspan="4">Status <img id="CH_LS" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let event of checkedInEventList;let j = index">
                                    <td (click)="propertyDetail(event.property_id)" class="cursor-pointer">
                                        <span class="vertical-align-top"><img *ngIf="event.property_file != ''" src="{{event.property_file}}"  class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""></span>
                                        <span class="vertical-align-top"><img *ngIf="event.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""></span>
                                        <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{event.address}}<br></span>{{event.location}}</div>
                                    </td>

                                    <td>
                                        <div [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">{{event?.event_type_msg}}</div>
                                    </td>
                                    <td><span class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}} <br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>

                                    <td>
                                        <span *ngIf="event.property_rate.rating == '0'"><img src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown.png" class="symbols-glyph-checkin-thumbsup dis_inline" alt=""></span>
                                        <span *ngIf="event.property_rate.rating == '1'"><img src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png" class="symbols-glyph-checkin-thumbsup dis_inline" alt=""></span>
                                    </td>
                                    <td class="font_semibold my-list-status">{{event.property_status}}</td>
                                    <td class="action-view">
                                        <div class="save_notes margin_zero" (click)="reviewRatings(event)">Review</div>
                                    </td>
                                    <!-- <td>
                                        <div class="open_click_menu"  (click)="openMenu(j,event.event_id)">
                                            <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                            <ul id="oh_{{j}}_{{event.event_id}}" class="click_menu_open events">
                                                <li class="cursor-pointer option-menu" (click)="eventDetailView(event,'past')">Event Detail</li>
                                                <li class="cursor-pointer option-menu" (click)="propertyDetail(event.property_id)">Property Detail</li>
                                                <li *ngIf="currentUserId != event.open_house_agent_id"  (click)="contactOpenHouseAgent(event)" class="cursor-pointer option-menu" >Contact Open House Agent</li>
                                            </ul>
                                        </div>
                                    </td> -->
                                </tr>
                            </tbody>
                        </table>
                            <div *ngIf="checkedInTotalCount > chItemPerPage &&  checkedInTotalCount != checkedInEventList.length" class="new_form_group load_more_btn">
                                <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreOpenHouseEventList('CH', chIndex)" value="Load More">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<add-event></add-event>
<event-modal (setPropertyLatestInfo)="UpdatePropertyInfo($event)"></event-modal>

<div>
    <footer></footer>
</div>
