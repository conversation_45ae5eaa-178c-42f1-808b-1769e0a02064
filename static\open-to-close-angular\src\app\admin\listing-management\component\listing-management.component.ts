import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ListingManagementService } from '@app/admin/listing-management/service/listing-mange-services';
import { ServiceLocator } from '@app/base/components/service-locator';

declare var $;

@Component({
  selector: 'listing-management',
  templateUrl: '../views/listing-management.component.html',
  styleUrls: ['../css/listing-management.component.css','../../users/css/users.component.css']
})
export class ListingManagementComponent extends BaseComponent implements OnInit {

  public currentMenuId;
  public currentMenuIndex;

  public searchAgentList=[];
  public showAgentList = false;

  public searchBrokeragetList=[];
  public showBrokeragetList = false;

  public selectedListingAgent = '';
  public tempSelectedListingAgent = '';
  public selectedListingAgentId = '';
  public selectedBroker = '';
  public tempSelectedBroker = '';
  public selectedBrokerId = '';
  public isSelectedListingAgent : Boolean = false;
  public propertyStatus = '';

  listingService : ListingManagementService;

  listingSearchParms: URLSearchParams = new URLSearchParams();
  public pageCount: number = 1;
  public listingList = [];
  public totalRecordsCount : number;
  public itemsPerPage : number;

  public ListingStatus = ['Active','PRE-MLS/Coming Soon','Pending'];

  constructor() {
    super();
    this.listingService = ServiceLocator.injector.get(ListingManagementService);
   }

  ngOnInit() {
    $("body").addClass("admin-body");
    this.getPageChange(1);
    let self = this;
    $(document).ready(function()
    {
      $(document).mouseup(function(e)
      {
        if(self.currentMenuId != undefined && self.currentMenuIndex != undefined){
          $("#user_"+self.currentMenuIndex+"_"+self.currentMenuId).hide();
          self.currentMenuId = undefined;
          self.currentMenuIndex = undefined;
        }
      });
    });
  }

  openMenu(index,id){
    this.currentMenuIndex = index;
    this.currentMenuId = id;
    $("#user_"+index+"_"+id).toggle();
  }

  deleteListing(){
    $("#deleteListingModal").modal("show");
  }

  searchListing(mlsId,address,parcelId){
    if(mlsId == '' && address == '' && this.selectedListingAgentId == '' && this.selectedBrokerId == ''
     && parcelId == '' && this.propertyStatus == ''){
      this.listingSearchParms.set('mls_id',"");
      this.listingSearchParms.set('address_city_state',"");
      this.listingSearchParms.set('agent',"");
      this.listingSearchParms.set('brokerage',"");
      this.listingSearchParms.set('parcel_id',"");
      this.listingSearchParms.set('status',"");
      this.getPageChange(1);
    }
    else{
      if(mlsId != ''){
        this.listingSearchParms.set('mls_id',mlsId);
      }
      else{
        this.listingSearchParms.delete('mls_id');
      }
      if(address != ''){
        this.listingSearchParms.set('address_city_state',address.toString());
      }
      else{
        this.listingSearchParms.delete('address_city_state');
      }
      if(this.selectedListingAgentId != ''){
        this.listingSearchParms.set('agent',this.selectedListingAgentId.toString());
      }
      else{
        this.listingSearchParms.delete('agent');
      }
      if(this.selectedBrokerId != ''){
        this.listingSearchParms.set('brokerage',this.selectedBrokerId.toString());
      }
      else{
        this.listingSearchParms.delete('brokerage');
      }
      if(parcelId != ''){
        this.listingSearchParms.set('parcel_id',parcelId.toString());
      }
      else{
        this.listingSearchParms.delete('parcel_id');
      }
      if(this.propertyStatus != ''){
        this.listingSearchParms.set('status',this.propertyStatus.toString());
      }
      else{
        this.listingSearchParms.delete('status');
      }
    }
    this.getPageChange(1);    
  }
  
  getPageChange(pageNo){
    this.pageCount = pageNo;
    this.listingSearchParms.set('page_no',pageNo);
    this.listingService.filterListingSearch(this.listingSearchParms).subscribe(res =>{
      this.listingList = res['records'];
      this.totalRecordsCount = res['total_records_count'];
      this.itemsPerPage = res['items_per_page'];
    },err=>{
      this.errorResponse(err.josn());
    });
  }

  searchListingAgent(name){
    if(name.trim().length >3){
      this.listingService.searchAgent(name).subscribe(res =>{
        this.searchAgentList = res['result'];      
        this.showAgentList = true;
      },err =>{
        this.searchAgentList = [];      
        this.showAgentList = false;
      });
    }else{
      this.showAgentList = false;
      this.selectedListingAgentId = '';
    }
  }

  searchBrokerage(name){
    if(name.trim().length >3){
      this.listingService.searchBrokerage(name).subscribe(res =>{
        this.searchBrokeragetList = res['result'];
        this.showBrokeragetList = true;
      },err =>{
        this.searchBrokeragetList = [];      
        this.showBrokeragetList = false;
      });
    }else{
      this.showBrokeragetList = false;
      this.selectedBrokerId = '';
    }
  }

  setListingAgent(agent){
    this.selectedListingAgent = agent['name'];
    this.selectedBroker = agent['brokerage_name'];
    this.selectedListingAgentId = agent['agent_user_id'];
    this.selectedBrokerId = agent['brokerage_user_id'];
    this.showAgentList = false;
    this.isSelectedListingAgent = true;
  }

  setBrokerage(broker){
    this.selectedBrokerId = broker['brokerage_user_id'];
    this.selectedBroker = broker['firm_name'];
    this.showBrokeragetList = false;
    this.selectedListingAgentId = '';
    this.selectedListingAgent = '';
  }

  setListingStatus(status){
    if(status == 'Active'){
      this.propertyStatus = 'Active';
    }else if(status == 'PRE-MLS/Coming Soon'){
      this.propertyStatus = 'PRE-MLS/Coming Soon';
    }else if(status == 'Pending'){
      this.propertyStatus = 'Pending';
    }
  }

  impersonateUser(userEmail,propertyId){
    let impersonateParams = new URLSearchParams;
    impersonateParams.set("email",userEmail)
    this.listingService.getImpersonateUserToken(impersonateParams).subscribe(res =>{
      this.setUserToken(res['result']['token']);
      let userType = this.userTypes.filter(item => item.code === res['result']['user_type']);
      localStorage.setItem('userType',userType[0]['userType']);
      window.open(window.location.origin+'/my-listing/edit-property?propertyId='+propertyId);
    },err=>{
      this.errorResponse(err.json());
    });
  }
}
