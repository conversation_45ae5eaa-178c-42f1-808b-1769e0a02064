<admin-header></admin-header>

<div class="login-box">
  <div class="center-title">Forgot Password</div>
  <div class="center-sub-title">Please enter your email address. You will receive <br> an email to reset your password.</div>

  <form [formGroup]="adminForgotPasswordForm">
      <div class="input-div">
        <input type="text" formControlName="email" placeholder="Your Email Address*" class="input-textbox">
        <div class="error-align" *ngIf="adminForgotPasswordForm.controls['email'].untouched">
          <span></span>
        </div>
      
        <div class="error-align" *ngIf="adminForgotPasswordForm.controls['email'].touched && adminForgotPasswordForm.controls.email.errors?.email">
          <span class="form-error">Enter valid email address</span>
        </div>
      </div>
      
      <div class="input-span">
        <label (click)="routeOnUrl('admin')" class="forgot-pw">Back to Login</label> 
      </div>

      <div class="input-div">
        <input type="submit" [ngClass]="{'submit-disable':adminForgotPasswordForm.invalid}" [disabled]="adminForgotPasswordForm.invalid" (click)="ForgotPassword(adminForgotPasswordForm)" value="Request Password Reset" class="rest-pw">
      </div>
  </form>
</div>
