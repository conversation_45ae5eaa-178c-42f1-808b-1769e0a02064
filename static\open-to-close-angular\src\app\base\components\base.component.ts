import { Injectable, OnInit } from '@angular/core';
import { Http, Headers, RequestOptions, URLSearchParams, ResponseContentType } from '@angular/http';
import { Router, ActivatedRoute } from '@angular/router';
import { PATH } from '@app/base/components/base.constants';
import { ServiceLocator } from '@app/base/components/service-locator';
import { Subject } from 'rxjs';
import { Params } from '@angular/router/src/shared';
import { ToastService } from '@app/base/services/toast.service';
import { User } from '@app/base/model/user';
import { isObject } from 'util';
import { FavoriteService } from '@app/favorite/service/favorite-service';
import * as moment from 'moment';
import { Plans } from '@app/profile/models/plan';
import { ProfileService } from '@app/profile/service/profile.service';
import { GetPlansService } from '@app/base/services/get.plans.service';
import { environment } from "../../../environments/environment";

declare var google;

@Injectable()
export class BaseComponent implements OnInit {

  public router: Router
  public route: ActivatedRoute;

  public static userType: String = '';
  public static accessToken: String = '';
  public static fcm_token: String = '';
  public static baseselectedHeader: String = '';

  public static user: User;
  public static notificationList = [];

  public static currentUserLatitude: any;
  public static currentUserLongitude: any;

  public agentPlans: Plans[] = [];
  public brokerPlans: Plans[] = [];

  public subscribedRoute: any;

  public staticWebUrl = environment.staticWebUrl;

  // public imagePrefix = "static/open-to-close-angular/dist/assets/images/";
  public imagePrefix = environment.imagePrefix;
  // public imagePrefix = "assets/images/";
  // public imagePrefix = "https://storage.googleapis.com/high-apricot-196023.appspot.com/open-to-close-functional2/static/open-to-close-angular/dist/assets/images/";

  public toastService: ToastService;

  public userTypes: any[] = [
    { code: 'LA', userType: 'listingAgent' },
    { code: 'BR', userType: 'brokerage' },
    { code: 'HB', userType: 'homeBuyer' },
    { code: 'ML', userType: 'mortgageLender' }
  ];

  public timeListBase = [
    { "key": "00:00:00", "value": "12:00 AM" }, { "key": "00:30:00", "value": "12:30 AM" }, { "key": "01:00:00", "value": "1:00 AM" }, { "key": "01:30:00", "value": "1:30 AM" }, { "key": "02:00:00", "value": "2:00 AM" }, { "key": "02:30:00", "value": "2:30 AM" },
    { "key": "03:00:00", "value": "3:00 AM" }, { "key": "03:30:00", "value": "3:30 AM" }, { "key": "04:00:00", "value": "4:00 AM" }, { "key": "04:30:00", "value": "4:30 AM" }, { "key": "05:00:00", "value": "5:00 AM" }, { "key": "05:30:00", "value": "5:30 AM" },
    { "key": "06:00:00", "value": "6:00 AM" }, { "key": "06:30:00", "value": "6:30 AM" }, { "key": "07:00:00", "value": "7:00 AM" }, { "key": "07:30:00", "value": "7:30 AM" }, { "key": "08:00:00", "value": "8:00 AM" }, { "key": "08:30:00", "value": "8:30 AM" },
    { "key": "09:00:00", "value": "9:00 AM" }, { "key": "09:30:00", "value": "9:30 AM" }, { "key": "10:00:00", "value": "10:00 AM" }, { "key": "10:30:00", "value": "10:30 AM" }, { "key": "11:00:00", "value": "11:00 AM" }, { "key": "11:30:00", "value": "11:30 AM" },
    { "key": "12:00:00", "value": "12:00 PM" }, { "key": "12:30:00", "value": "12:30 PM" }, { "key": "13:00:00", "value": "1:00 PM" }, { "key": "13:30:00", "value": "1:30 PM" }, { "key": "14:00:00", "value": "2:00 PM" }, { "key": "14:30:00", "value": "2:30 PM" },
    { "key": "15:00:00", "value": "3:00 PM" }, { "key": "15:30:00", "value": "3:30 PM" }, { "key": "16:00:00", "value": "4:00 PM" }, { "key": "16:30:00", "value": "4:30 PM" }, { "key": "17:00:00", "value": "5:00 PM" }, { "key": "17:30:00", "value": "5:30 PM" },
    { "key": "18:00:00", "value": "6:00 PM" }, { "key": "18:30:00", "value": "6:30 PM" }, { "key": "19:00:00", "value": "7:00 PM" }, { "key": "19:30:00", "value": "7:30 PM" }, { "key": "20:00:00", "value": "8:00 PM" }, { "key": "20:30:00", "value": "8:30 PM" },
    { "key": "21:00:00", "value": "9:00 PM" }, { "key": "21:30:00", "value": "9:30 PM" }, { "key": "22:00:00", "value": "10:00 PM" }, { "key": "22:30:00", "value": "10:30 PM" }, { "key": "23:00:00", "value": "11:00 PM" }, { "key": "23:30:00", "value": "11:30 PM" },
  ]

  public UrlsInfo = [
    { 'screenName': '/search', 'value': 'search', 'url': 'search' },
    { 'screenName': '/my-list', 'value': 'my list', 'url': 'my-list' },
    { 'screenName': '/my-list/myList-open-house-list', 'value': 'my list list', 'url': 'my-list/myList-open-house-list' },
    { 'screenName': '/my-open-houses', 'value': 'my open houses', 'url': '/my-open-houses' },
    { 'screenName': '/my-open-houses/my-open-houses-list', 'value': 'my open houses list', 'url': '/my-open-houses/my-open-houses-list' },
    { 'screenName': '/event-manager', 'value': 'event manager', 'url': 'event-manager' },
    { 'screenName': '/event-manager/event-list', 'value': 'event list', 'url': 'event-manager/event-list' },
    { 'screenName': '/my-listing', 'value': 'my listing', 'url': '/my-listing' },
    { 'screenName': '/my-listing/listing-detail', 'value': 'listing detail', 'url': '/my-listing/listing-detail' },
    { 'screenName': '/favorites', 'value': 'favorites', 'url': '/favorites' },
    { 'screenName': '/favorites/favorites-list', 'value': 'favorites list', 'url': '/favorites/favorites-list' },
  ];

  public mapMarkerCluster = [[{
    url: this.imagePrefix + 'cluster.png',
    height: 26,
    width: 27,
    textColor: 'white',
    textSize: 13,
    anchor: [0, 0],
  }
  ]];

  constructor() {
    this.router = ServiceLocator.injector.get(Router);
    this.route = ServiceLocator.injector.get(ActivatedRoute);
    this.toastService = ServiceLocator.injector.get(ToastService);
  }

  ngOnInit() {
    this.findLocation();
  }

  public getUserToken() {
    return localStorage.getItem('token')
  }

  public getAdminToken() {
    return localStorage.getItem('adminToken')
  }

  public getWiseAgentToken() {
    return localStorage.getItem('wiseAgentToken')
  }

  public setUserToken(token) {
    var accessToken = token.split('JWT');
    localStorage.setItem('token', accessToken[1]);
  }

  public setAdminToken(token) {
    var accessToken = token.split('JWT');
    localStorage.setItem('adminToken', accessToken[1]);
  }

  public setWiseAgentToken(token) {
    return localStorage.setItem('wiseAgentToken', token);
  }

  public setUserType(userType) {
    localStorage.setItem('userType', userType);
  }

  public getUserType() {
    return localStorage.getItem('userType');
  }

  public setPreviousScreen(screenName) {
    localStorage.setItem('bts', screenName);
  }

  public getPreviousScreen() {
    return localStorage.getItem('bts');
  }

  public clearLocalStorageSearch() {
    localStorage.removeItem('recentSearches');
    localStorage.removeItem('sear');
  }

  public getLocalToUtcDate(date) {
    var currentTime = moment().format('HH:mm:ss');
    return moment(date + ' ' + currentTime).utc().format('YYYY-MM-DD');
  }

  public getPurchaseHistoryDate(date) {
    return moment.utc(date).local().format('MMMM DD, YYYY');
  }

  public refreshBasePropertys() {
    BaseComponent.user = new User();
    BaseComponent.userType = '';
    BaseComponent.accessToken = '';
    localStorage.removeItem('token');
    localStorage.removeItem('userType');
    BaseComponent.notificationList = [];
  }

  gotToPropertyDetail(url, id) {
    this.router.navigate([url], { queryParams: { propertyId: id } });
  }

  propertyDetailInNewTab(id) {
    window.open(this.staticWebUrl + 'search/property-detail?propertyId=' + id);
  }

  checkMessageAsLink(message) {
    var urlRegex = /(https?:\/\/[^\s]+)/g;
    return message.replace(urlRegex, function (url) {
      return '<a class="link-message" target="_blank" href="' + url + '">' + url + '</a>';
    });
  }
  public sucMessageResponseOnlyText(msg,description='') {
    this.toastService.addToast('success', msg, description || '');
  }

  public sucMessageResponse(msg) {
    this.toastService.addToast('success', 'Success', msg);
  }

  public errMessageResponse(msg) {
    this.toastService.addToast('error', 'Error', msg);
  }

  public warningMessage(msg) {
    this.toastService.addToast('warning', 'Warning', msg);
  }

  public successResponse(res) {
    this.toastService.addToast('success', 'Success', res['message']);
  }
  public staticRegister() {
    this.toastService.addToast('success', 'Success', 'Registration successful!');
  }

  public errorResponse(err) {
    this.toastService.addToast('error', 'Error', err['message']);
    if (err['statusCode'] == 401 || err['statusCode'] == 403) {
      localStorage.removeItem('adminToken');
      localStorage.removeItem('userType');
      localStorage.removeItem('boundryZoom');
      localStorage.removeItem('token');
      localStorage.removeItem('bts');
      localStorage.removeItem('zoomLevel');
      localStorage.removeItem('recentSearches');
    }
  }

  validateNumber(number: String) {
    number = number.toString().trim().replace(/[^0-9]/g, '');
    if (number.length != 0) {
      if (number.replace(/[^0-9]/g, '').length == 3) {
        number = number + "-"
      }
      if (number.replace(/[^0-9]/g, '').length >= 4) {
        number = number.slice(0, 3) + "-" + number.replace(/[^0-9]/g, '').slice(3)
      }
      if (number.replace(/[^0-9]/g, '').length >= 6) {
        number = number.slice(0, 7) + "-" + number.replace(/[^0-9]/g, '').slice(6)
      }
      return number;
    }
  }

  validateOtp(number: String) {
    number = number.toString().trim().replace(/[^0-9]/g, '');
    return number;
  }

  validateParcelNumber(number: String) {
    if (number.length > 0 && number.length < 11) {
      number = number.toString().trim().replace(/[^0-9]/g, '');
      if (number.length != 0) {
        if (number.replace(/[^0-9]/g, '').length == 3) {
          number = number + "-"
        }
        if (number.replace(/[^0-9]/g, '').length >= 4) {
          number = number.slice(0, 3) + "-" + number.replace(/[^0-9]/g, '').slice(3)
        }
        if (number.replace(/[^0-9]/g, '').length >= 5) {
          number = number.slice(0, 6) + "-" + number.replace(/[^0-9]/g, '').slice(5)
        }
      }
    }
    if (number.length != 0) {

      if (number.replace(/[^0-9A-Z]/g, '').length >= 8) {
        if (number.length == 11) {
          number = number.slice(0, 10) + "-" + number.replace(/[^0-9]/g, '').slice(11)
        }
      }
      if (number.length > 12) {
        number = number.slice(0, 12)
      }

      return number.toUpperCase();
    }
  }

  getDayName(date, time, isListHub) {
    var dateTime = date + ' ' + time;
    if (isListHub == false) {
      return moment.utc(dateTime).local().format('ddd').toUpperCase();
    }
    else {
      return moment(dateTime).format('ddd').toUpperCase();
    }
  }

  getDay(date, time, isListHub) {
    if (date != undefined) {
      var dateTime = date + ' ' + time;
      if (isListHub == false) {
        return moment.utc(dateTime).local().format('DD');
      }
      else {
        return moment(dateTime).format('DD');
      }
    }
  }

  getStringEventDate(date, time, isListHub) {
    var dateTime = date + ' ' + time;
    if (date != undefined && date != '') {
      if (isListHub == false) {
        return moment.utc(dateTime).local().format('dddd, MMMM DD');
      }
      else {
        return moment(dateTime).format('dddd, MMMM DD');
      }
    }
  }

  getInvoiceDateFormat(date) {
    return moment.utc(date).local().format('MMMM_DD');
  }

  setEventDateFormat(date, time, isListHub) {
    var dateTime = date + ' ' + time;
    if (date != undefined && date != '') {
      if (isListHub == false) {
        return moment.utc(dateTime).local().format("MMMM DD, YYYY");
      }
      else {
        return moment(dateTime).format("MMMM DD, YYYY");
      }
    }
  }

  leadDateFormat(dateTime) {
    return moment.utc(dateTime).local().format("MMMM DD, YYYY");
  }

  setDateFormat(date) {
    return moment.utc(date).local().format('DD/MM/YYYY');
    // return moment(date).format("DD/MM/YYYY");
  }

  getAdminDateFormat(date: Date, format: string) {
    return moment.utc(date).local().format(format);
  }

  getTimeTypes(time, scrreenName: string = '', eventDate, isListHub) {
    let pmString = "pm";
    let amString = "am";
    if (scrreenName != '') {
      pmString = "PM"
      amString = "AM"
    }

    if (isListHub == false) {
      time = moment.utc(eventDate + ' ' + time).local().format('HH:mm:ss');
    }
    else {
      time = moment(eventDate + ' ' + time).format('HH:mm:ss');
    }

    if (time != undefined) {
      let filterTimeList = this.timeListBase.filter((timeList) => timeList.key == time);
      if (filterTimeList.length != 0) {
        let timeFormat = filterTimeList[0]['value'].split(" ");
        if (timeFormat[1] == 'PM') {
          return timeFormat[0] + pmString;
        }
        else if (timeFormat[1] == 'AM') {
          return timeFormat[0] + amString;
        }
      }
    }
  }

  priceFormat(num) {
    var thousand = 1000;
    var million = 1000000;
    var billion = 1000000000;
    var trillion = 1000000000000;

    if (num < thousand) {
      return String(num);
    }

    if (num >= thousand && num <= 1000000) {
      return Math.round(num / thousand) + 'K';
    }

    if (num >= million && num <= billion) {
      return (num / million).toFixed(1) + 'M';
    }

    if (num >= billion && num <= trillion) {
      return (num / million).toFixed(1) + 'B';
    }

    else {
      return (num / million).toFixed(1) + 'T';
    }
  }

  public profileSuccessResponse() {
    this.toastService.addToast('success', 'Sucess', 'Profile Update successfully');
  }

  public showUpdateBrokerInformation() {
    this.toastService.addToast('warning', 'Warning', 'Broker information is required', 5000);
  }

  public isInteger(x) {
    return x % 1 === 0;
  }

  public isIntegerOrFloat(y) {
    y = Math.round(y);
    return y % 1 === 0;
  }

  clientNameImage(name) {
    var shortName;
    var fullName = name.split(' ');
    if (fullName.length == 1) {
      shortName = fullName[0].substring(0, 1)
    }
    else if (fullName.length > 1) {
      shortName = fullName[0].substring(0, 1) + "" + fullName[1].substring(0, 1);
    }
    return shortName.toUpperCase();
  }

  getClientTime(clientDate) {
    const date = new Date();
    const currentDate = moment(date).format('YYYY-MM-DD');
    var start = moment(currentDate);
    var end = moment(clientDate, "YYYY-MM-DD");
    return this.timeCount(moment.duration(start.diff(end)).asDays())
  }

  timeCount(days) {
    var y = 365;
    var y2 = 31;
    var remainder = days % y;
    var day = remainder % y2;
    var year = (days - remainder) / y;
    var month = (remainder - day) / y2;

    if (year != 0 && month != 0) {
      return year + " years & " + month + " months"
    }
    else if (year == 0 && month == 0 && day != 0) {
      return day + " days"
    }
    else if (year == 0 && month != 0 && day != 0) {
      return month + " months & " + day + " days"
    }
    else if (year != 0 && month == 0 && day != 0) {
      return year + " years & " + day + " days"
    }
    else if (year != 0 && month != 0 && day != 0) {
      return year + " years & " + month + " months & " + day + " days"
    }
    else if (year == 0 && month == 0 && day == 0) {
      return day + " days"
    }
    else if (year == 0 && month != 0 && day == 0) {
      return month + " month"
    }
    // return result;
  }

  getNotificationTime(dateTime) {
    var todayDate = new Date();
    var msgUtcDate = moment(dateTime).format('YYYY-MM-DD HH:mm:ss');
    var msgDate = moment.utc(dateTime).local();
    var date = moment(dateTime).format('MMMM DD,YYYY');
    return this.notificationTimeDifference(todayDate, msgDate, date)
  }

  notificationTimeDifference(current, previous, date) {
    var msPerMinute = 60 * 1000;
    var msPerHour = msPerMinute * 60;
    var msPerDay = msPerHour * 24;
    var msPerMonth = msPerDay * 30;
    var msPerYear = msPerDay * 365;

    var elapsed = current - previous;

    if (elapsed < msPerMinute) {
      return Math.round(elapsed / 1000) + ' sec ago';
    }

    else if (elapsed < msPerHour) {
      return Math.round(elapsed / msPerMinute) + ' min ago';
    }

    else if (elapsed < msPerDay) {
      return Math.round(elapsed / msPerHour) + ' hours ago';
    }

    else if (elapsed < msPerMonth) {
      var day = Math.round(elapsed / msPerDay);
      if (day <= 6) {
        if (day == 1) {
          return Math.round(elapsed / msPerDay) + ' day ago';
        }
        else {
          return Math.round(elapsed / msPerDay) + ' days ago';
        }
      }
      else if (7 == day) {
        return 1 + ' week ago';
      }
      else if (day > 7) {
        return date
      }
    }
    else {
      return date
    }
  }

  getClientFirstName(clientName) {
    var fullName = clientName.split(' ');
    if (fullName.length != 0) {
      return fullName[0];
    }
  }

  checkNotificationType(notification) {
    if (notification['notification_type'] == 'USER_CHECKIN') {
      this.router.navigate(['/my-open-houses/my-open-houses-list'], { queryParams: { event_id: notification['notification_data']['event_id'] } });
    }
    else if (notification['notification_type'] == ['USER_RATING']) {
      this.router.navigate(['event-manager/run-event-manager'], { queryParams: { eventId: notification['notification_data']['event_id'] } });
    }
    else if (notification['notification_type'] == ['USER_GOING']) {
      this.router.navigate(['event-manager/run-event-manager'], { queryParams: { eventId: notification['notification_data']['event_id'] } });
    }
    else if (notification['notification_type'] == ['EVENT_ENDED']) {
      this.router.navigate(['/property-detail'], { queryParams: { propertyId: notification['notification_data']['property_id'] } });
    }
    else if (notification['notification_type'] == ['UPDATE_EVENT']) {
      this.router.navigate(['/property-detail'], { queryParams: { propertyId: notification['notification_data']['property_id'] } });
    }
    else if (notification['notification_type'] == ['CANCEL_EVENT']) {
      this.router.navigate(['/property-detail'], { queryParams: { propertyId: notification['notification_data']['property_id'] } });
    }
    else if (notification['notification_type'] == ['NEW_OPEN_HOUSE']) {
      this.router.navigate(['/property-detail'], { queryParams: { propertyId: notification['notification_data']['property_id'] } });
    }
  }

  mapDirections(lat, lng) {
    if (lat != 0 && lng != 0) {
      if /* if we're on iOS, open in Apple Maps */
        ((navigator.platform.indexOf("iPhone") != -1) ||
        (navigator.platform.indexOf("iPad") != -1) ||
        (navigator.platform.indexOf("iPod") != -1))
        window.open("maps://maps.google.com/maps/dir/?api=1&origin=" + BaseComponent.currentUserLatitude + "," + BaseComponent.currentUserLongitude + "&destination=" + lat + "," + lng);
      // window.open("https://www.google.com/maps/dir/?api=1&origin=40.712775,-74.005973&destination="+lat+","+lng)
      // window.open("http://maps.google.com/maps?saddr="+lat+"&daddr="+lng)
      // window.open("maps://maps.google.com/maps/dir/?api=1&destination="+lat+","+lng);
      else /* else use Google */
        window.open("https://www.google.com/maps/dir/?api=1&origin=" + BaseComponent.currentUserLatitude + "," + BaseComponent.currentUserLongitude + "&destination=" + lat + "," + lng);
      // window.open("http://maps.google.com/maps?saddr="+lat+"&daddr="+lng)
      // window.open("https://www.google.com/maps/dir/?api=1&destination="+lat+","+lng);
    }
  }

  getIconImage(google, markerColorType) {

    let x = 13;
    let y = 37;
    let xSize = 70;
    let ySize = 35;

    if (markerColorType == 'no-event' || markerColorType == '' || markerColorType == 'BO') {
      x = 20;
      y = 40;
      xSize = 80;
      ySize = 45;
    }
    else {
      x = 13;
    }


    let iconImage = {
      url: this.imagePrefix + markerColorType + ".png",
      size: new google.maps.Size(xSize, ySize),
      scaledSize: new google.maps.Size(xSize, ySize),
      labelOrigin: new google.maps.Point(y, x)
    };

    return iconImage;
  }

  routeOnUrl(routePath) {
    this.router.navigate([routePath]);
  }

  public getRequestOptions(
    method: any,
    url: any,
    data: any,
    params: URLSearchParams = new URLSearchParams(),
    responseType: ResponseContentType = null,
    headers: Headers = new Headers(),
    isFormData: boolean = false,
    isUrlParams: boolean = false) {

    let header = new Headers();

    let urlType = this.router.routerState.snapshot.url;

    if (urlType.includes('admin') && this.getAdminToken()) {
        header.set('Authorization', 'JWT ' + this.getAdminToken());
    } else if (this.getUserToken()) {
        header.set('Authorization', 'JWT ' + this.getUserToken());
    }

    if (isUrlParams) {
        header.set('Content-Type', 'application/x-www-form-urlencoded');
    } else if (!isFormData) {
        header.set('Content-Type', 'application/json'); // Ensure JSON content type is set
    }

    return new RequestOptions({
        headers: header,
        url: PATH + url,
        body: data, // This should be a JSON string now
        method: method,
        responseType: responseType,
        search: params
    });
}


  findLocation() {
    var self = this;
    if (navigator.geolocation) {
      /*
        * @Desc: Find current position
        * @Param:
        * @return:display infowindow on map with given string
        *
      */
      navigator.geolocation.getCurrentPosition((position) => {
        var currentPosition
        BaseComponent.currentUserLatitude = position.coords.latitude;
        BaseComponent.currentUserLongitude = position.coords.longitude;

        currentPosition = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
      },
        () => {
          this.LocationError(true);
        });

    }
    else {
      this.LocationError(false);
    }
  }

  LocationError(browserHasGeolocation) {
    /*
      * @Desc:showing error message in infowindow if read location permission id block by user
      * @Param:
      * @return:error message.
      *
    */
    // infoWindow.setPosition(pos);
    // infoWindow.setContent(browserHasGeolocation ?'Error: The Geolocation service failed.' :'Error: Your browser doesn\'t support geolocation.');
    // infoWindow.open(this.gmap);
  }

  downloadFile(data, fileName) {
    var blob = new Blob([data._body], { type: 'text/csv' });
    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
      window.navigator.msSaveOrOpenBlob(blob, fileName);
    } else {
      var a = document.createElement('a');
      a.href = URL.createObjectURL(blob);
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  }

  getMapPixelOffsetY(map: any, marker: any) {
    var scale = Math.pow(2, map.getZoom());
    var nw = new google.maps.LatLng(
      map.getBounds().getNorthEast().lat(),
      map.getBounds().getSouthWest().lng()
    );
    var worldCoordinateNW = map.getProjection().fromLatLngToPoint(nw);
    var worldCoordinate = map.getProjection().fromLatLngToPoint(marker.getPosition());
    var pixelOffset = new google.maps.Point(
      Math.floor((worldCoordinate.x - worldCoordinateNW.x) * scale),
      Math.floor((worldCoordinate.y - worldCoordinateNW.y) * scale)
    );

    return pixelOffset.y;
  }
}
