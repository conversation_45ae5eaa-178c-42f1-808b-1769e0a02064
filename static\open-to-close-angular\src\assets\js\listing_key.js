function set_listing_key_value_obj(listingKey){
    (function(l, i, s, t, h, u, b) {
    l['ListHubAnalyticsObject'] = h;
        l[h] = l[h] || function() {
            (l[h].q = l[h].q || []).push(arguments)
        }, l[h].d = 1 * new Date();
        u = i.createElement(s), b = i.getElementsByTagName(s)[0];
        u.async = 1;
        u.src = t;
        b.parentNode.insertBefore(u, b)
    })(window, document, 'script', '//tracking.listhub.net/la.min.js', 'lh');
    lh('init', {
        provider: 'M-4554',
        test: 'TEST'
    });

    lh('submit', 'DETAIL_PAGE_VIEWED', {
        lkey: listingKey
    });
}