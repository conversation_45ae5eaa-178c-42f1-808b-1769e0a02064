<admin-header> </admin-header>

<div class="col-sm-16 top-bar">
  <div class="log-mt"></div>
</div>

<div class="user-box-pd">
  <div class="user-box">
    <div class="col-sm-16 box-title-admin">
      <div class="col-sm-14">Find a Home Buyer</div>
      <div class="col-sm-2">
        <input
          type="submit"
          value="Export CSV"
          class="download-csv-button btn submit_button with_bg"
          (click)="downloadCSV(userName.value, email.value)"
        />
      </div>
    </div>

    <div class="container-fluid">
      <div class="row">
        <div class="col-sm-16 in-pdt">
          <div class="col-sm-5">
            <input
              type="text"
              placeholder="Name"
              class="user-search"
              #userName
            />
          </div>

          <div class="col-sm-5">
            <input type="text" placeholder="Email" class="user-search" #email />
          </div>

          <div class="col-sm-4">
            <ng-select
              class="custom ad-user-dropdown cursor_poiner"
              placeholder="Agent Status"
              [items]="agentStatusList"
              bindValue="value"
              bindLabel="value"
              [clearable]="false"
              [searchable]="false"
              [virtualScroll]="true"
              (change)="setAgentStatus($event)"
            >
            </ng-select>
          </div>

          <div class="col-sm-2">
            <input
              type="submit"
              value="Search"
              class="ad-search-btn btn submit_button with_bg"
              (click)="searchHomeBuyerUser(userName.value, email.value)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="table-top">
  <table class="table">
    <tr>
      <th>
        Name
      </th>
      <th>
        Email
      </th>
      <th>
        Agent Status
      </th>
      <th>
        Agent Name
      </th>
      <th>
        Sign Up Date
      </th>
      <th>
        Last Login Date
      </th>
    </tr>
    <tbody>
      <tr
        *ngFor="
          let user of HBuserList
            | paginate
              : {
                  itemsPerPage: itemsPerPage,
                  currentPage: pageCount,
                  totalItems: totalRecordsCount
                };
          let i = index
        "
      >
        <td class="cursor-pointer">
          <span class="dark">{{ user.name }}</span>
        </td>
        <td>
          <span class="dark">{{ user.email }}</span>
        </td>
        <td>
          <span class="dark" *ngIf="user.agent_status == 'R'">Represented</span>
          <span class="dark" *ngIf="user.agent_status == 'U'">Unrepresented</span>
        </td>
        <td>
          <span class="dark">{{user.agent_name}}</span>
        </td>
        <td>
          <span class="dark">{{ user.sign_up_date }}</span>
        </td>
        <td>
          <span class="dark">{{ user.last_login_date }}</span>
        </td>
        <td>
          <div class="open_click_menu" (click)="openMenu(i, user.id)">
            <img
              src="{{ imagePrefix }}symbols-glyph-more.png"
              class="symbols-glyph-more"
              alt=""
            />
            <ul id="user_{{ i }}_{{ user.id }}" class="click_menu_open events">
              <li
                (click)="resetUserPassword(user.email)"
                class="cursor-pointer option-menu"
              >
                Reset user password
              </li>
              <li
                (click)="impersonateUser(user.email)"
                class="cursor-pointer option-menu"
              >
                Impersonate user
              </li>
              <li
                *ngIf="user.is_active"
                (click)="manageUserModal('deActive', user.email, user)"
                class="cursor-pointer option-menu"
              >
                Deactive user
              </li>
              <li
                *ngIf="!user.is_active"
                (click)="manageUserModal('active', user.email, user)"
                class="cursor-pointer option-menu"
              >
                Active user
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </tbody>
    <div class="pagination-center" *ngIf="totalRecordsCount > itemsPerPage">
      <span *ngIf="pageCount != 1">
        <pagination-controls
          (pageChange)="getPageChange($event)"
          maxSize="6"
          previousLabel="PREVIOUS"
          nextLabel="NEXT"
        >
        </pagination-controls>
      </span>

      <span *ngIf="pageCount == 1">
        <pagination-controls
          (pageChange)="getPageChange($event)"
          previousLabel=""
          maxSize="6"
          nextLabel="NEXT"
        >
        </pagination-controls>
      </span>
    </div>
  </table>
</div>

<div>
  <div id="deactiveUser" class="modal fade" role="dialog">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">{{ dialogTitile }}</h4>
        </div>
        <div class="modal-body">
          <p>{{ dialogMessage }}</p>
        </div>
        <div class="modal-footer">
          <input
            type="submit"
            [ngClass]="{ 'submit-disable': disableConfirmBtn == true }"
            [disabled]="disableConfirmBtn == true"
            value="Confirm"
            class="submit_button with_bg model-confi"
            (click)="manageUser()"
          />
          <input
            type="submit"
            class="submit_button with_bg model-cancel"
            data-dismiss="modal"
            value="Cancel"
          />
        </div>
      </div>
    </div>
  </div>
</div>
