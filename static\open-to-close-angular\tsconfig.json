{"compileOnSave": false, "compilerOptions": {"outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es5", "typeRoots": ["node_modules/@types"], "lib": ["es2017", "dom"], "baseUrl": "src", "paths": {"@env/*": ["environments/*"], "@app/*": ["app/*"], "@root/*": ["app/root/*"], "@base/*": ["app/base/*"], "@auth/*": ["app/auth/*"]}}}