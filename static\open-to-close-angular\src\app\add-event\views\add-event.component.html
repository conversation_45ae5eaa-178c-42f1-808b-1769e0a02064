<div class="modal fade sign_modal" id="manageEvent" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-md add_property_empty">
       <div class="modal-content">
          <div class="modal-body bor_top_bg">
             <div class="modal_content col-sm-16">
                <div class="add_event_em_gr mt-5 add-event-top col-sm-16">
                    <span class="vertical-align-top" *ngIf="selectedEventObj?.property_file != ''"><img src="{{selectedEventObj.property_file}}" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""></span>
                    <span class="vertical-align-top" *ngIf="selectedEventObj?.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                    <div *ngIf="showAddEventBtn == true" class="dis_inline po_rel add_event_em_gr_ti"><span class="dark title-bold">Add new open house<br></span><div class="event-title-clamp">{{selectedEventObj?.street}}</div></div>
                   <div *ngIf="showAddEventBtn == false" class="dis_inline po_rel add_event_em_gr_ti"><span class="dark event-title-clamp"> {{selectedEventObj?.address}}  <br></span>{{selectedEventObj?.location}}</div>
                </div>
                <div class="event_gr_de">
                    <div class="title_ad_e event-text-bold col-sm-16">Event Details</div>
                    <span class="add-event-bro col-sm-16"></span>
                    <div class="col-sm-8">
                        <label for="" class="ad_e">Date</label>
                        <div class="form_group addeventdate">
                            <input type="text" id="manageEventDatePicker" class="form-control addeventdatebox event-text-bold" [value]="selectedEventObj?.updatedDate" [disabled]="disabledEvent && upComing">
                        </div>
                    </div>
                    <div class="col-sm-8">
                        <label for="" class="ad_e">Type</label>
                        <div class="new_form eventType">
                            <ng-select class="custom date-drop-down event-type date-drop-downEvent mobile event-text-bold"
                            placeholder="Event Type"
                            [items]="eventTypesList"
                            bindValue="key"
                            bindLabel="value"
                            [clearable]=false
                            [searchable]=false
                            (change)="onEventTypeChange($event)"
                            [(ngModel)]="selectedEventObj.event_type"
                            [disabled]="disabledEvent && upComing"
                            >
                            <!-- [disabled]="disabledEvent || upComing" -->
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-sm-8 date-height" [ngClass]="{'timeError': eventError.date_error == '' && eventError.event_type_error == ''}">
                        <span class="error-validation"  *ngIf="eventError.date_error != ''">{{eventError.date_error}}</span>
                     </div>
                     <div class="col-sm-8" [ngClass]="{'timeError': eventError.event_type_error == ''}">
                        <span class="error-validation" *ngIf="eventError.event_type_error != ''">{{eventError.event_type_error}}</span>
                     </div>
                    <div class="col-sm-8">
                        <label for="" class="ad_e">Start Time</label>
                        <div class="new_form eventType">
                            <ng-select class="custom date-drop-down date-drop-downEvent event-text-bold"
                            placeholder="Start Time"
                            [items]="timeList"
                            bindValue="key"
                            bindLabel="value"
                            autofocus
                            [clearable]=false
                            [searchable]=false
                            (change)="isValidStartTime($event)"
                            [(ngModel)]="selectedEventObj.start_time"
                            [disabled]="disabledEvent"
                            >
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-sm-8">
                        <label for="" class="ad_e">End Time</label>
                        <div class="new_form eventType">
                            <ng-select class="custom date-drop-down date-drop-downEvent event-text-bold"
                            placeholder="End Time"
                            [items]="timeList"
                            bindValue="key"
                            bindLabel="value"
                            [clearable]=false
                            [searchable]=false
                            (change)="isValidEndTime($event)"
                            [(ngModel)]="selectedEventObj.end_time"
                            [disabled]="disabledEvent"
                            >
                            </ng-select>
                        </div>
                    </div>

                    <div class="col-sm-8" [ngClass]="{'timeError': eventError.start_time_error == ''}">
                        <span class="error-validation"  *ngIf="eventError.start_time_error != ''">{{eventError.start_time_error}}</span>
                    </div>

                    <div class="col-sm-8" [ngClass]="{'timeError': eventError.end_time_error == ''}">
                        <span class="error-validation" *ngIf="eventError.end_time_error != ''">{{eventError.end_time_error}}</span>
                    </div>

                    <div class="col-sm-16 timeError">
                        <p class="form-validation" *ngIf="timeError != ''">{{timeError}}</p>
                    </div>

                    <div class="col-sm-16">
                        <label for="" class="ad_e">Description</label>
                        <!-- <textarea type="text" class="new_form textarea-input-height event-text-bold" placeholder="" value="" [(ngModel)]="selectedEventObj.description" (keyup)="onEventDescriptionAdd()" 
                            disabled
                            *ngIf="disabledEvent || !isDescriptionEditable"
                            >
                        </textarea> -->
                        <textarea type="text" class="new_form textarea-input-height event-text-bold" placeholder="" value="" [(ngModel)]="selectedEventObj.description" (keyup)="onEventDescriptionAdd()" 
                            [disabled]="disabledEvent || !isDescriptionEditable"
                            >
                        </textarea>
                        <!-- <textarea type="text" class="new_form textarea-input-height event-text-bold" placeholder="" value="" [(ngModel)]="selectedEventObj.description" (keyup)="onEventDescriptionAdd()" [disabled]="disabledEvent && upComing"></textarea> -->
                        <!-- <ckeditor
                            *ngIf="!disabledEvent && isDescriptionEditable"
                            [disabled]="disabledEvent || !isDescriptionEditable"
                            [editor]="Editor"
                            [(ngModel)]="selectedEventObj.description"
                            [config]="{ toolbar: [ 'heading', '|', 'bold', 'italic', 'link' ] }"
                        >
                        </ckeditor> -->
                    </div>

                    <div class="col-sm-16" [ngClass]="{'timeError': eventError.description_error == ''}">
                        <span class="error-validation" *ngIf="eventError.description_error != ''">{{eventError.description_error}}</span>
                    </div>

                    <div class="title_ad_e mt-10 event-text-bold col-sm-16">Open House Agent</div>
                   <span class="add-event-bro col-sm-16"></span>
                    <div class="col-sm-16">
                       <div class="check_group mt-10">
                          <div class="form_group">
                             <input type="checkbox" [checked]="meAgentType" (click)="setEventAgentType('ME',$event)" [disabled]="disabledEvent">
                             <span class="checkmark"></span>
                             <label class="oha-label">Me</label>
                             <br>
                             <label class="oha-label-description">Assigning to yourself confirms the event immediately and makes it public to consumers.</label>
                          </div>
                          <div class="form_group">
                             <input type="checkbox" [checked]="brAgentType" (click)="setEventAgentType('BG',$event)" [disabled]="disabledEvent || upComing" *ngIf="isAllowEventSharing">
                             <span *ngIf="isAllowEventSharing" class="checkmark"></span>
                             <label [ngClass]="{'padding-left-check': isAllowEventSharing == false}" class="oha-label">Anyone in brokerage</label>
                             <span *ngIf="!isAllowEventSharing" >
                                 <a class="locked" (click)="openModal()">LOCKED</a>
                             </span>
                             <br>
                             <label class="oha-label-description">Assigning to anyone in brokerage makes the event available in the Guest Book for anyone in your brokerage to hold the event. Event is not made public to consumers until another Agent has accepted the event.</label>
                          </div>
                          <div class="form_group">
                             <input *ngIf="isAllowEventSharing" type="checkbox" [checked]="saAgentType" (click)="setEventAgentType('SA',$event)" [disabled]="disabledEvent">
                             <span *ngIf="isAllowEventSharing" class="checkmark"></span>
                             <label [ngClass]="{'padding-left-check': isAllowEventSharing == false}" class="oha-label">Assign to specific agent</label>
                             <span *ngIf="!isAllowEventSharing">
                                <a class="locked" (click)="openModal()">LOCKED</a>
                             </span>
                             <br>
                             <label class="oha-label-description">Assigning to a specific agent sends a request to the selected Agent and places the event in the requests section of Guest Book. Event is not made public to consumers until specified Agent has accepted the event.</label>
                          </div>
                       </div>
                    </div>

                    <div class="col-sm-16" [ngClass]="{'timeError': eventError.event_agent_type_error == ''}">
                        <span class="error-validation" *ngIf="eventError.event_agent_type_error != ''">{{eventError.event_agent_type_error}}</span>
                    </div>

                    <div class="col-sm-16" [ngClass]="{'timeError': eventError.agent_id_error == ''}">
                        <span class="error-validation" *ngIf="eventError.agent_id_error != ''">{{eventError.agent_id_error}}</span>
                    </div>

                    <div class="add_eve_sear col-sm-16 event-search-agent-mt-10" *ngIf="saAgentType == true  && onlyShowSpecificAgent == true">
                            <div *ngIf="saAgentType == true">
                                <div class="check_group mt-10 found_agent_add_agent">
                                    <div class="form_group searched-agents">
                                        <input type="checkbox" (click)="removeSpecificAgentId($event)" [checked]="true" [disabled]="disabledEvent">
                                        <span class="checkmark"></span>
                                        <label>
                                            <div class="agent_found_css_one">
                                                <span *ngIf="selectedEventObj.open_house_agent_image == ''"><img src="{{imagePrefix}}testmonial-default (1).png" class="search-agent-event dis_inline" alt=""></span>
                                                <span *ngIf="selectedEventObj.open_house_agent_image != ''"><img src="{{selectedEventObj.open_house_agent_image}}" class="search-agent-event dis_inline" alt=""></span>
                                                <span>
                                                    <span *ngIf="selectedEventObj.open_house_agent_name != null" class="name">{{selectedEventObj.open_house_agent_name}}</span><br>
                                                </span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <div class="add_eve_sear col-sm-16 event-search-agent-mt-10" *ngIf="saAgentType == true && onlyShowSpecificAgent == false">
                        <div class="title2 event-search-agent-title">Search for agent</div>
                        <div class="col-sm-8">
                          <input appAutoComplete type="text" id="agentName" [(ngModel)]="agentName" class="new_form event-text-bold" placeholder="" autocomplete="off" (OnTypeCompleteMethod)="searchAgent(agentName)"/> 
                          <!-- (keyup)="searchAgent(agentName.value) -->
                        </div>

                        <div *ngIf="searchAgentList.length != 0">
                            <div class="match_found_ad_e">{{searchAgentList.length}} match found</div>
                            <div [ngClass]="{'search-agent-list': searchAgentList.length > 3}">
                              <div class="check_group mt-10 found_agent_add_p" *ngFor="let agent of searchAgentList">
                                  <div class="form_group searched-agents">
                                    <input type="radio" name="specificAgentCheck" class="specificAgent" [checked]="agent.agent_id == event.agent_id" (click)="setAgentId(agent, $event)">
                                    <span class="checkmark"></span>
                                    <label>
                                        <div class="agent_found_css">
                                          <span *ngIf="agent.profile_photo == ''"><img src="{{imagePrefix}}testmonial-default (1).png" class="search-agent-event dis_inline" alt=""></span>
                                          <span *ngIf="agent.profile_photo != ''"><img src="{{agent.profile_photo}}" class="search-agent-event dis_inline" alt=""></span>
                                            <div *ngIf="agent.name != null" class="name">{{agent.name}}<br>
                                                <span *ngIf="agent.brokerage_name ! null" class="name">{{agent.brokerage_name}}</span>
                                            </div><br>
                                            <span *ngIf="agent.name == null" class="name">{{agent.mls_agent_id}}</span><br>
                                        </div>
                                    </label>
                                  </div>
                              </div>
                            </div>
                        </div>
                     </div>

                    <div *ngIf="selectedEventObj?.is_listhub_event == true" class="col-sm-16">
                        <div class="check_group mt-10">
                           <div class="form_group">
                              <label class="oha-label-description"><u><b>Note</b></u> :- This event will no longer be updated by the feed.</label>
                           </div>
                        </div>
                    </div>

                    <div class="new_form_group profile_save_button ad_e_pro col-sm-16" *ngIf="closeBtn == false">
                        <input *ngIf="showAddEventBtn == true" (click)="addPropertyEvent()" [disabled]="eventModalValidation()" type="submit" class="submit_button with_bg dis_inline" [ngClass]="{'submit-disable':eventModalValidation()==true}" value="Submit">
                        <input *ngIf="showAddEventBtn == false" (click)="updateEvent('UPDATE')" [disabled]="eventModalValidation()" type="submit" class="submit_button with_bg dis_inline" [ngClass]="{'submit-disable':eventModalValidation()==true}" value="Update">
                        <input (click)="updateEvent('CANCEL')" type="reset" class="cancle_button dis_inline " value="Cancel">
                    </div>
                    <div class="new_form_group profile_save_button ad_e_pro col-sm-16" *ngIf="closeBtn == true">
                        <input (click)="updateEvent('CANCEL')" type="submit" class="submit_button with_bg dis_inline" value="Close">
                    </div>
                </div>
             </div>
          </div>
       </div>
    </div>
 </div>

 <div class="modal fade sign_modal" id="lockedFeatureModal1" role="dialog">
    <div class="modal-dialog modal-md">
       <div class="modal-content">
          <div class="modal-body">
             <div class="modal_content">
                <div class=" new_account">
                   <div class="title font-weight-title upddate-email">Locked Feature</div>
                   <div class="blueBorder"></div>
                   <div class="upddate-email-border"></div>
                   <p class="upddate-email-p paddingTop text-color">To unlock inter-brokerage event assignment, your brokerage must have a <b>premium</b> Open Houses Direct account.</p><br>
                   <p class="upddate-email-p text-color">Talk to your brokerage about upgrading their account to give you team access event management tools.</p>
                </div>
                <div class="form_group cursor-pointer">
                    <input type="button" data-dismiss="modal" class="new_form" value="Close"/>
                </div>
             </div>
          </div>
       </div>
    </div>
 </div>
