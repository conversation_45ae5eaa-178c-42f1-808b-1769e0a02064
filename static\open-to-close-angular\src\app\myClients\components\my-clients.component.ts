import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { MyClientService } from '@app/myClients/services/my-clients.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ChatService } from '@app/messaging/service/chat-service';

declare var $;

@Component({
  selector: 'my-clients',
  templateUrl: '../views/my-clients.component.html',
  styleUrls: ['../css/my-clients.component.css']
})
export class MyClientsComponent extends BaseComponent implements OnInit {

  currentId: any;
  currentIndex: number;

  myClientService:MyClientService;
  chatService: ChatService;
  public clientList = []; 
  public tempClientList = [];
  public DeActieClientList = [];
  private previousSearchClient: String = '';

  clientIndex: any = 1;
  clientTotalCount: any = 0;
  clientItemPerPage:any;
  prevClientIndex: any = 1;
  prevClientTotalCount: any = 0;
  prevclientItemPerPage:any;

  public disableLoadMore : Boolean = false;
  public showClientLoader : Boolean = false;

  public clientSortObject : any = {};
  public clientSortList :any[] = [];

  public searchClientSubscription: any;
  public clientCSVSubscription: any;
  public isTabletScreen : Boolean = false;
  public myInviteLink = '';

  constructor() {
    super();
    this.myClientService = ServiceLocator.injector.get(MyClientService);
    this.chatService = ServiceLocator.injector.get(ChatService);
   }

  ngOnInit() {
    this.setPreviousScreen('/my-clients');
    this.loadClients('AC', 1);
    this.getInviteLink();
    this.showClientLoader = true;

    let self = this;

    $(document).ready(function()
    {
      $(document).mouseup(function(e)
      {
        if(self.currentId != undefined && self.currentIndex != undefined){
          $("#mc_"+self.currentIndex+"_"+self.currentId).hide();
          self.currentId = undefined;
          self.currentIndex = undefined;
        }
      });
    });

    if($(window).width() > 767 && $(window).width() < 1025){
      self.isTabletScreen = true;
    }
    window.onresize = (e) =>{
      if($(window).width() > 767 && $(window).width() < 1025){
        self.isTabletScreen = true;
      }
    }
  }

  openMenu(index,id){
    this.currentIndex = index;
    this.currentId = id;
    $("#mc_"+index+"_"+id).toggle();
  }

  loadClients(listType, index){
    let sortList = [];
    let clientUrlParams = new URLSearchParams();
    clientUrlParams.set('page_no', index.toString());
    if(this.clientSortList.length !=0){
      sortList = this.clientSortList;
      clientUrlParams.set('sort_list', JSON.stringify(sortList));
    }
    this.disableLoadMore = true;
    this.myClientService.getClients(clientUrlParams).subscribe(res =>{
      this.showClientLoader = false;
      this.clientItemPerPage = res['result']['items_per_page'];
      this.disableLoadMore = false;
      if(this.clientIndex > 1){
        res['result']['records'].forEach(record => {
          this.clientList.push(record);
        });
        this.tempClientList = this.clientList;
      }
      else{
        this.clientList = res['result']['records'];
        this.tempClientList = res['result']['records'];
        this.clientTotalCount = res['result']['total_records_count'];
      }
      this.clientIndex++;
      this.prevClientIndex = this.clientIndex;
      this.prevClientTotalCount = this.clientTotalCount;
    },err => this.errorResponse(err.json()));
  }

  getInviteLink(){
    this.myClientService.getInviteLink().subscribe(res =>{
      this.myInviteLink = res['result']['invite_link'];
    },err=>{
      console.log(err.json());
    });
  }

  checkIns(url,clientDetail,type){
    var clientDetailObj = {
     'clientId' : clientDetail.client_id,
     'clientNameImage' : this.clientNameImage(clientDetail.name),
     'clientFullName' : clientDetail.name,
     'timeAsClient' : this.getClientTime(clientDetail.time_as_client),
     'messageName'  : this.getClientFirstName(clientDetail.name),
     'profileImage' : clientDetail.profile_image
    }
    this.myClientService.setClientObj(clientDetailObj);
    if(type !='addNote'){
      this.router.navigate([url],{queryParams:{client:clientDetail.client_id}});
    }
    else{
      this.router.navigate([url],{queryParams:{client:clientDetail.client_id,isNote:'add_Note'}});
    }
  }

  searchClient(clientName){
    if(clientName.trim().length != 0){
      if(this.previousSearchClient != clientName){
        let clientSearchParams = new URLSearchParams();
        clientSearchParams.set('client_name', clientName);
        if(this.searchClientSubscription){
          this.searchClientSubscription.unsubscribe();
        }
        this.searchClientSubscription = this.myClientService.searchClient(clientSearchParams).subscribe(res => {
          this.previousSearchClient = clientName;
          this.clientList = res['result']['records'];
          this.clientTotalCount = res['result']['total_records_count'];
        }, err => this.errorResponse(err.json()));
      }
    }
    else{
      this.clientList = this.tempClientList;
      this.previousSearchClient = '';
      this.clientIndex = this.prevClientIndex;
      this.clientTotalCount = this.prevClientTotalCount;
    }
  }

  messageClient(selectedClient){
    var client = {};
    client['user_name'] = selectedClient['name'];
    client['profile_image'] = selectedClient['profile_image'];
    client['chat_thread_id'] = selectedClient['client_id'];
    client['receiver_id'] = selectedClient['client_id'];
    client['last_message_time'] = "";
    client['last_message'] = '';
    this.chatService.setClientChatThread(client);
    this.router.navigate(['messaging']);
  }

  clientCSV(){
    if(this.clientCSVSubscription){
      this.clientCSVSubscription.unsubscribe();
    }
    this.sucMessageResponseOnlyText('Export CSV Started...','');
    this.clientCSVSubscription = this.myClientService.exportClientCSV().subscribe(res=>{
      this.downloadFile(res,"clients.csv");
      this.sucMessageResponseOnlyText('Export CSV Completed','');
    },err => this.errorResponse(err.json()));
  }

  clientSorting(filedName){
    let sortList = [];
    this.clientIndex = 1;
    if(this.clientSortObject[filedName] == undefined){
      this.clientSortObject[filedName] = true;
      $('#CA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
    }
    else{
      this.clientSortObject[filedName] = this.clientSortObject[filedName] === true ? false : true;
      if(this.clientSortObject[filedName]){
      $('#CA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }else{
      $('#CA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      }
    }
    this.clientSortList[0] = this.clientSortObject;
    this.loadClients('AC',1);
  }
}
