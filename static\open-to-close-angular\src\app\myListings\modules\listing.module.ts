import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListingComponent } from '@app/myListings/components/listing.component';
import { ListingDetailComponent } from '@app/myListings/components/listing-detail.component';
import { BaseModule } from '@app/base/modules/base.module';
import { ListingAddNewPropertyComponent }  from '@app/myListings/components/listing-add-new-property.component';
import { KeysPipe } from '@app/myListings/pipes/get-keys.pipe';
import { MyListingService } from '@app/myListings/service/my-listing-services';

@NgModule({
  imports: [
    CommonModule,
    BaseModule
  ],
  declarations: [ListingComponent, ListingDetailComponent,ListingAddNewPropertyComponent,KeysPipe],
  providers: [MyListingService]
})
export class ListingModule { }
