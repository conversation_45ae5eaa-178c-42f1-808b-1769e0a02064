import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { MyLeadsService } from '@app/myLeads/service/my-leads.services';
import { ServiceLocator } from '@app/base/components/service-locator';
import { MyLeadDialog } from '@app/myLeads/model/my-lead-dialog.model';
import * as moment from 'moment';
import { CommentStmt } from '@angular/compiler/src/output/output_ast';
import { ChatService } from '@app/messaging/service/chat-service';
import { URLSearchParams } from '@angular/http';
import { NgForm, FormGroup, FormControl, Validators, FormArray } from '@angular/forms';
declare var $;

@Component({
  selector: 'my-leads',
  templateUrl: '../views/my-leads.component.html',
  styleUrls: ['../css/my-leads.component.css']
})
export class MyLeadsComponent extends BaseComponent implements OnInit {

  leadProfileForm: FormGroup;

  public myLeadsService: MyLeadsService;
  public leadDialog: MyLeadDialog = new MyLeadDialog();
  chatService: ChatService;

  public myNoteList = [];
  updateNoteIndex;
  public newNote: String = '';
  public noteParams: any = {};
  public disableNotebtn: Boolean = true;
  public myNoteTotalCount = 0;
  public myNoteItemPerPage: any;
  public myNoteIndex: number = 2;
  public showMNLoader: Boolean = false;

  public leadsItemsPerPage: any;
  public ArchiveItemsPerPage: any;

  public myLeadsTotalCount = 0;
  public myLeadsIndex = 2;

  public myLeadsArchiveTotalCount = 0;
  public myLeadsArchiveIndex = 2;
  public tempMyLeadsArchiveIndex = 1;
  public myLeadsArchiveList = [];
  public tempMyLeadsArchiveList = [];
  public tempMyLeadsArchiveTotalCount = 0;

  public myLeadsList = [];
  public tempMyLeadsList = [];
  public tempMyLeadsIndex = 1;
  public tempMyLeadsTotalCount = 0;

  public selectedTab = 'leads'
  public showUnArchiveBtn: Boolean = false;
  public prvSearch = '';

  public disableLoadMore: Boolean = false;

  public showARLoader: Boolean = false;
  public showLELoader: Boolean = false;

  //Lead Sorting
  public LESortingObject: any = {};
  public LESortingList: any[] = [];

  //Archive Sorting

  public ARSortingObject: any = {};
  public ARSortingList: any[] = [];

  public leadSearchSubscription: any;
  public leadsCSVSubscription: any;
  public lastSearchedLead: String = "";

  constructor() {
    super();
    this.myLeadsService = ServiceLocator.injector.get(MyLeadsService);
    this.chatService = ServiceLocator.injector.get(ChatService);
  }

  async ngOnInit() {
    this.setPreviousScreen('/my-leads');
    let leadsParams = new URLSearchParams();
    leadsParams.set('page_no', '1');
    leadsParams.set('type', 'LE');
    this.loadLeads(leadsParams, 'LE');

    leadsParams.set('type', 'AR');
    this.loadLeads(leadsParams, 'AR');

    this.showARLoader = true;
    this.showLELoader = true;
    await this.defineForms();
  }


  loadLeads(url, type) {
    this.myLeadsService.getMyLeads(url).subscribe(res => {
      if (type == 'LE') {
        this.showLELoader = false;
        this.myLeadsList = res['result']['records'];
        this.leadsItemsPerPage = res['result']['items_per_page'];
        this.myLeadsTotalCount = res['result']['total_records_count'];

        this.tempMyLeadsList = this.myLeadsList;
        this.tempMyLeadsTotalCount = res['result']['total_records_count'];
      }
      else if (type == 'AR') {
        this.showARLoader = false;
        this.myLeadsArchiveList = res['result']['records'];
        this.myLeadsArchiveTotalCount = res['result']['total_records_count'];
        this.ArchiveItemsPerPage = res['result']['items_per_page'];

        this.tempMyLeadsArchiveList = this.myLeadsArchiveList;
        this.tempMyLeadsArchiveTotalCount = res['result']['total_records_count'];
      }
    }, err => this.errorResponse(err.json()));
  }

  loadMoreLeads(type, index) {
    let sortList = [];
    let loadMoreParams = new URLSearchParams();
    loadMoreParams.delete('sort_list');
    loadMoreParams.set('page_no', index);
    loadMoreParams.set('type', type);
    this.disableLoadMore = true;

    if (type == "LE") {
      if (this.LESortingList.length != 0) {
        sortList = this.LESortingList;
        loadMoreParams.set('sort_list', JSON.stringify(sortList));
      }
    } else if (type == "AR") {
      if (this.ARSortingList.length != 0) {
        sortList = this.ARSortingList;
        loadMoreParams.set('sort_list', JSON.stringify(sortList));
      }
    }

    if (this.prvSearch == '') {
      this.myLeadsService.getMyLeads(loadMoreParams).subscribe(res => {
        this.disableLoadMore = false;
        if (type == 'LE') {
          this.leadsItemsPerPage = res['result']['items_per_page'];
          res['result']['records'].forEach(record => {
            this.myLeadsList.push(record);
          });
          this.myLeadsIndex += 1;
          this.tempMyLeadsList = this.myLeadsList;
        }
        else if (type == 'AR') {
          this.ArchiveItemsPerPage = res['result']['items_per_page'];
          res['result']['records'].forEach(record => {
            this.myLeadsArchiveList.push(record);
          });
          this.myLeadsArchiveIndex += 1;
        }
      }, err => this.errorResponse(err.json()));
    }
    else if (this.prvSearch != '') {
      if (this.selectedTab == 'leads') {
        this.tempMyLeadsIndex += 1
        this.searchUser(this.prvSearch);
      }
      else if (this.selectedTab == 'archive') {
        this.tempMyLeadsArchiveIndex += 1;
        this.searchUser(this.prvSearch);
      }
    }
  }

  getLeadDate(date) {
    return moment(date).format("MM/DD/YYYY");
  }

  openArchivedialog(lead, type) {
    if (type == 'LE') {
      this.showUnArchiveBtn = false;
    }
    else if (type == 'AR') {
      this.showUnArchiveBtn = true;
    }
    this.leadDialog = lead;
    $("#originEvent").modal("show");
    console.log(this.leadDialog)
    this.leadProfileForm.controls['user_email'].setValue(this.leadDialog['user_email']);
    this.leadProfileForm.controls['user_phone'].setValue(this.leadDialog['user_phone']);
  }




  addRemoveLead(lead, status, listType) {
    var listIndex;
    var manageLeads = new URLSearchParams();
    manageLeads.set('lead_id', lead['lead_id']);
    manageLeads.set('is_archive', status);
    this.myLeadsService.manageArchive(manageLeads).subscribe(res => {
      this.successResponse(res);
      let refreshParams = new URLSearchParams();
      if (listType == 'LE') {
        listIndex = this.myLeadsList.indexOf(lead);
        this.myLeadsList[listIndex]['is_archive'] = true;
        this.myLeadsArchiveList.push(this.myLeadsList[listIndex]);
        this.myLeadsList.splice(listIndex, 1);
        this.myLeadsTotalCount -= 1;
        this.myLeadsArchiveTotalCount += 1;

        if (this.myLeadsList.length == 1) {
          refreshParams.set('page_no', "1");
          refreshParams.set('type', 'LE');
          this.loadLeads(refreshParams, 'LE');
        }
      }
      else if (listType == 'AR') {
        listIndex = this.myLeadsArchiveList.indexOf(lead);
        this.myLeadsArchiveList[listIndex]['is_archive'] = false;
        this.myLeadsList.push(this.myLeadsArchiveList[listIndex]);
        this.myLeadsArchiveList.splice(listIndex, 1);
        this.myLeadsTotalCount += 1;
        this.myLeadsArchiveTotalCount -= 1;

        if (this.myLeadsArchiveList.length == 1) {
          refreshParams.set('page_no', "1");
          refreshParams.set('type', 'AR');
          this.loadLeads(refreshParams, 'AR');
        }
      }
    }, err => this.errorResponse(err.json()));
  }

  onTabChange(tabName) {
    this.selectedTab = tabName;
    $('#searchUserInput').val('');
  }

  searchUser(userName) {
    if (userName.trim().length != 0) {
      if (this.lastSearchedLead != userName.trim()) {
        this.lastSearchedLead = userName.trim()
        let searchParams = new URLSearchParams();
        searchParams.set('name', userName.trim());
        if (this.selectedTab == 'leads') {
          searchParams.set('type', 'LE');
          searchParams.set('page_no', this.tempMyLeadsIndex.toString());
        } else if (this.selectedTab == 'archive') {
          searchParams.set('type', 'AR');
          searchParams.set('page_no', this.tempMyLeadsArchiveIndex.toString());
        }
        if (this.leadSearchSubscription) {
          this.leadSearchSubscription.unsubscribe();
        }
        this.leadSearchSubscription = this.myLeadsService.searchLeads(searchParams).subscribe(res => {
          this.prvSearch = userName;
          if (this.selectedTab == 'leads') {
            if (this.tempMyLeadsIndex > 1) {
              res['result']['records'].forEach(record => {
                this.myLeadsList.push(record);
              });
            }
            else {
              this.myLeadsList = res['result']['records'];
            }
            this.myLeadsTotalCount = res['result']['total_records_count'];
          }

          else if (this.selectedTab == 'archive') {
            if (this.tempMyLeadsArchiveIndex > 1) {
              res['result']['records'].forEach(record => {
                this.myLeadsArchiveList.push(record);
              });
            }
            else {
              this.myLeadsArchiveList = res['result']['records'];
            }
            this.myLeadsArchiveTotalCount = res['result']['total_records_count'];
          }
        }, err => console.log(err));
      }
    }
    else {
      this.prvSearch = '';
      this.myLeadsList = this.tempMyLeadsList;
      this.myLeadsArchiveList = this.tempMyLeadsArchiveList;

      this.myLeadsTotalCount = this.tempMyLeadsTotalCount;
      this.myLeadsArchiveTotalCount = this.tempMyLeadsArchiveTotalCount;

      this.tempMyLeadsIndex = 1;
      this.tempMyLeadsArchiveIndex = 1;
    }
  }

  leadSorting(type, filedName) {
    if (type == 'LE') {
      let LESortingParams = new URLSearchParams;
      if ($('#searchUserInput').val() != '' && $('#searchUserInput').val() != null) {
        LESortingParams.set('name', $('#searchUserInput').val())
      }
      if (this.LESortingObject[filedName] == undefined) {
        this.LESortingObject[filedName] = true;
        $('#LE_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
      }
      else {
        this.LESortingObject[filedName] = this.LESortingObject[filedName] === true ? false : true;
        if (this.LESortingObject[filedName]) {
          $('#LE_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
        } else {
          $('#LE_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
        }
      }
      this.LESortingList[0] = this.LESortingObject;
      LESortingParams.set('sort_list', JSON.stringify(this.LESortingList));
      LESortingParams.set('type', 'LE');
      this.loadLeads(LESortingParams, 'LE');
    }
    else if (type == 'AR') {
      let ARSortingParams = new URLSearchParams;
      if ($('#searchUserInput').val() != '' && $('#searchUserInput').val() != null) {
        ARSortingParams.set('name', $('#searchUserInput').val())
      }
      if (this.ARSortingObject[filedName] == undefined) {
        this.ARSortingObject[filedName] = true;
        $('#AR_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
      }
      else {
        this.ARSortingObject[filedName] = this.ARSortingObject[filedName] === true ? false : true;
        if (this.ARSortingObject[filedName]) {
          $('#AR_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
        } else {
          $('#AR_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
        }
      }
      this.ARSortingList[0] = this.ARSortingObject;
      ARSortingParams.set('sort_list', JSON.stringify(this.ARSortingList));
      ARSortingParams.set('type', 'AR');
      this.loadLeads(ARSortingParams, 'AR');
    }
  }

  exportCSV() {
    var csvParmas = new URLSearchParams();
    if (this.selectedTab == 'leads') {
      csvParmas.set('type', 'LE');
    } else if (this.selectedTab == 'archive') {
      csvParmas.set('type', 'AR');
    }
    if (this.leadsCSVSubscription) {
      this.leadsCSVSubscription.unsubscribe();
    }
    this.sucMessageResponseOnlyText('Export CSV Started...','');
    this.leadsCSVSubscription = this.myLeadsService.exportCSV(csvParmas).subscribe(res => {
      this.downloadFile(res, "my_leads.csv");
      this.sucMessageResponseOnlyText('Export CSV Completed','');
    }, err => this.errorResponse(err.json()));
  }

  //Start chat with selected HB.
  startChat(leadDetails: JSON): void {
    var client = {};
    client['user_name'] = leadDetails['user_name'];
    client['profile_image'] = leadDetails['user_profile_image'];
    client['chat_thread_id'] = leadDetails['user_id'];
    client['receiver_id'] = leadDetails['user_id'];
    client['last_message_time'] = "";
    client['last_message'] = '';
    this.chatService.setClientChatThread(client);
    this.router.navigate(['messaging']);
  }


  startNote(lead) {
    console.log('lead', lead)
    this.leadDialog = lead;
    $("#noteEvent").modal("show");
    this.newNote = '';
    this.myNoteList = []
    if (Object.keys(this.leadDialog['notes']).length === 0) {
      this.leadDialog['notes'] = []
    }
    this.myNoteList = this.leadDialog['notes'] || []
    // this.myLeadsService.getMyNotes(this.leadDialog.lead_id).subscribe(res => {
    //   console.log('Final Result', res)
    //   this.myNoteList = res.result.records
    //   this.newNote = '';
    // }, err =>
    //   this.errorResponse(err.json())
    // )
  }

  SaveNote() {
    console.log('Save notes')
    let noteParams = new URLSearchParams();
    noteParams.set("note", this.newNote.toString());
    noteParams.set('lead_id', this.leadDialog.lead_id);
    console.log(noteParams)

    // this.noteParams = {};
    // this.noteParams.note = this.newNote;
    // this.noteParams.lead_id = this.leadDialog.lead_id
    // this.noteParams.date = new Date()
    // this.noteParams.updated_date_time = new Date()
    // console.log(this.noteParams)
    // this.myNoteList.push(this.noteParams);
    // this.newNote = '';
    // this.noteParams = {}

    this.myLeadsService.addNewNote(noteParams).subscribe(res => {
      this.successResponse(res);
      var resObj = {
        'lead_id': res['result']['lead_id'],
        'updated_date_time': new Date(),
        'note': res['result']['note'],
        'id': res['result']['id'],
        'user': res['result']['user']
      }
      this.myNoteList.push(resObj);
      this.newNote = '';
      if (Object.keys(this.leadDialog['notes']).length === 0) {
        this.leadDialog['notes'] = []
      }
      this.leadDialog['count'] = this.leadDialog['count'] + 1
      // this.leadDialog['notes'].push(resObj)
    }, err =>
      this.errorResponse(err.json())
    )
  }
  showNote(note, manageType, index) {
    if (manageType == "UPDATE") {
      this.updateNoteIndex = index;
    }
    else if (manageType == "DELETE") {
      let deleteNoteParams = new URLSearchParams();
      deleteNoteParams.set('note_id', note.id);
      this.myLeadsService.deleteNote(deleteNoteParams).subscribe(res => {
        this.successResponse(res);
        this.myNoteList.splice(index, 1);
        this.leadDialog['count'] = this.leadDialog['count'] - 1
        // this.leadDialog['notes'].splice(index, 1);
      }, err => this.errorResponse(err.json()));
    }
  }

  manageNote(selectedNote, type, updatedNotes, noteIndex) {
    if (type == 'UPDATE') {
      let updateNoteParams = new URLSearchParams();
      updateNoteParams.set('note', updatedNotes);
      updateNoteParams.set('id', selectedNote.id);
      this.myLeadsService.updateNote(updateNoteParams).subscribe(res => {
        this.successResponse(res);
        this.myNoteList[noteIndex].note = updatedNotes;
        this.updateNoteIndex = undefined;
      }, err => this.errorResponse(err.json()));
    }
    else if (type == 'CANCEL') {
      this.updateNoteIndex = undefined;
    }
  }
  isValidNote() {
    if (this.newNote.trim().length != 0) {
      this.disableNotebtn = false;
    }
    else {
      this.disableNotebtn = true;
    }
  }
  utcDateFormat(date) {
    return moment(date).format('MM.DD.YYYY');
  }

  defineForms() {
    this.leadProfileForm = new FormGroup({
      user_email: new FormControl('', [Validators.required, Validators.email]),
      user_phone: new FormControl('', [Validators.required, Validators.minLength(12), Validators.maxLength(12)]),
    });
  }
  saveLead(form: FormGroup) {

    let params = new URLSearchParams();
    params.set("phone", form.value.user_phone);
    params.set("email", form.value.user_email);
    params.set('lead_id', this.leadDialog.lead_id);
    console.log(params)

    this.myLeadsService.updateLeadProfile(params).subscribe(res => {
      this.successResponse(res);
      this.leadDialog['user_phone'] = form.value.user_phone
      this.leadDialog['user_email'] = form.value.user_email
      // form.reset();
    }, err =>
      this.errorResponse(err.json())
    )

    console.log(form.value)

  }

  validateFormat(number: String) {
    this.leadProfileForm.controls['user_phone'].setValue(this.validateNumber(number));
  }

}
