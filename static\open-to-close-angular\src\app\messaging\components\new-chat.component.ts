import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { MessagingComponent } from '@app/messaging/components/messaging.component';
import { BaseComponent } from '@app/base/components/base.component';
import { ChatService } from '@app/messaging/service/chat-service';
import { ServiceLocator } from '@app/base/components/service-locator';

@Component({
  selector: 'new-chat',
  templateUrl: '../views/new-chat.html',
  styleUrls: ['../css/messaging.component.css']
})
export class NewChatComponent extends BaseComponent implements OnInit,OnDestroy {

  public myClientList = [];
  public tempMyClientList = [];
  public chatService : ChatService;
  public getAllClientSubscription;

  constructor() { 
    super();
    this.chatService = ServiceLocator.injector.get(ChatService);
  }

  ngOnInit(){
   this.getAllClientSubscription = this.chatService.getAllMyClient().subscribe(res =>{
      this.myClientList =  res['result'];
      this.tempMyClientList = res['result'];
    },err=>{
      console.log(err);
    });
  }

  selectedClient(client){
    client['chat_name'] = client['user_name'];
    client['profile_image'] = client['profile_image'];
    client['chat_thread_id'] = client['user_id'];
    client['receiver_id'] = client['user_id'];
    client['last_message_time'] = "";
    this.chatService.startNewChat.emit(client);
  }

  filterUser(userName){
    if(userName.trim().length !=0){
      this.myClientList = this.tempMyClientList.filter(name => {
        return name['user_name'].toLowerCase().includes(userName.toLowerCase());
      });
    }else{
      this.myClientList = this.tempMyClientList;
    }
  }

  ngOnDestroy(){
    if(this.getAllClientSubscription != undefined){
      this.getAllClientSubscription.unsubscribe();
    }
  }
}
