import { Component, OnInit,Input,<PERSON><PERSON><PERSON><PERSON>,Ng<PERSON>one } from '@angular/core';
import { ListingComponent } from '@app/myListings/components/listing.component';
import { FormControl,Validators,FormBuilder,FormGroup ,FormArray} from '@angular/forms';
import { Params } from '@angular/router/src/shared';
import { State, City, ZipCode } from '@app/profile/models/profile';
import { AddProperty } from '@app/myListings/models/add-property';
import { BaseComponent } from '@app/base/components/base.component';
import { isNumber } from 'util';
import { ListingAgentProfileComponent } from '@app/profile/component/listing-agent-profile.component';
import { from } from 'rxjs/observable/from';
import { Location } from '@angular/common';

declare var $;

@Component({
  selector: 'listing-add-new',
  templateUrl: '../views/listing-add-new.html',
  styleUrls: ['../css/listing.component.css']
})

export class ListingAddNewPropertyComponent extends ListingComponent implements <PERSON><PERSON><PERSON><PERSON>,OnDestroy {

  public propertyActionType : String;
  public mainurl : String;
  addPropertyFormGroup:FormGroup;
  propertyInternalFormGroup:FormGroup;
  propertyExternalFormGroup:FormGroup;
  propertyBuildingFormGroup:FormGroup;
  propertyLocationFormGroup:FormGroup;
  public static propertyDetail: AddProperty = new AddProperty();

  // propertyTypeList = ['Apartment','Condominium','Duplex','Manufactured Home','Mobile Home','Quadruplex','Single Family Attached',
  //                     'Single Family Detached','Townhouse','Triplex'];

  propertyTypeList = ['Single Family - Detached', 'Loft Style', 'Moduler/Pre-Fab', 'Mfg/Mobile Housing', 'Gemini/Twin Home',
                    'Apartment Style/Flat', 'Townhouse', 'Patio Home'];

  // styleList = ['A Frame','Art Deco','Bungalow','Cape Cod','Colonial','Contemporary','Conventional','Cottage','Craftsman',
  //                     'Creole','Dome','Dutch Colonial','English','Federal','French','French Provincial','Georgian','Gothic Revival','Greek Revival',
  //                     'High Rise','Historical','International','Italianate','Loft','Mansion','Mediterranean','Modern','Monterey','Mountain','National',
  //                     'Neoclassical','New Traditional','Prairie','Pueblo','Queen Anne','Rambler','Ranch','Regency','Rustic','Saltbox','Santa Fe','Second Empire',
  //                     'Shed','Shingle','Shotgun','Spanish','Spanish Eclectic','Split Level','Stick','Tudor','Victorian','Other'];

  styleList = ['Detached', 'Stacked', 'Attached'];

  yearBuiltList = [1962,1963,1964];
  BedroomsList = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20];
  BathroomsizeList = [1,2,3,4,5];
  numberList = ['1','2','3','4'];
  // schoolTypeList = ['Elementary','High','Middle','Junior High'];
  schoolTypeList = ['Elementary School', 'Jr. High School', 'High School'];
  statusTypeList = [{'name':'Active', 'value' : 'Active'},{'name':'Coming Soon', 'value':'PRE-MLS/Coming Soon'}];

  // expenseTypeList = ['Trash Fee','Yard Care Fee','Home Owner Assessments Fee'];
  expenseTypeList = ['Association List'];

  public Expsenses: boolean = false;
  public removeTaxes: boolean = false;
  public url;
  public maxImageSelect:String = '';
  public fileTypeError:String = '';
  public selectedImages = [];
  public temp = [];
  public tempSelectedImages = [];
  public text:any = 1;
  public length_valid_parcel_id:boolean=false;

  appliances = [{'BOR':'Barbeque or Grill'},{'RNG':'Range'},{'COF':'Coffee System'},{'RGB':'Range - Built In'},{'CTP':'Cooktop'},{'RGD':'Range - Dual'},{'CEL':'Cooktop - Electric'},{'RGE':'Range - Electric'},{'CGS':'Cooktop - Gas'},{'RGG':'Range - Gas'},{'CID':'Cooktop - Induction'},{'RGI':'Range - Induction'},{'DWR':'Dishwasher'},{'RGO':'Range - Other'},{'DYR':'Dryer'},{'RPE':'Rangetop - Electric'},{'DDF':'Dryer - Dual Fuel'},{'RPG':'Rangetop - Gas'},{'DEL':'Dryer - Electric'},{'RPI':'Rangetop - Induction'},
                {'DAS':'Dryer - Gas'},{'REF':'Refrigerator'},{'FZR':'Freezer'},{'REB':'Refrigerator - Bar'},{'GDP':'Garbage Disposer'},{'RBI':'Refrigerator - Built-in'},{'ICM':'Ice Maker'},{'RED':'Refrigerator - Drawer'},{'MCW':'Microwave'},{'RSS':'Refrigerator - Side by Side'}, {'OVN':'Oven'},{'RFU':'Refrigerator - Undercounter'},{'OCN':'Oven - Convection'},{'RFW':'Refrigerator - Wine Storage'},{'OVD':'Oven - Double'},{'THC':'Trash Compactor'}, {'OGS':'Oven - Gas'},{'VMS':'Vacuum System'},{'OVC':'Oven - Self Cleaning'},
                {'VTH':'Vent Hood'},{'OVS':'Oven - Steam'},{'NON':'None'}, {'OVT':'Oven - Twin'},{'OTR':'Other'}];

  basement=[{'FNS':'Finished'},{'NNE':'None'},{'PFN':'Partially finished'},{'UFN':'Unfinished'}];

  floor_Covering=[{'BMB':'Bamboo'},{'PQW':'Parquet Wood'},{'BCK':'Brick'},{'RHI':'Rough-in'},{'CPT':'Carpet'},{'STE':'Slate'},{'CFL':'Carpet - Full'},{'SWD':'Soft Wood'},{'CPL':'Carpet - Partial'},{'SWD':'Solid Wood'},{'CRT':'Concrete'},{'SPY':'Specialty'},{'CTB':'Concrete - Bare'},{'SCT':'Specialty Concrete'},{'CPI':'Concrete - Painted'},{'TLE':'Tile'},{'CRK':'Cork'},{'TCM':'Tile - Ceramic'},{'DGE':'Drainage'},{'TPN':'Tile - Porcelain'},{'EWD':'Engineered Wood'},{'TSE':'Tile - Stone'},
                  {'GSS':'Glass'},{'TOS':'Tile or Stone'},{'GTE':'Granite'},{'VYL':'Vinyl'},{'HWD':'Hardwood'},{'WOD':'Wood'},{'LTE':'Laminate'},{'UKN':'Unknown'},{'LUM':'Linoleum'},{'NON':'None'},{'LRN':'Load Restriction'},{'OTR':'Other'},{'MBL':'Marble'}];

  rooms=[{'RAM':'Atrium'},{'RLB':'Library'},{'RAF':'Attic - Finished'},{'RLR':'Living Room'},{'RAU':'Attic - Unfinished'},{'RLT':'Loft'},{'RBF':'Basement - Finished'},{'RMB':'Master Bathroom'},{'RBU':'Basement - Unfinished'},{'RMR':'Media Room'},{'RBM':'Bedroom'},{'RMD':'Mudroom'},{'RBM':'Bonus Room'},{'RMR':'Music Room'},{'RBN':'Breakfast Nook'},{'ROF':'Office'},{'RBF':'Breakfast Room'},{'RQB':'One-Quarter Bath'},{'RCR':'Crafts Room'},
        {'RPO':'Patio'},{'RDN':'Den'},{'RPL':'Photo Lab'},{'RDR':'Dining Room'},{'RRR':'Recreational Room'},{'REK':'Eat-In Kitchen'},{'RSA':'Sauna'},{'REY':'Efficiency'},{'RSQ':'Servant Quarters'},{'REP':'Enclosed Patio'},{'RSR':'Sitting Room'},{'RFR':'Family Room'},{'RSM':'Solarium'},{'RFL':'Florida Room'},{'RSE':'Storage'},{'RFD':'Formal Dining Room'},{'RSO':'Studio'},{'RFO':'Foyer'},{'RMS':'Study'},{'RFB':'Full Bath'},{'RSN':'Sunroom'},{'RGR':'Game Room'},
        {'RTR':'Theatre'},{'RGM':'Great Room'},{'RTU':'Three-Quarter Bath'},{'RGH':'Guest House'},{'RUT':'Utility'},{'RHB':'Half Bath'},{'RWC':'Walk-In Closet'},{'RLS':'In-Law Suite'},{'RWP':'Walk-In Pantry'},{'RKN':'Kitchen'},{'RWK':'Wok Kitchen'},{'RMI':'Kitchenette'},{'RWS':'Workshop'},{'RLC':'Laundry Closet'},{'RUK':'Unknown'},{'RLR':'Laundry Room'},{'ROT':'Other'}];

  indoor_Features=[{'DPW':'Double Pane Windows'},{'WTB':'Wet bar'},{'ETR':'Elevator'},{'ICM':'Intercom'},{'JBT':'Jetted Bathtub'},{'CBR':'Cable Ready'},{'SNA':'Sauna'},{'WED':'Wired'},{'SLT':'Skylight'},{'SSM':'Security system'},{'VCG':'Vaulted Ceiling'},{'SLS':'Skylights'}];

  building_Amenities=[{'OAC':'Over 55+ active community'},{'ELR':'Elevator'},{'ALC':'Assisted living community'},{'FNC':'Fitness center'},{'BBC':'Basketball court'},
                    {'GEY':'Gated entry'},{'SCT':'Sports court'},{'SRG':'Storage'},{'CAS':'Controlled access'},{'NTP':'Near transportation'},{'DRM':'Doorman'},{'TNC':'Tennis court'}];

  architectural_Style=[{'AFM':'A Frame'},{'MDN':'Modern'},{'ADO':'Art Deco'},{'MTY':'Monterey'},{'BLW':'Bungalow'},{'MTI':'Mountain'},{'COD':'Cape Cod'},{'NOL':'National'},{'CAL':'Colonial'},{'NCL':'Neoclassical'},{'CRY':'Contemporary'},{'NTL':'New Traditional'},{'CTL':'Conventional'},{'OTH':'Other'},{'CTE':'Cottage'},{'PIE':'Prairie'},{'CTN':'Craftsman'},{'PLO':'Pueblo'},{'CLE':'Creole'},{'QNE':'Queen Anne'},{'DME':'Dome'},{'RLR':'Rambler'},{'DCL':'Dutch Colonial'},{'RCH':'Ranch'},
                      {'ENG':'English'},{'RGY':'Regency'},{'FDL':'Federal'},{'RTC':'Rustic'},{'FNC':'French'},{'SBX':'Saltbox'},{'FPL':'French Provincial'},{'STF':'Sante Fe'},{'GON':'Georgian'},{'SDE':'Second Empire'},{'GRL':'Gothic Revival'},{'SED':'Shed'},{'GRL':'Greek Revival'},{'SLE':'Shingle'},{'HRE':'High Rise'},{'SGN':'Shotgun'},{'HTC':'Historical'},{'SPH':'Spanish'},{'ITL':'International'},{'SNE':'Spanish Eclectic'},{'ITE':'Italianate'},{'STL':'Split Level'},{'LFT':'Loft'},{'SCK':'Stick'},{'MON':'Mansion'},{'TDR':'Tudor'},{'MTR':'Mediterranean'},{'VTN':'Victorian'}];

  exterior_Type=[{'ADB':'Adobe'},{'LSG':'Log Siding'},{'AMS':'Aluminum Siding'},{'MTE':'Masonite'},{'ATS':'Asbestos'},{'MRY':'Masonry'},{'ALT':'Asphalt'},{'MTL':'Metal'},{'BLK':'Block'},{'MLS':'Metal Siding'},{'BAB':'Board and Batten'},{'NON':'None'},{'BCK':'Brick'},{'OTH':'Other'},{'BAW':'Brick and Wood'},{'PCE':'Poured Concrete'},{'BKV':'Brick Veneer'},{'SLS':'Shingles (Not Wood)'},{'CSG':'Cedar Siding'},{'SNE':'Stone'},{'CMB':'Comb'},{'SNV':'Stone Veneer'},{'CON':'Composition'},{'SCO':'Stucco'},{'CSS':'Composition Shingles'},{'SOS':'Stucco - Synthetic'}
                ,{'CNT':'Concrete'},{'TLE':'Tile'},{'CTB':'Concrete Block'},{'TTU':'Tilt-up (Pre-Cast Concrete)'},{'EIF':'EIFS'},{'UNK':'Unknown'},{'FRG':'Fiberglass'},{'VLS':'Vinyl Siding'},{'GLS':'Glass'},{'WOD':'Wood'},{'HBD':'Hardboard'},{'WSE':'Wood Shingle'},{'LOG':'Log'},{'WDS':'Wood Siding'}];

  outdoor_Amenities=[{'PRH':'Porch'},{'GHE':'Greenhouse'},{'BPO':'Balcony / Patio'},{'HTS':'Hot tub / spa'},{'RVP':'RV parking'},{'SUA':'Sauna'},{'BRA':'Barbecue area'},{'LWN':'Lawn'},{'DCK':'Deck'},{'STM':'Sprinkler system'}
                    ,{'DOK':'Dock'},{'POD':'Pond'},{'FYD':'Fenced yard'},{'POL':'Pool'},{'GDN':'Garden'}];

  // parking=[{'ALY':'Alley'},{'OSD':'Oversized'},{'ASD':'Assigned'},{'OWD':'Owned'},{'BOT':'Boat'},{'PLT':'Parking Lot'},{'BIN':'Built-in'},{'PGS':'Parking Structure'},{'CRT':'Carport'},{'POS':'Paved or Surfaced'},{'CML':'Commercial'},{'POE':'Pole'},{'CRD':'Covered'},{'PRC':'Porte-Cochere'},{'DWY':'Driveway'},{'PTH':'Pull-through'},{'FEE':'Fee'},{'RMP':'Ramp'},{'FCD':'Fenced'},{'PRV':'RV'},{'GRG':'Garage'},{'SRD':'Secured'},{'GGA':'Garage - Attached'},{'SAN':'Side Apron'},{'GGD':'Garage - Detached'},
  //         {'SBS':'Side by Side'},{'GTD':'Gated'},{'SND':'Special Needs'},{'GCT':'Golf Cart'},{'SKD':'Stacked'},{'GUT':'Guest'},{'TDM':'Tandem'},{'HTD':'Heated'},{'TKU':'Tuck-Under'},{'LSD':'Leased'},{'USG':'Unassigned'},{'MCS':'Mechanics'},{'UGB':'Underground/Basement'},{'MTR':'Meter'},{'UPR':'Unimproved'},{'MIX':'Mixed'},{'VLT':'Valet'},{'OFA':'Off Alley'},{'WKS':'Workshop'},{'OST':'Off Street'},{'ZOP':'Zoned Permit'},{'OFS':'Offsite'},{'UNK':'Unknown'},{'OSR':'On Street'},{'OTH':'Other'},{'OPN':'Open'},{'NON':'None'}];
  parking = [{"AGC":"Attch'd Gar Cabinets"}, {"ASP":"Assigned Parking"}, {"DTH":"Detached"}, {"EDO": "Electric Door Opener"}, {"ELG":"Extnded Lngth Garage"}, {"GCG":"Golf Cart Garage"}, {"OHG":"Over Height Garage"}, {"RVE":"Rear Vehicle Entry"}, {"SSA":"Separate Strge Area"}, {"SVE":"Side Vehicle Entry"}, {"TDG":"Tandem Garage"}, {"UAP":"Unassigned Parking"}, {"RVG":"RV Gate"}, {"RVP":"RV Parking"}, {"CMS":"Community Structure"}, {"GTP":"Gated Parking"}, {"PDR":"Permit/Decal Req'd"}, {"VEL":"Valet"}, {"RGG":"RV Garage"},
             {"APS":"Addtn'l Purchasable"}, {"TCT":"Temp Controlled"}, {"HNG":"Hangar"}, {"EFG":"Dir Entry frm Garage"}, {"SDW":"Shared Driveway"}];

  roof_Type=[{'ANM':'Aluminum'},{'SLT':'Slate'},{'ATS':'Asbestos'},{'SPL':'Solar Panel'},{'ALT':'Asphalt'},{'SSM':'Standing Seam'},{'BTP':'Built-up'},{'STL':'Steel'},{'CTL':'Clay Tile'},{'TAG':'Tar and Gravel'},{'CSL':'Composition Shingle'},{'TED':'Thatched'},{'COT':'Concrete Tile'},{'TLE':'Tile'},{'CPT':'Copper'},{'UNE':'Urethane'},{'CML':'Corrugated Metal'},{'WDS':'Wood Shake'},{'GEN':'Green'},{'WDS':'Wood Shingle'},{'MCS':'Masonite or Cement Shake'},{'UNK':'Unknown'},{'MNE':'Membrane'},{'OTH':'Other'},{'MTL':'Metal'}];

  cooling_type=[{'ATF':'Attic Fan'},{'RGL':'Radiant Floor Ground Loop'},{'CFN':'Celing Fan(s)'},{'RFE':'Refrigerator/Evaporative'},{'CAC':'Central A/C'},{'SAC':'Solar A/C - Active'},{'CLE':'Central Evaporative'},{'SPC':'Solar A/C - Passive'},{'CFN':'Central Fan'},{'UNK':'Unknown'},{'CDW':'Chilled Water'},{'WUC':'Wall Unit(s) A/C'},{'DFS':'Dehumidifiers'},{'WUE':'Wall Unit(s) Evaporative'},{'DLR':'Dessicant Cooler'},{'WOC':'Window Unit(s) A/C'},{'EPE':'Evaporative'},{'WOE':'Window Unit(s) Evaporative'},{'HPS':'Heat Pumps'},{'ZAC':'Zoned A/C'},{'PTl':'Partial'},
                {'NON':'None'},{'RTF':'Radiant Floor'},{'OTR':'Other'}];

  heating_System=[{'BRD':'Baseboard'},{'RDT':'Radiant'},{'CFE':'Central Furnace'},{'RCG':'Radiant Ceiling'},{'EAF':'Electric Air Filter'},{'RFL':'Radiant Floor'},{'FPE':'Fireplace'},{'RTR':'Radiator'},{'FPI':'Fireplace - Insert'},{'SWC':'S-W Changeover'},{'FFE':'Floor Furnace'},{'SAE':'Solar Active'},{'FRW':'Floor Wall'},{'SAP':'Solar Active and Passive'},{'FAR':'Forced Air'},{'SPE':'Solar Passive'},{'GMl':'Geothermal'},{'SHR':'Space Heater'},{'GAR':'Gravity Air'},{'STM':'Steam'},{'GHW':'Gravity Hot Water'},{'STV':'Stove'},
                  {'HPP':'Heat Pump'},{'WUT':'Wall Unit'},{'HWR':'Hot Water'},{'ZND':'Zoned'},{'HWF':'Hot Water Radiant Floor'},{'UNK':'Unknown'},{'HFF':'Humidifier'},{'NON':'None'},{'PSV':'Pellet Stove'},{'OTR':'Other'}];

  heating_fuel=[{'BGS':'Butane Gas'},{'PLT':'Pellet'},{'COL':'Coal'},{'PGS':'Propane Gas'},{'ELC':'Electric'},{'SOL':'Solar'},{'GTL':'Geothermal'},{'SLP':'Solar Panel'},{'KRO':'Kerosene'},{'WOD':'Wood'},{'NGS':'Natural Gas'},{'UNK':'Unknown'},{'OIL':'Oil'},{'NON':'None'},{'PHP':'Passive Heat Pump'},{'OTR':'Other'},{'PSL':'Passive Solar'}];

  // water = [{'DCK':'Dock'},{'HTS':'Hot Tub Spa'},{'PND':'Pond'},{'IWF':'Is Waterfront'},{'POL':'Pool'}];
  water = [{"BPC":"Both Private & Community"}, {"PVO":"Private Only"}, {"COM":"Community Only"}, {"NON":"None"}];

  landscape =[{'GDN':'Garden'},{'LWN':'Lawn'},{'GHS':'Greenhouse'},{'SRS':'Sprinkler System'}];

  // security = [{'GTE':'Gated Entry'},{'DMN':'Doorman'},{'STS':'Security System'}];
  security = [{ "CML":"Community Laundry"}, { "COL":"Coin-Op Laundry"}, { "CMP":"Community Pool"}, { "CPH":"Community Pool Htd"},{ "CMS":"Community Spa" }, { "CSH":"Community Spa Htd" }, { "GDE":"Guarded Entry" }, { "HTD":"Historic District" }, { "LSD":"Lake Subdivision" }, { "GCM":"Gated Community" },
              { "OSG":"On-Site Guard" }, { "NLR":"Near Light Rail Stop" }, { "GCS":"Golf Course" }, { "HFL":"Horse Facility" }, { "CCR":"Concierge" }, { "TPS":"Transportation Svcs" }, { "NBS":"Near Bus Stop" }, { "CTC":"Comm Tennis Court(s)" }, { "HRB":"Handball/Racquetball" }, { "CPG":"Children's Playgrnd" }, { "BWP":"Biking/Walking Path" },
              { "CRR":"Clubhouse/Rec Room" }, { "CMR":"Community Media Room" }, { "WFL":"Workout Facility" }, { "RAC":"Runway Access" }];

  // accessibility = [{'DAS':'Disabled Access'}];
  accessibility = [{ "ZGE":"Zero-Grade Entry" }, { "DAW":"Dr/Access 32in+ Wide" }, { "HWW":"Hallways 36in+ Wide" }, { "HNF":"Hard/Low Nap Floors" }, { "BRS":"Bath Roll-In Shower" }, { "BRT":"Bath Raised Toilet" }, { "BGB":"Bath Grab Bars" }, { "BUS":"Bath Roll-Under Sink" }, { "BLF":"Bath Lever Faucets" },
                   { "KAL":"Ktch Apps Low/Secure" }, { "KLC":"Ktch Low Counters" }, { "KRS":"Ktch Roll-Under Sink" }, { "KMR":"Ktch Modified Range" }, { "KCB":"Ktch Low Cabinetry" }, { "LHD":"Lever Handles" }, { "TVM":"Tactile/Visual Mrkrs" }, { "CBR":"Closet Bars 15-48in" }, { "RMP":"Ramps" }, { "SLF":"Stair Lifts" },
                   { "RMD":"Remote Devices" }, { "ECC":"Exterior Curb Cuts" }, { "BTR":"Bath 60in Trning Rad" }, { "BSC":"Bath Scald Ctrl Fct" }, { "BIP":"Bath Insulated Pipes" }, { "KTR":"Ktch 60in Trning Rad" }, { "KIP":"Ktch Insulated Pipes" }, { "KRD":"Ktch Raised Dishwshr" }, { "KSO":"Ktch Side Open Oven" }, { "PRE":"Pool Ramp Entry" },
                   { "PPL":"Pool Power Lift" },{ "MEE":"Mltpl Entries/Exits" }];

  OtherExternalFeatures = [{'BEA':'Barbecue Area'},{'PCH':'Porch'},{'DCK':'Deck'},{'SPC':'Sports Court'},{'PIO':'Patio'}];

  // view_Types =[{'APT':'Airport'},{'MTN':'Mountain'},{'ARG':'Average'},{'NON':'None'},{'BUF':'Bluff'},{'OCN':'Ocean'},{'BGE':'Bridge'},{'OTH':'Other'},{'CYN':'Canyon'},{'PRM':'Panorama'},{'CIT':'City'},{'PRK':'Park'},{'DST':'Desert'},{'RNE':'Ravine'},{'FOT':'Forest'},{'RVR':'River'},{'GFC':'Golf Course'},{'TTL':'Territorial'},{'HOR':'Harbor'},
  //             {'UNK':'Unknown'},{'HIL':'Hills'},{'VLY':'Valley'},{'LKE':'Lake'},{'VTA':'Vista'},{'MNA':'Marina'},{'WTR':'Water'}]
  view_Types = [{ "ALY": "Alley" }, { "ATW": "Adjacent to Wash" }, { "BCA": "Borders Common Area" }, { "BPL": "Border Pres/Pub Lnd" }, { "CDS": "Cul-De-Sac Lot" }, { "CLT": "Corner Lot" },
    { "CLV": "City Light View(s)" }, { "GCL": "Golf Course Lot" }, { "HSL": "Hillside Lot" }, { "MTV": "Mountain View(s)" }, { "NSE": "North/South Exposure" }, { "SNP": "Street(s) Not Paved" },
    { "WFL": "Waterfront Lot" }, { "RHH": "Nat Reg Historic Hms" }];

  // otherBuildingCon = [{'NWC':'New Construction'},{'YRU':'Year Updated'}];
  otherBuildingCon = [{ "ADB":"Adobe" }, { "BLK":"Block" }, { "BRK":"Brick" }, { "FRM":"Frame - Metal" }, { "FRW":"Frame - Wood" }, { "OSR":"Other (See Remarks)" },
                      { "SMB":"Slump Block" }, { "SFI":"Spray Foam Insulatn" }, { "BCL":"Blown Cellulose" }, { "STB":"Straw-bale" }, { "RME":"Rammed Earth" }, { "PNC":"Panelized Constrctn" },
                      { "ICF":"Insltd Concrete Form" }, { "LVI":"Low VOC Insulation" }, { "IRL":"ICAT Recessed Lights" }, { "LVW":"Low VOC Wood Products" }, { "DPA":"Ducts Professionally Air-Sealed" }];

  appliancesList:any[] = [];
  basementList:any[] = [];
  floor_CoveringList:any[] = [];
  roomsList:any[] = [];
  indoor_FeaturesList:any[] = [];
  building_AmenitiesList:any[] = [];
  architectural_StyleList:any[] = [];
  exteriorList:any[] = [];
  outdoor_AmenitiesList:any[] = [];
  parkingList:any[] = [];
  roofList:any[] = [];
  cooling_typeList:any[] = [];
  heating_SystemList:any[] = [];
  heating_fuelList:any[] = [];
  waterList:any[] = [];
  landscapeList:any[] = [];
  securityList:any[] = [];
  accessibilityList :any [] = [];
  OtherExternalFeaturesList : any [] = [];
  addViewTypeList : any [] = [];
  addOBCList : any [] = [];

  stateList: State[] = [];
  cityList: City[];
  zipCodeList: ZipCode[];

  deletedTaxesList = [];
  deletedExpsensesList = [];

  public taxesForm: FormGroup;
  public expenseForm: FormGroup;

  private propertyId;
  builtingYear = "^((\\+91-?)|0)?[0-9]{4}$";

  public showStateLabel : Boolean = false;
  public showCityLabel : Boolean = false;
  public showZipCodeLabel : Boolean = false;
  public showBedroomsLabel : Boolean = false;
  public showFullBathsLabel : Boolean = false;
  public showPropertyStyleLabel : Boolean = false;
  public showPropertyTypeLabel : Boolean = false;

  public showCitySearchResult : Boolean = false;
  public showZipCodeSearchResult : Boolean = false;
  public showSchool1Label : Boolean = false;
  public showSchool2Label : Boolean = false;
  public showSchool3Label : Boolean = false;

  public showStatusLabel : Boolean = false;

  public selectedCity = '';
  public selectedState = '';
  public selectedZipCode = '';
  public selectedCityId = '';

  public isSeletedCity : Boolean = false;
  public isSeletedZipCode : Boolean = false;

  // public propertyImageBorderId = '';
  public showImgSaveChanges : Boolean = false;

  public isListhubProperty : Boolean = false;
  public isManuallySyndicate : Boolean = false;
  private addEditPropertyRouting;

  public citySearchSubscription: any;
  public zipSearchSubscription: any;

  public lastSearchedCity: String = "";
  public lastZipCodeSearch: String = "";

  public isImageUploading : boolean = false;

  constructor(private formBuilder: FormBuilder,private ExformBuilder: FormBuilder,zone: NgZone,private location: Location) {
    super(zone);
  }

  ngOnInit() {
    var self = this;
    $(document).ready(function()
      {
        $(document).mouseup(function(e)
        {
          if(e.target.id != 'city_menu_li' && e.target.id != 'city_menu_ul'){
            self.showCitySearchResult = false;
          }
          if(e.target.id != 'zipcode_menu_li' && e.target.id != 'zipcode_menu_ul'){
            self.showZipCodeSearchResult = false;
          }
        });
      });

    this.initData();

    this.taxesForm = this.formBuilder.group({
      taxes: this.formBuilder.array([this.createItem()])
    });

    this.expenseForm = this.ExformBuilder.group({
      customerName: '',
      email: '',
      expsenses: this.ExformBuilder.array([this.createItemEX()])
    });
    // this.addPropertyFormGroup =  this.ExformBuilder.group({
    //   listing_status: 'Active',
    // });
    this.addEditPropertyRouting = this.route.queryParams.subscribe((params:Params)=>{
      this.mainurl = this.router.routerState.snapshot.url;
      if(this.mainurl.includes('edit-property')){
        this.propertyActionType = 'edit';
        this.getPropertyDetail(params['propertyId']);
        this.propertyId = params['propertyId'];
      }
      else
      {
        this.propertyActionType = 'newProperty';
        if(params['propertyId'] != undefined){
          this.getPropertyDetail(params['propertyId']);
          this.propertyId = params['propertyId'];
          this.propertyActionType = 'edit';
        }
      }
    });
  }

  initDetail(){
    this.addPropertyFormGroup = new FormGroup({
      street: new FormControl('',Validators.required),
      listing_status: new FormControl('Active',Validators.required),
      unit_number: new FormControl(''),
      agent_name: new FormControl(''),
      brokerage_name: new FormControl(''),
      city_name: new FormControl(''),
      city: new FormControl(null,Validators.required),
      state: new FormControl(null,Validators.required),
      zipcode_code: new FormControl(''),
      zipcode: new FormControl(null,Validators.required),
      home_price: new FormControl('',Validators.required),
      bedroom : new FormControl(null,Validators.required),
      full_bath: new FormControl(null,Validators.required),
      lot_size: new FormControl(''),
      living_area : new FormControl('',Validators.required),
      property_type :new FormControl(null,Validators.required),
      property_style :new FormControl(null),
      year_built :new FormControl('',[Validators.minLength(4), Validators.maxLength(4)]),
      property_description: new FormControl(''),
      builder: new FormControl(''),
      parcel_id: new FormControl('',Validators.required)
    });

    this.propertyInternalFormGroup = new FormGroup({
      rooms : new FormControl('',Validators.required)
    });

    this.propertyExternalFormGroup = new FormGroup({
      water : new FormArray ([]),
      parking : new FormArray ([]),
      parking_total_space : new FormControl(),
      garage_space : new FormControl(),
      accessibility : new FormArray ([]),
      security : new FormArray ([]),
    });

    this.propertyBuildingFormGroup = new FormGroup({
      // condo_floor_no : new FormControl(),
      // building_unit_count : new FormControl (),
      // building_floors : new FormControl (),
      building_construction : new FormArray ([]),
      view_type: new FormArray([])
    });

    this.propertyLocationFormGroup = new FormGroup({
      district : new FormControl(),
      school_1_name : new FormControl(''),
      school_1_type :new FormControl(null),
      school_2_name: new FormControl(''),
      school_2_type: new FormControl(null),
      school_3_name: new FormControl(''),
      school_3_type: new FormControl(null),
      neighborhood: new FormControl(''),
      subdivision: new FormControl(''),
      county: new FormControl(''),
      directions: new FormControl(''),
      // elevation: new FormControl(''),
      latitude: new FormControl(''),
      longitude: new FormControl('')
    });
  }

  async initData(): Promise<any>{
    await this.initDetail();
    // this.profileService.getStates('US').subscribe(res => {
    //   this.stateList = [];
    //   this.zipCodeList = [];
    //   this.cityList = [];
    // });
  }

  searchCity(cityName){
    this.isSeletedCity = false;
    if(cityName.trim().length !=0){
      if(this.lastSearchedCity != cityName.trim()){
        this.lastSearchedCity = cityName.trim();

        this.showCitySearchResult = true;
        var cityPramas = new URLSearchParams();
        cityPramas.set('q',cityName.trim());
        if(this.citySearchSubscription){
          this.citySearchSubscription.unsubscribe();
        }
        this.citySearchSubscription = this.myListingService.searchCity(cityPramas).subscribe(res =>{
          this.cityList = res;
        },err => console.log(err))
      }
    }else{
      this.showCitySearchResult = false;
    }
  }

  searchZipCode(zipCode){
    this.isSeletedZipCode = false;
    if(zipCode.trim().length !=0){
      if(this.lastZipCodeSearch != zipCode.trim()){
        this.lastZipCodeSearch = zipCode.trim();
        this.showZipCodeSearchResult = true;
        var zipCodePramas = new URLSearchParams();
        zipCodePramas.set('code',zipCode.trim());
        zipCodePramas.set('city',this.selectedCityId);
        if(this.zipSearchSubscription){
          this.zipSearchSubscription.unsubscribe();
        }
        this.zipSearchSubscription = this.myListingService.searchZipCode(zipCodePramas).subscribe(res =>{
          this.zipCodeList = res;
        },err => console.log(err))
      }
    }else{
      this.showZipCodeSearchResult = false;
    }
  }

  onCitySelect(value){
    this.selectedState = value['state_name'];
    this.selectedCityId = value['city_id'];

    var stateObj=[{
      name : value['state_name'],
      id : value['state_id'],
      country : 1
    }];

    this.addPropertyFormGroup.controls.city.patchValue(value['city_id']);
    this.selectedCity = value['city_name'];
    this.addPropertyFormGroup.controls.city_name.setValue(this.selectedCity);

    this.stateList = stateObj;

    this.addPropertyFormGroup.controls.state.patchValue(value['state_id']);
    this.showStateLabel = true;
    this.selectedZipCode = '';
    this.addPropertyFormGroup.controls.zipcode.setValue('');
    this.addPropertyFormGroup.controls.zipcode_code.setValue('');
    this.showCitySearchResult = false;
    this.zipCodeList = [];
    this.isSeletedCity = true;
    this.isSeletedZipCode = false;
  }

  onZipCodeSelect(value){
    this.addPropertyFormGroup.controls.zipcode.patchValue(value['id']);
    this.selectedZipCode = value['code'];
    this.addPropertyFormGroup.controls.zipcode_code.patchValue(this.selectedZipCode);
    this.showZipCodeSearchResult = false;
    this.isSeletedZipCode = true;
  }


  getCityList(state_id){
    this.showStateLabel = true;
    // this.addPropertyFormGroup.controls.city.setValue(null);
    // this.addPropertyFormGroup.controls.zipcode.setValue(null);
    // this.profileService.getCities(state_id).subscribe(res => {
    //   this.cityList = res['result'];
    // }, err => this.errorResponse(err.json()));
  }

  // getZipCode(city_id){
  //   this.showCityLabel = true;
  //   this.addPropertyFormGroup.controls.zipcode.setValue(null);
  //   this.profileService.getZipCodes(city_id).subscribe(res => {
  //     this.zipCodeList = res['result'];
  //   }, err => this.errorResponse(err.json()));
  // }

  setZipCodeLabel(){
    this.showZipCodeLabel = true;
  }

  showLabel(dropDownName){
    if(dropDownName == 'Bedrooms'){
      this.showBedroomsLabel = true;
    }
    else if(dropDownName == 'Full Baths'){
      this.showFullBathsLabel = true;
    }
    else if(dropDownName == 'Property Type'){
      this.showPropertyTypeLabel = true;
    }
    else if(dropDownName == 'Style'){
      this.showPropertyStyleLabel = true;
    }
  }

  newProperty(form:FormGroup){
    form.value['lot_size'] = form.value['lot_size'] == '' ? 0 : form.value['lot_size'];    
    form.value['year_built'] = form.value['year_built'] == '' ? 0 : form.value['year_built'];
    form.value['property_style'] = form.value['property_style'] == null ? '' : form.value['property_style'];
    if(this.propertyId !=undefined && this.propertyId != null){
      form.value['id']= parseInt(this.propertyId);
      form.value['user'] = BaseComponent.user.id;
      this.myListingService.updateProperty(form.value).subscribe(res =>{
        this.successResponse(res);
        ListingAddNewPropertyComponent.propertyDetail = res['result'];
      }, err =>{
        this.errorResponse(err.json());
      });
    }
    else
    {
      this.myListingService.addNewProperty(form.value).subscribe(res =>{
        this.successResponse(res);
        this.gotToPropertyDetail('my-listing/add-new-property',res['result']['id']);
        ListingAddNewPropertyComponent.propertyDetail = res['result'];
      }, err =>{
        this.errorResponse(err.json());
      });
    }
  }

  getPropertyDetail(id){
    this.myListingService.getProperty(id).subscribe(res =>{
      if(res['result']['agent_info']['is_myproperty'] == false){
        this.location.back();
      }
      else
      {
        this.isListhubProperty = res.result['basic_info']['is_listhub'];
        this.isManuallySyndicate = res.result['manually_syndicate'];
        this.showStateLabel = true;
        this.showBedroomsLabel = true;
        this.showFullBathsLabel = true;
        this.showPropertyTypeLabel = true;
        ListingAddNewPropertyComponent.propertyDetail = res.result['basic_info']
        this.addPropertyFormGroup.patchValue(res.result['basic_info']);

        var stateObj=[{
          name : res.result['basic_info'].state_name,
          id : res.result['basic_info'].state,
          country : 1
        }];

        this.stateList= stateObj;
        this.selectedCityId = res.result['basic_info'].city;
        this.isSeletedCity = true;
        this.isSeletedZipCode = true;

        // this.loadCity(res.result['basic_info'].state);
        // this.loadZipCode(res.result['basic_info'].city);
        if(res.result['basic_info']['property_style'] != ''){
          this.showPropertyStyleLabel = true;
        }
        this.addPropertyFormGroup.controls.property_style.setValue(res.result['basic_info']['property_style'] == '' ? null : res.result['basic_info']['property_style']);
        this.addPropertyFormGroup.controls.year_built.setValue(res.result['basic_info']['year_built'] == 0 ? '' : res.result['basic_info']['year_built']);
        this.addPropertyFormGroup.controls.lot_size.setValue(res.result['basic_info']['lot_size'] == 0 ? '' : res.result['basic_info']['lot_size']);
        this.addPropertyFormGroup.controls.listing_status.setValue(res.result['basic_info']['property_status'] == 0 ? '' : res.result['basic_info']['property_status']);        

        if(res.result['external_info'] != null){
          this.waterList = res.result['external_info']['water'];
          this.parkingList = res.result['external_info']['parking'];
          this.securityList = res.result['external_info']['security'];
          this.accessibilityList = res.result['external_info']['accessibility'];
          this.propertyExternalFormGroup.patchValue(res.result['external_info']);
        }

        if(res.result['building_info'] != null){
          this.addViewTypeList = res.result['building_info']['view_type'];
          this.addOBCList = res.result['building_info']['building_construction'];
          this.propertyBuildingFormGroup.patchValue(res.result['building_info']);

          // this.propertyBuildingFormGroup.controls.condo_floor_no.setValue(res.result['building_info']['condo_floor_no'] == 0 ? '' : res.result['building_info']['condo_floor_no']);
          // this.propertyBuildingFormGroup.controls.building_unit_count.setValue(res.result['building_info']['building_unit_count'] == 0 ? '' : res.result['building_info']['building_unit_count']);
          // this.propertyBuildingFormGroup.controls.building_floors.setValue(res.result['building_info']['building_floors'] == 0 ? '' : res.result['building_info']['building_floors']);
        }

        if(res.result['location_info'] != null){
          this.propertyLocationFormGroup.patchValue(res.result['location_info']);
          this.propertyLocationFormGroup.controls.school_1_type.setValue(res.result['location_info']['school_1_type'] == "" ? null : res.result['location_info']['school_1_type']);
          this.propertyLocationFormGroup.controls.school_2_type.setValue(res.result['location_info']['school_2_type'] == "" ? null : res.result['location_info']['school_2_type']);
          this.propertyLocationFormGroup.controls.school_3_type.setValue(res.result['location_info']['school_3_type'] == "" ? null : res.result['location_info']['school_3_type']);

          if(res.result['location_info']['school_1_type'] != ""){
            this.showSchool1Label = true;
          }
          if(res.result['location_info']['school_2_type'] != ""){
            this.showSchool2Label = true;
          }
          if(res.result['location_info']['school_3_type'] != ""){
            this.showSchool3Label = true;
          }
        }

        if(res['result']['taxes_info'] != null){
          this.taxesForm.controls.taxes.patchValue(res['result']['taxes_info']);

          for(let i=1;i<res['result']['taxes_info'].length;i++){
            this.addItem();
            this.taxesForm.controls.taxes.patchValue(res['result']['taxes_info']);
          }
        }

          if(res['result']['expenses_info'] !=null){
          this.expenseForm.controls.expsenses.patchValue(res['result']['expenses_info']);

          for(let j=1;j<res['result']['expenses_info'].length;j++){
            this.addEXItem();
            this.expenseForm.controls.expsenses.patchValue(res['result']['expenses_info']);
          }
        }

        if(res['result']['location_info']['latitude'] == 0){
          this.propertyLocationFormGroup.controls.latitude.setValue('');
        }

        if(res['result']['location_info']['longitude'] == 0){
          this.propertyLocationFormGroup.controls.longitude.setValue('');
        }

        if(res['result']['property_file'] !=null){
          this.selectedImages = res['result']['property_file'];
        }
      }
    }, err => {
      console.log(err);
    });
  }

  loadCity(state_id){
    this.showCityLabel = true;
    this.profileService.getCities(state_id).subscribe(res => {
      this.cityList = res.result;
    });
    this.addPropertyFormGroup.controls.city.setValue(ListingAddNewPropertyComponent.propertyDetail.city);
  }

  validateFormat(parcelId: String) {
    if (parcelId.length >= 21) {
      this.length_valid_parcel_id = true;
    } else {
      this.addPropertyFormGroup.controls.parcel_id.setValue(parcelId);
      this.length_valid_parcel_id = false;
    }
  }

  loadZipCode(city_id){
    this.showZipCodeLabel = true;
    this.profileService.getZipCodes(city_id).subscribe(res => {
      this.zipCodeList = res.result;
    });
    this.addPropertyFormGroup.controls.zipcode.setValue(ListingAddNewPropertyComponent.propertyDetail.zipcode);
  }

  focusOutFunction(elementType : string, inputValue:string) : void{
    setTimeout(() => {
      if(elementType == 'city' && inputValue !=''){
        if(this.isSeletedCity == false){
          this.errMessageResponse("Please select a City from the list.");
        }
      }else if(elementType == 'zipCode' && inputValue !=''){
        if(this.isSeletedZipCode == false){
          this.errMessageResponse("Please select a ZipCode from the list.");
        }
      }
    }, 200);
  }

  checkBuiltingYear(year){
    if(this.isInteger(year)){
      if(year.toString().length > 3){
        var currentYear = new Date().getFullYear();
        if(currentYear >= year){
        }
        else
        {
          this.addPropertyFormGroup.controls.year_built.setErrors({'incorrect': true});
        }
      }
    }
    else
    {
      this.addPropertyFormGroup.controls.year_built.setErrors({'number': true});
    }
  }

  createItem(): FormGroup {
    return this.formBuilder.group({
      taxes_amount: new FormControl(''),
      taxes_description: new FormControl(''),
      property: new FormControl(ListingAddNewPropertyComponent.propertyDetail.id),
      id: new FormControl('')
    });
  }

  createItemEX(): FormGroup {
    return this.formBuilder.group({
      expense_amount: new FormControl(''),
      expense_type: new FormControl(null),
      property: new FormControl(ListingAddNewPropertyComponent.propertyDetail.id),
      id: new FormControl('')
    });
  }

   get items(): FormArray {
    return this.taxesForm.get('taxes') as FormArray;
  };

  get EXitems(): FormArray {
    return this.expenseForm.get('expsenses') as FormArray;
  };

  addItem(): void {
    if(this.items.value[this.items.value.length-1]['taxes_amount'] ==''){
      this.warningMessage('Please add Tax amount to add more taxes')
    }
    else{
      this.items.push(this.createItem());
      this.removeTaxes = true;
    }
  }

  addEXItem(): void {
    if(this.EXitems.value[this.EXitems.value.length-1]['expense_amount'] == '' || this.EXitems.value[this.EXitems.value.length-1]['expense_type'] == null){
      this.warningMessage('Please add both expense amount and expense type values to add more expenses')
    }
    else{
      this.EXitems.push(this.createItemEX());
      this.Expsenses = true;
    }
  }

  removeItem (form:FormGroup) :void {
    this.taxesDeleteList(this.items.length,form);
    this.items.removeAt(this.items.length -1);
    if(this.items.length == 1){
      this.removeTaxes = false;
    }
  }

  removeEXItem(form:FormGroup) :void {
    this.expsensesDeleteList(this.EXitems.length,form);
    this.EXitems.removeAt(this.EXitems.length -1);
    if(this.EXitems.length == 1){
      this.Expsenses = false;
    }
  }


  taxesDeleteList(index,form){
    if(form.value['taxes'][index-1].id != ""){
      this.deletedTaxesList.push(form.value['taxes'][index-1].id);
    }
  }

  expsensesDeleteList(index,form){
    if(form.value['expsenses'][index-1].id != ""){
      this.deletedExpsensesList.push(form.value['expsenses'][index-1].id);
    }
  }

  addAppliancesKeys(appliancesKey){
    this.appliancesList.push(appliancesKey);
  }

  addBasementKeys(basementKey){
    this.basementList.push(basementKey);
  }

  addFloorCoveringKeys(floorCoveringKey){
    this.floor_CoveringList.push(floorCoveringKey);
  }

  addroomsKeys(roomsKey){
    this.roomsList.push(roomsKey);
  }

  addIndoorFeaturesKeys(indoorFeaturesKey){
    this.indoor_FeaturesList.push(indoorFeaturesKey);
  }

  addBuildingAmenitiesKeys(buildingAmenitiesKey){
    this.building_AmenitiesList.push(buildingAmenitiesKey);
  }

  addArchitecturalStyleKeys(architecturalStyleKey){
    this.architectural_StyleList = this.addRemoveInList(this.architectural_StyleList, architecturalStyleKey);
  }

  addExteriorKeys(exteriorKey){
    this.exteriorList.push(exteriorKey);
  }

  addOutdoorAmenitiesKeys(outdoorAmenitiesKey){
    this.outdoor_AmenitiesList.push(outdoorAmenitiesKey);
  }

  addRoofKeys(roofKey){
    this.roofList.push(roofKey);
  }

  addCoolingTypeKeys(coolingTypeKey){
    this.cooling_typeList.push(coolingTypeKey);
  }

  addHeatingTypeKeys(heatingTypeKey){
    this.heating_SystemList.push(heatingTypeKey);
  }

  addHeatingFuelKeys(heatingFuelKey){
    this.heating_fuelList.push(heatingFuelKey);
  }

  addRemoveInList(list, key){
    if(list.length ==0){
      list.push(key);
    }
    else{
      let filteredList = list.filter(item => item == key);
      if(filteredList.length == 0){
        list.push(key);
      }
      else{
        list.splice(list.indexOf(filteredList[0]), 1);
      }
    }
    return list;
  }

  addParkingKeys(parkingKey){
    this.parkingList = this.addRemoveInList(this.parkingList, parkingKey);
  }

  addWaterKeys(waterKey){
    this.waterList = this.addRemoveInList(this.waterList, waterKey);
   }

  addLandscapeKeys(landscapeKey){
    this.landscapeList.push(landscapeKey);
  }

  addSecurityKeys(securityKey){
    this.securityList = this.addRemoveInList(this.securityList, securityKey);
  }

  addaccessibilityKeys(accessKey){
    this.accessibilityList = this.addRemoveInList(this.accessibilityList, accessKey);
  }

  addOExtFeatureKeys(key){
    this.OtherExternalFeaturesList.push(key);
  }

  addViewTypeKeys(key){
    this.addViewTypeList = this.addRemoveInList(this.addViewTypeList, key);
  }

  OBCListKey(key){
    this.addOBCList = this.addRemoveInList(this.addOBCList, key);
  }

  // openCaption(id){
  //   var self =  this;
  //   $(document).ready(function() {
  //     $("#caption_"+id).slideToggle();
  //     self.propertyImageBorderId = id;
  //   });
  // }

  uploadImageVideo(event){
    if(event.srcElement.files.length != 0){
      let tempList = event.srcElement.files;
      for(let i=0; i<event.srcElement.files.length; i++){
        var reader = new FileReader();
        reader.onload = (event:any) => {
          this.url = event.target.result;

          if(this.selectedImages.length >15)
          {
            this.maxImageSelect = 'error';
          }
          else{
            if(tempList[i]['type'].split('/')[0] == 'image' || tempList[i]['type'].split('/')[0] == 'video'){
              this.selectedImages.push(
                {
                  file_url:this.url,
                  file:tempList[i],
                  file_caption:'',
                  file_index:this.selectedImages.length,
                  file_type:tempList[i]['type'].split('/')[0]
                }
              );
              this.maxImageSelect = '';
              this.fileTypeError = '';
            }
            else{
              this.fileTypeError = 'error';
            }
          }
        }
        reader.readAsDataURL(event.target.files[i]);
      }
    }
  }

  setCaption(caption,id){
    this.selectedImages[id]['file_caption']=caption;
  }

  removeImage(image){
    let imageIndex = this.selectedImages.indexOf(image);
    if(imageIndex != undefined){
      this.selectedImages.splice(imageIndex,1);
    }
    if(this.selectedImages.length == 0){
      this.showImgSaveChanges = true;
    }
  }

  uploadAllImage(){
    if(ListingAddNewPropertyComponent.propertyDetail.id !=undefined){
      let formData = new FormData();
      for(let i=0;i<this.selectedImages.length;i++){
        this.selectedImages[i]['property'] = ListingAddNewPropertyComponent.propertyDetail.id;
        this.selectedImages[i]['file_index'] = i;
        for(let key of Object.keys(this.selectedImages[i])){
          if(key != 'file_url'){
            formData.append('property_file_list['+i+']'+key,this.selectedImages[i][key]);
          }
        }
      }
      formData.append('property_id',ListingAddNewPropertyComponent.propertyDetail.id);
      if(this.selectedImages.length > 15){
        this.maxImageSelect = 'error';
      }
      else
      {
        if(this.isImageUploading){
          return;
        }
        this.isImageUploading = true;
        this.myListingService.addPropertyImage(formData).subscribe(res =>{
          this.successResponse(res);
          this.selectedImages = [];
          this.selectedImages = res['result'];
          // this.propertyImageBorderId = '';
          this.showImgSaveChanges = false;
          this.isImageUploading = false;
          },err =>{
            this.isImageUploading = false;
            this.errorResponse(err.json());
        });
      }
    }
    else{
      this.warningMessage("Please add basic information to add new listing");
    }
  }

  unitNumberType(input){
    if(!this.isInteger(input)){
      this.addPropertyFormGroup.controls.unit_number.setErrors({'number':true});
    }
  }

  homePriceType(input){
    if(!this.isInteger(input)){
      // this.addPropertyFormGroup.controls.home_price.setErrors({'number':true});
    }
  }

  lotSizeType(input){
    if(!this.isIntegerOrFloat(input)){
      // this.addPropertyFormGroup.controls.lot_size.setErrors({'number':true});
    }
  }

  fullBathValidation(input){
    if(!this.isIntegerOrFloat(input)){
      this.addPropertyFormGroup.controls.full_bath.setErrors({'number':true});
    }
    if(this.isIntegerOrFloat(input)){
      let value = input;
      if(this.countDecimals(value) > 2){
        var inputStr = value.toString();
        if(inputStr.length > 5) {
          let inputStrSplit = inputStr.split('.');
          inputStrSplit[1] = inputStrSplit[1].substring(0, 2);
          inputStr  = inputStrSplit.join('.');
          var inputNumber = parseFloat(inputStr);
          this.addPropertyFormGroup.controls.full_bath.setValue(inputNumber);
        }
        else{
          inputStr  = inputStr.substring(0, 4);
          var inputNumber = parseFloat(inputStr);
          this.addPropertyFormGroup.controls.full_bath.setValue(inputNumber);
        }
      }
    }
  }

  roomType(input){
    if(!this.isInteger(input)){
      this.propertyInternalFormGroup.controls.rooms.setErrors({'number':true});
    }
  }

  spacesType(input){
    if(!this.isInteger(input)){
      this.propertyExternalFormGroup.controls.parking_total_space.setErrors({'number':true});
    }
  }
  garageSpacesType(input){
    if(!this.isIntegerOrFloat(input)){
      this.propertyExternalFormGroup.controls.garage_space.setErrors({'number':true});
    }
  }
  floorNumberType(input){
    if(!this.isInteger(input)){
      this.propertyBuildingFormGroup.controls.condo_floor_no.setErrors({'number':true});
    }
  }

  unitCountType(input){
    if(!this.isInteger(input)){
      this.propertyBuildingFormGroup.controls.building_unit_count.setErrors({'number':true});
    }
  }

  floorsType(input){
    if(!this.isInteger(input)){
      this.propertyBuildingFormGroup.controls.building_floors.setErrors({'number':true});
    }
  }

  propertyExternal(form:FormGroup){
    if(ListingAddNewPropertyComponent.propertyDetail.id !=undefined){
      form.value['id'] = ListingAddNewPropertyComponent.propertyDetail.id;
      form.value['water'] = this.waterList;
      form.value['parking'] = this.parkingList;
      form.value['accessibility'] =this.accessibilityList;
      form.value['security'] = this.securityList;
      this.myListingService.addPropertyExternal(form.value).subscribe(res =>{
        this.successResponse(res);
      },err =>{
        this.errorResponse(err.json());
      });
    }
    else{
      this.warningMessage("Please add basic information to add new listing");
    }
  }

  AddPropertyBuilding(form:FormGroup){
    if(ListingAddNewPropertyComponent.propertyDetail.id !=undefined){
      form.value['id'] = ListingAddNewPropertyComponent.propertyDetail.id;
      form.value['view_type'] = this.addViewTypeList;
      form.value['building_construction']= this.addOBCList;
      form.value['condo_floor_no'] = form.value['condo_floor_no'] == '' ? 0 : form.value['condo_floor_no'];
      form.value['building_unit_count'] = form.value['building_unit_count'] == '' ? 0 : form.value['building_unit_count'];
      form.value['building_floors'] = form.value['building_floors'] == '' ? 0 : form.value['building_floors'];

      this.myListingService.addPropertyBuildingInfo(form.value).subscribe(res =>{
        this.successResponse(res);
      },err =>{
        this.errorResponse(err.json());
      });
    }
    else{
      this.warningMessage("Please add basic information to add new listing");
    }
  }

  AddPropertyLocation(form:FormGroup){
    if(ListingAddNewPropertyComponent.propertyDetail.id !=undefined){
      form.value['id'] = ListingAddNewPropertyComponent.propertyDetail.id;
      form.value['school_1_type'] = form.value['school_1_type'] == null ? '' : form.value['school_1_type'];
      form.value['school_2_type'] = form.value['school_2_type'] == null ? '' : form.value['school_2_type'];
      form.value['school_3_type'] = form.value['school_3_type'] == null ? '' : form.value['school_3_type'];
      form.value['latitude'] = form.value['latitude'] == '' ? 0 : form.value['latitude'];
      form.value['longitude'] = form.value['longitude'] == '' ? 0 : form.value['longitude'];
      this.myListingService.addPropertyLocation(form.value).subscribe(res =>{
        this.successResponse(res);
      },err =>{
        this.errorResponse(err.json());
      });
    }else{
      this.warningMessage("Please add basic information to add new listing");
    }
  }

  addPropertyTaxesExpenses(form:FormGroup, formExps:FormGroup){
    var taxErrorCount = 0;
    var expenseErrorCount = 0;

    if(ListingAddNewPropertyComponent.propertyDetail.id !=undefined){
      form.value['taxes'][0].property = ListingAddNewPropertyComponent.propertyDetail.id;
      formExps.value['expsenses'][0].property = ListingAddNewPropertyComponent.propertyDetail.id;

      var taxesList = [];
      var expsensesList = [];

      form.value['taxes'].forEach(taxas => {
        let index = form.value['taxes'].indexOf(taxas);
       if(taxas['taxes_amount'] !='' && taxas['taxes_amount'] != null){
          for (var key in taxas) {
            if (taxas[key] === "") {
              delete taxas[key];
            }
          }
          $('#'+index).text('');
          taxesList.push(taxas);
        }
        else{
          if (taxas['taxes_amount'] == null || taxas['taxes_amount'] == ''){
            $('#'+index).text('');
          }
          else{
            if (taxas['taxes_amount'] == '' || taxas['taxes_amount'] == null){
              $('#'+index).text('Please add Tax amount to store this Taxes information');
              taxErrorCount = +1;
            }
          }
        }
      });

      formExps.value['expsenses'].forEach(expanses => {
        let expansesIndex = formExps.value['expsenses'].indexOf(expanses);
        if(expanses['expense_amount'] !='' && expanses['expense_amount'] != null && expanses['expense_type'] != null){
          for (var key in expanses) {
            if (expanses[key] === "") {
              delete expanses[key];
            }
          }
          expsensesList.push(expanses);
          $('#'+"exp"+expansesIndex).text('');
        }
        else{
          if(expanses['expense_amount'] == null || expanses['expense_amount'] == '' && expanses['expense_type'] == null){
            $('#'+"exp"+expansesIndex).text('');
          }
          else{
            if((expanses['expense_amount'] != '' && expanses['expense_amount'] != null && expanses['expense_type'] == null)){
              $('#'+"exp"+expansesIndex).text('Please add Expense type to store this Expenses information');
              expenseErrorCount = +1;
            }
            else if (expanses['expense_amount'] == '' || expanses['expense_amount'] == null && expanses['expense_type'] != null){
              $('#'+"exp"+expansesIndex).text('Please add Expense  amount to store this Expenses information');
              expenseErrorCount = +1;
            }
          }
        }
      });

      var formData;
      formData = {
        "expsenses" : expsensesList,
        "taxes" : taxesList,
        "deleted_taxes" : this.deletedTaxesList,
        "deleted_expsenses" : this.deletedExpsensesList
      }

      if(formData['expsenses'].length !=0 || formData['taxes'].length !=0 || formData['deleted_taxes'].length !=0 || formData['deleted_taxes'].length !=0){
        if(expenseErrorCount ==0 && taxErrorCount ==0){
          this.myListingService.addPropertyTaxExpsenses(formData).subscribe(res =>{
            this.successResponse(res);
            this.taxesForm.controls.taxes.patchValue(res['result']['taxes']);
            this.expenseForm.controls.expsenses.patchValue(res['result']['expsenses']);
          },err =>{
            this.errorResponse(err.json())
          });
        }
      }
    }
    else{
      this.warningMessage("Please add basic information to add new listing");
    }
  }

  checkIsInList(key,list){
    if(list.length !=0 || list != null){
      for(let i=0; i<list.length;i++){
        if(list[i] == key){
          return true;
        }
      }
    }
  }

  showSchoolDropDownLabel(type){
    if(type == 'SchoolType1'){
      this.showSchool1Label = true;
    }
    if(type == 'SchoolType2'){
      this.showSchool2Label = true;
    }
    if(type == 'SchoolType3'){
      this.showSchool3Label = true;
    }
  }



  countDecimals (value) {
    if(Math.floor(value) === value) return 0;

    if(value.toString().includes('.')){
      return value.toString().split(".")[1].length || 0;
    }
    else{
      return 0;
    }
  }

  ngOnDestroy(){
    ListingAddNewPropertyComponent.propertyDetail = new AddProperty();
    if(this.addEditPropertyRouting  != undefined){
      this.addEditPropertyRouting.unsubscribe();
    }
  }
}
