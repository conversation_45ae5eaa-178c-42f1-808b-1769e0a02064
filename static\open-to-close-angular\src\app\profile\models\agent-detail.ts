import { AddressId } from "@app/profile/models/profile";

export class AgentDetail{    
    email:String;
    paid_account:Boolean;
    permanent_premium_account:Boolean;
    profile:AgentProfile;
    billing_info:BillingDetails; 
    payment_method:PaymentsDetails;
    selected_plan_id:String;
    jwt_token:String;
    invite_link :String;
    brokerage_info:BrokerageInfo;
    lender_info : ParticipatingLender;
    wise_api_key:String;
    is_wise_registered:Boolean;
}
export class ParticipatingLender{
    lender_id : any;
    cell_phone : any;
    lending_company : string;
    name : string;
    office_phone : any;
    profile_photo : string;
}

export class BrokerageInfo{
    name: String;
    address: String;
    city: String;
    state: String;
    zipcode: String;
    brokerage_id: String;
}

export class AgentProfile{
    profile_photo:any;
    name:String;
    fname:String;
    lname:String;
    mls_agent_id:String;
    mls_status:String;
    password:string;
    cell_phone:String;
    office_phone:String;
    email_preferences:String[];
    als_city:any;
    als_state:any;
    als_neighbourhood:String;
    als_zipcode:any;
    as_home_type:String[];
    as_price_range_low:any;
    as_price_range_high:any;
    ohp_city:any;
    ohp_city_s:any;
    ohp_state:any;
    ohp_state_s:any;
    ohp_neighbourhood:String;
    ohp_zipcode:any;
    ohp_zipcode_s:any;
    ohp_home_type:String[];
    ohp_price_low:any;
    ohp_price_high:any;        
    subscription_status:String;
    state: AddressId;
    city: AddressId;
    zip_code: AddressId;
    zipcode: AddressId;
   
}
export class BillingDetails{
    user:LAUser;
    address_1:String;
    address_2:String;
    state: String;
    city: String;
    zipcode: String;
}
export class PaymentsDetails{
    expiration_month:String;
    last_name: String;
    token:String;
    last_4:String;
    number:String;
    first_name: String;
    expiration_year:String;
}
export class LAUser{
    id:any;
}


export class AgentResponse{
    result: AgentDetail;
    message: String;
    status: any;
    statusCode: any;
}