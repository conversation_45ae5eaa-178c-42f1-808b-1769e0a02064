.modal-icon{
    padding-left: 18px;
}
.icon-space{
    padding-left: 12px;
}
img.check_icon.back_check_2{
    height: 58px !important
}
.ls {
    font-size: 36px;
    color: #FFFFFF;
    position: relative;
    letter-spacing: -1px;
    line-height: 40px;
    background: #10B8A8;
    display: inline-block;
    padding: 10px 3px !important;
    border-radius: 40px;
    top: -15px;
    height: 60px;
    width: 60px;
    text-align: center !important;
}
.po_rel_1 {
    position: relative !important;
    top: 10px !important;
}
.archive.pull-right{
    border: 1px solid #10B8A8;
    border-radius: 100px;
    font-size: 12px;
    color: #10B8A8;
    padding: 7px 10px 5px 10px;
    margin-top: 6px;
    cursor: pointer;
    width: 152%;
    text-align: center;
}
.un-archive{
    cursor: pointer;
    width: 105%;
    text-align: center;
}
.thum-size{
    width: 46px !important;
    height: 46px !important
}
img.check_icon.back_check_2{
    height: 46px !important
}
.red-archive{
    width: 30% !important;
    text-align: center !important;
}
.property-text-lead{
    width: 50%;
}
.un-archive-btn{
    border: 1px solid #F06292;
    border-radius: 100px;
    background: transparent;
    font-size: 12px;
    color: #F06292;
    padding: 7px 20px 5px 20px;
    outline: 0 !important;
}
.lead-name{
    width: 200px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.lead-icon{
    /* vertical-align: top !important; */
    vertical-align: super !important;
}
.lead-chat{
  margin-top: 8%;
  margin-right: 40%;
}

.badge-notify{
    background: #AD5FBF;
    position: absolute;
    color: white !important;
    top: 30px;
    left: 45px;
    font-size: 13px !important;
   }

.badge-notify-1 {
    background: #AD5FBF;
    position: relative;
    color: white !important;
    top: -20px;
    left: -20px;
    font-size: 13px !important;
}   