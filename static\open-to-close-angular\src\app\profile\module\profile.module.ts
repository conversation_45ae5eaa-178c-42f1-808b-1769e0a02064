import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProfileComponent } from '@app/profile/component/profile.component';
import { ProfileService } from  '@app/profile/service/profile.service';
import { BaseModule } from '@app/base/modules/base.module';
import { BrokerageProfileComponent } from '@app/profile/component/brokerage-profile.component';
import { HomeBuyerProfileComponent } from '@app/profile/component/home-buyer-profile.component';
import { ListingAgentProfileComponent } from '@app/profile/component/listing-agent-profile.component';
import { LenderProfileComponent } from '@app/profile/component/lender-profile.component';
import { TagInputModule } from 'ngx-chips';
import { BRAgentPipe } from '@app/profile/pipes/br-agent-status.pipe';


@NgModule({
  imports: [
    CommonModule,
    BaseModule,
    TagInputModule
  ],
  declarations: [ProfileComponent,BrokerageProfileComponent,HomeBuyerProfileComponent,ListingAgentProfileComponent,LenderProfileComponent,BRAgentPipe],
  providers: [ProfileService,BRAgentPipe]
})
export class ProfileModule { }
