import { Component, OnInit, ViewChild } from '@angular/core';
import { ProfileComponent } from '@app/profile/component/profile.component';
import { FormGroup, FormControl, Validators, FormArray } from '@angular/forms';
import { validateConfig } from '@angular/router/src/config';
import { State, ZipCode, City } from '@app/profile/models/profile';
import { PurchaseService } from '@app/purchase/service/purchase.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { BaseComponent } from '@app/base/components/base.component';
import { HeaderComponent } from '@app/root/components/header.component';
import { ListingAgentProfileComponent } from "@app/profile/component/listing-agent-profile.component";
import { UpdatePaymentMethod } from '@app/profile/models/update-payment';
import { BRAgentPipe, } from '@app/profile/pipes/br-agent-status.pipe';
import * as FileSaver from 'file-saver';

declare var $;
declare var require;

@Component({
  selector: 'app-brokerage-profile',
  templateUrl: '../views/brokerage-profile.html',
  styleUrls: ['../css/profile.component.css']
})

export class BrokerageProfileComponent extends ProfileComponent implements OnInit {

  @ViewChild(HeaderComponent) headerComponent: HeaderComponent;
  // @ViewChild(ListingAgentProfileComponent) listingAgentProfileComponent : ListingAgentProfileComponent;

  brokerageProfileForm: FormGroup;
  brokeragePaymentForm: FormGroup;
  brokerageBillingInfo: FormGroup;
  agentInviteForm: FormGroup;

  public updatePaymentModal: UpdatePaymentMethod = new UpdatePaymentMethod();

  fileUrl: any = this.imagePrefix + "testmonial-default (1).png";

  profileName: String;
  accountStatus: Boolean = false;
  permanentPremium: Boolean = false;
  public msgCP: boolean = false;
  public w_ohCP: boolean = false;
  public CLIENT_AUTHORIZATION;
  brokerPlansList = this.brokerPlans;
  public showextrafeild: boolean = false;
  public cardLast4Digit;
  public inVisibaleNumber = "************";
  public cardFirstName;
  public cardLastName;
  public selectedPlan: String = ' ';
  public defaultCardNuber: any;
  public selectedMonth: string;
  public selectedYear: String;
  public invitedUnVerifyAgentList = [];
  public invitedVerifyAgentList = [];
  public purchaseHistory = [];
  public brokerageIdText = 'Brokerage ID*';

  stateList: State[];
  cityList: City[];
  zipCodeList: ZipCode[];
  emailPrefences: any[] = [];
  public clearCity: boolean = false;
  public showAllinvoicesLink: Boolean = false;

  public myLender: any;
  public searchLenderList = [];
  public showLenderList: Boolean = false;

  cardNuberPattern = "^((\\+91-?)|0)?[0-9]{13,16}$";
  cvvPattern = "^((\\+91-?)|0)?[0-9]{3,4}$";
  EXMonthPattern = "^((\\+91-?)|0)?[0-9]{1,2}$";
  EXYearPattern = "^((\\+91-?)|0)?[0-9]{4}$";

  //Agent verify Sorting
  public verifyAgentSortObject: any = {};
  public verifyAgentSortList: any[] = []

  //Agent unverify Sorting
  public unverifyAgentSortObject: any = {};
  public unverifyAgentSortList: any[] = []

  //Download Invoice
  public invoiceLoadingIndex: string = null;
  public showInvoiceLoading: Boolean = false;

  public disablecancelSubsBtn: Boolean = false;

  constructor(public brAgentPipe: BRAgentPipe) {
    super();
  }

  ngOnInit() {
    if (this.getPreviousScreen() != '/profile') {
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/profile/brokerage');
    // this.getPlans();    
    this.initData();
    this.initAPI();
    this.headerComponent.getPlans();

    document.getElementById('zip').addEventListener('keydown', function (e) {
      if (e.which === 38 || e.which === 40) {
        e.preventDefault();
      }
    });

    this.checkBrokerInfo()
  }

  checkBrokerInfo() {
    console.log("broker Information")
    try {
      if (localStorage.getItem("showBrokerInfo")) {
        document.querySelector('#' + 'otpInfo').scrollIntoView();
        var current = document.getElementById("brokerDiv");
        current.classList.add("broker-info");
        setTimeout(() => {
          current.classList.remove("broker-info");
        }, 3000);

        this.showUpdateBrokerInformation();
      }
    } catch (e) { }
    if (!BaseComponent.user) {
      this.router.navigate(['/search']);
    }
    if (BaseComponent.user.is_open_registration) {
      this.brokerageProfileForm.controls.profile['controls']['brokerage_id'].clearValidators()
      this.brokerageProfileForm.controls.profile['controls']['brokerage_id'].updateValueAndValidity()
      this.brokerageIdText = 'Brokerage ID'
    }

  }

  initAPI() {
    this.purchaseService.subscriptionClientToken().subscribe(res => {
      this.CLIENT_AUTHORIZATION = res['result']['client_token'];
    }, err => console.log(err));
  }

  defineFormGroups() {
    this.brokerageProfileForm = new FormGroup({
      email: new FormControl('', [Validators.required, Validators.email]),
      profile: new FormGroup({
        office_phone: new FormControl('', [Validators.minLength(12), Validators.maxLength(12)]),
        firm_name: new FormControl(''),
        brokerage_id: new FormControl('', Validators.required),
        contact_name: new FormControl(''),
        address: new FormControl(null),
        city: new FormControl(null, Validators.required),
        state: new FormControl(null, Validators.required),
        zipcode: new FormControl(null, Validators.required),
        email_preferences: new FormArray([])
      }),
    });

    this.brokeragePaymentForm = new FormGroup({
      firstName: new FormControl('', Validators.required),
      lastName: new FormControl('', Validators.required),
      creditCard: new FormGroup({
        number: new FormControl('', [Validators.pattern(this.cardNuberPattern)]),
        cvv: new FormControl('', [Validators.pattern(this.cvvPattern)]),
        expirationMonth: new FormControl('', [Validators.required, Validators.maxLength(2), Validators.minLength(1)]),
        expirationYear: new FormControl('', [Validators.required, Validators.maxLength(4), Validators.minLength(4)]),
        billingAddress: new FormGroup({
          firstName: new FormControl(''),
          lastName: new FormControl(''),
        })
      }),
    });

    this.brokerageBillingInfo = new FormGroup({
      address_1: new FormControl('', Validators.required),
      address_2: new FormControl(''),
      state: new FormControl('', Validators.required),
      city: new FormControl('', Validators.required),
      zipcode: new FormControl('', [Validators.required, Validators.minLength(5)]),
      user: new FormGroup({
        id: new FormControl('')
      })
    });

    this.agentInviteForm = new FormGroup({
      email: new FormControl('', [Validators.required, Validators.email])
    });

  }

  async initData(): Promise<any> {

    await this.defineFormGroups();

    this.profileService.getStates('US').subscribe(res => {
      this.stateList = res.result;
      this.zipCodeList = [];
      this.cityList = [];
      if (Object.keys(ProfileComponent.brokerageUser).length == 0) {
        this.profileService.brokerageDetails().subscribe( (res:any) => {
          if (res.result.profile.profile_photo != "") {
            this.fileUrl = res.result.profile.profile_photo;
          }
          this.brokerageProfileForm.patchValue(res.result);
          this.brokerageProfileForm.controls.profile['controls']['office_phone'].setValue(this.validateNumber(res.result.profile.office_phone));
          this.brokerageBillingInfo.patchValue(res.result.billing_info);
          ProfileComponent.brokerageUser = res.result;
          this.profileName = res.result.profile.contact_name;
          this.accountStatus = res.result.paid_account;
          this.permanentPremium = res.result.permanent_premium_account;
          this.checkCP(res.result.profile.email_preferences);
          this.brokeragePaymentForm.controls.firstName.setValue(res.result.payment_method.first_name);
          this.brokeragePaymentForm.controls.lastName.setValue(res.result.payment_method.last_name);
          this.brokeragePaymentForm.controls.creditCard['controls']['number'].setValue(res.result.payment_method.number);
          ProfileComponent.brokerageUser.billing_info = res.result.billing_info;
          ProfileComponent.brokerageUser.payment_method = res.result.payment_method;
          this.cardFirstName = res.result.payment_method.first_name;
          this.cardLastName = res.result.payment_method.last_name;
          this.headerComponent.selectedPlan = res.result.selected_plan_id;
          this.defaultCardNuber = res.result.payment_method.number;
          this.getInvitesAgent();

          if (res.result.profile.state.id != undefined) {
            this.brokerageProfileForm.controls['profile']['controls']['state'].setValue(res.result.profile.state.id);
            this.loadCities(res.result.profile.state.id);
          }
          else {
            this.brokerageProfileForm.controls['profile']['controls']['state'].setValue(null);
            this.brokerageProfileForm.controls['profile']['controls']['city'].setValue(null);
            this.brokerageProfileForm.controls['profile']['controls']['zipcode'].setValue(null);
          }
          this.myLender = res.result.lender_info;
          this.getPurchasehistory(false);
          $("#inviteLender").val(res.result.invite_link);
        }, err => this.errorResponse(err.json()));
      }
      else {
        if (ProfileComponent.brokerageUser.profile.profile_photo != null && ProfileComponent.brokerageUser.profile.profile_photo != undefined && ProfileComponent.brokerageUser.profile.profile_photo != '') {
          this.fileUrl = ProfileComponent.brokerageUser.profile.profile_photo;
        }

        this.checkCP(ProfileComponent.brokerageUser.profile.email_preferences);
        this.brokerageBillingInfo.patchValue(ProfileComponent.brokerageUser.billing_info);
        this.brokerageProfileForm.patchValue(ProfileComponent.brokerageUser);
        this.brokeragePaymentForm.controls.firstName.setValue(ProfileComponent.brokerageUser.payment_method.first_name);
        this.brokeragePaymentForm.controls.lastName.setValue(ProfileComponent.brokerageUser.payment_method.last_name);
        this.brokeragePaymentForm.controls.creditCard['controls']['number'].setValue(ProfileComponent.brokerageUser.payment_method.number);
        this.cardFirstName = ProfileComponent.brokerageUser.payment_method.first_name;
        this.cardLastName = ProfileComponent.brokerageUser.payment_method.last_name;
        this.headerComponent.selectedPlan = ProfileComponent.brokerageUser.selected_plan_id;
        this.accountStatus = ProfileComponent.brokerageUser.paid_account;
        this.permanentPremium = ProfileComponent.brokerageUser.permanent_premium_account;
        this.defaultCardNuber = ProfileComponent.brokerageUser.payment_method.number;
        this.brokerageProfileForm.controls.profile['controls']['office_phone'].setValue(this.validateNumber(ProfileComponent.brokerageUser.profile.office_phone));
        this.getInvitesAgent();
        if (ProfileComponent.brokerageUser.profile != undefined) {
          if (ProfileComponent.brokerageUser.profile.state.id != null) {
            this.brokerageProfileForm.controls['profile']['controls']['state'].setValue(ProfileComponent.brokerageUser.profile.state.id);
            this.loadCities(ProfileComponent.brokerageUser.profile.state.id);
          }
          else {
            this.brokerageProfileForm.controls['profile']['controls']['state'].setValue(null);
            this.brokerageProfileForm.controls['profile']['controls']['city'].setValue(null);
            this.brokerageProfileForm.controls['profile']['controls']['zipcode'].setValue(null);
          }
        }
        this.profileName = ProfileComponent.brokerageUser.profile.contact_name;
        this.myLender = ProfileComponent.brokerageUser.lender_info;
      }
      $("#inviteLender").val(ProfileComponent.brokerageUser.invite_link);
      this.getPurchasehistory(false);
    }, err => this.errMessageResponse(err.json()));
  }

  getPurchasehistory(getType: Boolean) {
    if (getType == true) {
      this.showAllinvoicesLink = false;
    }
    this.profileService.getSubscriptionPurchaseHistory(getType).subscribe(res => {
      this.purchaseHistory = res['result']['purchase_history'];

      if (res['result']['total_count'] > 5) {
        this.showAllinvoicesLink = true;
      }
    });
  }

  getInvitesAgent() {
    this.profileService.getInvitesAgent().subscribe(res => {
      this.invitedUnVerifyAgentList = this.brAgentPipe.transform(res['result'], 'U');
      this.invitedVerifyAgentList = this.brAgentPipe.transform(res['result'], 'V');
    }, err => {
      this.errorResponse(err.json());
    });
  }

  acceptAgentRequest(agentId, type, agent) {
    let index = this.invitedUnVerifyAgentList.indexOf(agent);
    let urlParams = new URLSearchParams();
    urlParams.set('brokerage_agent_id', agentId);
    urlParams.set('invite_status', type);
    this.profileService.UpdateAgentInvitesStatus(urlParams).subscribe(res => {
      this.successResponse(res);
      if (type == 'D') {
        this.invitedUnVerifyAgentList.splice(index, 1);
      } else if (type == 'A') {
        this.invitedUnVerifyAgentList[index]['invite_status'] = type;
        this.invitedVerifyAgentList.push(this.invitedUnVerifyAgentList[index]);
        this.invitedUnVerifyAgentList.splice(index, 1);
      }
    }, err => {
      this.errorResponse(err.json())
    });
  }

  denyAgentRequest(agentId) {
    let urlParams = new URLSearchParams();
    urlParams.set('brokerage_agent_id', agentId);
    urlParams.set('invite_status', "D");
    this.profileService.UpdateAgentInvitesStatus(urlParams).subscribe(res => {
      this.successResponse(res);
      this.getInvitesAgent();
    }, err => {
      this.errorResponse(err.json())
    });
  }

  newAgentInvite(form: FormGroup) {
    let urlParams = new URLSearchParams();
    urlParams.set('agent_email', form.value['email']);
    urlParams.set('brokerage_user_id', BaseComponent.user.id);
    this.profileService.agentInvite(urlParams).subscribe(res => {
      this.successResponse(res);
      form.reset();
    }, err => {
      this.errorResponse(err.json())
    });
  }

  updateBrokerageProfile(form: FormGroup) {
    var email_preferencesList = [];
    if (this.msgCP == true) {
      email_preferencesList.push('msg');
    }
    if (this.w_ohCP == true) {
      email_preferencesList.push('w_oh');
    }

    let state = this.stateList.filter(item => {
      if (item.id == this.brokerageProfileForm.value['profile']['state']) {
        return item;
      }
    });
    form.value['profile']['state'] = state[0];

    let city = this.cityList.filter(item => {
      if (item.id == this.brokerageProfileForm.value['profile']['city']) {
        return item;
      }
    });
    form.value['profile']['city'] = city[0];

    let zipCode = this.zipCodeList.filter(item => {
      if (item.id == this.brokerageProfileForm.value['profile']['zipcode']) {
        return item;
      }
    });
    form.value['profile']['zipcode'] = zipCode[0];
    form.value['profile']['email_preferences'] = email_preferencesList;
    if(BaseComponent && BaseComponent.user && BaseComponent.user.is_open_registration){
      form.value.profile.is_open_registration = true;
    }

    this.profileService.brokerageUpdate(form.value).subscribe(res => {

      this.checkCP(res.result.profile.email_preferences);
      if (ProfileComponent.brokerageUser.email == res.result.email) {
        ProfileComponent.brokerageUser = res.result;
        this.brokerageProfileForm.controls['profile']['controls']['state'].setValue(res.result.profile.state.id);
        this.loadCities(res.result.profile.state.id);
        this.profileName = res.result.profile.contact_name;
        this.successResponse(res);
      }
      else {
        this.successResponse(res);
        ProfileComponent.brokerageUser = res.result;
        BaseComponent.accessToken = 'JWT ' + res.result.jwt_token;
        this.setUserToken('JWT ' + res.result.jwt_token);
        this.router.navigate(['/profile/brokerage']);
      }
      if (localStorage.getItem("showBrokerInfo")) {
        BaseComponent.user.is_broker_has_updated = true
        localStorage.removeItem("showBrokerInfo");
        this.router.navigate(['/search']);
      }
    }, err => {
      this.errorResponse(err.json())
    });
  }

  updatePassword(form: FormGroup) {
    this.authService.changePassword(form.value).subscribe(res => {
      form.reset();
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    });
  }

  getCityList(state_id) {
    this.brokerageProfileForm.controls['profile']['controls']['city'].setValue(null);
    this.brokerageProfileForm.controls['profile']['controls']['zipcode'].setValue(null);
    this.profileService.getCities(state_id).subscribe(res => {
      this.cityList = res.result;
    }, err => this.errorResponse(err.json()));
  }

  getZipCode(city_id) {
    this.brokerageProfileForm.controls['profile']['controls']['zipcode'].setValue(null);
    this.profileService.getZipCodes(city_id).subscribe(res => {
      this.zipCodeList = res.result;
    }, err => this.errorResponse(err.json()));
  }

  setZipCode(event) {
  }

  updateBillinInfo(form: FormGroup) {
    this.profileSuccessResponse();
  }

  openPlansModal() {
    this.headerComponent.brokerage = true;
    this.headerComponent.openHeaderPlansModal();
  }

  upgradeAccount(plan) {
    this.purchaseService.setPlan(plan);
    this.routeOnUrl('/purchase');
    $("#upgradeModal").modal("hide");
  }

  loadCities(stateId: number) {
    this.profileService.getCities(stateId).subscribe(res => {
      this.cityList = res.result;
      if (ProfileComponent.brokerageUser != null) {
        this.brokerageProfileForm.controls['profile']['controls']['city'].setValue(ProfileComponent.brokerageUser.profile.city.id);
        this.loadZipcode(ProfileComponent.brokerageUser.profile.city.id);
      }
    }, err => this.errMessageResponse(err));
  }

  loadZipcode(cityId: number) {
    this.profileService.getZipCodes(cityId).subscribe(res => {
      this.zipCodeList = res.result;
      if (ProfileComponent.brokerageUser != null) {
        this.brokerageProfileForm.controls['profile']['controls']['zipcode'].setValue(ProfileComponent.brokerageUser.profile.zipcode.id);
      }
    }, err => this.errMessageResponse(err));
  }

  validateFormat(number: String) {
    this.brokerageProfileForm.controls.profile['controls']['office_phone'].setValue(this.validateNumber(number));
  }

  checkCP(res) {
    for (let i = 0; i < res.length; i++) {
      if (res[i] == 'msg') {
        this.msgCP = true;
      }
      if (res[i] == 'w_oh') {
        this.w_ohCP = true;
      }
    }
  }

  CPStatusChange(status) {
    if (status == 'msg') {
      if (this.msgCP == false) {
        this.msgCP = true;
      }
      else {
        this.msgCP = false;
      }
    }
    if (status == 'w_oh') {
      if (this.w_ohCP == false) {
        this.w_ohCP = true;
      }
      else {
        this.w_ohCP = false;
      }
    }
  }

  updateBillingInfo(form: FormGroup) {
    form.value['user']['id'] = BaseComponent.user.id;
    this.profileService.updateBillingInfo(form.value).subscribe(res => {
      ProfileComponent.brokerageUser.billing_info = res.result;
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    })
  }

  enableEditPaymentMethod(value) {
    if (value == true) {

      this.brokeragePaymentForm.controls.creditCard['controls']['number'].setValue('');
      this.brokeragePaymentForm.controls.creditCard['controls']['cvv'].setValue('');
      this.brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].setValue('');
      this.brokeragePaymentForm.controls.creditCard['controls']['expirationYear'].setValue('');
      this.brokeragePaymentForm.controls.creditCard['controls']['number'].focus;
      this.brokeragePaymentForm.controls.creditCard['controls']['number'].reset();
      this.brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].reset();
      this.brokeragePaymentForm.controls.creditCard['controls']['cvv'].reset();
      this.brokeragePaymentForm.controls.creditCard['controls']['expirationYear'].reset();
      this.showextrafeild = value;
    }
    else {
      this.showextrafeild = value;
      this.brokeragePaymentForm.controls.firstName.setValue(this.cardFirstName);
      this.brokeragePaymentForm.controls.lastName.setValue(this.cardLastName);
      this.brokeragePaymentForm.controls.creditCard['controls']['number'].setValue(ProfileComponent.brokerageUser.payment_method.number);
      this.defaultCardNuber = ProfileComponent.brokerageUser.payment_method.number;
    }

  }

  updatePayment(form: FormGroup) {
    var self = this;
    form.value['cardHolderName'] = form.value['firstName'] + ' ' + form.value['lastName'];
    form.value['creditCard']['billingAddress']['firstName'] = form.value['firstName'];
    form.value['creditCard']['billingAddress']['lastName'] = form.value['lastName'];
    form.value['options'] = { validate: true }

    var createClient = require('braintree-web/client').create;

    createClient({
      authorization: this.CLIENT_AUTHORIZATION
    }, function (createErr, clientInstance) {
      clientInstance.request({
        endpoint: 'payment_methods/credit_cards',
        method: 'post',
        data: form.value
      }, function (requestErr, response) {
        if (requestErr) {
          self.errMessageResponse('Invalid CreditCard');
          throw new Error(requestErr);
        }
        self.cardFirstName = form.value['firstName'];
        self.cardLastName = form.value['lastName'];
        ProfileComponent.brokerageUser.payment_method.first_name = self.cardFirstName;
        ProfileComponent.brokerageUser.payment_method.last_name = self.cardLastName;
        self.updatePaymentMethod(response.creditCards[0].nonce);
      });
    });
  }

  updatePaymentMethod(nonce) {
    this.updatePaymentModal.nonce = nonce;
    this.updatePaymentModal.user.id = BaseComponent.user.id;
    this.profileService.updatePaymentMethod(this.updatePaymentModal).subscribe(res => {
      this.successResponse(res);
      ProfileComponent.brokerageUser.payment_method.token = res.result.token;
      ProfileComponent.brokerageUser.payment_method.last_4 = res.result.last_4;
      this.cardLast4Digit = this.inVisibaleNumber + res.result.last_4;
      ProfileComponent.brokerageUser.payment_method.number = this.cardLast4Digit;
      this.brokeragePaymentForm.controls.creditCard['controls']['number'].setValue(ProfileComponent.brokerageUser.payment_method.number);
      this.defaultCardNuber = ProfileComponent.brokerageUser.payment_method.number;
      this.showextrafeild = false;
    }, err => {
      this.errorResponse(err.json());
    })
  }

  /*
  * @desc: upload/change profile image
  */
  uploadProfileImage(event) {
    let formData = new FormData();
    formData.append('profile_photo', event.srcElement.files[0]);
    this.profileService.uploadUserImage(formData).subscribe(res => {
      this.fileUrl = res['result'];
      ProfileComponent.brokerageUser.profile.profile_photo = res['result'];
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    })
  }

  cardExDateV(value) {
    if (this.isInteger(value)) {
      if (value.length == 0) {
        this.brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'incorrect': true });
      }
      else {
        if (value > 12 || value.length > 2) {
          this.brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'incorrect': true });
        }
        else {
          if (value.length == 1) {
            this.selectedMonth = '0' + value.toString();
          }
          else {
            this.selectedMonth = value;
          }
        }
        if (this.selectedYear != null) {
          this.cardExYearV(this.selectedYear);
        }
      }
    }
    else {
      this.brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'number': true });
    }
  }

  cardExYearV(year) {
    let currentYear: String = new Date().getFullYear().toString();

    if (year.toString() >= currentYear.toString()) {
      this.selectedYear = year;
      let month: Number = new Date().getMonth() + 1;

      let months: String;

      if (month <= 9) {
        months = "0" + month;
      }
      else {
        months = month.toString();
      }

      if (this.selectedMonth >= months && this.selectedYear == currentYear) {
        if (this.selectedMonth >= months) {
        }
      }
      else {
        if (this.selectedYear > currentYear && this.selectedYear.length == 4) {

          if (this.selectedYear.length == 4 && parseInt(this.selectedMonth) > 12) {
            this.brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'incorrect': true });
          }
        }
        else {
          this.brokeragePaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'incorrect': true });
        }
      }
    }
    else {
      this.brokeragePaymentForm.controls.creditCard['controls']['expirationYear'].setErrors({ 'incorrect': true });
    }
  }

  searchLenderUser(lenderName) {
    if (lenderName.trim().length != 0) {
      let lenderParam = new URLSearchParams();
      lenderParam.set('name', lenderName);

      this.profileService.lenderSearch(lenderParam).subscribe(res => {
        this.searchLenderList = res['result'];
        this.showLenderList = true;
      }, err => this.errorResponse(err.json()));
    }
  }

  manageBrokerLender(lenderId, status) {
    let manageLender = new URLSearchParams();
    manageLender.set('attach_user_id', lenderId);
    this.profileService.addRemoveLender(manageLender).subscribe(res => {
      this.myLender = res.result.lender_info;
      ProfileComponent.brokerageUser = res.result;
      if (status == 'add') {
        this.searchLenderList = [];
        this.showLenderList = false;
        $("#searchLenderTxt").val('');
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  agentSorting(type, filedName) {
    let sortList = [];
    if (type == 'V') {
      if (this.verifyAgentSortObject[filedName] == undefined) {
        this.verifyAgentSortObject[filedName] = true;
        $('#V_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
      }
      else {
        this.verifyAgentSortObject[filedName] = this.verifyAgentSortObject[filedName] === true ? false : true;
        if (this.verifyAgentSortObject[filedName]) {
          $('#V_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
        } else {
          $('#V_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
        }
      }
      this.verifyAgentSortList[0] = this.verifyAgentSortObject;
      sortList = this.verifyAgentSortList;
    }

    if (type == 'U') {
      if (this.unverifyAgentSortObject[filedName] == undefined) {
        this.unverifyAgentSortObject[filedName] = true;
        $('#U_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
      }
      else {
        this.unverifyAgentSortObject[filedName] = this.unverifyAgentSortObject[filedName] === true ? false : true;
        if (this.unverifyAgentSortObject[filedName]) {
          $('#U_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
        } else {
          $('#U_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
        }
      }
      this.unverifyAgentSortList[0] = this.unverifyAgentSortObject;
      sortList = this.unverifyAgentSortList;
    }

    if (sortList.length != 0) {
      let sortingParams = new URLSearchParams;
      sortingParams.set('sort_list', JSON.stringify(sortList));
      this.profileService.getInvitesAgentSorting(sortingParams).subscribe(res => {
        if (type == 'V') {
          this.invitedVerifyAgentList = this.brAgentPipe.transform(res['result'], 'V');
        }
        else if (type == 'U') {
          this.invitedUnVerifyAgentList = this.brAgentPipe.transform(res['result'], 'U');
        }
      }, err => {
        this.errorResponse(err.json());
      });
    }
  }

  getValidForm(): boolean {
    if (Object.keys(this.brokerageProfileForm.value['profile']['state']).length == 0) {
      return true;
    }
    return false;
  }

  downLoadInvoiceHistory(transaction) {
    this.invoiceLoadingIndex = transaction['id'];
    this.showInvoiceLoading = true;
    var invoiceName = this.getInvoiceDateFormat(transaction['transaction_date']);
    var transactionId = transaction['id'];
    this.profileService.genratePurchaseHistoryPdf(transactionId).subscribe(res => {
      this.invoiceLoadingIndex = null;
      this.showInvoiceLoading = false;
      let fileBlob = res.blob();
      let blob = new Blob([fileBlob], {
        type: 'application/pdf'
      });
      let filename = 'Invoice_' + invoiceName.toLocaleLowerCase() + '.pdf';
      FileSaver.saveAs(blob, filename);
    }, err => {
      this.invoiceLoadingIndex = null;
      this.showInvoiceLoading = false;
      this.errorResponse(err.json());
    });
  }

  cancelSubscriptionBroker() {
    this.disablecancelSubsBtn = true;
    this.profileService.cancelSubscription().subscribe(res => {
      this.successResponse(res);
      this.accountStatus = false;
      this.permanentPremium = false;
      this.headerComponent.selectedPlan = '';
      ProfileComponent.listingAgent.selected_plan_id = '';
      ProfileComponent.listingAgent.paid_account = false;
      BaseComponent.user.is_paid_account = false;
      this.disablecancelSubsBtn = false;
      $("#cancelSubscriptionBroker").modal("hide");
    }, err => {
      this.errorResponse(err.json());
    });
  }
}
