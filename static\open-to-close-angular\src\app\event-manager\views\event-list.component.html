<div>
    <header></header>
</div>

<div>
    <search-bar (searchObjEvent)="getSearchObj($event)" [eventType]='currentEventType' [searchFrom]="'eventManager'" [listType]='searchListType' [pageNo]='searchPageNo' [currentPage]="'listingAgent'" [isListViewScreen]='true'></search-bar>
    <div class="myclient_page My_Listings">
            <div class="container">
               <div class="ls_group Favorites_page">
                  <div class="title_group ml_zero">
                     <div class="title pull-left">Guest Book</div>
                     <a (click)="routeOnUrl('event-manager')" class="cursor-pointer"><img src="{{imagePrefix}}Icon.png" class="Icon_png" alt=""></a>
                  </div>
               </div>

               <div class="my_client_table_group">
                  <div class="myclient_navbar">
                    <ul>
                    <li class="active" data-toggle="pill" href="#menu1" >Upcoming</li>
                    <li *ngIf="userRole == 'menu-unlock'" data-toggle="pill" href="#menu3">Available</li>
                    <li *ngIf="userRole == 'menu-unlock'" data-toggle="pill" href="#menu4">Requests <i *ngIf="isIncomingEventReq()" class="fa fa-circle request-dot"></i></li>
                    <li *ngIf="userRole == 'menu-lock'"  data-toggle="pill" href="#menu3" (click)="checkUserRole()">Available</li>
                    <li *ngIf="userRole == 'menu-lock'"  data-toggle="pill" href="#menu4" (click)="checkUserRole()">Requests <i *ngIf="isIncomingEventReq()" class="fa fa-circle request-dot"></i></li>
                    <li data-toggle="pill"  href="#menu5" >Past Events</li>
                    </ul>
                  </div>

                  <div class="tab-content">
                    <div id="menu1" class="event-list tab-pane fade  in table-responsive selected_saved active">
                        <div *ngIf="upcomingEventList.length == 0 && showUPLoader == false" class="No_matches">
                            <div class="title">No Events</div>
                            <div class="text">You have no upcoming events in your list matching your search criteria.</div>
                        </div>

                        <div class="No_matches" *ngIf="showUPLoader == true">
                            <div class="loader">
                            <div class="message">Loading...</div>
                            <div class="dots"><div class="center"></div></div>
                            </div>
                        </div>

                        <div *ngIf="upcomingEventList.length != 0" class="property-list-table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th (click)="eventSortting('UP', 'PR')">Property <img id="UP_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('UP', 'EV')">Type <img id="UP_EV" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('UP', 'DT')">Date <img id="UP_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('UP', 'OA')">Open House Agent<img id="UP_OA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('UP', 'GO')"colspan="4">Going <img id="UP_GO" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let event of upcomingEventList; let i = index">
                                        <td (click)="gotToPropertyDetail('event-manager/property-detail',event.property_id)" class="cursor-pointer">
                                            <span class="vertical-align-top" *ngIf="event.property_file != ''"><img [src]="event.property_file" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                                            <span class="vertical-align-top" *ngIf="event.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                                            <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{event?.address}} {{event?.unit_number}}<br></span>{{event?.location}}</div>
                                        </td>
                                        <td>
                                            <div [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">{{event?.event_type_msg}}</div>
                                        </td>
                                        <td><span class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}}<br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>
                                        <td>
                                            <span *ngIf="event.open_house_agent_image != ''"><img [src]="event.open_house_agent_image" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                            <span *ngIf="event.open_house_agent_image == ''"><img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                            <div class="dis_inline po_rel"><span class="dark font_semibold u-name-table">{{event?.open_house_agent_name}} <br></span>
                                            </div>
                                        </td>
                                        <td><div class="bold_font going-text">{{event?.going}}</div></td>
                                        <td>going</td>
                                        <td class="action-view">
                                            <a *ngIf="event.allow_run_event == true && !isBrokerage" (click)="runEvent(event.event_id,'newEvent')">
                                                <div class="save_notes margin_zero">Run Event</div>
                                            </a>
                                            <a *ngIf="event.is_running == true && !isBrokerage" (click)="runEvent(event.event_id,'runningEvent')">
                                                <div class="save_notes margin_zero">Run Event</div>
                                            </a>
                                        </td>
                                        <td class="action-option">
                                            <div class="open_click_menu"  (click)="openMenu(i,event.event_id)">
                                                <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                                <ul id="em_{{i}}_{{event.event_id}}" class="click_menu_open events">
                                                    <li class="cursor-pointer option-menu" (click)="eventDetailView(event)">Event Detail</li>
                                                    <li class="cursor-pointer option-menu" (click)="gotToPropertyDetail('event-manager/property-detail',event.property_id)">Property Detail</li>
                                                    <li *ngIf="!isBrokerage" class="cursor-pointer option-menu" (click)="cancelEvent(event.event_id, 'UP', i)">Cancel Event</li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div *ngIf="upcTotalCount > upcItemPerPage && upcTotalCount != upcomingEventList.length" class="new_form_group load_more_btn">
                                <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreEventManagerList(upcIndex, 'UP')" value="Load More">
                            </div>
                        </div>
                     </div>


                     <div id="menu3" class="event-list tab-pane fade   table-responsive selected_saved">
                        <div class="No_matches" *ngIf="showAVLoader == true">
                            <div class="loader">
                            <div class="message">Loading...</div>
                            <div class="dots"><div class="center"></div></div>
                            </div>
                        </div>

                        <div *ngIf="availableEventList.length == 0 && showAVLoader == false" class="No_matches">
                            <div class="title">No Events</div>
                            <div class="text">There are no available events in your brokerage matching your search criteria.</div>
                        </div>
                        <div *ngIf="availableEventList.length != 0" class="property-list-table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th (click)="eventSortting('AV', 'PR')">Property <img id="AV_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('AV', 'EV')">Event <img id="AV_EV" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('AV', 'DT')">Date & Time <img id="AV_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('AV', 'LA')">Listing Agent<img id="AV_LA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('AV', 'HP')" colspan="4">Property Details<img id="AV_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    </tr>
                                </thead>
                                    <tbody>
                                        <tr *ngFor="let event of availableEventList; let i = index">
                                            <td (click)="gotToPropertyDetail('event-manager/property-detail',event.property_id)" class="cursor-pointer">
                                                <span class="vertical-align-top" *ngIf="event.property_file != ''"><img [src]="event.property_file" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                                                <span class="vertical-align-top" *ngIf="event.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                                                <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{event?.address}} {{event?.unit_number}} <br></span>{{event?.location}}</div>
                                            </td>
                                            <td>
                                                <div [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">{{event?.event_type_msg}}</div>
                                            </td>
                                            <td><span class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}} <br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>
                                            <td>
                                                <span *ngIf="event.listing_agent_image != ''"><img [src]="event.listing_agent_image" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                                <span *ngIf="event.listing_agent_image == ''"><img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                                <div class="dis_inline po_rel"><span class="dark font_semibold u-name-table">{{event?.listing_agent_name}} <br></span>
                                                </div>
                                            </td>
                                            <td><span class="font_semibold">{{event?.home_price | currency:"":symbol:"1.0"}}<br></span>{{event?.property_type}} </td>
                                            <td class="action-view">
                                                <a *ngIf="!isBrokerage" (click)="acceptRequest(event.event_id, 'PU', i, 'PICK',event)"> <div class="save_notes margin_zero" >Hold this Event</div> </a>
                                            </td>
                                            <td class="action-option">
                                                <div class="open_click_menu"  (click)="openMenu(i,event.event_id)">
                                                    <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                                    <ul id="em_{{i}}_{{event.event_id}}" class="click_menu_open events">
                                                        <li [ngClass]="{ 'option-menu': currentUserId != event.listing_agent_id, 'disable-option-menu': currentUserId == event.listing_agent_id }" (click)="contactListingAgent(event)" class="cursor-pointer">Contact Listing Agent</li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                </tbody>
                            </table>
                            <div *ngIf="availTotalCount > availItemPerPage && availTotalCount != availableEventList.length" class="new_form_group load_more_btn">
                                <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreEventManagerList(availIndex, 'AV')" value="Load More">
                            </div>
                        </div>
                     </div>

                     <div id="menu5" class="event-list tab-pane fade table-responsive selected_saved ">
                            <div class="No_matches" *ngIf="showPALoader == true">
                                <div class="loader">
                                <div class="message">Loading...</div>
                                <div class="dots"><div class="center"></div></div>
                                </div>
                            </div>

                            <div *ngIf="pastEventList.length == 0 && showPALoader == false" class="No_matches">
                                <div class="title">No Events</div>
                                <div class="text"> You have no past events matching your search criteria.</div>
                            </div>
                            <div *ngIf="pastEventList.length != 0" class="property-list-table">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th (click)="eventSortting('PA', 'PR')">Property <img id="PA_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                            <th (click)="eventSortting('PA', 'EV')">Event <img id="PA_EV" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                            <th (click)="eventSortting('PA', 'DT')">Date & Time <img id="PA_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                            <th (click)="eventSortting('PA', 'LA')">Listing Agent<img id="PA_LA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                            <th (click)="eventSortting('PA', 'RA')" colspan="5">Ratings<img id="PA_RA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let event of pastEventList; let pev = index">
                                            <td (click)="gotToPropertyDetail('event-manager/property-detail',event.property_id)" class="cursor-pointer">
                                                <span class="vertical-align-top" *ngIf="event.property_file != ''"><img [src]="event.property_file" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                                                <span class="vertical-align-top" *ngIf="event.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                                                <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{event?.address}}  {{event?.unit_number}}<br></span>{{event?.location}}</div>
                                            </td>
                                            <td>
                                                <div [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">{{event?.event_type_msg}}</div>
                                            </td>
                                            <td><span class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}} <br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>
                                            <td>
                                                <span *ngIf="event.listing_agent_image != ''"><img [src]="event.listing_agent_image" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                                <span *ngIf="event.listing_agent_image == ''"><img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                                <div class="dis_inline po_rel"><span class="dark font_semibold u-name-table"> {{event?.listing_agent_name}} <br></span></div>
                                            </td>
                                            <td><div class="bold_font">{{event?.ratings}}</div></td>
                                            <td>
                                                <span *ngIf="event.ratings_type == 'P' && event.ratings !=0">
                                                    <img src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png" class="symbols-glyph-checkin-thumbsup dis_inline" alt="">
                                                </span>
                                                <span *ngIf="event.ratings_type == 'N' && event.ratings !=0">
                                                    <img src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown.png" class="symbols-glyph-checkin-thumbsup dis_inline" alt="">
                                                </span>
                                            </td>
                                            <td class="action-view">
                                                <a *ngIf="!isBrokerage" (click)="runEvent(event.event_id,'runningEvent')"> <div class="Message_Lilly pull-right mt_10 action-btn">Run Event</div> </a>
                                            </td>
                                            <td class="action-option">
                                                <div class="open_click_menu"  (click)="openMenu(pev,event.event_id)">
                                                    <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                                    <ul id="em_{{pev}}_{{event.event_id}}" class="click_menu_open events">
                                                        <li class="cursor-pointer option-menu" (click)="gotToPropertyDetail('event-manager/property-detail',event.property_id)">Property Detail</li>
                                                        <li class="cursor-pointer option-menu" (click)="eventDetailView(event, 'past')">Event Detail</li>
                                                        <li *ngIf="currentUserId != event.listing_agent_id"  (click)="contactListingAgent(event)" class="cursor-pointer option-menu">Contact Listing Agent</li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div *ngIf="pastTotalCount > pastItemPerPage && pastTotalCount != pastEventList.length" class="new_form_group load_more_btn">
                                    <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreEventManagerList(pastIndex, 'PA')" value="Load More">
                                </div>
                            </div>
                         </div>

                     <div id="menu4" class="event-list tab-pane fade table-responsive selected_saved">
                        <div class="No_matches" *ngIf="showRELoader == true">
                            <div class="loader">
                            <div class="message">Loading...</div>
                            <div class="dots"><div class="center"></div></div>
                            </div>
                        </div>
                        <div *ngIf="requestsEventList.length == 0 && showRELoader == false" class="No_matches">
                            <div class="title">No Events</div>
                            <div class="text"> You have no requested events assigned to you matching your search criteria.</div>
                        </div>
                        <!-- request-tab -->
                        <div *ngIf="requestsEventList.length != 0" class="property-list-table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th (click)="eventSortting('RE', 'RE')">Request <img id="RE_RE" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('RE', 'PR')">Property <img id="RE_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('RE', 'EV')">Event <img id="RE_EV" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th (click)="eventSortting('RE', 'DT')">Date & Time <img id="RE_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <th colspan="5" (click)="eventSortting('RE', 'LA')">Listing Agent<img id="RE_LA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        <!-- <th colspan="5" (click)="eventSortting('RE', 'OAR')">Open House Agent<img id="RE_OAR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let event of requestsEventList; let i = index">
                                        <td><div class="bold_font font_semibold">
                                            <span *ngIf="event.request_type == 'O'">Outgoing </span>
                                            <span *ngIf="event.request_type == 'I'">Incoming </span>
                                        </div></td>
                                        <td (click)="gotToPropertyDetail('event-manager/property-detail',event.property_id)" class="cursor-pointer">
                                            <span class="vertical-align-top" *ngIf="event.property_file != ''"><img [src]="event.property_file" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                                            <span class="vertical-align-top" *ngIf="event.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
                                            <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{event?.address}} {{event?.unit_number}}<br></span>{{event?.location}}</div>
                                        </td>
                                        <td>
                                            <div [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">{{event?.event_type_msg}}</div>
                                        </td>
                                        <td><span class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}} <br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>
                                        <td>
                                            <span *ngIf="event.listing_agent_image != ''"><img [src]="event.listing_agent_image" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                            <span *ngIf="event.listing_agent_image == ''"><img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                            <div class="dis_inline po_rel"><span class="dark font_semibold u-name-table">{{event.listing_agent_name}} <br></span>
                                            </div>
                                        </td>
                                        <!-- <td>
                                            <span *ngIf="event.open_house_agent_image != ''"><img [src]="event.open_house_agent_image" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                            <span *ngIf="event.open_house_agent_image == ''"><img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                            <div class="dis_inline po_rel"><span class="dark font_semibold u-name-table">{{event.open_house_agent_name}} <br></span>
                                            </div>
                                        </td> -->
                                        <td class="action-view action-event-view">
                                            <div *ngIf="event.request_type == 'I' && !isBrokerage" class="save_notes margin_zero" (click)="acceptRequest(event.event_id, 'AC', i)">Accept</div>
                                            <div *ngIf="event.request_type == 'O' && !isBrokerage"></div>
                                        </td>
                                        <td class="action-view">
                                            <div *ngIf="event.request_type == 'I' && !isBrokerage" class="save_notes margin_zero deny_css action-btn" (click)="acceptRequest(event.event_id, 'DE', i)">Deny</div>
                                            <div *ngIf="event.request_type == 'O' && !isBrokerage" class="save_notes margin_zero deny_css action-btn" (click)="cancelEvent(event.event_id, 'RE', i)">Cancel</div>
                                        </td>
                                        <td class="action-option">
                                            <div class="open_click_menu"  (click)="openMenu(i,event.event_id)">
                                                <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                                <ul id="em_{{i}}_{{event.event_id}}" class="click_menu_open events">
                                                    <li  [ngClass]="{ 'option-menu': currentUserId != event.listing_agent_id, 'disable-option-menu': currentUserId == event.listing_agent_id }" (click)="contactListingAgent(event)" class="cursor-pointer">Contact Listing Agent</li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div *ngIf="reqTotalCount > reqItemPerPage && reqTotalCount != requestsEventList.length" class="new_form_group load_more_btn">
                                <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreEventManagerList(reqIndex, 'RE')" value="Load More">
                            </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>

<add-event (addEventResponse)="updateUpcomingEvent($event)"></add-event>

<div>
    <footer></footer>
</div>
