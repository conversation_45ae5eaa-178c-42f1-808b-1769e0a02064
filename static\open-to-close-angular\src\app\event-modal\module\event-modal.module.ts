import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { BaseModule } from '@app/base/modules/base.module';
import { EventModalService } from '@app/event-modal/service/event-service';

@NgModule({
  imports: [
    BaseModule,
    CommonModule
  ],
  declarations: [],
  providers: [EventModalService]
})
export class EventModalModule { }
