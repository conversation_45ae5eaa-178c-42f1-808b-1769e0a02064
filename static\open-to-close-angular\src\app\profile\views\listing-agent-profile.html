<div>
    <header></header>
</div>

<div class="listing-profile">
        <div class="new_profiles">
          <div class="new_profile_title">
            <img src="{{fileUrl}}" class="new_symbols-avatar dis_inline" alt="">
             <div class="new_title dis_inline file-upload-title">
                {{profileName}}
                 <label for="file-upload" class="file-upload-label">
                   <span class="custom-file-upload">
                     <span class="file-upload-text">Add Profile Image</span>
                   </span>
                 </label>
                 <input id="file-upload" type="file" (change)="uploadProfileImage($event)"/>
              </div>
          </div>
                <div class="new_profile_group_wrap">
                   <div class="new_profile_group dis_inline">
                      <ul class="nav nav-pills">
                         <li class="active"><a data-toggle="pill" href="#Profile">Profile</a></li>
                         <li><a data-toggle="pill" href="#Password">Password</a></li>
                         <li><a data-toggle="pill" href="#Billing">Billing</a></li>
                         <!-- <li><a data-toggle="pill" href="#participating">Participating Lender</a></li> -->
                      </ul>
                      <div class="tab-content">
                         <div id="Profile" class="tab-pane fade in active">
                          <form [formGroup]="listingAgentProfileForm">
                            <div class="group_1 mt-20">
                               <div class="title2">Basic Information</div>
                               <div class="new_form">
                                  <div class="new_form_group" formGroupName="profile">
                                     <div class="group new_form_label">
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="name">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Name*</label>
                                     </div>
                                  </div>

                                  <!-- <div class="new_form_group" formGroupName="profile">
                                     <div class="group new_form_label">
                                        <input type="text" placeholder=" "  class=" width_350" formControlName="mls_agent_id">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>{{mlsIdText}}</label>
                                     </div>
                                  </div> -->
                                  <div class="new_form_group">
                                     <div class="group new_form_label">
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="email">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Email Address*</label>
                                     </div>
                                  </div>
                                 <div class="new_form_group" formGroupName="profile" >
                                    <div class="group new_form_label">
                                       <input  type="text" maxlength="12" #phone (keyup)="validateFormat(phone.value)" class="width_350" formControlName="cell_phone" placeholder=" " required/>
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Cell Phone Number*</label>
                                    </div>
                                 </div>
                                  <div class="new_form_group " formGroupName="profile"  id ="brockerInfo">
                                     <div class="group new_form_label">
                                        <input  type="text" maxlength="12" #officePhone (keyup)="validateOfficePhoneFormat(officePhone.value)" class="width_350" formControlName="office_phone" placeholder=" "/>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Office Phone Number</label>
                                     </div>
                                  </div>
                               </div>
                            </div>
                            <!-- <div class="group_1 mt-20">
                               <div class="title2">Contact Preferences</div>
                               <div class="new_form">
                                  <div class="check_group profile_checkbox width_350 ">
                                     <div class="form_group flex_none">
                                        <input type="checkbox" (change)="CPStatusChange('msg')" [checked]="msgCP">   <span class="checkmark"></span>
                                        <label class="width_auto">Message Notifications</label>
                                     </div>
                                     <div class="form_group">
                                        <input type="checkbox" (change)="CPStatusChange('w_oh')" [checked]="w_ohCP">   <span class="checkmark"></span>
                                        <label class="width_auto">Weekly Open House Notification</label>
                                     </div>
                                  </div>
                               </div>
                            </div> -->
                            <!-- <div class="group_1 mt-20 borkerInfo" id="brokerDiv">
                               <div class="title2">Broker Information
                               <p class="form-validation" *ngIf="isBrokerInfoExist==false">Broker information is required</p>
                               </div>
                               <div class="new_form">
                                  <div class="new_form_group " formGroupName="profile">
                                    <div class="group new_form_label">
                                        <input type="text" placeholder=" " formControlName="brokerage_id" class=" width_350" appAutoComplete  #brokerEmail (OnTypeCompleteMethod)="getBrokerEmail(brokerEmail.value)">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Broker Email</label>
                                     </div>
                                  </div>

                                  <div class="new_form_group " formGroupName="profile" *ngIf="isBrokerEmailFound == false">
                                    <div class="group new_form_label disable_form" >
                                       <input type="text" placeholder=" "  formControlName="broker_name_actual" class=" width_350">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Broker Name</label>
                                    </div>
                                 </div>

                                 <div class="new_form_group " formGroupName="profile">
                                      <div class="group new_form_label disable_form" >
                                         <input type="text" placeholder=" "  formControlName="broker_name" class=" width_350">
                                         <span class="highlight"></span>
                                         <span class="bar"></span>
                                         <label>Brokerage Name</label>
                                      </div>
                                   </div>

                                  <div class="new_form_group " formGroupName="profile">
                                     <div class="group new_form_label disable_form" >
                                        <input type="text" placeholder=" "  formControlName="broker_address" class=" width_350">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Street</label>
                                     </div>
                                  </div> -->

                                  <!-- <div class="new_form_group " formGroupName="profile">
                                    <div class="group new_form_label">
                                       <input type="text" required class=" width_350">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Cell Phone Number</label>
                                    </div>
                                 </div> -->
                                 <!-- <div class="new_form_group " formGroupName="profile">
                                    <div class="group new_form_label disable_form" [ngClass]="{'disable_form_blank':listingAgentProfileForm['controls'].profile['controls']['broker_state'].value == null}">
                                       <input type="text" placeholder=" " required  formControlName="broker_state" class=" width_350">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>State</label>
                                    </div>
                                 </div> -->
                                 <!-- <div class="new_form_group " formGroupName="profile">
                                    <div class="group new_form_label disable_form" [ngClass]="{'disable_form_blank':listingAgentProfileForm['controls'].profile['controls']['broker_city'].value == null}">
                                       <input type="text" placeholder=" " required  formControlName="broker_city" class=" width_350">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>City</label>
                                    </div>
                                 </div> -->
                                 <!-- <div class="new_form_group " formGroupName="profile">
                                    <div class="group new_form_label disable_form" [ngClass]="{'disable_form_blank':listingAgentProfileForm['controls'].profile['controls']['broker_zipcode'].value == null}">
                                       <input type="text" placeholder=" " required  formControlName="broker_zipcode" class=" width_350">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Zipcode</label>
                                    </div>
                                 </div> -->

                                 <!-- New Code -->
                                 <!-- <div class="new_form_group " formGroupName="profile">
                                    <div class="group new_form_label disable_form" [ngClass]="{'disable_form_blank':listingAgentProfileForm['controls'].profile['controls']['broker_city'].value == null}">
                                      <input appAutoComplete type="text" #city (focusout)="focusOutFunction('city',city.value)"
                                      autocomplete="off" (OnTypeCompleteMethod)="searchCity(city.value)" formControlName="broker_city" class="width_350" placeholder=" ">
                                      <span class="highlight"></span>
                                      <span class="bar"></span>
                                      <label>City</label>
                                      <div class="baths_group price_ul search-drop-down" *ngIf="showCitySearchResult == true">
                                        <ul id="city_menu_ul" class="search-drop-down-result search-bg">
                                          <li id="city_menu_li" class="search-drop-down-li" *ngFor="let value of br_cityList_new"
                                            (click)="onCitySelect(value)">
                                            {{value.city_name}}
                                          </li>
                                        </ul>
                                      </div>
                                    </div>
                                  </div>

                                  <div class="new_form_group " formGroupName="profile">
                                    <div class="group new_form_label disable_form width_350" [ngClass]="{'disable_form_blank':listingAgentProfileForm['controls'].profile['controls']['broker_state'].value == null}">
                                      <ng-select class="custom agent-dropdown" [virtualScroll]="true" placeholder=""
                                        formControlName="broker_state" notFoundText="No State found" [items]="br_stateList_new" bindLabel="name"
                                        bindValue="name" [clearable]=false [searchable]=false (change)="getCityList($event.id)">
                                      </ng-select>
                                      <label *ngIf="showStateLabel == true" class="drop_down_label">State</label>
                                      <span class="highlight"></span>
                                      <span class="bar"></span>
                                    </div>
                                  </div>

                                  <div class="new_form_group " formGroupName="profile">
                                    <div class="group new_form_label disable_form" [ngClass]="{'disable_form_blank':listingAgentProfileForm['controls'].profile['controls']['broker_zipcode'].value == null}">
                                      <input appAutoComplete type="text" #zipCode (focusout)="focusOutFunction('zipCode',zipCode.value)"
                                      autocomplete="off" (OnTypeCompleteMethod)="searchZipCode(zipCode.value)" formControlName="broker_zipcode" class="width_350"
                                        placeholder=" ">
                                      <span class="highlight"></span>
                                      <span class="bar"></span>
                                      <label>ZipCode</label>
                                      <div class="baths_group price_ul search-drop-down" *ngIf="showZipCodeSearchResult == true">
                                        <ul id="zipcode_menu_ul" class="search-drop-down-result search-bg">
                                          <li id="zipcode_menu_li" class="search-drop-down-li" *ngFor="let value of br_zipCodeList_new"
                                            (click)="onZipCodeSelect(value)">
                                            {{value.code}}
                                          </li>
                                        </ul>
                                      </div>
                                    </div>
                                  </div> -->

                                  <!-- <div class="new_form_group" formGroupName="profile" *ngIf="isBrokerEmailFound == false" >
                                    <div class="group new_form_label">
                                       <input  type="text" maxlength="12" #brokerphone (keyup)="validateFormatBroker(brokerphone.value)" class="width_350" formControlName="broker_cell_phone" placeholder=" " />
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Cell Phone Number*</label>
                                    </div>
                                 </div> -->
                                  <!-- <div class="new_form_group " formGroupName="profile"  id ="brockerInfo" *ngIf="isBrokerEmailFound == false">
                                     <div class="group new_form_label">
                                        <input  type="text" maxlength="12" required #brokerofficePhone (keyup)="validateOfficePhoneFormatBroker(brokerofficePhone.value)"
                                        class="width_350" formControlName="broker_office_phone" placeholder=" "
                                        [ngClass]="{'disable_form_blank':listingAgentProfileForm['controls'].profile['controls']['broker_office_phone'].value == null}"/>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Office Phone Number</label>
                                     </div>
                                  </div> -->







                                 <!-- OLD Code  -->
                                  <!-- <div class="new_form_group ">
                                    <div class="group new_form_label">
                                      <div class="form_group width_350">
                                           <ng-select class="custom agent-dropdown"
                                                      placeholder = "State"
                                                      notFoundText="No State found"
                                                      [items]="br_stateList"
                                                      bindLabel="name"
                                                      bindValue="id"
                                                      [clearable]=false
                                                      [searchable]=false
                                                      (change)="getBRCityList($event.id)">
                                            </ng-select>
                                        <img src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="img-responsive drop_down" alt="">
                                      </div>
                                    </div>
                                </div>
                                  <div class="new_form_group " formGroupName="profile">
                                    <div class="form_group width_350" >
                                      <ng-select class="custom agent-dropdown"
                                          placeholder = "City"
                                          notFoundText="No City found"
                                          [items]="br_cityList"
                                          bindLabel="name"
                                          bindValue="id"
                                          [clearable]=false
                                          [searchable]=false
                                          formControlName="city_br"
                                          (change)="getBRZipCode($event.id)">
                                      </ng-select>
                                      <img src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="img-responsive drop_down" alt="">
                                    </div>
                                  </div>

                                  <div class="new_form_group " formGroupName="profile">
                                    <div class="form_group width_350">
                                        <ng-select class="custom agent-dropdown"
                                          placeholder = "Zip Code"
                                          formControlName="zipcode_br"
                                          notFoundText="No Zip Code found"
                                          [items]="br_zipCodeList"
                                          bindLabel="code"
                                          bindValue="id"
                                          [clearable]=false
                                          [searchable]=false
                                          (change)="setBRZipCode($event.id)">
                                        </ng-select>
                                      <img src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="img-responsive drop_down" alt="">
                                    </div>
                                  </div>  -->

                                  <!-- OLD Code END -->
                               <!-- </div>
                            </div> -->



                            <div class="group_1 mt-20">
                              <div class="my-market-div">
                               <div class="title2">My Market</div><br>
                               <div class="my-market-text">
                                  Select your preferences
                                   for being notified about
                                   new and upcoming
                                   Broker Open events.
                               </div>
                              </div>

                                <div class="new_form">
                                  <div class="new_form_group " formGroupName="profile">
                                      <div class="form_group width_350">
                                        <!-- <select class=" width_350" name="" (change)="getCityList($event)" >
                                            <option>choose your state</option>
                                            <option *ngFor="let state of stateList" [value]="state.id">{{state.name}}</option>
                                        </select> -->

                                        <!-- <ng-select
                                            class="agent-dropdown"
                                            [items]="stateList"
                                            [multiple]="true"
                                            bindLabel="name"
                                            bindValue="id"
                                            placeholder="State"
                                            notFoundText="No State found"
                                            (add)="onItemAdd($event)"
                                            (remove)="onItemRemoved($event)"
                                            [clearable]=false
                                            [searchable]=false>
                                            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                                                <div class="ng-value" *ngFor="let item of items">
                                                    <span class="ng-value-label">{{item.name}}</span>
                                                    <span class="ng-value-icon right" (click)="clear(item); $event.stopPropagation()" aria-hidden="true">×</span>
                                                </div>
                                            </ng-template>
                                        </ng-select> -->

                                        <tag-input [(ngModel)]='states'
                                                    [placeholder]="''"
                                                    [secondaryPlaceholder]="'State'"
                                                    [onlyFromAutocomplete]="true"
                                                    [ngModelOptions]="{standalone: true}"
                                                    theme='bootstrap'
                                                    [inputId]="'stateTag'"
                                                    [identifyBy]="'id'"
                                                    [displayBy]="'name'"
                                                    (onAdd)="onItemAdd($event)"
                                                    (onRemove)="onItemRemoved($event)">
                                            <tag-input-dropdown [autocompleteItems]="stateList"
                                                                    [identifyBy]="'id'"
                                                                    [displayBy]="'name'"
                                                                    [showDropdownIfEmpty]="true">
                                            </tag-input-dropdown>
                                        </tag-input>
                                        <label *ngIf="showOHPStatesLabel == true" class="drop_down_label">State</label>
                                        <img (click)="dropDownArrowClick('state')" src="{{imagePrefix}}symbols-glyph-arrow-line-teal.png" class="tag-dropdown-icon img-responsive drop_down cursor-pointer" alt="">
                                      </div>
                                    </div>
                                  <div class="new_form_group " formGroupName="profile">
                                      <div class="form_group width_350" >
                                      <!-- <select name="" class=" width_350" (change)="getZipCode($event)" formControlName="ohp_city">
                                        <option>choose your city</option>
                                        <option *ngFor="let city of cityList" [value]="city.id">{{city.name}}</option>
                                      </select> -->
                                      <!-- <select name="" class=" width_350" (change)="getZipCode($event)">
                                          <option>choose your city</option>
                                          <option *ngFor="let city of cityList" [value]="city.id">{{city.name}}</option>
                                        </select> -->

                                        <!-- <ng-select
                                            class="agent-dropdown"
                                            [items]="cityList"
                                            [multiple]="true"
                                            bindLabel="name"
                                            bindValue="id"
                                            placeholder="City"
                                            notFoundText="No City found"
                                            (add)="onItemAdd($event)"
                                            (remove)="onItemRemoved($event)"
                                            [clearable]=false
                                            [searchable]=false>
                                            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                                                <div class="ng-value" *ngFor="let item of items">
                                                    <span class="ng-value-label">{{item.name}}</span>
                                                    <span class="ng-value-icon right" (click)="clear(item); $event.stopPropagation()" aria-hidden="true">×</span>
                                                </div>
                                            </ng-template>
                                        </ng-select> -->

                                        <tag-input  [(ngModel)]='cities'
                                                    [placeholder]="''"
                                                    [secondaryPlaceholder]="'City'"
                                                    [onlyFromAutocomplete]="true"
                                                    [ngModelOptions]="{standalone: true}"
                                                    theme='bootstrap'
                                                    [identifyBy]="'id'"
                                                    [inputId]="'cityTag'"
                                                    [displayBy]="'name'"
                                                    (onAdd)="onItemAdd($event)"
                                                    (onRemove)="onItemRemoved($event)">
                                            <tag-input-dropdown [autocompleteItems]="cityList"
                                                                    [identifyBy]="'id'"
                                                                    [displayBy]="'name'"
                                                                    [showDropdownIfEmpty]="true">
                                            </tag-input-dropdown>
                                        </tag-input>
                                      <img (click)="dropDownArrowClick('city')" src="{{imagePrefix}}symbols-glyph-arrow-line-teal.png" class="tag-dropdown-icon img-responsive drop_down cursor-pointer" alt="">
                                      <label *ngIf="showOHPCitysLabel == true" class="drop_down_label">City</label>
                                    </div>
                                  </div>

                                  <div class="new_form_group " formGroupName="profile">
                                    <div class="form_group width_350">
                                      <!-- <select class=" width_350" (change)="setZipCode($event)" name="" formControlName="ohp_zip">
                                          <option>choose your zip code</option>
                                          <option *ngFor="let zipCode of zipCodeList" [value]="zipCode.id">{{zipCode.code}}</option>
                                      </select> -->
                                      <!-- <select class=" width_350" (change)="setZipCode($event)" name="">
                                          <option>choose your zip code</option>
                                          <option *ngFor="let zipCode of zipCodeList" [value]="zipCode.id">{{zipCode.code}}</option>
                                      </select> -->

                                      <!-- <ng-select
                                          class="agent-dropdown"
                                          [items]="zipCodeList"
                                          [multiple]="true"
                                          bindLabel="code"
                                          bindValue="id"
                                          placeholder="ZipCode"
                                          notFoundText="No ZipCode found"
                                          (add)="onItemAdd($event)"
                                          (remove)="onItemRemoved($event)"
                                          [clearable]=false
                                          [searchable]=false>
                                          <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                                              <div class="ng-value" *ngFor="let item of items">
                                                  <span class="ng-value-label">{{item.name}}</span>
                                                  <span class="ng-value-icon right" (click)="clear(item); $event.stopPropagation()" aria-hidden="true">×</span>
                                              </div>
                                          </ng-template>
                                      </ng-select> -->

                                      <tag-input [(ngModel)]='zipCodes'
                                                      [placeholder]="''"
                                                      [secondaryPlaceholder]="'Zip'"
                                                      [onlyFromAutocomplete]="true"
                                                      [ngModelOptions]="{standalone: true}"
                                                      theme='bootstrap'
                                                      [identifyBy]="'id'"
                                                      [inputId]="'zipCodeTag'"
                                                      [displayBy]="'code'"
                                                      (onAdd)="onItemAdd($event)"
                                                      (onRemove)="onItemRemoved($event)">
                                          <tag-input-dropdown [autocompleteItems]="zipCodeList"
                                                                    [identifyBy]="'id'"
                                                                    [displayBy]="'code'"
                                                                    [showDropdownIfEmpty]="true">

                                          </tag-input-dropdown>
                                      </tag-input>
                                      <img (click)="dropDownArrowClick('zipCode')" src="{{imagePrefix}}symbols-glyph-arrow-line-teal.png" class="tag-dropdown-icon img-responsive drop_down cursor-pointer" alt="">
                                      <label *ngIf="showOHPZipCodesLabel == true" class="drop_down_label">Zip</label>
                                      </div>
                                  </div>
                                  <div class="new_form_group " formGroupName="profile">
                                      <div class="form_group width_350" >
                                        <tag-input  [(ngModel)]='homeTypes'
                                                    [placeholder]="''"
                                                    [secondaryPlaceholder]="'Types'"
                                                    [onlyFromAutocomplete]="true"
                                                    [ngModelOptions]="{standalone: true}"
                                                    theme='bootstrap'
                                                    [inputId]="'homeTypeTag'"
                                                    (onAdd)="onHomeTypeAdd($event)"
                                                    (onRemove)="onHomeTypeRemoved($event)">
                                            <tag-input-dropdown [autocompleteItems]="homeTypeList"
                                                                    [showDropdownIfEmpty]="true">
                                            </tag-input-dropdown>
                                        </tag-input>
                                      <img (click)="dropDownArrowClick('homeTypeTag')" src="{{imagePrefix}}symbols-glyph-arrow-line-teal.png" class="tag-dropdown-icon img-responsive drop_down cursor-pointer" alt="">
                                      <label *ngIf="showOHPTypeLabel == true" class="drop_down_label">Types</label>
                                    </div>
                                  </div>
                                  <!-- <div class="new_form_group " formGroupName="profile">
                                     <div class="group new_form_label">
                                        <input type="text" required class=" width_350" formControlName="ohp_cell_phone_number">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Cell Phone Number</label>
                                     </div>
                                  </div> -->
                                  <label class="label-space">Price Range</label>
                                  <div class="new_form_group dis_inline fullSize-input" formGroupName="profile">
                                      <div class="group new_form_label fullSize">
                                         <input type="text" placeholder=" " class=" " formControlName="ohp_price_low">
                                         <span class="highlight"></span>
                                         <span class="bar"></span>
                                         <label>Min</label>
                                      </div>
                                   </div>
                                   <div class="new_form_group dis_inline ml-10 fullSize-input" formGroupName="profile">
                                      <div class="group new_form_label fullSize">
                                         <input type="text" placeholder=" " class=" " formControlName="ohp_price_high">
                                         <span class="highlight"></span>
                                         <span class="bar"></span>
                                         <label>Max</label>
                                      </div>
                                   </div>
                                  <!-- <div class="new_form_group " formGroupName="profile"> -->
                                     <!-- <div class="group new_form_label">       -->
                                        <!-- <input type="text" required class=" width_350" formControlName="ohp_price_range"> -->
                                        <!-- <input type="text" required class=" width_350"> -->
                                        <!-- <span class="highlight"></span> -->
                                        <!-- <span class="bar"></span> -->
                                        <!-- <label>Price Range</label> -->
                                     <!-- </div> -->
                                  <!-- </div> -->
                                  <div class="new_form_group ">
                                        <input type="submit" [ngClass]="{'submit-disable':listingAgentProfileForm.invalid}" [disabled]="listingAgentProfileForm.invalid" class="submit_button with_bg" value="Submit" (click)="checkEmailIsChanged(listingAgentProfileForm)">
                                     </div>
                               </div>
                            </div>
                            </form>
                         </div>


                         <div id="Password" class="tab-pane fade">
                            <div class="group_1 mt-20">
                               <div class="title2">Change your Password</div>
                               <div class="new_form">
                                <form [formGroup]="profilePassword" >
                                  <div class="new_form_group ">
                                     <div class="group new_form_label">
                                        <input type="password" placeholder=" " required class=" width_350" formControlName="password">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Old Password*</label>
                                        <div *ngIf="profilePassword.controls.password.touched">
                                          <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.required">Enter old password</p>
                                          <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.minlength">Password must be 5-15 characters</p>
                                          <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.maxlength">Password must be 5-15 characters</p>
                                      </div>
                                     </div>
                                  </div>
                                  <div class="new_form_group ">
                                     <div class="group new_form_label">
                                        <input type="password" placeholder=" " required class=" width_350" formControlName="new_password">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>New Password*</label>
                                        <div *ngIf="profilePassword.controls.new_password.touched">
                                          <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.required">Enter new password</p>
                                          <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.minlength">Password must be 5-15 characters</p>
                                          <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.maxlength">Password must be 5-15 characters</p>
                                      </div>
                                     </div>
                                  </div>
                                  <div class="new_form_group ">
                                     <div class="group new_form_label">
                                        <input type="password" placeholder=" " required class=" width_350" formControlName="confirm_new_password">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Confirm New Password*</label>
                                        <div *ngIf="profilePassword.controls.confirm_new_password.touched">
                                          <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.required">Enter confirm password</p>
                                          <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.minlength">Password must be 5-15 characters</p>
                                          <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.maxlength">Password must be 5-15 characters</p>
                                          <p class="form-validation" *ngIf="profilePassword.hasError('mismatch')">Confirm password not match</p>
                                        </div>
                                     </div>
                                  </div>
                                  <div class="new_form_group ">
                                     <input type="submit" [ngClass]="{'submit-disable':profilePassword.invalid}" [disabled]="profilePassword.invalid" class="submit_button with_bg" value="Submit" (click)="updatePassword(profilePassword)">
                                  </div>
                                </form>
                               </div>
                            </div>
                         </div>
                         <div id="Billing" class="tab-pane fade billing_ss">
                            <div class="group_1 mt-20">
                               <div class="title2">
                                  Payment Method
                               </div>
                               <div class="new_form">
                                <form [formGroup]="listingAgentPaymentForm">
                                  <div class="new_form_group dis_inline fullSize-input">
                                    <div class="group new_form_label fullSize" [ngClass]="{'disable_form':showextrafeild ? null : 'disabled','disable_form_blank':listingAgentPaymentForm.controls.firstName.value == null && showextrafeild == false}">
                                       <input type="text" placeholder=" " id="firstname" required class="" [attr.disabled] = "showextrafeild ? null : 'disabled'" formControlName="firstName">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>First Name*</label>
                                    </div>
                                 </div>
                                 <div class="new_form_group dis_inline ml-10 fullSize-input">
                                    <div class="group new_form_label fullSize"[ngClass]="{'disable_form':showextrafeild ? null : 'disabled','disable_form_blank':listingAgentPaymentForm.controls.lastName.value == null && showextrafeild == false}">
                                       <input type="text" placeholder=" " required class=" " [attr.disabled] = "showextrafeild ? null : 'disabled'"  formControlName="lastName">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Last Name*</label>
                                    </div>
                                 </div>
                                 <div class="new_form_group " *ngIf="showextrafeild">
                                    <div class="group new_form_label" formGroupName="creditCard">
                                       <input type="text" placeholder=" " #creditCardNumber required class=" width_350" [attr.disabled] = "showextrafeild ? null : 'disabled'"  formControlName="number" >
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Card Number*</label>
                                       <div *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['number'].touched">
                                          <p class="form-validation" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['number'].errors?.required">Enter Card number</p>
                                          <p class="form-validation" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['number'].errors?.pattern">Card number must be Digit or max length 16</p>
                                        </div>
                                    </div>
                                 </div>

                                 <div class="new_form_group" *ngIf="!showextrafeild">
                                    <div class="group new_form_label" [ngClass]="{'disable_form':showextrafeild ? null : 'disabled','disable_form_blank':listingAgentPaymentForm.controls.creditCard['controls']['number'].value == null}">
                                       <input type="text" placeholder=" " class=" width_350" [attr.disabled] = "showextrafeild ? null : 'disabled'" [(ngModel)] = "defaultCardNuber"  [ngModelOptions]="{standalone: true}">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Card Number*</label>
                                    </div>
                                 </div>

                                 <span *ngIf="showextrafeild">
                                  <div class="new_form_group ">
                                    <div class="group new_form_label" formGroupName="creditCard">
                                       <input type="password" placeholder=" " required class=" width_350"  formControlName="cvv">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>CVV*</label>
                                       <div *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['cvv'].touched">
                                          <p class="form-validation" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['cvv'].errors?.required">Enter cvv number</p>
                                          <p class="form-validation" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['cvv'].errors?.pattern">cvv must be Digit or max length 4</p>
                                        </div>
                                    </div>
                                 </div>

                                 <label class="label-space">Expires On</label>
                                 <div class="new_form_group dis_inline fullSize-input">
                                    <div class="group new_form_label fullSize" formGroupName="creditCard">
                                       <input type="text" placeholder=" " required class=" " #date (keyup)="cardExDateV(date.value)" formControlName="expirationMonth">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Month*</label>
                                    </div>
                                 </div>

                                 <div class="new_form_group dis_inline ml-10 fullSize-input">
                                    <div class="group new_form_label fullSize" formGroupName="creditCard">
                                       <input type="text" placeholder=" " required class="smallSize-number " #year (keyup)="cardExYearV(year.value)"  formControlName="expirationYear">
                                       <span class="highlight"></span>
                                       <span class="bar"></span>
                                       <label>Year*</label>
                                    </div>
                                 </div>
                                 <!-- <div class="new_form_group"> -->
                                  <div class="errmsg">
                                    <div class="col-md-6 err-month-paddding">
                                    <span class="err-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].touched">
                                      <p class="form-validation err-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].errors?.required" >Enter Month</p>
                                      <p class="form-validation err-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].errors?.number">Month must be Digit or max length 2</p>
                                      <p class="form-validation err-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].errors?.incorrect">Invalid Month</p>
                                    </span>
                                  </div>

                                  <div class="col-md-6">
                                    <span class="err-year-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationYear'].touched">
                                      <p class="form-validation err-year-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationYear'].errors?.required">Enter Year</p>
                                      <p class="form-validation err-year-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationYear'].errors?.yearNumber">Year must be Digit or max length 4</p>
                                      <p class="form-validation err-year-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationYear'].errors?.maxlength">Year must be Digit or max length 4</p>
                                      <p class="form-validation err-year-width" *ngIf="listingAgentPaymentForm.controls.creditCard['controls']['expirationYear'].errors?.incorrect">Invalid Year</p>
                                    </span>
                                  </div>
                                  </div>
                                <!-- </div> -->
                                </span>
                                  <div class="new_form_group">
                                     <input type="submit"  *ngIf="!showextrafeild" (click)="enableEditPaymentMethod(true)" class="submit_button" value="Change">
                                     <input type="submit" *ngIf="showextrafeild" class="submit_button with_bg" value="Update" [ngClass]="{'submit-disable':listingAgentPaymentForm.invalid}" [disabled]="listingAgentPaymentForm.invalid" (click)="updatePayment(listingAgentPaymentForm)">
                                     <input type="submit"  *ngIf="showextrafeild" (click)="enableEditPaymentMethod(false)" class="cancle_button dis_inline" value="Cancel">
                                    </div>

                                </form>
                               </div>
                            </div>

                            <div class="group_1 mt-20">
                               <div class="title2">Billing Info</div>
                               <div class="new_form">
                                <form [formGroup]="listingAgentBillingInfo">
                                  <div class="new_form_group ">
                                     <div class="group new_form_label">
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="address_1">
                                        <div *ngIf="listingAgentBillingInfo.controls.address_1.touched">
                                          <p class="form-validation" *ngIf="listingAgentBillingInfo.controls.address_1.errors?.required">Billing address is required</p>
                                        </div>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Billing Address*</label>
                                     </div>
                                  </div>
                                  <div class="new_form_group ">
                                     <div class="group new_form_label">
                                        <input type="text" placeholder=" " class=" width_350" formControlName="address_2">
                                        <div *ngIf="listingAgentBillingInfo.controls.address_2.touched">
                                          <p class="form-validation" *ngIf="listingAgentBillingInfo.controls.address_2.errors?.required">Address(Cont.) is required</p>
                                        </div>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Address (Cont.)</label>
                                     </div>
                                  </div>
                                  <div class="new_form_group ">
                                     <div class="group new_form_label">
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="city"  >
                                        <div *ngIf="listingAgentBillingInfo.controls.city.touched">
                                          <p class="form-validation" *ngIf="listingAgentBillingInfo.controls.city.errors?.required">City is required</p>
                                        </div>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>City*</label>
                                     </div>
                                  </div>

                                  <!-- <div class="group_1 mt-20">
                                    <div class="title2 sub_title">Address</div>
                                    <div class="new_form">
                                      <div class="new_form_group ">
                                        <div class="group new_form_label">
                                          <input type="text" required="" class=" width_350" formControlName="street" placeholder=" ">
                                          <span class="highlight"></span>
                                          <span class="bar"></span>
                                          <label>Street*</label>
                                        </div>
                                      </div> -->

                                  <div class="new_form_group ">
                                     <div class="group new_form_label">
                                        <input type="text" placeholder="" required class=" width_350" formControlName="state" >
                                        <div *ngIf="listingAgentBillingInfo.controls.state.touched">
                                          <p class="form-validation" *ngIf="listingAgentBillingInfo.controls.state.errors?.required">State is required</p>
                                        </div>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>State*</label>
                                     </div>
                                  </div>
                                  <div class="new_form_group ">
                                     <div class="group new_form_label">
                                        <input type="number" placeholder=" " id="zip"  required class=" width_350" formControlName="zipcode" >
                                        <div *ngIf="listingAgentBillingInfo.controls.zipcode.touched">
                                          <p class="form-validation" *ngIf="listingAgentBillingInfo.controls.zipcode.errors?.required">Zipcode is required</p>
                                          <p class="form-validation" *ngIf="listingAgentBillingInfo.controls.zipcode.errors?.minlength">Zipcode must be 6 characters</p>
                                          <p class="form-validation" *ngIf="listingAgentBillingInfo.controls.zipcode.errors?.maxlength">Zipcode must be 6 characters</p>
                                        </div>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Zip*</label>
                                     </div>
                                  </div>
                                  <div class="new_form_group ">
                                     <input type="submit" class="submit_button" [ngClass]="{'submit-disable':listingAgentBillingInfo.invalid}" [disabled]="listingAgentBillingInfo.invalid" value="Submit" (click)="updateBillingInfo(listingAgentBillingInfo)">
                                  </div>
                                  </form>
                               </div>
                            </div>
                            <div class="group_1 mt-20">
                               <div class="title2">Invoice History</div>
                               <div class="invoice_history">
                                 <div class="invoice_group" *ngIf="purchaseHistory.length == 0">
                                    <div class="in_date dis_inline">No History</div>
                                 </div>
                                  <div class="invoice_group" *ngFor="let history of purchaseHistory">
                                     <div class="in_date dis_inline">{{getPurchaseHistoryDate(history.transaction_date)}}</div>
                                     <div class="in_rs dis_inline">${{history.price}}</div>
                                     <div *ngIf="invoiceLoadingIndex == null && showInvoiceLoading == false" class="question_mark dis_inline"><i class="fa fa-download" (click)="downLoadInvoiceHistory(history)"></i></div>
                                     <div *ngIf="invoiceLoadingIndex == history.id && showInvoiceLoading == true" class="question_mark dis_inline"><img class="invoice-loging" src="{{imagePrefix}}loading.gif"></div>
                                  </div>
                                  <div *ngIf="showAllinvoicesLink == true" class="show_all_invoice" (click)="getPurchasehistory(true)">Show all invoices</div>
                               </div>
                            </div>
                         </div>
                         <!-- <div id="participating" class="tab-pane new_profile_details">
                            <div class="group_1 mt-20" *ngIf="(myLender | json) != '{}'">
                               <div class="title2">Your participating lender</div>
                               <div class="new_form">
                                  <div class="new_form_group">
                                    <span *ngIf="myLender?.profile_photo == ''">
                                        <img src="{{imagePrefix}}default-placeholder.png" class="new_symbols-avatar searched-agent-image dis_inline" alt="">
                                    </span>
                                    <span *ngIf="myLender?.profile_photo != ''">
                                        <img src="{{myLender?.profile_photo}}" class="new_symbols-avatar searched-agent-image dis_inline" alt="">
                                    </span>
                                     <div class="new_details dis_inline">
                                        <div class="title3">
                                          {{myLender?.name}}
                                        </div>
                                        <div class="sub_title">
                                          {{myLender?.lending_company}}
                                        </div>
                                     </div>
                                     <div class="lender dis_inline">
                                        <input type="submit" (click)="manageLender(0,'remove')" class="remove-lender" value="Remove">
                                    </div>
                                  </div>
                               </div>
                            </div>
                            <div class="group_1 mt-20">
                               <div class="title2">Change your participating lender</div>
                               <div class="new_form">
                                  <div class="new_form_group ">
                                     <div class="group new_form_label ">
                                        <input id="searchLenderTxt" type="text"  #lender placeholder=" " required class=" width_350" (keyup)="searchLenderUser(lender.value)">
                                        <i class="fa fa-search search_button"></i>
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Lender Name</label>
                                     </div>
                                  </div>
                                  <div *ngIf="showLenderList != false">
                                    <div class="agent_found">
                                        {{searchLenderList.length}} agent found
                                    </div>
                                    <div [ngClass]="{'search-agent-list': searchLenderList.length > 3}">
                                       <div class="remove_agent search-agent-padding" *ngFor="let lender of searchLenderList" >
                                           <span *ngIf="lender.profile_photo == null || lender.profile_photo == '' || lender.profile_photo == undefined" [ngClass]="{ 'remove-span' : lender.profile_photo == null || lender.profile_photo == '' || lender.profile_photo == undefined}"><img src="{{imagePrefix}}testmonial-default (1).png" class="img-responsive" alt=""></span>
                                           <span *ngIf="lender.profile_photo != null || lender.profile_photo != undefined || lender.profile_photo != ''"><img src="{{lender.profile_photo}}" width="130px" height="130px" class="search-agent-image" alt=""></span>
                                           <div class="remove_details">
                                               <div *ngIf="lender.name != null" class="name">{{lender.name}}</div>
                                               <div *ngIf="lender.lending_company != null" class="name">{{lender.lending_company}}</div>
                                               <div class="remove_button select" (click)="manageLender(lender.id,'add')">Add Lender</div>
                                           </div>
                                       </div>
                                    </div>
                                   </div>
                               </div>
                            </div>
                            <div class="pl_detail">
                               <div class="title2">My lender isn’t showing up</div>
                               <div class="sub_title pref-lender-sub-title">Send your preferred lender this link so they can set up a profile on Open to Close</div>
                               <div class="new_form_group">
                                  <input type="text" id="lenderInviteLink" placeholder="Lender Name" disabled class="width_350 input_new">
                               </div>
                            </div>
                         </div> -->
                      </div>
                   </div>
                   <div class="artboard6_sidebar side_bar_height upgrade-box">
                      <div class="Account">MLS Status</div>
                      <span *ngIf="agentMLSStatus == 'U'" class="agent-status">Unverified</span>
                      <span *ngIf="agentMLSStatus == 'V'" class="agent-status">Verified</span>
                      <div *ngIf="agentMLSStatus == 'U'" class="mls_detail account-text">
                        To gain access to your listings on MLS, make sure your email address matches the email on file with your MLS account.
                      </div>
                      <!-- <div>
                        <input *ngIf="agentMLSStatus == 'U'" type="button" class="submit_button with_bg mls_input" value="Verify Now" (click)="verifyAgent()">
                        <input *ngIf="agentMLSStatus == 'V'" type="button" class="submit_button with_bg mls_input" value="Verified">
                      </div> -->
                      <div class="border"></div>
                      <div *ngIf="accountStatus != false || permanentPremium != false">
                        <div class="Account account_mtp">Account Status</div>
                        <span class="agent-status">Premium</span>
                        <p class="Account_text">You have full access to all features.</p>
                        <div class="new_form_group" *ngIf="accountStatus == true || permanentPremium == true">
                          <input type="submit" class="Cancel-Subscription-btn" value="Cancel Subscription" data-toggle="modal" data-target="#cancelSubscription">
                        </div>
                      </div>

                      <div *ngIf="accountStatus == false">
                        <div class="Account">Account Status</div>
                        <span class="agent-status">Free</span>
                      <p class="Account_text">Upgrade your account today to take advantage of all the features available in Open Houses Direct.</p>
                      </div>
                      <input *ngIf="accountStatus == false && permanentPremium == false" type="button" class="submit_button button_with_bg upgrade_input" (click)="gotoUpgradeView()" value="Upgrade Now">
                      <!-- <div class="a_10">10 <span>seats available</span></div> -->
                      <!-- <div class="Manage">Manage Seats</div>
                      -->
                      <button *ngIf="accountStatus != false || permanentPremium != false" type="button" class="btn btn-wiseagent" data-toggle="modal" data-target="#connectWithWiseAgent">
                         <span>Connect with</span> <br/>
                         <img src="https://wiseagent.com/SECURE/assets/images/logos/WiseAgentLogo01.svg" alt="">
                      </button>
                      <!-- <a  href="https://wiseagent.com/secure/userlogin.asp" target="_blank" >
                        <button *ngIf="(accountStatus != false || permanentPremium != false) && userIsWiseAgentRegistered === true" type="button" class="btn btn-wiseagent btn-wiseagent-dashboard" >
                           <span>Open</span> <br/>
                           <img src="https://wiseagent.com/SECURE/assets/images/logos/WiseAgentLogo01.svg" alt=""> <br/>
                           <span>Dashboard</span>
                        </button>
                      </a> -->
                   </div>

                </div>
             </div>
</div>

<div id="connectWithWiseAgent" class="modal fade" role="dialog">
   <div class="modal-dialog" style="width: 420px;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Connect with Wise Agent Account</h5>
        </div>
        <div class="modal-body">
            <form class="wiseagent-form" [formGroup]="wiseAgentSignupForm">
               <div class="mb-3">
                  <p for="exampleFormControlInput0">Enter your Wise Agent API key</p>
                  <input type="text" class="form-control" for="exampleFormControlInput0" placeholder="Wise Agent API Key" formControlName="wiseAgentKey" />
               </div>
               <button type="buton" class="btn btn-new-wiseagent" [ngClass]="{'submit-disable':wiseAgentSignupForm.invalid}" [disabled]="wiseAgentSignupForm.invalid" (click)="createWiseAgentAccount(wiseAgentSignupForm)">Connect</button>
            </form>
            <div>
               <h6 class="wiseagent-title">How to find your personal Wise Agent API Key</h6>
               <p>To find the API key associated with your Wise Agent account, please follow the instructions below:</p>
               <ol class="pl-3">
                  <li>Login to your Wise Agent account</li>
                  <li>Click on the "Integration" drop-down on the top of your account</li>
                  <li>Select "Settings" from the drop-down</li>
                  <li>From the "Integrations" page click on the down arrow on the "Wise Agent Integrations" sub-heading</li>
                  <li>You will then need to copy the string of characters listed under "API Key"</li>
               </ol>
            </div>
            <hr>
            <div>
               <h6 class="wiseagent-auth-text">Don't have a Wise Agent account ?</h6>
               <a href="https://wiseagent.com/secure/registration.asp?adref=254&promocode=OHD" target="_blank">
               <button *ngIf="accountStatus != false || permanentPremium != false" type="button" class="btn btn-new-wiseagent" data-toggle="modal" data-target="#connectWithWiseAgent">
                  <span>Connect with</span> <br/>
                  <img class="wiseagent-img" src="https://wiseagent.com/SECURE/assets/images/logos/WiseAgentLogo01.svg" alt="">
               </button>
               </a>
               <h6 class="wiseagent-auth-text">Already have an account ? <span><a href="https://wiseagent.com/secure/userlogin.asp?adref=254&promocode=OHD" target="_blank" style="font-weight: 600;">Sign in here</a></span> </h6>
            </div>
            <!-- <div class="mb-3">
               <label for="exampleFormControlInput1" class="form-label">Full Name</label>
               <input type="text" class="form-control" id="exampleFormControlInput1" placeholder="John Doe" disabled formControlName="name">
            </div>
            <div class="mb-3">
               <label for="exampleFormControlInput2" class="form-label">Email address</label>
               <input type="email" class="form-control" id="exampleFormControlInput2" placeholder="<EMAIL>" disabled formControlName="email">
            </div>
            <div class="mb-3">
               <label for="exampleFormControlInput3" class="form-label">Brokerage ID</label>
               <input type="text" class="form-control" id="exampleFormControlInput3" placeholder="Broker123455DE" disabled formControlName="brokerage_id">
            </div>
            <div class="mb-3">
               <label for="exampleFormControlInput4" class="form-label">Phone</label>
               <input type="text" class="form-control" id="exampleFormControlInput4" placeholder="+01 2221567890" disabled formControlName="cell_phone">
            </div>
            <div class="mb-3" formGroupName="passwordForm">
               <label for="exampleFormControlInput5" class="form-label">Password</label>
               <input type="password" class="form-control" id="exampleFormControlInput5" placeholder="Enter Password" formControlName="password" required>
               <p class="form-validation" *ngIf="wiseAgentSignupForm['controls'].passwordForm['controls'].password.errors?.minlength">Password must be 5-15 characters</p>
               <p class="form-validation" *ngIf="wiseAgentSignupForm['controls'].passwordForm['controls'].password.errors?.maxlength">Password must be 5-15 characters</p>
            </div>
            <div class="mb-3" formGroupName="passwordForm">
               <label for="exampleFormControlInput6" class="form-label">Confirm Password</label>
               <input type="password" class="form-control" id="exampleFormControlInput6" placeholder="Confirm Password" formControlName="confirm_password" required>
               <p class="form-validation" *ngIf="wiseAgentSignupForm['controls'].passwordForm['controls'].confirm_password.errors?.minlength">Password must be 5-15 characters</p>
               <p class="form-validation" *ngIf="wiseAgentSignupForm['controls'].passwordForm['controls'].confirm_password.errors?.maxlength">Password must be 5-15 characters</p>
               <p class="form-validation" *ngIf="wiseAgentSignupForm['controls'].passwordForm.errors?.mismatch">Confirm password not match</p>
            </div>
          </form> -->
        </div>
        <div class="modal-footer text-center px-0 pb-0 pt-0">
          <!-- <button type="button" class="btn btn-agent" [ngClass]="{'submit-disable':wiseAgentSignupForm.invalid}" [disabled]="wiseAgentSignupForm.invalid" (click)="createWiseAgentAccount(wiseAgentSignupForm)" >Create Account</button> -->
          <div class="p-4 mt-4" style="background-color: #E0F2F1;">
            By creating an account you agree to the Open Houses Direct <span> <a href="#">Terms of Use</a> </span> and <span> <a href="#">Privacy Policy</a> </span>
          </div>
        </div>
      </div>
    </div>
</div>

 <div>
    <div id="cancelSubscription" class="modal fade" role="dialog">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
             <h4 class="modal-title">Cancel Subscription</h4>
            </div>
            <div class="modal-body">
              <p>Are you sure to cancel subscription?</p>
            </div>
            <div class="modal-footer">
              <input type="submit" [disabled]="disablecancelSubsBtn == true" value="Yes" class="submit_button with_bg" data-dismiss="modal" (click)="cancelSubscription()">
              <input type="submit" class="Cancel-Subscription-btn" data-dismiss="modal" value="No">
            </div>
          </div>
        </div>
      </div>
 </div>

 <div class="modal fade sign_modal" id="confirmEmailChangePopup" role="dialog">
  <div class="modal-dialog modal-md">
     <div class="modal-content">
        <div class="modal-body">
           <div class="modal_content">
              <div class=" new_account">
                 <div class="title email-confirm-email-title">Confirm email change</div>
                 <div class="email-confirm-border"></div>
                 <p class="email-confirm-p">You have chosen to change your email on your account. Please verify the email below is correct before proceeding:</p>
                 <p class="email-confirm-email">{{NewEmailAddress}}</p>
                 <p class="email-confirm-p">If you confirm, you will be logged out and a we will send you a confirmation email to verify your new email address.</p>
              </div>
              <div class="form_group cursor-pointer">
                  <input type="button" class="new_form" value="Confirm" (click)="onConfirmEmailClick()"/>
                  <span class="email-confirm-cancel" data-dismiss="modal">Cancel</span>
              </div>
           </div>
        </div>
     </div>
  </div>
</div>

<div class="footer">
    <footer></footer>
</div>


