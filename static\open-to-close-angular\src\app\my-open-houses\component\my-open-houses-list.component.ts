import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { MyOpenHousesComponent } from '@app/my-open-houses/component/my-open-houses.component';
import { BaseComponent } from '@app/base/components/base.component';
import { PropertyEvent } from '@app/property-detail/models/event.model';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ChatService } from '@app/messaging/service/chat-service';
import { AddEventComponent } from '@app/add-event/components/add-event.component';
import { Params } from '@angular/router/src/shared';
import * as moment from 'moment';

declare var google;
declare var $;

@Component({
  selector: 'my-open-houses-list',
  templateUrl: '../views/my-open-houses-list.component.html',
  styleUrls: ['../css/my-open-houses.component.css']
})
export class MyOpenHousesListComponent extends MyOpenHousesComponent implements OnInit {

  @ViewChild(EventModalComponent) eventModal: EventModalComponent;
  @ViewChild(AddEventComponent) addEventModal: AddEventComponent;

  public checkedInEventList:PropertyEvent[] = [];
  public upcomingEventList:PropertyEvent[] = [];

  public upcTotalCount: number = 0;
  public checkedInTotalCount: number = 0;
  public upIndex: number = 2;
  public upItemPerPage:any;
  public chIndex: number = 2;
  public chItemPerPage:any;
  public currentId: number;
  public currentIndex : number;
  public searchPageNo = 0;
  public searchListType = 3;

  meAgentType: boolean = false;
  brAgentType: boolean = false;
  saAgentType: boolean = false;
  closeBtn: boolean = false;
  disabledEvent: boolean = false;
  public currentUserId = '';
  houseTypeParams = new URLSearchParams();

  disableLoadMore : Boolean = false;
  public showCKLoader : Boolean = false;
  public showUPLoader : Boolean = false;

  public selectedTab = 'upcoming';

  chatService : ChatService;
  private rateCardSubscription;
  private isOpenRateCardSubscription : Boolean = false;

   //Upcoming Sorting
   public upSortObject : any = {};
   public upSortList :any[] = [];

   //Checking Sorting
   public chSortObject : any = {};
   public chSortList :any[] = [];

  public detailSubscription: any;

  constructor(zone: NgZone) {
    super(zone);
    this.chatService = ServiceLocator.injector.get(ChatService);
  }

  ngOnInit(){
    this.findLocation();
    this.searchBarComponent.listType = this.searchListType;
    this.selectedComponent = this.router.routerState.snapshot.url;

    if(this.selectedComponent.includes('my-open-houses')){
      BaseComponent.baseselectedHeader = 'myOpenHouse';
    }
    else if(this.selectedComponent.includes('my-list')){
      BaseComponent.baseselectedHeader = 'my-list';
    }

    if(BaseComponent.baseselectedHeader == 'my-list'){
      if(this.getPreviousScreen() != '/my-list/myList-open-house-list'){
        this.clearLocalStorageSearch();
      }
      this.setPreviousScreen('/my-list/myList-open-house-list');
    }
    if(BaseComponent.baseselectedHeader == 'myOpenHouse'){
      if(this.getPreviousScreen() != '/my-open-houses/my-open-houses-list'){
        this.clearLocalStorageSearch();
      }
      this.setPreviousScreen('/my-open-houses/my-open-houses-list');
    }

    if(localStorage.getItem('recentSearches') == null){
      this.getOpenHouseEventList("UP",1);
      this.getOpenHouseEventList("CH",1);
      this.showCKLoader = true;
      this.showUPLoader = true;
    }else{
      this.searchBarComponent.allowCallGetMapPolygons = false;
      this.searchBarComponent.allowLocalStorageSearch();
      this.showCKLoader = true;
      this.showUPLoader = true;
    }

    let self = this;
    $(document).ready(function()
      {
        $(document).mouseup(function(e)
        {
          if(self.currentId != undefined && self.currentIndex != undefined){
            $("#oh_"+self.currentIndex+"_"+self.currentId).hide();
            self.currentId = undefined;
            self.currentIndex = undefined;
          }
        });
      });
  }

  openMenu(index,id){
    if(BaseComponent.user != undefined){
      this.currentUserId = BaseComponent.user.id;
    }
    this.currentIndex = index;
    this.currentId = id;
    $("#oh_"+index+"_"+id).toggle();
  }

  loadMoreOpenHouseEventList(listType, index){
    var todayDate = moment().utc().format('YYYY-MM-DD');
    this.houseTypeParams.set('today_date',todayDate.toString());
    this.houseTypeParams.set("page_no", index);
    this.houseTypeParams.set("type",listType);
    this.houseTypeParams.set("is_map_list",'false');
    this.houseTypeParams.set("request_type",'WEB');
    this.houseTypeParams.set("geo_bounding_box",'{}');
    this.disableLoadMore = true;

    let sortList = [];
    this.houseTypeParams.delete("sort_list");

    if(listType == "UP"){
      if(this.upSortList.length !=0){
        sortList = this.upSortList;
        this.houseTypeParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(listType == "CH"){
      if(this.chSortList.length !=0){
        sortList = this.chSortList;
        this.houseTypeParams.set('sort_list', JSON.stringify(sortList));
      }
    }

    if(this.detailSubscription){
      this.detailSubscription.unsubscribe();
    }

    this.detailSubscription = this.openHouseService.getOpenHouseDetailView(this.houseTypeParams).subscribe(res =>{
      this.disableLoadMore = false;
      if(BaseComponent.user != undefined){
        this.currentUserId = BaseComponent.user.id;
      }
      if(listType == "UP"){
          res['result']['records'].forEach(record => {
          this.upcomingEventList.push(record);
        });
        this.upcTotalCount = res['result']['total_records_count'];
        this.upItemPerPage = res['result']['items_per_page'];
        this.upIndex++;
      }
      else if(listType == "CH"){
        res['result']['records'].forEach(record => {
          this.checkedInEventList.push(record);
        });
        this.chItemPerPage = res['result']['items_per_page'];
        this.checkedInTotalCount = res['result']['total_records_count'];
        this.chIndex++;
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  getOpenHouseEventList(listType, index){
    var todayDate = moment().utc().format('YYYY-MM-DD');
    this.houseTypeParams.set('today_date',todayDate.toString());
    this.houseTypeParams.set("page_no", index);
    this.houseTypeParams.set("type",listType);
    this.houseTypeParams.set("is_map_list",'false');
    this.houseTypeParams.set("request_type",'WEB');
    this.houseTypeParams.set("geo_bounding_box",'{}');

    this.openHouseService.getOpenHouseDetailView(this.houseTypeParams).subscribe(res => {
      if(BaseComponent.user != undefined){
        this.currentUserId = BaseComponent.user.id;
      }
      if(listType == "UP"){
        this.showUPLoader = false;
        this.upcomingEventList = res['result']['records'];
        this.upcTotalCount = res['result']['total_records_count'];
        this.upItemPerPage = res['result']['items_per_page'];
      }
      else if(listType == "CH"){
        this.showCKLoader = false;
        this.checkedInEventList = res['result']['records'];
        this.checkedInTotalCount = res['result']['total_records_count'];
        this.chItemPerPage = res['result']['items_per_page'];
        this.rateCardSubscription = this.route.queryParams.subscribe((params:Params)=>{
          this.filterEventCardId(params);
        });
        // reviewRatings
      }
    }, err => {
      console.log(err.json());
    });
  }

  eventDetailView(event,eventType: string = ""){
    this.addEventModal.manageEventDetailView(event,eventType);
  }

  reviewRatings(event){
    event.id = event.event_id;
    this.eventModal.openRatingModal(event,true);
  }

  openEventModel(myListevent){
    if(myListevent['event']['event_type'] == 'BO'){
      this.eventModal.openEventModal('brokerOpen',myListevent['event'],true);
    }else if(myListevent['event']['event_type'] == 'OH'){
      this.eventModal.openEventModal('openHouse',myListevent['event'],true);
    }else if(myListevent['event']['event_type'] == 'AO'){
      this.eventModal.openEventModal('appointmentOnly',myListevent['event'],true);
    }
  };

  contactOpenHouseAgent(selectedClient){
    var client = {};
    client['user_name'] = selectedClient['open_house_agent_name'];
    client['profile_image'] = selectedClient['open_house_agent_image'];
    client['chat_thread_id'] = selectedClient['open_house_agent_id'];
    client['receiver_id'] = selectedClient['open_house_agent_id'];
    client['last_message_time'] = "";
    client['last_message'] = ' ';
    this.chatService.setClientChatThread(client);
    this.router.navigate(['messaging']);
  }

  getSearchObj(openHouseParams){
    this.zone.run(
      () => {
        this.upIndex = 2;
        this.chIndex = 2;
        this.houseTypeParams = openHouseParams;
        this.houseTypeParams.set("list_type",'3');
        this.getOpenHouseEventList('UP',1);
        this.getOpenHouseEventList('CH',1);
        this.houseTypeParams.delete("sort_list");
        this.removeSorting();
    });
  }
  removeSorting(){
    var sort = 'UP';
    for(let i=0; i<2; i++){
      if(i == 0){
        sort = 'UP';
        this.upSortList = [];
        this.upSortObject = {};
      }
      else if(i == 1){
        sort = 'CH';
        this.chSortList = [];
        this.chSortObject = {};
      }
      $('#'+sort+'_PR').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_ET').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_DT').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_OA').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_OS').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_RA').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_LS').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
    }
  }

  mapView(){
    if(BaseComponent.baseselectedHeader == 'myOpenHouse'){
      this.router.navigate(['my-open-houses']);
    }
    if(BaseComponent.baseselectedHeader == 'my-list'){
      this.router.navigate(['my-list']);
    }
  }

  propertyDetail(id){
    if(BaseComponent.baseselectedHeader == 'myOpenHouse'){
      this.router.navigate(['my-open-houses/property-detail'],{ queryParams:{propertyId:id}});
    }
    if(BaseComponent.baseselectedHeader == 'my-list'){
      this.router.navigate(['my-list/property-detail'],{queryParams:{propertyId:id}});
    }
  }

  onTabChange(tab){
    this.selectedTab = tab;
  }

  UpdatePropertyInfo(propertyInfo){
    var property;
    var propertyIndex;
    if(this.selectedTab == 'checkIns'){
      property = this.checkedInEventList.filter(
        (listEvent) => listEvent.property_id == propertyInfo['property']
        && listEvent.event_id == propertyInfo['eventId']
      );
      propertyIndex = this.checkedInEventList.indexOf(property[0]);
    }
    else if(this.selectedTab == 'upcoming'){
      property = this.upcomingEventList.filter(
        (listEvent) => listEvent.property_id == propertyInfo['property']
        && listEvent.event_id == propertyInfo['eventId']
      );
      propertyIndex = this.upcomingEventList.indexOf(property[0]);
    }

    if(property.length !=0){
      var updatedRating = propertyInfo['rating'];
      if(this.selectedTab == 'checkIns'){
       this.checkedInEventList[propertyIndex]['property_rate'] = updatedRating;
      }
      else if(this.selectedTab == 'upcoming'){
        if(propertyInfo['deleteFromList'] == true){
          this.upcomingEventList.splice(propertyIndex,1);
        }
        else{
          this.upcomingEventList[propertyIndex]['event']['property_rate'] = updatedRating;
        }
      }
    }
    if(this.isOpenRateCardSubscription == true){
      this.rateCardSubscription.unsubscribe();
      this.isOpenRateCardSubscription = false;
      this.router.navigate(['/my-open-houses/my-open-houses-list']);
    }
  }

  filterEventCardId(params){
    if(params['event_id'] != undefined){
     let eventDetail = this.checkedInEventList.filter(
        (listEvent) => listEvent.event_id == params['event_id']
      );
      if(eventDetail.length != 0){
        this.isOpenRateCardSubscription = true;
        this.reviewRatings(eventDetail[0]);
        this.onTabChange('checkIns');
        $('#MyListTable .myclient_navbar li:eq(' + 1 + ')').tab('show');
      }
    }
  }

  eventSortting(listingType,filedName){
    let sortList = [];
    if(listingType == 'UP'){
      this.upIndex = 2;
      if(this.upSortObject[filedName] == undefined){
        this.upSortObject[filedName] = true;
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.upSortObject[filedName] = this.upSortObject[filedName] === true ? false : true;
       if(this.upSortObject[filedName]){
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
       }else{
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.upSortList[0] = this.upSortObject;
      sortList = this.upSortList;
    }

    if(listingType == 'CH'){
      this.chIndex = 2;
      if(this.chSortObject[filedName] == undefined){
        this.chSortObject[filedName] = true;
        $('#CH_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.chSortObject[filedName] = this.chSortObject[filedName] === true ? false : true;
       if(this.chSortObject[filedName]){
        $('#CH_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
       }else{
        $('#CH_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.chSortList[0] = this.chSortObject;
      sortList = this.chSortList;
    }

    this.houseTypeParams.set('sort_list', JSON.stringify(sortList));
    this.getOpenHouseEventList(listingType,1);
  }

  ngOnDestroy(): void {
    if(this.rateCardSubscription != undefined){
      this.rateCardSubscription.unsubscribe();
    }
  }
}
