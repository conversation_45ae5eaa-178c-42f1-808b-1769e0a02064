import { <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>, <PERSON>Zone,OnInit,EventEmitter,Input,Output, OnChanges, SimpleChanges } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { HeaderComponent } from '@app/root/components/header.component';
import { SearchService } from '@app/search/service/search.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { FormGroup, FormControl,Validators} from '@angular/forms';
import { every } from 'rxjs/operator/every';
import { EventManagerService } from '@app/event-manager/services/event-manager.service';
import * as moment from 'moment';
import { FilterModel } from '@app/searchBar/models/filter-model';
import { min } from 'moment';
declare var $;

@Component({
  selector: 'search-bar',
  templateUrl: '../views/search-bar.html',
  styleUrls: ['../css/search-bar.css']
})
export class SearchBarComponent extends BaseComponent implements OnInit, OnChanges, <PERSON><PERSON><PERSON>roy{

  @Input()
  currentPage:String="";
  @Input()
  pageNo:any = 0;
  @Input()
  listType:any = 0;
  @Input()
  searchFrom:String="";
  @Input()
  eventType:String="";
  @Input()
  showSaveSearchbtn : Boolean = false;
  @Input()
  isListViewScreen : Boolean = false;

  searchObj: any;
  searchSqFtObj:any;
  numberOfGoing :any;
  openSearch: boolean = false;
  isFavoriteSearch : boolean = false;
  public savedBath :any;
  public savedBeds :any;
  public searchLocation :any;
  public polygonList :any;

  @Output() searchObjEvent = new EventEmitter<any>();
  @Output() polygonListEvent = new EventEmitter<any>();
  @Output() googleMapPosition = new EventEmitter<any>();
  @Output() googleMapViewPort = new EventEmitter<any>();
  @Output() googleMapCustomBoundry = new EventEmitter<any>();
  @Output() polygonErrorHandle = new EventEmitter<any>();

  items = [];
  PriceListForUI = ['$0','$75,000','$100,000','$150,000','$200,000','$250,000','$300,000','$400,000','$500,000','$600,000','$700,000',
                  '$800,000','$900,000','$1,000,000','$1,500,000','$2,000,000'];
  BedsList = ['1+','2+','3+','4+','5+','6+','7+','8+','9+','10+','11+','12+','13+','14+','15+','16+','17+','18+','19+','20+'];
  BathList = ['1+','2+','3+','4+','5+','6+','7+','8+','9+','10+','11+','12+','13+','14+','15+','16+','17+','18+','19+','20+'];
  SqftList = ['option 1','option 1','option 1'];
  emailSettingList = ['Never','Weekly'];
  selectedBeds;
  selectedBaths;
  filterPriceList = [];
  filterSqFtList = [];
  filterGoingList = [];
  searchProperty: Object = {};

  previousSearch: any[] = [];
  localStorageSearch: any[] = [];
  public isDateSelected : Boolean = false;
  public allowDefaultDateRange : Boolean = false;
  public allowCallGetMapPolygons : Boolean = true;
  isLocalStorageSearch : Boolean = false;

  filterHomeType = [];
  anyStatus = [];
  openHouseAgentList = [];
  eventTypeList = [];
  openHouseTypeList = [];
  selectedOpenHouseAgent = '';
  selectedOHAId = '';

  searchService : SearchService;
  eventMangerService: EventManagerService;
  searchedValue: string = "";
  emailSettings: string = "";
  isEmail : Boolean = false;
  savedSearchList: any[] = [];
  saveSearchForm : FormGroup;

  public mapListView: Boolean = false;
  public allowMapIdle : boolean = true;
  public showTabletFilter : Boolean = false;
  public handalMaptechError : Boolean = true;
  public boundry = {};
  public ngModelfullPrice = "";
  public ngModelMinPrice = "";
  public ngModelMaxPrice = "";

  public ngModelfullSqft = "";
  public ngModelMinSqft = "";
  public ngModelMaxSqft = "";

  public lat;
  public lng;
  public zipCode;
  public city;

  public flag: boolean = true;
  public locationList:any[] = [];
  searchSuggestion: any;
  priceDict = {
                "0" : 0,
                "75,000" : 75000,
                "100,000" : 100000,
                "150,000" : 150000,
                "200,000" : 200000,
                "250,000" : 250000,
                "300,000" : 300000,
                "400,000" : 400000,
                "500,000" : 500000,
                "600,000" : 600000,
                "700,000" : 700000,
                "800,000" : 800000,
                "900,000" : 900000,
                "1,000,000" : 1000000,
                "1,500,000" : 1500000,
                "2,000,000" : 2000000
              }
  anyStatusDict = {
        "Active" : 'Active',
        "Pending" : 'Pending',
        "PRE-MLS/Coming Soon" : 'PRE-MLS/Coming Soon'
  }

  eventTypeDict = {
    "Appointment Only" : 'AO',
    "Open House" : 'OH',
    "Broker Open" : 'BO',
    "No Events" : 'NO'
  }

  openHouseEventTypeDict = {
      "SHOW ALL Open Houses" : '0',
      "SHOW ONLY PRE-MLS /Coming Soon" : '1',
      "Active Listings - No Open Houses" : '2'
    }

  eventTypeValueOfKey = {
    'AO'  : "Appointment Only",
    'OH' : "Open House",
    'BO' : "Broker Open",
    'NO' : "No Events"
  }

  openHouseEventTypeValueOfKey = {
    '0'  : "SHOW ALL Open Houses",
    '1' : "SHOW ONLY PRE-MLS /Coming Soon",
    '2' : "Active Listings - No Open Houses"
  }

  public searchSubscription: any;
  public searchMapSubscription: any;

  // Show/hide filter type:
  // POHP2-76
  public showStatusFilter : boolean = false;
  public showopenHouseFilter : boolean = false;

  //Show save/search after HB login/register
  //POHP2-83
  private showSaveSearchModel : boolean = false;

  constructor(public zone: NgZone) {
    super();
    this.searchService = ServiceLocator.injector.get(SearchService);
    this.eventMangerService =ServiceLocator.injector.get(EventManagerService);
  }

  ngOnInit() {
    // && $(window).width() < 1025
    if($(window).width() > 767 ){
      this.showTabletFilter = true;
    }

    window.onresize = (e) =>{
      if($(window).width() > 767){
        this.showTabletFilter = true;
      }else{
        this.showTabletFilter = false;
      }
    }

    this.saveSearchForm = new FormGroup({
      name : new FormControl('',Validators.required),
      email : new FormControl()
    });

    if(this.searchFrom == "eventManager"){
      this.searchService.getOpenHouseAgent().subscribe(res => {
        this.openHouseAgentList = res['result'];
      },err => console.log(err));
    }

    if(this.searchService.getSearchSuggestions() != undefined){
      var location = this.searchService.getSearchSuggestions();
      this.searchSuggestion = this.searchService.getSearchSuggestions();
      this.searchLocation = location['suggestion'];

      var search = {
        "suggestion" :location['suggestion']
      }
      this.getMapPolygons(search);
    }else if(this.searchService.getMyClientSearch() !=undefined){
      setTimeout(() => {
        this.showSavedSearch(this.searchService.getMyClientSearch());
      }, 150);
    }

    if(this.searchService.getFilterSearch() != undefined){
      var searchObjFilter = this.searchService.getFilterSearch();
      if(searchObjFilter['isSearched']){
        this.listType = searchObjFilter['listType'];
      }
    }

    var self = this;
    $(document).ready(function () {
      $("footer").click(function(){
        $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
      });

      $(".google_map").click(function(){
        $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
      });

      $("input.select_box_price").click(function(){
        $(".form_group.col-sm-2.price_select .square_footage").fadeIn(500);
        self.hideMoreFilter();
      });

      $("input.select_box_price2").click(function(){
        $(".form_group.col-sm-2.price_select .square_footage2").fadeIn(500);
      });

      $("input.number-of-going").click(function(){
        $(".no-of-going .square_footage2").fadeIn(500);
      });

      $(".google_map").click(function(){
        $(".form_group.col-sm-2.price_select .square_footage2").fadeOut(500);
        $(".no-of-going .square_footage2").fadeOut(500);
      });

      $(".google_map").click(function(){
        $(".search-result").fadeOut(500);
      });

      $(".google_map").click(function(){
        self.hideMoreFilter();
      });

      $("#tabSearchLocation").click(function(){
        self.hideMoreFilter();
      });

      $("#tabHomeType").click(function(){
        self.hideMoreFilter();
      });

      $("#event_tablet").click(function(){
        // self.hideMoreFilter();
      });

      $(document).mouseup(function(e)
        {
          if(e.target.id != "pricetxt" && e.target.parentNode.id != "targetId"
            && e.target.id != "min_price" && e.target.id != "max_price"
            && e.target.id !="sqfttxt" && e.target.parentNode.id !="sqftul"
            && e.target.id !="minSqft" && e.target.id !="maxSqft"){
            $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
            $(".form_group.col-sm-2.price_select .square_footage2").fadeOut(500);
          }

          var moreFilter = $(".show_more_search_filter");
          var moreFilterId = $("#moreFilter");
          if (!moreFilter.is(e.target) && moreFilter.has(e.target).length === 0 && !moreFilterId.is(e.target)){
            self.hideMoreFilter();
          }

          var searchSuggestion = $(".search-result");
          if (!searchSuggestion.is(e.target) && searchSuggestion.has(e.target).length === 0){
            $(".search-result").fadeOut(500);
          }
        })

      $("input.new_form").click(function(){
        $(".search-result").fadeIn(500);
      });

      var clicks = 0;
      var price = '';
      var ans
      var newPrice = ''
      $("#min_price").click(function(){
        clicks = 0;
      });

      $("#max_price").click(function(){
        clicks = 1;
      });

      $(".baths_group.price_ul li").click(function() {
        if(clicks == 0){
          ans = $(this).html();
          var minValue = ans.replace('$','');
          $("input.minimum").val(self.getPriceValue(minValue));
              ++clicks;

              if(minValue != '0'){
                var minPricesFormat = self.priceFormat(minValue.replace(new RegExp(',', 'g'),''))
                $("input.select_box_price").val('$'+minPricesFormat);
              }
              else{
                $("input.select_box_price").val(ans);
              }
              price = ans;

              var minPrice = $('#min_price').val();
              var maxPrice = $('#max_price').val();

              if(maxPrice != '' && maxPrice.trim().length !=0){
                 var minStr = minPrice.replace(new RegExp(',', 'g'),'');
                 var maxStr = maxPrice.replace(new RegExp(',', 'g'),'');
                  maxStr = maxPrice.replace('$','');
                  minStr = minPrice.replace('$','');

                if(parseInt(minStr) > parseInt(maxStr)){
                  $("input.minimum").val('');
                  $( "input.select_box_price" ).val('');
                  clicks = 0;
                  self.warningMsg('please select valid min and max price');
                }
                else
                {
                  self.searchObj =
                    {
                      "min_price" : minPrice,
                      "max_price" : maxPrice
                    }
                    self.setPageNumber(0);
                    self.filterPriceList = self.searchObj;
                    self.filterProperty();
                }
              }
            }
        else
        {
          newPrice = '';
          ans = $(this).html();
          var maxValue = ans.replace('$','');
          $("input.max").val(self.getPriceValue(maxValue));
          clicks = 0;
          newPrice = price +' to '+ ans;
          var minPriceFormat = price;
          var maxPriceFormat = ans;

          if(minPriceFormat != '0'){
            minPriceFormat = self.priceFormat(minPriceFormat.split('$')[1].replace(new RegExp(',', 'g'),''));
          }

          if(maxPriceFormat != '0'){
            maxPriceFormat = self.priceFormat(maxPriceFormat.split('$')[1].replace(new RegExp(',', 'g'),''));
          }

          var minMaxPrice = '$'+minPriceFormat +' to '+'$'+maxPriceFormat;

          $( "input.select_box_price" ).val(minMaxPrice);

          var minPrice = $('#min_price').val();
          var maxPrice = $('#max_price').val();
          if(minPrice !='' && minPrice.trim().length !=0){
            $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
          }
          self.searchObj =
            {
              "min_price" : minPrice,
              "max_price" : maxPrice
            }
          if(parseInt(self.searchObj['min_price']) >= parseInt(self.searchObj['max_price'])){
              $("input.max").val('');
              $( "input.select_box_price" ).val('');
              self.warningMsg('Please select valid min and max price');
          }
          else
          {
            if(self.searchObj['min_price'] !=undefined && self.searchObj['max_price'] !=undefined){
              self.setPageNumber(0);
              self.filterPriceList = self.searchObj;
              self.filterProperty();
            }
            else{
              self.warningMsg('Please select valid min and max price');
            }
          }
        }
      });

      var goingClick = 0;
      var going = '';
      var ans1
      var newGoing = ''
      $("#minNoOFGoing").click(function(){
        goingClick = 0;
      });

      $("#maxNoOFGoing").click(function(){
        goingClick = 1;
      });

      $(".goingGroup.price_ul li").click(function(){
        if(goingClick == 0){
          ans1 = $(this).html();
          $("input.mintext").val(ans1);
          ++goingClick;
          $("input.number-of-going").val(ans1);
          going = ans1;
        }
        else{
          newPrice = '';
          ans1 = $(this).html();
          $("input.maxtext").val(ans1);
          goingClick = 0;
          newGoing = going +' to '+ ans1;
          $( "input.number-of-going" ).val(newGoing);
          var minGoing = $('#minNoOFGoing').val();
          var maxGoing = $('#maxNoOFGoing').val();
          self.numberOfGoing =
            {
              "min_going" : minGoing,
              "max_going" : maxGoing
            }
          if(self.numberOfGoing['min_going'] >= self.numberOfGoing['max_going']){
            $("input.mintext").val('');
            $("input.maxtext").val('');
            $( "input.number-of-going" ).val('');
            self.warningMsg('Please select valid min and max');
          }
          else{
            if(self.numberOfGoing['min_going'] != undefined && self.numberOfGoing['max_going'] != undefined){
              $(".no-of-going .square_footage2").fadeOut(500);
              self.filterGoingList = self.numberOfGoing;
              self.filterProperty();
            }
            else{
              self.warningMsg('Please select valid min and max');
            }
          }
        }
      });

      window['testSelAll2'] = $('.testSelAll2').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});
      window['testSelAll3'] = $('.testSelAll3').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});
      // window['sumoEvent'] = $('.sumoEvent').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});
      window['sumoOpenHouseEvent'] = $('.sumoOpenHouseEvent').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});

      $('.homeType').click(function(){
        var obj = []
        $('.testSelAll2 option:selected').each(function(i) {
          obj.push($(this).val());
          });
          self.setPageNumber(0);
          self.filterHomeType = obj;
          self.searchProperty['property_type'] = [];
          self.filterProperty()
        });

        // $('.testSelAll3').on('sumo:opening', function(e) {
        //   $(this).closest('.SumoSelect').find('.optWrapper li').each(function(idx, ele) {
        //       if (idx==0) {
        //         $(this).prepend('<img class="property-status-dropdown-icon" src="'+self.imagePrefix+'active.png"/>');
        //         $(this).find('label').css('display', 'inline');
        //       }
        //       else if (idx==1) {
        //         $(this).prepend('<img class="property-status-dropdown-icon" src="'+self.imagePrefix+'pending.png"/>');
        //         $(this).find('label').css('display', 'inline');
        //       }
        //       else if (idx==2) {
        //         $(this).prepend('<img class="property-status-dropdown-icon" src="'+self.imagePrefix+'preMLS.png"/>');
        //         $(this).find('label').css('display', 'inline', 'margin', '13px');
        //       }
        //   });
        // });

      //   $('.testSelAll3').on('sumo:closing', function(e){
      //     $(this).closest('.SumoSelect').find('.optWrapper li').each(function(idx, ele) {
      //         if (idx==0) {
      //           $(this).find('>img').remove();
      //           $(this).find('label').css('display', 'inline');
      //         }
      //         else if (idx==1) {
      //           $(this).find('>img').remove();
      //           $(this).find('label').css('display', 'inline');
      //         }
      //         else if (idx==2) {
      //           $(this).find('>img').remove();
      //           $(this).find('label').css('display', 'inline');
      //         }
      //     });
      // });

      // $('.sumoEvent').on('sumo:opening', function(e) {
      //   $(this).closest('.SumoSelect').find('.optWrapper li').each(function(idx, ele) {
      //       if (idx==0) {
      //         $(this).prepend('<img class="marker-legend-dropdown-icons" src="'+self.imagePrefix+'OH.png"/>');
      //         $(this).find('label').css('display', 'inline');
      //       }
      //       else if (idx==1) {
      //         $(this).prepend('<img class="marker-legend-dropdown-icons" src="'+self.imagePrefix+'AO.png"/>');
      //         $(this).find('label').css('display', 'inline');
      //       }
      //       else if (idx==2) {
      //         $(this).prepend('<img class="marker-dropdown-bo-icon" src="'+self.imagePrefix+'BO.png"/>');
      //         $(this).find('label').css('display', 'inline');
      //       }
      //       else if (idx==3) {
      //         $(this).prepend('<img class="marker-dropdown-no-event-icon" src="'+self.imagePrefix+'no-event.png"/>');
      //         $(this).find('label').css('display', 'inline');
      //       }
      //   });
      // });

    //   $('.sumoEvent').on('sumo:closing', function(e){
    //     $(this).closest('.SumoSelect').find('.optWrapper li').each(function(idx, ele) {
    //         if (idx==0) {
    //           $(this).find('>img').remove();
    //           $(this).find('label').css('display', 'inline');
    //         }
    //         else if (idx==1) {
    //           $(this).find('>img').remove();
    //           $(this).find('label').css('display', 'inline');
    //         }
    //         else if (idx==2) {
    //           $(this).find('>img').remove();
    //           $(this).find('label').css('display', 'inline');
    //         }
    //         else if (idx==3) {
    //           $(this).find('>img').remove();
    //           $(this).find('label').css('display', 'inline');
    //         }
    //     });
    // });

      $('.anyStatus').click(function(){
        var obj = []
        $('.testSelAll3 option:selected').each(function(i) {
            obj.push(self.getStatusValue($(this).val()));
          });
          self.setPageNumber(0);
          self.anyStatus = obj;
          self.searchProperty['property_status'] = [];
          self.filterProperty()
        });

        // $('.eventType-drop-down').click(function(){
        //   var obj = []
        //   $('.sumoEvent option:selected').each(function(i) {
        //       obj.push(self.getEventTypeValue($(this).val()));
        //     });
        //     self.setPageNumber(0);
        //     self.eventTypeList = obj;
        //     self.searchProperty['event_type'] = [];
        //     self.filterProperty()
        // });

        setTimeout(() => {
          $('.openHouseEventTypeDropDown').click(function(){
            var obj = []
            $('.sumoOpenHouseEvent option:selected').each(function(i) {
                obj.push(self.getOpenHouseEventTypeValue($(this).val()));
              });
              self.setPageNumber(0);
              self.openHouseTypeList = obj;
              self.searchProperty['open_house_type'] = [];
              self.filterProperty()
          });
        }, 100);


      $('#datePicker').daterangepicker({
           "opens": "left",
           endDate: moment().add(30, 'day'),
            minDate: moment(),
          locale: {
              format: 'MM/DD/YYYY'
          },
      },function(start, end, label) {
        self.openSearch = true;
        self.searchProperty['filter_by_day'] = '4';
        self.setPageNumber(0);
        self.searchProperty['local_event_start_date'] = start.format('YYYY-MM-DD');
        self.searchProperty['local_event_end_date'] = end.format('YYYY-MM-DD');

        self.searchProperty['event_start_date'] = self.getLocalToUtcDate(start.format('YYYY-MM-DD'));
        self.searchProperty['event_end_date'] = self.getLocalToUtcDate(end.format('YYYY-MM-DD'));

        $('#datePicker').val(start.format('MM/DD/YYYY')+' - '+end.format('MM/DD/YYYY'));
        self.filterProperty();
        self.isDateSelected = true;
        self.mapListView = true;
      });

      if(self.listType == 0 && self.searchFrom == '' && self.isDateSelected == false && self.isLocalStorageSearch == false)
      {
          var defaultDate =$('#datePicker').val().split(' - ');
          let intStartDate = new Date(defaultDate[0]);
          let inteEndDate = new Date(defaultDate[1]);
          var startDate = moment(intStartDate).format('YYYY-MM-DD');
          var endDate = moment(inteEndDate).format('YYYY-MM-DD');
          self.openSearch = true;
          self.searchProperty['filter_by_day'] = '4';
          self.pageNo = 0;
          self.searchProperty['local_event_start_date'] = startDate;
          self.searchProperty['local_event_end_date'] = endDate;
          self.searchProperty['event_start_date'] = self.getLocalToUtcDate(startDate);
          self.searchProperty['event_end_date'] = self.getLocalToUtcDate(endDate);;

          if(self.allowDefaultDateRange == true){
            self.filterProperty();
        }
      }
    });

      $(".price_group select.new_form.drop_down_icon").click(function(){
        $(".square_footage.price_group_box").toggle();
      });

      $(".square_footage.price_group_box ul li").click(function(){
        $(".square_footage.price_group_box").css("display" , "none");
      });

      $(".show_map").click(function(){
        $(".display_none_map").addClass("show_map_mobile");
        $(".map_side_bar").addClass("hide_map_mobile");
      });

      $(".show_sidebar_m").click(function(){
        $(".map_side_bar").removeClass("hide_map_mobile");
          $(".display_none_map").removeClass("show_map_mobile");
      });

      $('#saveSearch').on('hidden.bs.modal', function () {
        $(this).find('form').trigger('reset');
      })

      $(window).bind("load", function() {
        $("ul.cont_select_int li").click(function() {
          var ans = $(this).attr("data-value");
          $('li[href="#' + ans + '"]').tab('show');
        });
    });
  }


  setDefaultOpenHouseFilter(){
    if(this.isLocalStorageSearch == false){
      this.openHouseTypeList = [];
      this.openHouseTypeList.push(this.getOpenHouseEventTypeValue('SHOW ALL Open Houses'));
    }
  }


  allowLocalStorageSearch(){
    if(localStorage.getItem('recentSearches')){
      if(localStorage.getItem('recentSearches') != null){
        this.localStorageSearch = JSON.parse(localStorage.getItem('recentSearches'));
        this.reSearchProperty(this.localStorageSearch[0]);
        this.handalMaptechError = false;
        this.isLocalStorageSearch = true;
      }
    }
  }

  warningMsg(msg){
    this.zone.run(
      () => {
        this.warningMessage(msg);
      }
    )
  }
  minPrice(min,max,makeApiCall){
    this.minMaxPrice(min,max,makeApiCall);
  }

  minMaxPrice(min,max,makeApiCall){
    min = min.replace(new RegExp(',', 'g'),'');
    max = max.replace(new RegExp(',', 'g'),'');
    var minPriceStr = min.replace('$','');
    var maxPriceStr = max.replace('$','');
    if(minPriceStr.trim().length !=0 && maxPriceStr.trim().length !=0){
      if(parseInt(minPriceStr) >= parseInt(maxPriceStr)){
        this.ngModelfullPrice = '';
        this.ngModelMinPrice = '';
        this.warningMessage('Please select valid min and max price');
      }
      else
      {
        var priceStr = '';
        var minPriceFormat = minPriceStr;
        var maxPriceFormat = maxPriceStr;

          if(minPriceFormat != '0'){
            minPriceFormat = this.priceFormat(minPriceFormat)
          }

          if(maxPriceFormat != '0'){
            maxPriceFormat = this.priceFormat(maxPriceFormat)
          }
          var minMaxPrice = '$'+minPriceFormat +' to '+'$'+maxPriceFormat;
          this.ngModelfullPrice = minMaxPrice;
          this.searchObj =
            {
              "min_price" : minPriceStr,
              "max_price" : maxPriceStr
            }
        this.pageNo =0;
        this.filterPriceList = this.searchObj;
        this.ngModelMinPrice = minPriceStr;
        this.ngModelMaxPrice = maxPriceStr;

        if(makeApiCall == true){
          this.filterProperty();
        }
        // $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
      }
    }
    else
    {
      this.ngModelfullPrice = '';
      this.warningMessage('Please select valid min and max price');
    }
  }

  minSqft(min,max,makeApiCall){
    this.minMaxSqft(min,max,makeApiCall);
  }
  minMaxSqft(min,max,makeApiCall){
    if(min.trim().length !=0 && max.trim().length !=0){
      if(parseInt(min) >= parseInt(max)){
        this.ngModelMaxSqft = '';
        this.ngModelfullSqft = '';
        this.warningMessage('Please select valid min and max sq ft');
      }
        else{
          this.searchSqFtObj =
            {
              "min_sqft" : min,
              "max_sqft" : max
            }

            this.ngModelfullSqft = min +' to '+ max;
            this.ngModelMinSqft = min;
            this.ngModelMaxSqft = max;

            this.filterSqFtList = this.searchSqFtObj;
            if(makeApiCall == true){
              this.filterProperty();
            }
            $(".form_group.col-sm-2.price_select .square_footage2").fadeOut(500);
        }
    }
    else{
      if(Object.keys(this.filterSqFtList).length != 0){
        this.ngModelfullSqft = '';
        delete this.searchProperty['min_sqft'];
        delete this.searchProperty['max_sqft'];
        this.filterSqFtList = [];
        if($('#sqfttxt').val() != ''){
          this.filterProperty();
        }
      }
      else{
        this.ngModelfullSqft = '';
        this.warningMessage('Please select valid min and max sq ft');
      }
      $(".form_group.col-sm-2.price_select .square_footage2").fadeOut(500);
    }
  }

  minGoing(min,max){
    this.minMaxGoing(min,max);
  }
  minMaxGoing(min,max){
    if(min.trim().length !=0 && max.trim().length !=0){
      if(parseInt(min) >= parseInt(max)){
        $("input.mintext").val('');
        $( "input.number-of-going" ).val('');
        this.warningMessage('Please select valid min and max');
      }
        else{
          this.numberOfGoing =
            {
              "min_going" : min,
              "max_going" : max
            }
            var going = min+' to '+max;
            $("input.number-of-going").val(going);
            this.filterGoingList = this.numberOfGoing;
            this.filterProperty();
            $(".no-of-going .square_footage2").fadeOut(500);
        }
    }
    else{
      this.warningMessage('Please select valid min and max');
    }
  }

  onPriceChanged(minPrice,maxPrice){
    if(minPrice == '' && maxPrice == ''){
      delete this.searchProperty['min_price'];
      delete this.searchProperty['max_price'];
      this.filterPriceList = [];
      if($('#pricetxt').val() != ''){
        this.ngModelfullPrice = undefined;
        this.filterProperty();
      }
    }
    else if(minPrice != '' && maxPrice != ''){
      if(this.filterPriceList.length != 0){
        if(this.filterPriceList['min_price'] != undefined && this.filterPriceList['max_price'] != undefined){
          if(this.filterPriceList['min_price'] != minPrice || this.filterPriceList['max_price'] != maxPrice){
            this.minMaxPrice(minPrice,maxPrice,true);
          }
        }
      }
      if(this.filterPriceList.length == 0){
        this.minMaxPrice(minPrice,maxPrice,true);
      }
    }
  }

  closeMinMaxPrice(){
    $(".form_group.col-sm-2.price_select .square_footage").fadeOut(500);
  }

  reSearchProperty(search : FilterModel = new FilterModel()){
    //Unselect default first options.
    setTimeout(() => {
      if(this.showopenHouseFilter){
        window['sumoOpenHouseEvent'] = $('.sumoOpenHouseEvent').SumoSelect({selectAll:false});
        $('.sumoOpenHouseEvent')[0].sumo.unSelectItem(0);
      }
    }, 60);


    this.searchProperty = {};
    this.openSearch = true;
    this.openHouseTypeList = [];
    setTimeout(() => {
      if(this.searchFrom != 'eventManager'){
        window['testSelAll2'] = $('.testSelAll2').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});

        $('.testSelAll2')[0].sumo.unSelectAll();

        if(search.property_type != undefined && search.property_type.length !=0){
          for(let i=0;i<search.property_type.length;i++){
            $('.testSelAll2')[0].sumo.selectItem(search.property_type[i]);
          }
        }
      }

      // if(this.isFavoriteSearch != true){
      if(this.showStatusFilter){
        window['testSelAll3'] = $('.testSelAll3').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});
        $('.testSelAll3')[0].sumo.unSelectAll();

        if(search.property_status !=undefined && search.property_status.length !=0){
          for(let i=0;i<search.property_status.length;i++){
            $('.testSelAll3')[0].sumo.selectItem(search.property_status[i]);
          }
        }
      }

      // if(this.searchFrom == 'eventManager' || this.searchFrom == ''){
      //   window['sumoEvent'] = $('.sumoEvent').SumoSelect({selectAll:false});
      //   $('.sumoEvent')[0].sumo.unSelectAll();

      //   if(search.event_type !=undefined && search.event_type.length !=0){

      //     for(let i=0;i<search.event_type.length;i++){
      //       $('.sumoEvent')[0].sumo.selectItem(this.getEventTypeKeyValue(search.event_type[i]));
      //     }
      //   }
      // }

      if(this.showopenHouseFilter){
        window['sumoOpenHouseEvent'] = $('.sumoOpenHouseEvent').SumoSelect({selectAll:false});
        $('.sumoOpenHouseEvent')[0].sumo.unSelectAll();
        if(search.open_house_type !=undefined && search.open_house_type.length !=0){
          for(let i=0;i<search.open_house_type.length;i++){
            $('.sumoOpenHouseEvent')[0].sumo.selectItem(this.getOpenHouseEventTypeKeyValue(search.open_house_type[i]));
          }
        }
      }

    },100);

    if(search['polygon'] != undefined){
      this.searchProperty['polygon'] = search['polygon'];
    }

    if(search['search'] != undefined){
      this.searchSuggestion = search['search'];
      if(search['search']['suggestion'] != undefined){
        if(this.allowCallGetMapPolygons == true){
          this.getMapPolygons(search['search']);
          localStorage.setItem('boundryZoom',"false");
        }
      }
      this.searchLocation =  search['search']['suggestion'];
    }

    if(search.max_price !=0 && search.max_price !=undefined && search.min_price != undefined){
      this.minMaxPrice(search.min_price.toString(),search.max_price.toString(),false);
    }

    if(search.property_type != undefined && search.property_type.length !=0){
      this.filterHomeType = search.property_type;
    }

    // if(this.isFavoriteSearch != true){
    if(this.showStatusFilter != true){
      if(search.property_status !=undefined && search.property_status.length !=0){
        this.anyStatus = search.property_status;
      }
    }

    if(this.searchFrom == 'eventManager' || this.searchFrom == ''){
      if(search.event_type !=undefined && search.event_type.length !=0){
        this.eventTypeList = search.event_type;
      }

      if(search.open_house_type !=undefined && search.open_house_type.length !=0){
        this.openHouseTypeList = search.open_house_type;
      }

      if(search['open_house_agent'] !=undefined &&  search['open_house_agent'] !=null){
        this.selectedOpenHouseAgent = search['open_house_agent'];
        this.selectedOHAId = search['open_house_agent'];
      }
    }

    if(search.bedroom !=0 && search.bedroom !=undefined){
      this.displayBeds(search.bedroom);
    }
    if(search.bathroom !=0 && search.bathroom != undefined){
      this.displayBaths(search.bathroom);
    }

    if(search.min_sqft != undefined && search.max_sqft !=undefined){
      this.minMaxSqft(search.min_sqft.toString(),search.max_sqft.toString(),false);
    }

    if(localStorage.getItem('bts')){
      if(localStorage.getItem('bts') == '/my-list' || localStorage.getItem('bts') == '/my-open-houses'){
        if(search.filter_by_day != undefined){
          this.searchProperty['filter_by_day'] = search.filter_by_day;
          if(search.filter_by_day != 5){
            this.searchProperty['event_start_date'] = search.event_start_date;
          }
        }
      }

      if(localStorage.getItem('bts')){
        if(localStorage.getItem('bts') == '/favorites'){
          if(search.property_status != undefined){
            this.anyStatus = search.property_status
          }
        }
      }

      if(localStorage.getItem('bts')){
        if(localStorage.getItem('bts') == '/event-manager'){
          if(search['type'] != undefined && search['type'] == 'CU'){
            this.searchProperty['event_start_date'] = search.event_start_date;
            this.searchProperty['event_end_date'] = search.event_end_date;
          }
        }
      }
    }


    if(search.filter_by_day == 4){
      this.searchProperty['filter_by_day'] = '4';
      var cachingDate = new Date(search.event_start_date);
      var todayDate = new Date(moment().utc().format('YYYY-MM-DD'));

      this.searchProperty['event_start_date'] = search.event_start_date;
      if (todayDate.getTime() > cachingDate.getTime()){
        if(this.listType == 0){
          this.searchProperty['event_start_date'] = moment(todayDate).format('YYYY-MM-DD');
        }
      }
      this.searchProperty['event_end_date'] = search.event_end_date;

      let intStartDate = new Date(search['local_event_start_date']);
      let inteEndDate = new Date(search['local_event_end_date']);

      this.searchProperty['local_event_start_date'] = search['local_event_start_date'];
      this.searchProperty['local_event_end_date'] = search['local_event_end_date'];

      var startDate = moment(intStartDate).format('MM/DD/YYYY');
      var endDate = moment(inteEndDate).format('MM/DD/YYYY');

      setTimeout(() => {
      this.zone.run(()=>{
        if($("#datePicker").data('daterangepicker') != undefined){
          $("#datePicker").data('daterangepicker').setStartDate(startDate);
          $("#datePicker").data('daterangepicker').setEndDate(endDate);
        }
        });
      },200);
    }
    this.filterProperty();
  }

  openSaveSearchCard(){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      $("#saveSearch").modal("show");
    }else{
      this.showSaveSearchModel = true;
      $("#authModal").modal("show");
      $('#authModal .modal-body .nav-pills a:eq(' + 1 + ')').tab('show');
    }
  }

  saveSearch(title){
    if(Object.keys(this.searchProperty).length !=0){
      let saveSearchProperty = new URLSearchParams();
      if(localStorage.getItem('zoomLevel')){
        saveSearchProperty.set('map_zoom_level',localStorage.getItem('zoomLevel'));
      }
      for(let key of Object.keys(this.searchProperty)){
        if(key != "property_type" && key != "open_house_type" && key != "property_status" && key != "idleLatLng"){
          saveSearchProperty.set(key, this.searchProperty[key]);
        }
        else{
          if(key == "property_type"){
            for(let i=0;i<this.filterHomeType.length;i++){
              saveSearchProperty.set('property_type['+[i]+']',this.filterHomeType[i]);
            }
          }
          else if(key == "property_status"){
            for(let i=0;i<this.anyStatus.length;i++){
              saveSearchProperty.set('property_status['+[i]+']',this.anyStatus[i]);
            }
          }
          else if(key == "idleLatLng"){
            saveSearchProperty.set('idleLatLng',JSON.stringify(this.searchProperty['idleLatLng']));
          }
          else if(key == "open_house_type"){
            for(let i=0;i<this.openHouseTypeList.length;i++){
              saveSearchProperty.set('open_house_type['+[i]+']',this.openHouseTypeList[i]);
            }
          }
        }
        if(key == "zipcode"){
          saveSearchProperty.set("zipCode", this.searchProperty[key]);
        }
      }
      saveSearchProperty.set('title', title);
      saveSearchProperty.set('email_settings', this.emailSettings);
      this.searchService.saveSearch(saveSearchProperty).subscribe(res => {
        $("#saveSearch").modal("hide");
        this.getSavedSearches();
        this.successResponse(res);
      },err =>{
        this.errorResponse(err.json());
      });
    }else{
      $("#saveSearch").modal("hide");
      this.isEmail = false;
      this.saveSearchForm.controls.email.setValue('');
      this.saveSearchForm.controls.name.setValue('');
      this.warningMessage('Please add location to saved search');
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if(this.savedSearchList.length == 0){
      if(this.listType == 0 || this.listType == 3){
        this.getSavedSearches();
      }
    }
  }

  getSavedSearches(){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      let params = new URLSearchParams();
      params.set('list_type', this.listType.toString());
      this.searchService.getSavedSearch(params).subscribe(res => {
        this.savedSearchList = res['result']['search_list'];
      },err =>{
        this.errorResponse(err.json());
      });

      if(BaseComponent.user.user_type == 'HB' && this.showSaveSearchModel == true){
        $("#saveSearch").modal("show");
        this.showSaveSearchModel = false;
      }else{
        this.showSaveSearchModel = false;
      }
    }
  }

  suggestions(keyword){
    if(keyword.trim().length >2){
      if(this.searchSubscription){
        this.searchSubscription.unsubscribe();
      }
      $(".search-result").fadeIn(100);
      this.searchSubscription = this.searchService.searchLocation(keyword).subscribe(res =>{
        this.locationList = res['result'];
      });
    }
    else if(keyword.trim().length == 0){
      if(this.locationList.length != 0){
        this.zone.run(() => {
          this.locationList = [];
          this.locationList.length = 0;
        })
      }
    }
  }

  getLocation(){
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        if (position) {
          console.log("Latitude: " + position.coords.latitude +
            "Longitude: " + position.coords.longitude);
          this.lat = position.coords.latitude;
          this.lng = position.coords.longitude;
          this.fetchLocation(this.lat, this.lng);
        }
      },
        (error) => console.log(error));
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  }

  fetchLocation(lat, lng){
    fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`)
    .then(res => res.json())
    .then(resJson => {
      const { postcode, locality } = resJson;
      this.zipCode = postcode;
      this.city = locality;
      postcode !== "" ? this.suggestions(postcode) : this.suggestions(locality);
      this.searchedValue = locality;
    })
  }

  onselectSuggestion(search){
    this.allowMapIdle = false;
    this.handalMaptechError = true;
    $(".search-result").fadeOut(500);
    this.searchSuggestion = undefined;
    if(search != undefined){
      this.searchedValue = search['suggestion'];
      this.setPageNumber(1)
    }
    this.searchSuggestion = search;
    localStorage.setItem('boundryZoom',"true");

    if(this.isListViewScreen == false){
      this.getMapPolygons(search);
    }

    if(this.isListViewScreen == true){
      this.filterProperty();
    }
  }

  removeLocationValue(){
    this.searchedValue = "";
    this.searchLocation = "";
    this.searchSuggestion = undefined;

    if(this.searchProperty['search'] != undefined){
      delete this.searchProperty['search'];
      delete this.searchProperty['zipcode_code'];
      delete this.searchProperty['abrrevation'];
      delete this.searchProperty['geo_id'];
      delete this.searchProperty['suggestion'];
      delete this.searchProperty['zipcode'];
      delete this.searchProperty['city'];
      delete this.searchProperty['state'];
    }
    if(localStorage.getItem('recentSearches') != null){
      let recentSearches = JSON.parse(localStorage.getItem('recentSearches'));
      delete recentSearches[0]['search'];
      delete recentSearches[0]['suggestion'];
      delete recentSearches[0]['state'];
      delete recentSearches[0]['zipcode'];
      delete recentSearches[0]['zipcode_code'];
      delete recentSearches[0]['city'];
      delete recentSearches[0]['abrrevation'];
      delete recentSearches[0]['geo_id'];

      localStorage.removeItem('recentSearches');
      localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
    }
  }

  getMapPolygons(search){
    // For maptechnica.com

    // if(this.searchSuggestion['zipcode_code'] != 0){
    //   this.searchService.getMapBoundryUsingZip(this.searchSuggestion['zipcode_code']).subscribe(res=>{
    //     if(res['error'] != undefined){
    //       if(res['error'] == true){
    //         this.polygonErrorHandle.emit(true);
    //       }
    //     }
    //     else{
    //     this.polygonListEvent.emit(res);
    //     }
    //   },err => {
    //     this.polygonErrorHandle.emit(true);
    //   });
    // }
    // else if(this.searchSuggestion['city'] != 0 && this.searchSuggestion['geo_id'] != 0){
    //   this.searchService.getMapBoundryUsingGoId(this.searchSuggestion['geo_id']).subscribe(res=>{
    //     this.polygonListEvent.emit(res);
    //   },err => {
    //     this.polygonErrorHandle.emit(true);
    //   });
    // }
    // else if(this.searchSuggestion['state'] != 0 && this.searchSuggestion['abrrevation'] != ''){
    //   this.searchService.getMapBoundryUsingState(this.searchSuggestion['abrrevation']).subscribe(res=>{
    //     this.polygonListEvent.emit(res);
    //   },err => {
    //     this.polygonErrorHandle.emit(true);
    //   });
    // }

    // var placeName = search['suggestion'].split(',');
    // this.searchService.getMapLatLng(placeName[0],placeName[1]).subscribe(result=>{
    //   var res={
    //     'lat' :result['results'][0]['geometry']['location']['lat'],
    //     'lng' :result['results'][0]['geometry']['location']['lng']
    //   }
    //   this.googleMapPosition.emit(res);
    // },err => console.log(err));

    let urlParams = new URLSearchParams();
    this.getGoogleMapBoundary(search,false);

    if(this.searchSuggestion['zipcode'] != 0){
      urlParams.set('zip_id',this.searchSuggestion['zipcode'])
      this.searchService.getMapBoundryUsingZip(urlParams).subscribe(res=>{
        if(Object.keys(res['result']['map_boundary']).length == 0){
          this.polygonErrorHandle.emit(true);
          this.getGoogleMapBoundary(search,true);
        }else{
          this.polygonListEvent.emit(res['result']['map_boundary']);
        }
      },err => {
        this.polygonErrorHandle.emit(true);
        this.getGoogleMapBoundary(search,true);
      });
    }
    else if(this.searchSuggestion['city'] != 0 && this.searchSuggestion['geo_id'] != 0){
      urlParams.set('geo_id',this.searchSuggestion['geo_id'])
      this.searchService.getMapBoundryUsingGoId(urlParams).subscribe(res=>{
        if(Object.keys(res['result']['map_boundary']).length == 0){
          this.polygonErrorHandle.emit(true);
          this.getGoogleMapBoundary(search,true);
        }else{
          this.polygonListEvent.emit(res['result']['map_boundary']);
        }
      },err => {
        this.polygonErrorHandle.emit(true);
        this.getGoogleMapBoundary(search,true);
      });
    }
    else if(this.searchSuggestion['state'] != 0 && this.searchSuggestion['abrrevation'] != ''){
      urlParams.set('abrrevation',this.searchSuggestion['abrrevation'])
      this.searchService.getMapBoundryUsingState(urlParams).subscribe(res=>{
        if(Object.keys(res['result']['map_boundary']).length == 0){
          this.polygonErrorHandle.emit(true);
          this.getGoogleMapBoundary(search,true);
        }else{
          this.polygonListEvent.emit(res['result']['map_boundary']);
        }
      },err => {
        this.polygonErrorHandle.emit(true);
        this.getGoogleMapBoundary(search,true);
      });
    }
  }

  getGoogleMapBoundary(search,setMapPosition){
    var placeName = search['suggestion'].split(',');
    this.searchService.getMapLatLng(placeName[0],placeName[1]).subscribe(result=>{
      var res={
        'lat' :result['results'][0]['geometry']['location']['lat'],
        'lng' :result['results'][0]['geometry']['location']['lng'],
        'setAutoPosition' : setMapPosition
      }
      this.googleMapPosition.emit(res);
    },err => {
      console.log(err);
    });
  }

  getPriceValue(key){
   let price = Object.keys(this.priceDict)
    for(let i=0; i<price.length;i++){
      if(price[i] == key){
        return this.priceDict[key];
      }
    }
  }

   getStatusValue(key){
    let status = Object.keys(this.anyStatusDict)
     for(let i=0; i<status.length;i++){
       if(status[i] == key){
        return this.anyStatusDict[key];
       }
     }
   }

   getEventTypeValue(key){
    let event = Object.keys(this.eventTypeDict)
     for(let i=0; i<event.length;i++){

       if(event[i] == key){
        return this.eventTypeDict[key];
       }
     }
   }

   getOpenHouseEventTypeValue(key){
    let event = Object.keys(this.openHouseEventTypeDict)
     for(let i=0; i<event.length;i++){

       if(event[i] == key){
        return this.openHouseEventTypeDict[key];
       }
     }
   }

   getEventTypeKeyValue(key){
    let event = Object.keys(this.eventTypeValueOfKey)
     for(let i=0; i<event.length;i++){
       if(event[i] == key){
        return this.eventTypeValueOfKey[key];
       }
     }
   }

   getOpenHouseEventTypeKeyValue(key){
    let event = Object.keys(this.openHouseEventTypeValueOfKey)
     for(let i=0; i<event.length;i++){
       if(event[i] == key){
        return this.openHouseEventTypeValueOfKey[key];
       }
     }
   }

  setAllBeds(value){
    this.setPageNumber(0)
    this.selectedBeds = value
    this.filterProperty();
  }

  setAllBaths(value){
    this.setPageNumber(0)
    this.selectedBaths = value;
    this.filterProperty();
  }

  displayBaths(value){
    this.selectedBaths = value +'+';
    this.savedBath = value +'+';
  }

  displayBeds(value){
    this.selectedBeds = value +'+';
    this.savedBeds = value +'+';
  }

  setEmailSettings(emailValue){
    this.emailSettings = emailValue;
    this.isEmail = true;
  }

  onOpneHouseSelect(event){
    this.setPageNumber(0)
    this.selectedOpenHouseAgent =event['id'];
    this.filterProperty();
  }

  setPageNumber(pageNumber){
    this.pageNo = pageNumber;
    this.mapListView = true;
  }

  showSavedSearch(event){
    this.searchProperty = {};

    window['testSelAll2'] = $('.testSelAll2').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});
    window['testSelAll3'] = $('.testSelAll3').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});
    // window['sumoEvent'] = $('.sumoEvent').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});
    window['sumoOpenHouseEvent'] = $('.sumoOpenHouseEvent').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});

    this.setPageNumber(0);
    var city =0;
    var state=0;
    var zipcode=0;
    var savedMinPrice;
    var savedMaxPrice;
    var savedSqftMin;
    var savedSqftMax;
    this.savedBeds = undefined;
    this.savedBath = undefined;
    this.selectedBeds = 0;
    this.selectedBaths = 0;
    this.filterPriceList = [];
    this.filterSqFtList = [];
    this.anyStatus = [];
    this.filterHomeType = [];
    this.eventTypeList = [];
    this.searchedValue = '';
    this.openHouseTypeList = []

    $( "input.select_box_price" ).val('');
    $( "input.select_box_price2" ).val('');
    $('.testSelAll2')[0].sumo.unSelectAll();

    if(this.showopenHouseFilter){
      $('.sumoOpenHouseEvent')[0].sumo.unSelectAll();
      $('.sumoOpenHouseEvent')[0].sumo.unSelectItem(0);
      $('.sumoOpenHouseEvent')[0].sumo.unSelectItem(1);
      $('.sumoOpenHouseEvent')[0].sumo.unSelectItem(2);
    }
    // $('.sumoEvent')[0].sumo.unSelectAll();

    this.listType = event.list_type;


    if(event.max_price !=0 && event.max_price !=undefined && event.min_price != undefined && event.max_price != -1){
      this.minMaxPrice(event.min_price.toString(),event.max_price.toString(),false);
    }

    if(event.bedroom !=0 && event.bedroom != -1){
      this.displayBeds(event.bedroom)
    }
    if(event.bathroom !=0 && event.bathroom != -1){
      this.displayBaths(event.bathroom)
    }
    if(event.min_sqft != 0 && event.min_sqft != -1 && event.max_sqft !=0 && event.max_sqft != -1 && event.max_sqft !=undefined && event.min_sqft !=undefined){
      this.minMaxSqft(event.min_sqft.toString(),event.max_sqft.toString(),false);
    }

    if(event.property_type.length !=0){
      this.filterHomeType = event.property_type;
      for(let i=0;i<event.property_type.length;i++){
        $('.testSelAll2')[0].sumo.selectItem(event.property_type[i]);
      }
    }

    if(this.showStatusFilter){
      $('.testSelAll3')[0].sumo.unSelectAll();
      if(event.property_status.length !=0){
        this.anyStatus = event.property_status;
        for(let i=0;i<event.property_status.length;i++){
          $('.testSelAll3')[0].sumo.selectItem(event.property_status[i]);
        }
      }
    }

    if(this.showopenHouseFilter){
      $('.sumoOpenHouseEvent')[0].sumo.unSelectAll();
      if(event.open_house_type != undefined && event.open_house_type.length !=0){
        this.openHouseTypeList = event.open_house_type;
        for(let i=0;i<event.open_house_type.length;i++){
          $('.sumoOpenHouseEvent')[0].sumo.selectItem(this.getOpenHouseEventTypeKeyValue(event.open_house_type[i]));
        }
      }
    }

    if(event.event_type.length !=0){
      let event_type = event.event_type[0].trim().split(",")
      // this.eventTypeList = event_type;
      // for(let i=0;i<event_type.length;i++){
      //   $('.sumoEvent')[0].sumo.selectItem(this.getEventTypeKeyValue(event_type[i]));
      // }
    }

    if(event.geo_bounding_box != undefined && event.geo_bounding_box != ''){
      this.searchProperty['geo_bounding_box'] = event.geo_bounding_box;

      if(event.polygon != undefined && event.polygon != ''){
        var latLng = {
          "boundry" : event.polygon
        }
        this.searchProperty['polygon'] = event.polygon;
        this.googleMapCustomBoundry.emit(latLng);
        this.allowCallGetMapPolygons = false;
      }

      if(event.idleLatLng != undefined && event.idleLatLng != ''){

        var viewPortPostion = {
          'viewPort' : event.idleLatLng,
          'zoomLevel' : event.map_zoom_level
        }
        this.googleMapViewPort.emit(viewPortPostion);
      }
      if(event.map_zoom_level != undefined && event.map_zoom_level !=0){
        localStorage.setItem('zoomLevel',event.map_zoom_level);
      }
    }

    if(event.state !=undefined){
      if(event.state.id !=undefined)
      {
        state = event.state.id;
      }
    }
    if(event.city !=undefined){
      if(event.city.id !=undefined)
      {
        city = event.city.id;
      }
    }
    if(event.zipCode !=undefined){
      if(event.zipCode.id !=undefined)
      {
        zipcode = event.zipCode.id;
      }
    }

    if(event.event_start_date != undefined && event.event_end_date != undefined){
      if(event.event_start_date != '' && event.event_end_date != ''){
        this.searchProperty['filter_by_day'] = '4';
        this.searchProperty['event_start_date'] = event.event_start_date;
        this.searchProperty['event_end_date'] = event.event_end_date;

        var currentTime = moment().format('HH:mm:ss');
        var startDate = moment.utc(event.event_start_date +' '+currentTime).local().format('MM/DD/YYYY');
        var endDate = moment.utc(event.event_end_date +' '+currentTime).local().format('MM/DD/YYYY');

        this.searchProperty['local_event_start_date'] = moment.utc(event.event_start_date +' '+currentTime).local().format('YYYY-MM-DD');
        this.searchProperty['local_event_end_date'] = moment.utc(event.event_end_date +' '+currentTime).local().format('YYYY-MM-DD');

        this.zone.run(()=>{
          if($("#datePicker").data('daterangepicker') != undefined){
            $("#datePicker").data('daterangepicker').setStartDate(startDate);
            $("#datePicker").data('daterangepicker').setEndDate(endDate);
          }
        });
      }
    }

    if(event.location != ''){
      var city = 0;
      var zipcode_code = 0;
      var zipcode = 0;
      var state = 0;
      var geo_id = 0;
      var abrrevation = '';

      if(event.city['id'] != undefined){
        city = event.city['id'];
        geo_id =  event.city['geo_id'];
      }

      if(event.zipCode != undefined){
        if(event.zipCode['id'] != undefined){
          city = event.city['id'];
          zipcode = event.zipCode['id'];
          zipcode_code = event.zipCode['code'];
          geo_id =  event.city['geo_id'];
        }
      }

      if(event.state['id'] != undefined){
        state = event.state['id'];
        abrrevation =  event.state['abrrevation'];
      }

      this.searchSuggestion = {'city':city,'geo_id':geo_id,'zipcode':zipcode,'zipcode_code':zipcode_code,'state':state,'abrrevation':abrrevation,'suggestion':event.location}
      this.searchLocation =  event.location;
      if(this.allowCallGetMapPolygons == true){
        this.getMapPolygons(this.searchSuggestion);
        localStorage.setItem('boundryZoom','false');
      }
    }
    else{
      this.searchSuggestion = undefined;
      this.searchLocation = '';
    }
    this.filterProperty();
  }

  filterProperty(){
    if(!this.openSearch){
      this.searchProperty = {};
    }
    this.searchProperty['page_no'] = this.pageNo;

    if(this.mapListView == false){
      this.searchProperty['is_map_list'] = false;
    }
    else{
      this.searchProperty['is_map_list'] = true;
    }

    if(this.pageNo == 0 || this.pageNo == 1){
      this.searchProperty['is_map_list'] = true;
    }

    if((this.pageNo == 0 || this.pageNo == 1) && this.mapListView == false){
      this.searchProperty['is_map_list'] = false;
    }

    if(this.searchFrom === ''){
      this.searchProperty['list_type'] = this.listType;
    }
    if(this.searchFrom == 'eventManager'){
      this.searchProperty['type'] = this.eventType;
      if(this.selectedOpenHouseAgent != null && this.selectedOpenHouseAgent != ''){
        this.searchProperty['open_house_agent'] = this.selectedOpenHouseAgent;
      }
      if(this.filterGoingList.length !=0){
        this.searchProperty['min_going'] = this.filterGoingList['min_going'];
        this.searchProperty['max_going'] = this.filterGoingList['max_going'];
      }
    }

    if(this.filterPriceList.length !=0){
      this.searchProperty['max_price'] = this.filterPriceList['max_price'];
      this.searchProperty['min_price'] = this.filterPriceList['min_price'];
    }

    if(this.selectedBaths != undefined && this.selectedBaths !=0){
      this.selectedBaths = this.selectedBaths.replace("+", "");
      this.searchProperty['bathroom'] = this.selectedBaths;
    }
    if(this.selectedBeds != undefined && this.selectedBeds !=0){
      this.selectedBeds = this.selectedBeds.replace("+", "");
      this.searchProperty['bedroom'] = this.selectedBeds;
    }
    if(this.filterSqFtList.length !=0){
      this.searchProperty['min_sqft'] = this.filterSqFtList['min_sqft'];
      this.searchProperty['max_sqft'] = this.filterSqFtList['max_sqft'];
    }
    if(this.filterHomeType.length !=0){
      this.searchProperty['property_type'] = this.filterHomeType;
    }


    if(this.anyStatus.length !=0){
      this.searchProperty['property_status'] = this.anyStatus;
    }

    if(this.eventTypeList.length !=0){
      this.searchProperty['event_type'] = this.eventTypeList;
    }


    if(this.openHouseTypeList.length !=0){
      this.searchProperty['open_house_type'] = this.openHouseTypeList;
    }
    // if(this.filterGoingList.length !=0){
    //   this.searchProperty['min_sqft'] = this.filterSqFtList['min_sqft'];
    //   this.searchProperty['max_sqft'] = this.filterSqFtList['max_sqft'];
    // }
    if(this.searchSuggestion != undefined){
      for(let key of Object.keys(this.searchSuggestion)){
        // if(this.searchSuggestion[key] != 0){
          this.searchProperty[key] = this.searchSuggestion[key];
        // }
      }
    }
    this.searchProperties();
  }

  searchProperties(){
    setTimeout(() => {
      if(this.isListViewScreen == false){
        if(this.searchProperty['geo_bounding_box'] == undefined){
          if(localStorage.getItem("recentSearches") !=null && JSON.parse(localStorage.getItem("recentSearches"))[0]['geo_bounding_box'] != undefined){
            this.searchProperty['geo_bounding_box'] = JSON.parse(localStorage.getItem("recentSearches"))[0]['geo_bounding_box'];
          }
        }
      }
    let urlParams = new URLSearchParams();
    for(let key of Object.keys(this.searchProperty)){
      if(key != "property_type" && key != 'property_status' && key != 'open_house_type' && key != 'event_type' && key != 'search' && key != 'idleLatLng'){
        urlParams.set(key, this.searchProperty[key]);
      }
      else{
        if(key == "property_type"){
          for(let i=0;i<this.filterHomeType.length;i++){
            urlParams.set('property_type['+[i]+']',this.filterHomeType[i]);
          }
        }
        else if(key == "property_status"){
          for(let i=0;i<this.anyStatus.length;i++){
            urlParams.set('property_status['+[i]+']',this.anyStatus[i]);
          }
        }
        // else if(key == "event_type"){
        //   for(let i=0;i<this.eventTypeList.length;i++){
        //     urlParams.set('event_type['+[i]+']',this.eventTypeList[i]);
        //   }
        // }
        else if(key == "open_house_type"){
          if(this.showopenHouseFilter){
            for(let i=0;i<this.openHouseTypeList.length;i++){
              urlParams.set('open_house_type['+[i]+']',this.openHouseTypeList[i]);
            }
          }
        }
      }
    }

    this.previousSearch[0] = this.searchProperty;
    if(this.searchSuggestion != undefined){
      this.previousSearch[0]['search'] =  this.searchSuggestion;
    }
    localStorage.setItem('recentSearches',JSON.stringify(this.previousSearch));

    if(this.searchFrom == ''){
      if(this.searchMapSubscription){
        this.searchMapSubscription.unsubscribe();
      }
      this.searchMapSubscription = this.searchService.searchSuggestions(urlParams).subscribe(res => {
        var response = {
          "error": "false",
          "result" : res['result']['property_list'],
          "totalPage" : res['result']['total_proeprty_count'],
          "itemsPerPage" : res['result']['items_per_page'],
          "currentPageNumber" : this.pageNo,
          'map_record_list' : res['result']['map_record_list']
        }
        this.searchObjEvent.emit(response)
      },err =>{
        console.log(err);
        var response = {
          "error": "true"
        }
        this.searchObjEvent.emit(response);
      });
    }
    else if(this.searchFrom == 'eventManager')
    {
      this.searchObjEvent.emit(urlParams);
    }
    else if(this.searchFrom == 'openHouseListView'){
      this.searchObjEvent.emit(urlParams);
    }
    else if(this.searchFrom == 'myListingListView'){
      this.searchObjEvent.emit(urlParams);
    }
    else if(this.searchFrom == 'favoriteListView'){
      this.searchObjEvent.emit(urlParams);
    }
  },500);
}

  ClearLocationSearch(){
    this.searchSuggestion = undefined;
    delete this.searchProperty['state'];
    delete this.searchProperty['zipcode'];
    delete this.searchProperty['zipcode_code'];
    delete this.searchProperty['search'];
    delete this.searchProperty['geo_id'];
    delete this.searchProperty['city'];
    delete this.searchProperty['abrrevation'];
    delete this.searchProperty['suggestion'];
    this.searchLocation = ''
    localStorage.removeItem('recentSearches')
  }

  otherFilters(){
    this.searchService.setCurrentListType(this.listType);
    this.searchService.setFilterForm(this.searchFrom);
    this.searchService.setFavoriteScreen(this.isFavoriteSearch);
    this.searchService.setSearchProperty(this.searchProperty);
    this.routeOnUrl('search-bar-filter');
  }

  showMoreFilter(){
    $(".show_more_search_filter").slideToggle("fast",function(){
    });
  }

  hideMoreFilter(){
    if($(".show_more_search_filter").is(":visible")){
      $('.show_more_search_filter').hide();
    }
  }

  ngOnDestroy(){
    this.searchService.setMyClientSearch(undefined);
    this.searchService.setSearchSuggestions(undefined);
    this.searchProperty = {};
    if(this.searchMapSubscription){
      this.searchMapSubscription.unsubscribe();
    }
  }
}
