<admin-header>
  
  </admin-header>
  
  <div class="col-sm-16 top-bar">
    <div class="log-mt">
    </div>
  </div>
  
  <div class="user-box-pd">
    <div class="user-box white-box">
      <div class="col-sm-16 plans-box-title">
        <span>Manage billing plans</span>
      </div>
    </div>
  </div>


<div class="table-top">
  <table class="table">
    <tr>
      <th>Id</th>
      <th>Name</th>
      <th>Price</th>
      <th>Index</th>
      <th>Category</th>
      <th>License Min</th>
      <th>License Max</th>
    </tr>
    <tbody>
      <tr *ngFor="let plans of plansList">
        <td class="name-size">                               
        {{plans.id}}
        </td>

        <td class="name-size">
          {{plans.name}}
        </td>
        
        <td class="name-size">
          {{plans.price}}
        </td>
          
          
        <td class="name-size">
          <div class="form_group">
              <ng-select class="custom agent-dropdown" 
                placeholder = "Index"
                [items]="indexList"                                                                    
                notFoundText="No index found"                                                                        
                bindLabel="{{plans.index}}"
                bindValue="{{plans.index}}"
                [(ngModel)]="plans.index"
                [clearable]=false
                [searchable]=false 
                >
              </ng-select>
          </div>
        </td>

        <td class="name-size">
          <div class="form_group">
          <ng-select class="custom agent-dropdown" 
            placeholder = "Category"
            [items]="categoryList"
            [clearable]=false
            [searchable]=false                                         
            notFoundText="No category found"                                                                        
            bindLabel="{{plans.category}}"
            bindValue="{{plans.category}}"
            [(ngModel)]="plans.category"
            >
          </ng-select>
          </div>
        </td> 

        <td class="name-size">
          <div *ngIf="plans.category == 'BR'" class="new_form_group dis_inline ml-10 license-align">
              <div class="group new_form_label">      
                  <input type="text" placeholder=" " required class="smallSize-number" value="{{plans.s_liscence}}" [(ngModel)]="plans.s_liscence">
                  <span class="highlight"></span>
                  <span class="bar"></span>
                  <label>min License</label>
              </div>
          </div>                                
        </td>
          
        <td class="name-size">
          <div *ngIf="plans.category == 'BR'" class="new_form_group dis_inline ml-10 license-align">
              <div class="group new_form_label">      
                  <input type="text" placeholder=" " required class="smallSize-number" value="{{plans.e_liscence}}" [(ngModel)]="plans.e_liscence">
                  <span class="highlight"></span>
                  <span class="bar"></span>
                  <label>Max License</label>
              </div>
          </div>
        </td>
      </tr>
    </tbody>
    <div class="new_form_group save-btn">
      <input type="submit" class="submit_button with_bg save-btn-plans"  value="Save" (click)="Updateplans()">
    </div>
  </table>
</div>