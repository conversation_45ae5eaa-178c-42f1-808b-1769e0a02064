import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MyLeadsComponent } from '@app/myLeads/component/my-leads.component';
import { BaseModule } from '@app/base/modules/base.module';
import { MyLeadsService } from '@app/myLeads/service/my-leads.services';

@NgModule({
  imports: [
    BaseModule,
    CommonModule
  ],
  declarations: [MyLeadsComponent],
  providers : [MyLeadsService]
})
export class MyLeadsModule { }
