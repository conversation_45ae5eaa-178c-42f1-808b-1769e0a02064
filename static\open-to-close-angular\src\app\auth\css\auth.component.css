  .text-left{
    text-align: left !important;
  }
  .search-result{
    overflow: auto;
    list-style-type: none;
    text-align: left;
    padding: 0px;
    /* width: 372px; */
    max-height: 145px;
    background: white;
    font-size: 17px;
    color: #5A5A5A;
  }
  .search-result-li{
    /* border: 1px solid #CCCCCC;     */
    text-align: left;
    padding: 6px 7px 6px 18px;
    cursor: pointer;
  }
  .search-result-a{
    text-decoration: none !important;
    color: #676767 !important;
    font-size: 16px;
    cursor: pointer;
  }
  .search-result-a:focus,a:hover{
    text-decoration: none !important;
  }
  /* .new_form{
    margin-top: 14px !important;
} */
.title3{
  font-size: 12px;
  color: #8D8D8D;
  margin-top: 14px !important;
  padding-bottom: 25px !important;
}
.title2{
  font-size: 15px;
  color: #8D8D8D;
}

.ng-select .ng-control{
  border: 1px solid #bbbaba !important;
}

.search-agent-image{
  height: 130px !important;
  width: 130px !important;
  border-radius: 85px !important;
}

.search-result-li:hover{
  background: #10B8A8;
  color: #FFFFFF;
  transition: all 275ms ease-in-out;
}

.search-input-layout{
  font-size: 18px;
}
.dashboard-caption{
  padding-left: 158px;
  width: 87%;
  line-height: 22px;
  font-size: 17px !important;
}
.search-mt{
  margin-top: 10px;
}

.font-weight-banner-text{
  font-weight: 600 !important;
}
.caption{
  width: 85%;
}

.space_wrapper{
  padding-top: 5%;
}
.max_width{
  max-width: 56% !important;
}
#space_2{
  padding: 60px 0px 55px 0px;
}
.legend-style{
  padding-top: 5%;
}
.download-view{
  padding-top: 4em;
  padding-bottom: 17em;
}

.home_raw_btn.dis_inline {
  background: #10B8A8;
  font-size: 15px;
  color: #FFFFFF;
  vertical-align: bottom;
  border-radius: 21px;
  font-weight: 600;
  letter-spacing: 0;
  padding: 10px 26px 10px 26px;
  width: 25%;
  text-align: center;
}

.space_2_p{
  line-height: 32px !important;
}

@media only screen and (max-width: 767px) {
  .caption{
    width: 100%;
  }
  .max_width{
   width: 100% !important;
  }

  .home_raw_btn.dis_inline{
    width: 35%;
    margin-bottom: 10px;
  }
  .social{
    float: unset !important;
    text-align: center;
  }
  .download-view{
    padding-bottom: 2em;
  }
  .mobile_img{
    padding: 0px 0px !important;
  }
}
.mobile_img{
  padding: 50px 0px !important;
}
.section-video{
  padding-top: 1%;
  padding-bottom: 5%;
}

.content-pending{
  padding-top: 150px;
  padding-bottom: 20px;
  color: white;
  font-size: 16px;
  font-style: italic;
  font-weight: 600;
}