<div class="mar_zero">
<div class="Event_manager_add small-grid"  style="overflow: hidden;">
  <div class="add_event_image_group">
      <span *ngIf="eventBasicInfo?.property_file == ''">
          <div class="guest-overlay"></div>
          <img src="{{imagePrefix}}symbols-map-hover.png" class="add_event_bg"  alt="">
      </span>
      <span *ngIf="eventBasicInfo?.property_file != ''">
            <div class="guest-overlay"></div>
          <img src="{{eventBasicInfo.property_file}}" class="add_event_bg" alt="">
      </span>

      <img src="{{imagePrefix}}footer_logo.png" class="img-responsive footer_logo_event img-margin-left-7" alt="">
      <div class="event_address_title img-margin-left-5">{{eventBasicInfo?.address}}</div>
      <div class="event_question img-margin-left-5" style="display:none">Already have an Open Houses Direct account?</div>
      <div class="event_address_note img-margin-left-5" style="display:none">Please enter your email to check in</div>

      <div class="event_search img-margin-left-5" style="display:none">
        <form [formGroup]="checkInForm">
          <div class="search_button ">
            <div class="input-group stylish-input-group">
              <input type="text" formControlName="email" class="form-control" placeholder="Email address" required>
                <div *ngIf="checkInForm.controls['email'].untouched">
                  <span></span>
                </div>
                <div *ngIf="checkInForm.controls['email'].touched && checkInForm.controls.email.errors?.email">
                    <span class="form-validation">Enter valid email address</span>
                </div>
            </div>
          </div>
          <div class="search_submit form_group">
              <input type="submit" [ngClass]="{'checkin-disable': checkInForm.invalid }" [disabled]="checkInForm.invalid"  value="Check In" (click)="checkIn(checkInForm)">
          </div>
        </form>
      </div>

      <div class="your_open_agent img-margin-left-5" style="top: 70% !important;">
        <div class="title2">Your Open House Agent</div>
        <div class="open_agent_img">
            <span *ngIf="eventBasicInfo?.listing_agent_image == ''">
                <img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline"  alt="">
            </span>
            <span *ngIf="eventBasicInfo?.listing_agent_image != ''">
                <img src="{{eventBasicInfo?.listing_agent_image}}" class="search-agent-event symbols-property-image dis_inline" alt="">
            </span>
            <div class="open_agent_name_group">
              <div class="name">{{eventBasicInfo?.listing_agent_name}}</div>
              <div class="fname">{{eventBasicInfo?.brokerage_name}}</div>
            </div>
        </div>
      </div>
  </div>

<div class="event_agent_group" style="height: 98vh; overflow: auto;">
  <img src="{{imagePrefix}}logo.png" class="img-responsive event_agent_img" alt="logo">
  <div class="event_agent_title">The <span>premier</span> open house experience </div>
  <div class="not_a_member">Not an Open Houses Direct member?</div>
  <div class="event_form">
    <form [formGroup]="memberForm">
      <div class="col-sm-8" formGroupName="profile">
        <div class="ev_form">
          <label for="">Name<span style="color:#ff0000">*</span></label>
          <input type="text" formControlName="first_name" class="ev_form_group" placeholder="First Name*" required>
          <div *ngIf="memberForm['controls'].profile['controls']['first_name'].untouched">
              <span></span>
          </div>
          <div *ngIf="memberForm['controls'].profile['controls']['first_name'].touched && memberForm['controls'].profile['controls']['first_name'].errors?.required">
              <span class="form-validation">Enter first name</span>
          </div>
        </div>
      </div>
      <div class="col-sm-8" formGroupName="profile">
        <div class="ev_form">
          <label for="">&nbsp;</label>
          <input type="text" formControlName="last_name" class="ev_form_group" placeholder="Last Name*" required>
            <div *ngIf="memberForm['controls'].profile['controls']['last_name'].untouched">
                <span></span>
            </div>
            <div *ngIf="memberForm['controls'].profile['controls']['last_name'].touched && memberForm['controls'].profile['controls']['last_name'].errors?.required">
                <span class="form-validation">Enter last name</span>
            </div>
        </div>
      </div>
      <div class="col-sm-16"  >
        <div class="ev_form">
            <label for="">Email</label>
            <input type="text" #guest_email (keyup)="checkEmail(guest_email.value)" formControlName="email" class="ev_form_group" placeholder="Enter your email here" >
              <div *ngIf="memberForm.controls['email'].untouched">
                <span></span>
              </div>
              <div *ngIf="emailInvalid">
                  <span class="form-validation">Enter valid email address</span>
              </div>
        </div>
      </div>
      <div class="col-sm-16" formGroupName="profile" style="display:none">
        <div class="ev_form">
          <label for="">Country</label>
        <ng-select class="custom signup-dropdown"
                placeholder = "Country"
                notFoundText="No Country found"
                [items]="countryList"
                bindLabel="value"
                bindValue="name"
                [(ngModel)]="selectedCountry"
                [ngModelOptions]="{standalone: true}"
                [clearable]=false
                [searchable]=false
                >
            </ng-select>
            </div>
    </div>

      <div class="col-sm-16" formGroupName="profile">
        <div class="ev_form">
          <label for="">Phone Number<span style="color:#ff0000">*</span></label>
          <input type="text" maxlength="12" #phoneNumber (keyup)="validatePhoneFormat(phoneNumber.value)" formControlName="phone" class="ev_form_group" placeholder="************">
          <div *ngIf="memberForm['controls'].profile['controls']['phone'].touched">
              <!-- <p class="form-validation" *ngIf="memberForm['controls'].profile['controls']['phone'].errors?.required">Enter phone number</p> -->
              <p class="form-validation" *ngIf="memberForm['controls'].profile['controls']['phone'].errors?.minlength">phone number must be 10 characters</p>
              <p class="form-validation" *ngIf="memberForm['controls'].profile['controls']['phone'].errors?.maxlength">phone number must be 10 characters</p>
          </div>
          <div *ngIf="!memberForm['controls'].profile['controls']['phone'].touched">
            <div *ngIf="phoneNumberCheck">
              <span class="form-validation">phone number must be 10 characters</span>
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-16 otp-info" formGroupName="profile" *ngIf="showOtp"  >
        <div class="ev_form" >
          <label for="">OTP</label>
        <input  type="text" maxlength="4" #otp class="new_form" formControlName="otp" placeholder="Enter Verification Code"  (keyup)="validateOtpFormat(otp.value)" />
        <br/>
        <!-- <p>Haven't Received Any Code</p> -->
        <a class="registration-link" style="cursor: pointer;"><span (click)="sendOtp(memberForm)">Resend OTP</span></a>
        </div>
    </div>

      <div *ngIf="!alreadyExistUser" class="check_group profile_checkbox col-sm-16 ev_form_check" style="">
         <div class="checkbox-container">
                  <label class="custom-checkbox" style="margin-top: 3px;">
                    <input type="checkbox" formControlName="agree_terms" />
                    <span class="checkmark"></span>
                  </label>
                  <span style="font-size: 13px; line-height: 1.4;">
                    I agree to the <a href="/terms-of-use" target="_blank">Terms of Use</a> and
                    <a href="/privacy-policy" target="_blank">Privacy Policy</a>, and I am opting in to receive
                    communications via email, phone calls, and SMS/MMS messages. Message frequency may vary.
                    Standard message and data rates may apply. Reply STOP to unsubscribe or HELP for assistance.
                  </span>
                </div>
          <label class="already-register" for="">Are you working with an agent?</label>
          <div class="form_group ">
            <input type="radio"  formControlName="WorkingWithAgent" value="yes">   <span class="checkmark"></span>
            <label class="width_auto" >Yes</label>
          </div>
          <div class="form_group">
            <input type="radio"  formControlName="WorkingWithAgent" value="no">   <span class="checkmark"></span>
            <label class="width_auto">No</label>
          </div>

          <div *ngIf="showAgent != false">
              <div class="form_group">
                  <input type="text" #name class="new_form " placeholder="Type agent name here*" (keyup)="searchAgent(name.value)"/>
                  <img src="{{imagePrefix}}symbols-glyph-openhouse.png" class="img-responsive search" alt="">
              </div>
              <div *ngIf="agentList != false">
                  <div class="agent_found">
                          {{searchAgentList.length}} agent found
                  </div>
                   <!-- [ngClass]="{'search-agent-list': searchAgentList.length > 3} -->
                  <div class="search-agent-list-event">
                      <div class="remove_agent search-agent-padding" *ngFor="let agent of searchAgentList" >
                          <span *ngIf="agent.profile_photo == null || agent.profile_photo == '' || agent.profile_photo == undefined" [ngClass]="{ 'remove-span' : agent.profile_photo == null || agent.profile_photo == '' || agent.profile_photo == undefined}"><img src="{{imagePrefix}}testmonial-default (1).png" width="130px" height="130px" class="search-agent-image" alt=""></span>
                          <span *ngIf="agent.profile_photo != null || agent.profile_photo != undefined || agent.profile_photo != ''"><img src="{{agent.profile_photo}}" width="130px" height="130px" class="search-agent-image" alt=""></span>
                              <div class="remove_details">
                                  <div *ngIf="agent.name != null" class="name">{{agent.name}}</div>
                                  <div *ngIf="agent.name == null" class="name">{{agent.mls_agent_id}}</div>
                                  <div class="sub_name"> {{agent.broregisterFormkerage_name}}</div>
                                  <div *ngIf="getAgentId == agent.id" (click)="removeAgent()" class="remove_button">Remove agent</div>
                                  <div *ngIf="getAgentId != agent.id"(click)="showMyAgent(agent.id)" class="remove_button select">This is my agent</div>
                              </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>

      <!-- <div class="col-sm-16">
        <div class="ev_form">
          <div class="ev_note">
              The average home buyer spends $xxx too much on their mortgage. Do you want to hear about competitive mortgage rates?
          </div>
        </div>
      </div>

      <div class="check_group profile_checkbox col-sm-16 ev_form_check">
        <div class="form_group ">
          <input type="radio" formControlName="mortgageRate" value="yes">   <span class="checkmark"></span>
          <label class="width_auto">Yes</label>
        </div>
        <div class="form_group">
          <input type="radio" formControlName="mortgageRate" value="no">   <span class="checkmark"></span>
          <label class="width_auto">No</label>
        </div>
      </div> -->

      <div class="col-sm-16">
        <div class="ev_form">
          <div class="search_submit signin">
            <input type="submit" *ngIf="!showOtp" style="width: 150px !important; padding: 5px 2px;" [ngClass]="{'submit-disable': memberForm.invalid }" [disabled]="memberForm.invalid" class="new_form" value="Send Verification Code" (click)="sendOtp(memberForm)" />
             <input type="submit" *ngIf="showOtp" style="width: 150px !important;  padding: 5px 2px;"  [ngClass]="{'submit-disable': memberForm.invalid }" [disabled]="memberForm.invalid" class="new_form" value="Verify & Check In" (click)="verifyOtp(memberForm)" />
              <!-- <input type="submit" [ngClass]="{'submit-disable': memberForm.invalid }" [disabled]="memberForm.invalid"  value="Check In" (click)="newMember(memberForm)"> -->
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
</div>
</div>
