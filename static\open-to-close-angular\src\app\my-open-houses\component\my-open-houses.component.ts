import { Component, OnInit,ViewChild,NgZone, keyframes } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { MapInfoBubbleService } from '@app/base/services/map-info-bubble.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { MyOpenHousesService } from '@app/my-open-houses/services/my-open-houses.service';
import { SearchService } from '@app/search/service/search.service';
import * as moment from 'moment';
import { SearchBarComponent } from '@app/searchBar/component/search-bar.component';
import { FavoriteService } from '@app/favorite/service/favorite-service';

declare var google;
declare var $;
declare var MarkerClusterer;

@Component({
  selector: 'my-open-houses',
  templateUrl: '../views/my-open-houses.component.html',
  styleUrls: ['../css/my-open-houses.component.css']
})
export class MyOpenHousesComponent extends BaseComponent implements OnInit {

  @ViewChild(EventModalComponent) eventModal: EventModalComponent;
  @ViewChild(SearchBarComponent) searchBarComponent: SearchBarComponent;

  markerSet =[];
  public mapPolygons: any;
  public map;
  public poly;
  public bermudaTriangle = [];
  public freeHandPolygons = [];
  public matchMarker = [];
  public markers = [];
  public lat=40.730610;
  public lng=-73.935242;
  public currentLatLng;
  public infoBubble: any;
  InfoBubbleService : MapInfoBubbleService;
  houseImage = this.imagePrefix+ "symbols-map-hover.png";
  html:any = "";
  HouseDropDown = ['All future events','Today','Tomorrow','Next 7 Days','Next 30 Days','Custom'];

  openHouseService : MyOpenHousesService;
  searchService : SearchService;
  favoriteService :FavoriteService

  public openHousesList = [];
  public totalPorpertyCount = 0;
  public itemsPerPage:any;
  datePicker : Boolean = false;
  pageCount: number = 1;
  public positionStatus:Boolean =true;
  public geolocationPosition;
  public selectedEventType = 'All future events';
  public selectedSDate = '';
  public selectedEDate = '';
  public searchPageNo = 0;
  public searchListType = 3;
  public isPropertySearch = false;
  public markerCluster;
  public multiInfoWindowList = [];

  public autoMapPosition : Boolean = true;
  public showCancelDraw: Boolean = false;
  public showMapLoading: Boolean = false;
  public addLoaderClass: Boolean = true;
  public allowAddGeoJson : Boolean = true;
  public mobileReZoom : Boolean = true;
  public isMobileListView  : Boolean = true;

  public selectedComponent = '';
  public selected

  public mapViewPort = {};
  public isFirstGetList : Boolean = true;
  public openHouseMapGeoJson;

  constructor(public zone: NgZone) {
    super();
    this.InfoBubbleService = ServiceLocator.injector.get(MapInfoBubbleService);
    this.openHouseService = ServiceLocator.injector.get(MyOpenHousesService);
    this.searchService = ServiceLocator.injector.get(SearchService);
    this.favoriteService = ServiceLocator.injector.get(FavoriteService);
   }

  ngOnInit() {
    var self = this;
    this.selectedComponent = this.router.routerState.snapshot.url;
    this.searchBarComponent.openSearch = true;
    this.searchBarComponent.listType = this.searchListType;

    if(this.selectedComponent.includes('my-open-houses')){
      BaseComponent.baseselectedHeader = 'myOpenHouse';
    }
    else if(this.selectedComponent.includes('my-list')){
      BaseComponent.baseselectedHeader = 'my-list';
    }

    if(BaseComponent.baseselectedHeader == 'my-list'){
      if(this.getPreviousScreen() != '/my-list'){
        this.clearLocalStorageSearch();
      }
      this.setPreviousScreen('/my-list');
    }

    else if(BaseComponent.baseselectedHeader == 'myOpenHouse'){
      if(this.getPreviousScreen() != '/my-open-houses'){
        this.clearLocalStorageSearch();
      }
      this.setPreviousScreen('/my-open-houses');
    }

    if(localStorage.getItem('recentSearches') == null){
      this.searchBarComponent.searchProperty['filter_by_day'] = '5';
    }

    if($(window).width() < 767){
      this.searchBarComponent.mapListView = false;
    }

    this.initMap();
    $(document).ready(function () {
      $("#showDatePicker").hide();
      $('#datePickerDemo').daterangepicker({
        "opens": "left",
        autoUpdateInput: false
        }, function(start, end, label) {
          self.searchBarComponent.searchProperty['local_event_start_date'] = start.format('YYYY-MM-DD');
          self.searchBarComponent.searchProperty['local_event_end_date'] = end.format('YYYY-MM-DD');

          self.searchBarComponent.searchProperty['event_start_date'] = self.getLocalToUtcDate(start.format('YYYY-MM-DD'));
          self.searchBarComponent.searchProperty['event_end_date'] = self.getLocalToUtcDate(end.format('YYYY-MM-DD'));
          self.selectedSDate = self.getLocalToUtcDate(start.format('YYYY-MM-DD'));
          self.selectedEDate = self.getLocalToUtcDate(end.format('YYYY-MM-DD'));
          $('#datePickerDemo').val(start.format('MM/DD/YYYY')+' - '+end.format('MM/DD/YYYY'));
          self.searchBarComponent.filterProperty();
        });
    });

    $(".show_map").click(function(){
      $(".display_none_map").addClass("show_map_mobile");
      $(".map_side_bar").addClass("hide_map_mobile");
    });

    $(".show_list").click(function(){
      $(".map_side_bar").removeClass("hide_map_mobile");
      $(".display_none_map").removeClass("show_map_mobile");
      self.zone.run(() => {
        self.isMobileListView = true;
        self.searchBarComponent.mapListView = false;
      });
    });
  }

    initData(){
      this.onEventTypeChange('All future events',1);
    }

    initMap(){
      if(localStorage.getItem('recentSearches')){
        this.searchBarComponent.allowMapIdle = false;
        this.autoMapPosition = false;
      }
      if(BaseComponent.currentUserLatitude != undefined && BaseComponent.currentUserLongitude != undefined){
        this.autoMapPosition = false;
        this.currentLatLng = new google.maps.LatLng(BaseComponent.currentUserLatitude,BaseComponent.currentUserLongitude);
      }
      else{
        this.currentLatLng = new google.maps.LatLng(this.lat,this.lng);
      }

      var mapOptions = {
        zoom:8,
        center: this.currentLatLng,
        zoomControl: false,
        streetViewControl: false,
        fullscreenControl: false,
      };

      var marker = new google.maps.Marker({
        position: this.currentLatLng,
        map: this.map,
        optimized:false
      });
      this.map = new google.maps.Map(document.getElementById('map'),mapOptions);

      if(localStorage.getItem('recentSearches')){
        this.setPreviousMapPositon();
      }

      this.map.addListener('idle', function() {
        var lat0 = self.map.getBounds().getNorthEast().lat();
        var lng0 = self.map.getBounds().getNorthEast().lng();
        var lat1 = self.map.getBounds().getSouthWest().lat();
        var lng1 = self.map.getBounds().getSouthWest().lng();
        self.mapViewPort ={
          "location": {
            "top_right": {"lat":lat0,"lon": lng0},
            "bottom_left": {"lat": lat1,"lon": lng1}
          }
        };

        var southWest = new google.maps.LatLng(lat1,lng1);
        var northEast = new google.maps.LatLng(lat0,lng0);
        var bounds = new google.maps.LatLngBounds(southWest,northEast);
        self.searchBarComponent.searchProperty['gmap_bounds'] = bounds;

        var idleLatLng = {
          "lat" : self.map.data.map.center.lat(),
          "lng" : self.map.data.map.center.lng()
        }
        self.searchBarComponent.searchProperty['idleLatLng'] = idleLatLng;

        self.searchBarComponent.searchProperty['request_type'] = 'WEB'
        self.searchBarComponent.pageNo = 1;
        // self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.mapViewPort).toString();
        // self.searchBarComponent.searchProperty['is_map_list'] = true;

        if($(window).width() < 767 && self.searchBarComponent.mapListView == false){
          if(localStorage.getItem('recentSearches') == null){
            self.searchBarComponent.searchProperty['geo_bounding_box'] = "{}";
          }
          else{
            let recentSearches = JSON.parse(localStorage.getItem('recentSearches'));
            self.searchBarComponent.searchProperty['geo_bounding_box'] = recentSearches[0]['geo_bounding_box'];
          }
          self.searchBarComponent.searchProperty['is_map_list'] = false;
          self.searchBarComponent.mapListView = false;
        }
        else{
          self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.mapViewPort).toString();

          if($(window).width() < 767 && self.isMobileListView == true){
            self.searchBarComponent.searchProperty['geo_bounding_box'] = "{}";
          }

          self.searchBarComponent.searchProperty['is_map_list'] = true;
          self.searchBarComponent.mapListView = true;
        }

        self.zone.run(() => {
          self.showMapLoading = true;
        });
        self.disable();

        if(self.searchBarComponent.allowMapIdle == true){
          // self.searchBarComponent.mapListView = true;
          if(self.selectedEventType == 'All future events'){
            self.searchBarComponent.searchProperty['filter_by_day'] = '5';
          }
          self.searchBarComponent.filterProperty();
          self.searchBarComponent.allowMapIdle = false;
          localStorage.setItem('zoomLevel',self.map.getZoom());
        }

        if(self.infoBubble.isOpen() == true){
          self.infoBubble.close();
        }
      });

      var self=this;
      if(navigator.geolocation){
        /*
          * @Desc: Find current position
          * @Param:
          * @return:display infowindow on map with given string
          *
        */

      navigator.geolocation.getCurrentPosition((position)=>{
        var currentPosition
        BaseComponent.currentUserLatitude = position.coords.latitude;
        BaseComponent.currentUserLongitude = position.coords.longitude;

          currentPosition= {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          self.geolocationPosition=currentPosition;
          if(this.positionStatus == true && this.autoMapPosition == true){
            self.map.setCenter(currentPosition);
          }
        },
        ()=>{
          this.handleLocationError(true,this.map.getCenter());
        });

      }
      else{
          this.handleLocationError(false,this.map.getCenter());
      }

      $("#draw a").click((e)=>{
        /*
          * @Desc: Allow to draw polygon
          * @Param:
          * @return:
          *
        */
        if(self.showMapLoading == false){
          self.showCancelDraw = true;
          this.clearGoogleMap(true);
          delete this.searchBarComponent.searchProperty['polygon'];
          self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.mapViewPort);
          self.searchBarComponent.searchProperty['is_map_list'] = true;
          self.searchBarComponent.searchProperty['request_type'] = 'WEB'

          self.map.setOptions({ draggableCursor:'default'});
          e.preventDefault();
          self.disable();
          google.maps.event.addDomListener(self.map.getDiv(),'mousedown',(e)=>{
          self.drawFreeHand();
          });
        }
        else{
          e.preventDefault();
        }
      });

      $("#cancelDraw a").click((e)=>{
        self.showCancelDraw = false;
        self.clearGoogleMap(true);
        self.allowAddGeoJson = true;
        if(self.freeHandPolygons.length != 0){
          self.freeHandPolygons = [];
          delete this.searchBarComponent.searchProperty['polygon'];
          self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.mapViewPort);
          self.searchBarComponent.searchProperty['is_map_list'] = true;
          self.searchBarComponent.searchProperty['request_type'] = 'WEB'
          self.searchBarComponent.filterProperty();
          self.zone.run(() => {
            self.showMapLoading = true;
          });
        }
        self.enable();
      });

      this.html = this.InfoBubbleService.changeHTML("symbols-map-hover.png", "120000", "", "", "", "", {});
      this.infoBubble = this.InfoBubbleService.mapInfoBubble(this.html,0,0);

      if($(window).width() < 767){
        google.maps.event.addListener(self.map,'click', function() {
          if(self.infoBubble.isOpen() == true){
            self.infoBubble.close();
            $("#property_info").remove();
            self.multiInfoWindowList = [];
          }
        });
        this.addLoaderClass = false;
        this.searchBarComponent.mapListView = false;
      }
      else{
        this.addLoaderClass = true;
        this.searchBarComponent.mapListView = true;
      }
    }

    handleLocationError(browserHasGeolocation,pos){
      /*
        * @Desc:showing error message in infowindow if read location permission id block by user
        * @Param:
        * @return:error message.
        *
      */
    // infoWindow.setPosition(pos);
    // infoWindow.setContent(browserHasGeolocation ?'Error: The Geolocation service failed.' :'Error: Your browser doesn\'t support geolocation.');
    // infoWindow.open(this.gmap);
    }

  addMarkerCluster(){
    var cluster;
    let self = this;
    let j=1;
    var isSameLat = '';
    var isSameLng = '';
    var firstEvent = '';
    var markerColor = "#fffffff7";
    var markers =this.markerSet.map(function(location, i) {
      if(isSameLat == self.markerSet[i]["latitude"] && isSameLng == self.markerSet[i]["longitude"]){
        cluster = new google.maps.Marker({
          position: {lat :self.markerSet[i]["latitude"],lng: self.markerSet[i]["longitude"]},
          icon : self.imagePrefix+firstEvent+".png",
          map: self.map,
          label: {
            text: self.getTimeTypes(self.markerSet[i]["start_time"],'',self.markerSet[i]['date'],self.markerSet[i]['is_listhub_event']),
            background: '#AD5FBF',
            color: markerColor,
            align: 'center',
            padding: '0',
            fontSize: "14px",
            fontWeight: "600"
          },
          title:self.markerSet[i]["street_address"],
          opacity:0.0,
          zIndex:0,
          id:self.markerSet[i]["id"]
        });

      }else{
        if(self.markerSet[i]['listing_status']=="PRE-MLS/Coming Soon"){
          markerColor = "#fffffff7";
          if(self.markerSet[i]['event_type']=="OH"){
            firstEvent = "AO";
          }
          else if(self.markerSet[i]['event_type']=="BO"){
            firstEvent = "BO";
          }
          else if(self.markerSet[i]['event_type']=="AO"){
            firstEvent = "AO";
          }
          else if(self.markerSet[i]['event_type']==""){
            firstEvent = "no-event";
          }
          else {
            firstEvent = self.markerSet[i]['event_type'];
          }
        }
        else{
          if(self.markerSet[i]['event_type']=="AO"){
            firstEvent = "OH";
          }
          else if(self.markerSet[i]['event_type']=="OH"){
            firstEvent = "OH";
          }
          else{
            firstEvent = self.markerSet[i]['event_type'];
          }
        }
        // if(self.markerSet[i]['event_type']=="AO"){
        //   firstEvent = "OH";
        // }
        // else{
        //   firstEvent = self.markerSet[i]['event_type'];
        // }

        // if(self.markerSet[i]['event_type']=="OH" && self.markerSet[i]['listing_status']=="PreMLS/Coming Soon"){
        //   firstEvent = "AO";
        // }

        let iconImage = self.getIconImage(google, firstEvent)

        cluster = new google.maps.Marker({
          position: {lat :self.markerSet[i]["latitude"],lng: self.markerSet[i]["longitude"]},
          // icon : self.imagePrefix+self.markerSet[i]['event_type']+".png",
          icon: iconImage,
          map: self.map,
          label: {
            text: self.getTimeTypes(self.markerSet[i]["start_time"],'',self.markerSet[i]['date'],self.markerSet[i]['is_listhub_event']),
            background: '#AD5FBF',
            color: markerColor,
            align: 'center',
            padding: '0',
            fontSize: "14px",
            fontWeight: "600"
          },
          zIndex:1,
          title:self.markerSet[i]["street_address"],
          id:self.markerSet[i]['id']
        });
      }
      isSameLat = self.markerSet[i]["latitude"];
      isSameLng = self.markerSet[i]["longitude"];
      self.markers.push(cluster);
      //hide all markers when map load
      //this.setMapHideAll(i,this.markerSet[i]["id"] -1 ,null);

      if($(window).width() < 767){
        google.maps.event.addListener(cluster, 'click',((marker,event)=>{
          return function(){
            if(self.infoBubble.isOpen() == false){
              self.multiInfoWindowList = [];
              var allMarkers = self.markerCluster.getMarkers();
              if (allMarkers.length != 0) {
                for (j=0; j < allMarkers.length; j++) {
                  var existingMarker = allMarkers[j];
                  var pos = existingMarker.getPosition();
                  if(marker.getPosition().equals(pos)) {
                    self.multiInfoWindowList.push(self.markerSet[j]);
                  }
                }
              }
              self.openMobileListPropertyInfoBubble(i,marker,self.multiInfoWindowList);
            }
            else{
              self.infoBubble.close();
              $("#property_info").remove();
              self.multiInfoWindowList = [];
              self.openMobileListPropertyInfoBubble(i,marker,self.multiInfoWindowList);
            }
          }
        })(cluster));
        return cluster;
        }
      else{

      }
      google.maps.event.addListener(cluster, 'mouseover',((marker,event)=>{
        return function(){
        self.multiInfoWindowList = [];
        var allMarkers = self.markerCluster.getMarkers();
        if (allMarkers.length != 0) {
          for (j=0; j < allMarkers.length; j++) {
            var existingMarker = allMarkers[j];
            var pos = existingMarker.getPosition();
            if(marker.getPosition().equals(pos)) {
              self.multiInfoWindowList.push(self.markerSet[j]);
            }
          }
        }

        if(self.multiInfoWindowList.length > 1){
          self.html = self.InfoBubbleService.setHTML(self.multiInfoWindowList);
        }
        else{
          self.html = self.InfoBubbleService.changeHTML(self.markerSet[i]['property_file'], self.markerSet[i]['home_price'], self.markerSet[i]['bedroom'], self.markerSet[i]['full_bath'], self.markerSet[i]['living_area'],self.markerSet[i]['street_address'], self.markerSet[i]);
        }
          if(self.infoBubble.isOpen() == true){
            self.infoBubble.close();
          }
          var pixelOffsetY = self.getMapPixelOffsetY(this.map,marker);
          if(pixelOffsetY != undefined && pixelOffsetY < 260){
            self.infoBubble = self.InfoBubbleService.mapInfoBubble(self.html,0,-260);
          }else{
            self.infoBubble = self.InfoBubbleService.mapInfoBubble(self.html,0,0);
          }
          self.infoBubble.open(this.map, marker);
          if(self.multiInfoWindowList.length > 1){
            setTimeout(() => {
              $(".box_on_map_event").parent().parent().parent().addClass('pop_div');
              $(".box_on_map").parent().parent().parent().attr('id','property_info');
              // $(".box_on_map").parent().parent().addClass('info-bubble-top')
            }, 20);
          }
          else{
            setTimeout(() => {
              $(".box_on_map").parent().parent().parent().addClass('pop_div');
              $(".box_on_map").parent().parent().parent().attr('id','property_info');
              // $(".box_on_map").parent().parent().addClass('info-bubble-top')
            }, 20);
          }
        }
      })(cluster));

      google.maps.event.addListener(cluster, 'mouseout',((marker,event)=>{
        return function(){
          self.infoBubble.close();
          self.multiInfoWindowList = [];
          $("div.box_on_map_event", self.infoBubble.bubble_).on("click",function(){
            self.propertyDetail(marker['id']);
          });
          $("div.box_on_map", self.infoBubble.bubble_).on("click",function(){
            self.propertyDetail(marker['id']);
          });
        }
      })(cluster));
      return cluster;
    });

    this.markerCluster = new MarkerClusterer(this.map, markers,
      {
        maxZoom: 12,
        styles: this.mapMarkerCluster[0]
      });

      google.maps.event.addListener(this.markerCluster, 'clusterclick', function(clust) {
        self.mobileReZoom = false;
        if(self.searchBarComponent.searchMapSubscription){
          self.searchBarComponent.allowMapIdle = true;
          self.searchBarComponent.searchMapSubscription.unsubscribe();
        }
      });
  }

  openMobileListPropertyInfoBubble(i,marker,multiInfoWindowList){
    let self = this;
    if(this.infoBubble.isOpen() == false){
      if(multiInfoWindowList.length > 1){
        self.html = self.InfoBubbleService.setHTML(multiInfoWindowList);
      }
      else{
        self.html = self.InfoBubbleService.changeHTML(self.markerSet[i]['property_file'], self.markerSet[i]['home_price'], self.markerSet[i]['bedroom'], self.markerSet[i]['full_bath'], self.markerSet[i]['living_area'],self.markerSet[i]['street_address'], self.markerSet[i]);
      }
      var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);

      if(pixelOffsetY != undefined && pixelOffsetY < 260){
        this.infoBubble = this.InfoBubbleService.mapInfoBubble(this.html,0,-260);
      }else{
        this.infoBubble = this.InfoBubbleService.mapInfoBubble(this.html,0,0);
      }

      this.infoBubble.open(this.map, marker);
      if(multiInfoWindowList.length > 1){
        setTimeout(() => {
          $(".box_on_map_event").parent().parent().parent().addClass('pop_div');
          // $(".box_on_map").parent().parent().addClass('info-bubble-top')
          $(".box_on_map_event").parent().parent().parent().attr('id','property_info');
          $("div.box_on_map_event", self.infoBubble.bubble_).on("click",function(){
            self.propertyDetail(marker['id']);
          });
        }, 20);
      }
      else{
        setTimeout(() => {
          $(".box_on_map").parent().parent().parent().addClass('pop_div');
          // $(".box_on_map").parent().parent().addClass('info-bubble-top')
          $(".box_on_map").parent().parent().parent().attr('id','property_info');
          $("div.box_on_map", self.infoBubble.bubble_).on("click",function(){
            self.propertyDetail(marker['id']);
          });
        }, 20);
      }
    }
  }

  mapZoomOut(){
    if(this.showMapLoading == false){
      var getZoom = this.map.getZoom();
      this.map.setZoom(getZoom - 1);
    }
  }

  mapZoomIn(){
    if(this.showMapLoading == false){
      var getZoom = this.map.getZoom();
      this.map.setZoom(getZoom + 1);
    }
  }

  disable(){
    /*
      * @Desc:disable map controls.
    */
    this.map.setOptions({
      draggable: false,
      zoomControl: false,
      scrollwheel: false,
      disableDoubleClickZoom: false,
      clickable:false
    });
  }

  drawFreeHand(){
    if(this.showMapLoading == false){
      //the polygon
      var self=this;
      self.bermudaTriangle = [];
      self.freeHandPolygons = [];
      this.poly=new google.maps.Polyline({map:this.map,clickable:false,strokeColor: "#10B8A8"});

      //move-listener
      var move=google.maps.event.addListener(this.map,'mousemove',(e)=>{
        self.poly.getPath().push(e.latLng);
      });

      //mouseup-listener
      google.maps.event.addListenerOnce(this.map,'mouseup',(e)=>{
        google.maps.event.removeListener(move);
        var path=self.poly.getPath();
        self.poly.setMap(null);
        self.poly=new google.maps.Polygon({map:self.map,path:path, strokeColor: "#10B8A8",
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: "#10B8A8",
        fillOpacity: 0
      });
        var len = path.getLength();
        var latlist = [];
        for (var i = 0; i < len; i++) {
          latlist.push("new google.maps.LatLng(" + path.getAt(i).toUrlValue(5) + "), ");
          self.freeHandPolygons.push({lat:path.getAt(i).lat(), lon:path.getAt(i).lng()});
        }

        this.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.mapViewPort);

        if(self.freeHandPolygons.length == 0){
          delete self.searchBarComponent.searchProperty['polygon'];
        }else{
          this.searchBarComponent.searchProperty['polygon'] = JSON.stringify(self.freeHandPolygons);
        }
        this.searchBarComponent.searchProperty['is_map_list'] = true;
        this.searchBarComponent.searchProperty['request_type'] = 'WEB'
        this.searchBarComponent.filterProperty();

        self.zone.run(() => {
          self.showMapLoading = true;
        });
        self.disable();

        google.maps.event.clearListeners(self.map.getDiv(), 'mousedown');
        self.enable();

          setTimeout(function(){
            for (var j = 0; j <self.markerSet.length; j++){
              var currentMarkerPosition=new google.maps.LatLng(self.markerSet[j]["latitude"],self.markerSet[j]["longitude"]);
              var resultColor = google.maps.geometry.poly.containsLocation(currentMarkerPosition,self.poly)
              if(resultColor){
                  /*
                    * @desc:if marker available
                    */
                    self.matchMarker.push({lat:self.markerSet[j]["latitude"],lng:self.markerSet[j]["longitude"],id:self.markerSet[j]["id"]});
                //  self.setMapOnAll(j,self.markerSet[j]["id"] - 1,self.map);
                }
                else{
                  self.setMapHideAll(j,self.markerSet[j]["id"],null);
                }
            }
            for(let i=0; i< self.matchMarker.length; i++){
              self.setMapOnAll(i,self.matchMarker[i]['id'],self.map);
            }
          });
      });
    }
  }

  enable(){
    /*
      * @Desc:enable map controls.
    */
    this.map.setOptions({
      draggable: true,
      zoomControl: false,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      clickable:false
    });
  }

  setMapHideAll(index,id,map){
    /*
      * @Desc: set markers hide in map
      * @Param:
                index:number
                map:current map
      * @return:
      *
    */
    let markerCluster = this.markerCluster.getMarkers().filter(marker => marker['id'] == id);
    this.markerCluster.removeMarker(markerCluster[0])
    let marker = this.markers.filter(marker => marker['id'] == id);
    marker[0].setMap(map);
  }

  setMapOnAll(index,id,map){
    /*
      * @Desc: Set markers show in map
      * @Param:
                index:number
                map:current map
      * @return:
      *
    */
    let marker = this.markers.filter(marker => marker['id'] == id);
    marker[0].setMap(map);
  }

  selectedEvent(type,event){
   this.eventModal.openEventModal(type,event,true);
  };

  loadOpenHouses(houseTypeParams){
    this.openHouseService.getAllFutureEvent(houseTypeParams).subscribe(res =>{
      this.openHousesList =  res['result']['records'];
      this.totalPorpertyCount = res['result']['total_records_count'];
      this.itemsPerPage = res['result']['items_per_page'];
      // this.deleteMarkers();
    },err =>{
      this.errorResponse(err.json());
    });
  }

  searchOpenHouse(houseTypeParams){
    this.searchService.searchSuggestions(houseTypeParams).subscribe(res =>{
      this.isFirstGetList = false;
      this.openHousesList =  res['result']['property_list'];
      this.totalPorpertyCount = res['result']['total_proeprty_count'];
      this.itemsPerPage = res['result']['items_per_page'];
      if(this.searchBarComponent.searchProperty['is_map_list'] == true || event['currentPageNumber'] == 0){
        this.deleteMarkers(res['result']['map_record_list']);
        this.zone.run(() => {
          this.showMapLoading = false;
        });
        this.map.setOptions({draggable: true, zoomControl: true, scrollwheel: true, disableDoubleClickZoom: false});
      }
    },err =>{
      this.errorResponse(err.json());
    });
  }

  setUrlSearchParams(): URLSearchParams{
    let searchParams = new URLSearchParams();
    for(let key of Object.keys(this.searchBarComponent.searchProperty)){
      if(key != "property_type"){
        searchParams.set(key, this.searchBarComponent.searchProperty[key]);
      }
      else{
        if(key == "property_type"){
          for(let i=0;i<this.searchBarComponent.filterHomeType.length;i++){
            searchParams.set('property_type['+[i]+']',this.searchBarComponent.filterHomeType[i]);
          }
        }
        else if(key == "property_status"){
          for(let i=0;i<this.searchBarComponent.anyStatus.length;i++){
            searchParams.set('property_status['+[i]+']',this.searchBarComponent.anyStatus[i]);
          }
        }
      }
    }
    return searchParams;
  }

  onEventTypeChange(type,pageNumber){

    let searchParams = new URLSearchParams();

    this.selectedEventType = type;
    this.pageCount = pageNumber;
    $("#showDatePicker").hide();
    let date = new Date();
    const todayDate = moment(date).utc().format('YYYY-MM-DD');

    this.searchBarComponent.pageNo = pageNumber;
    this.searchBarComponent.listType = '3';
    this.searchBarComponent.openSearch = true;
    this.searchBarComponent.searchProperty['page_no'] = pageNumber;
    this.searchBarComponent.searchProperty['list_type'] = '3';

    // if($(window).width() < 767){
    //   searchParams.set('is_map_list','false');
    //   this.searchBarComponent.searchProperty['is_map_list'] = false;
    // }
    // else{
      searchParams.set('is_map_list','true');
      this.searchBarComponent.mapListView = true;
      this.searchBarComponent.searchProperty['is_map_list'] = true;
    // }

    if(type == 'All future events'){
      searchParams = this.setUrlSearchParams();
      this.searchBarComponent.searchProperty['filter_by_day'] = '5';
      this.searchBarComponent.filterProperty();
    }
    else if(type != 'Custom'){
      searchParams = this.setUrlSearchParams();
      if(type == 'Today'){
        searchParams.set('filter_by_day','0');
        this.searchBarComponent.searchProperty['filter_by_day'] = '0';
      }
      if(type == 'Tomorrow'){
        searchParams.set('filter_by_day','1');
        this.searchBarComponent.searchProperty['filter_by_day'] = '1';
      }
      if(type == 'Next 7 Days'){
        searchParams.set('filter_by_day','2')
        this.searchBarComponent.searchProperty['filter_by_day'] = '2';
      }
      if(type == 'Next 30 Days'){
        searchParams.set('filter_by_day','3');
        this.searchBarComponent.searchProperty['filter_by_day'] = '3';
      }
      this.searchBarComponent.searchProperty['event_start_date'] = todayDate;
      this.searchBarComponent.filterProperty();
    }

    if(type == 'Custom'){
      searchParams = this.setUrlSearchParams()
      let self = this;
      searchParams.set('filter_by_day','4');
      this.searchBarComponent.searchProperty['filter_by_day'] = '4';
      $("#showDatePicker").show();
      if(this.selectedSDate == '' && this.selectedEDate == ''){
        $(document).ready(function(){
          $("#datePickerDemo").trigger("click");
        });
      }else{
        searchParams.set('event_start_date',this.selectedSDate);
        searchParams.set('event_end_date',this.selectedEDate);
        this.searchBarComponent.searchProperty['event_start_date'] = this.selectedSDate;
        this.searchBarComponent.searchProperty['event_end_date'] = this.selectedEDate;
        // this.searchOpenHouse(searchParams);
        this.searchBarComponent.filterProperty();
      }
    }

  }

  addToFavorite(id,item){
    if(BaseComponent.user !=undefined && Object.keys(BaseComponent.user).length !=0){
      if(BaseComponent.userType == 'listingAgent'){
        if(BaseComponent.user.is_paid_account){
          let index = this.openHousesList.indexOf(item);
          this.openHousesList[index]['is_favourite'] = this.favoriteService.setFavourite(!this.openHousesList[index]['is_favourite'], id, this.openHousesList, index);
        }
      }
      else{
        let index = this.openHousesList.indexOf(item);
          this.openHousesList[index]['is_favourite'] = this.favoriteService.setFavourite(!this.openHousesList[index]['is_favourite'], id, this.openHousesList, index);
      }
    }
  }

  getSearchObj(event){
    this.searchBarComponent.allowMapIdle = true;
    this.zone.run(
      () => {
      if(event['error'] == "false"){
        this.removeInfoBubble();
        this.isPropertySearch = true;
        this.openHousesList = event['result'];
        this.totalPorpertyCount = event['totalPage'];
        this.itemsPerPage = event['itemsPerPage'];
        this.pageCount = event['currentPageNumber'];
        if(this.searchBarComponent.searchProperty['is_map_list'] == true || event['currentPageNumber'] == 0){
          this.deleteMarkers(event['map_record_list']);
          this.zone.run(() => {
            this.showMapLoading = false;
          });

          this.map.setOptions({draggable: true, zoomControl: false, scrollwheel: true, disableDoubleClickZoom: false});
        }
      }
      else{
        this.zone.run(() => {
          this.showMapLoading = false;
        });
        this.map.setOptions({draggable: true, zoomControl: false, scrollwheel: true, disableDoubleClickZoom: false});
      }
    });
  }


  getpage(pageNumber :number){
    var setScroll = document.getElementById('scroll');
    this.pageCount = pageNumber;
    this.searchBarComponent.searchProperty['is_map_list'] = false;
    if(pageNumber != 1){
      this.searchBarComponent.mapListView = false;
      this.searchBarComponent.searchProperty['is_map_list'] = false;
    }
    else{
      this.searchBarComponent.mapListView = true;
    }

    if(this.isPropertySearch == false){
      setScroll.scrollTop = 0;
      this.onEventTypeChange(this.selectedEventType,pageNumber);
    }
    if(this.isPropertySearch == true){
      this.searchBarComponent.pageNo = pageNumber;
      this.searchBarComponent.filterProperty();
      setScroll.scrollTop = 0;
    }
  }

  propertyOnMap(property){
    for(let i=0;i<property.length;i++){
      if(property[i]['latitude'] != 0 && property[i]['longitude'] !=0){
        property[i]['event_list'].forEach(record => {
          record['home_price'] = property[i]['home_price'];
          record['bedroom'] = property[i]['bedroom'];
          record['full_bath'] = property[i]['full_bath'];
          record['living_area'] = property[i]['living_area'];
          record['id'] = property[i]['id'];
          this.markerSet.push(record);
        });
      }
    }
    this.addMarkerCluster();
  }


  removeInfoBubble(){
    var infowin = document.getElementsByClassName("pop_div");
    for(var i=0;i<infowin.length;i++)
    {
      infowin[i].innerHTML = ''
      $("div.pop_div").remove();
    }
  }

  deleteMarkers(property) {
    if(this.markerSet.length !=0){
      this.markerCluster.clearMarkers();
    }
    for (var i = 0; i < this.markers.length; i++) {
      this.markers[i].setMap(null);
    }
    this.markers = [];
    this.markerSet = [];
    this.propertyOnMap(property);
  };

  propertyDetail(id){
    if(BaseComponent.baseselectedHeader == 'myOpenHouse'){
      this.router.navigate(['my-open-houses/property-detail'],{ queryParams:{propertyId:id}});
    }
    if(BaseComponent.baseselectedHeader == 'my-list'){
      this.router.navigate(['my-list/property-detail'],{queryParams:{propertyId:id}});
    }
  }

  checkUser(){
    if(BaseComponent.user !=undefined && Object.keys(BaseComponent.user).length !=0){
      if(BaseComponent.userType == 'brokerage' || BaseComponent.userType == 'homeBuyer' || BaseComponent.userType == 'mortgageLender'){
        return true
      }
      else if(BaseComponent.userType == 'listingAgent'){
        if(BaseComponent.user.is_paid_account){
          return true;
        }
      }
      return false;
    }
  }

  isIntrested(value){
    var property = this.openHousesList.filter((propertyId) => propertyId.id == value['propertyId']);
    var propertyIndex = this.openHousesList.indexOf(property[0]);
    if(property.length !=0){
      var event = property[0]['event_list'].filter((eventId) => eventId.id == value['eventId']);
      var eventIndex = this.openHousesList[propertyIndex]['event_list'].indexOf(event[0]);
      if(event.length !=0){
        this.openHousesList[propertyIndex]['event_list'][eventIndex]['is_interested'] = value['isIntrested'];
      }
    }
  }

  UpdatePropertyInfo(propertyInfo){
    var property = this.openHousesList.filter((propertyId) => propertyId.id == propertyInfo['property']);
    var propertyIndex = this.openHousesList.indexOf(property[0]);
    if(property.length !=0){
      let updatedPropertyParams = new URLSearchParams();
      updatedPropertyParams.set('property_id',propertyInfo['property']);
      updatedPropertyParams.set('list_type',this.searchListType.toString());

      this.favoriteService.getUpdatedPropertyInfo(updatedPropertyParams).subscribe(res =>{
        this.openHousesList[propertyIndex] = res['result'];
        if(res['result']['is_remove'] == true){
          this.openHousesList.splice(propertyIndex, 1);
          this.setMapHideAll(propertyIndex,res['result']['id'],null);
        }
      },err=>{
        this.errorResponse(err.json());
      });
    }
  }

  setMapPosition(position){
    this.searchBarComponent.allowMapIdle = false;
    if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
      this.map.setCenter(new google.maps.LatLng(position['lat'],position['lng']));
      this.currentLatLng = new google.maps.LatLng(position['lat'],position['lng']);
      if(position['setAutoPosition'] == true){
        this.map.setZoom(8);
      }
      this.allowAddGeoJson = true;
      this.autoMapPosition = false;
    }
  }

  clearGoogleMap(clearMarker){
    if(clearMarker == true){
      if(this.poly != undefined){
        this.poly.setMap(null);
      }
      if(this.mapPolygons != undefined){
        this.mapPolygons.setMap(null);
      }
      if(this.markerSet.length !=0){
        this.markerCluster.clearMarkers();
      }
      for (var i = 0; i < this.markers.length; i++) {
        this.markers[i].setMap(null);
      }
      this.markers = [];
      this.markerSet = [];
    }

    if(this.openHouseMapGeoJson != undefined){
      for (var i = 0; i < this.openHouseMapGeoJson.length; i++){
        this.map.data.remove(this.openHouseMapGeoJson[i])
      }
    }
  }

  drawPolygons(polygonObj){
    this.clearGoogleMap(false);
    this.mobileReZoom = true;

    if(polygonObj['isError'] == true){
      if(this.mapPolygons != undefined){
        this.mapPolygons.setMap(null);
      }
    }
    else{
      if(this.openHouseMapGeoJson != undefined){
        for (var i = 0; i < this.openHouseMapGeoJson.length; i++){
          this.map.data.remove(this.openHouseMapGeoJson[i])
        }
      }
      if(this.allowAddGeoJson == true){
        this.openHouseMapGeoJson = this.map.data.addGeoJson(polygonObj);
        this.map.data.setStyle({
          strokeColor: "#10B8A8",
          strokeWeight: 2,
          strokeOpacity: 0.8,
          fillColor: "#10B8A8",
          fillOpacity: 0
        });
      }

      if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
        this.zoom(this.map);
        delete this.searchBarComponent.searchProperty['polygon'];
        if(this.poly != undefined){
          this.poly.setMap(null);
        }
        if(this.mapPolygons != undefined){
          this.mapPolygons.setMap(null);
        }
        this.showCancelDraw = false;
      }
      this.searchBarComponent.allowMapIdle = true;
    }
  }

  setPreviousMapPositon(){
    this.searchBarComponent.listType = 3;
    var bound = new google.maps.LatLngBounds();
    var filterList = JSON.parse(localStorage.getItem("recentSearches"))[0];
    if(filterList['filter_by_day'] == 5){
      this.selectedEventType = "All future events";
    }else if(filterList['filter_by_day'] == 0){
      this.selectedEventType = "Today";
    }else if(filterList['filter_by_day'] == 1){
      this.selectedEventType = "Tomorrow";
    }else if(filterList['filter_by_day'] == 2){
      this.selectedEventType = "Next 7 Days";
    }else if(filterList['filter_by_day'] == 3){
      this.selectedEventType = "Next 30 Days";
    }else if(filterList['filter_by_day'] == 4){
      this.selectedEventType = "Custom";

      let intStartDate = new Date(filterList['local_event_start_date']);
      let inteEndDate = new Date(filterList['local_event_end_date']);

      this.searchBarComponent.searchProperty['local_event_start_date'] = filterList['local_event_start_date'];
      this.searchBarComponent.searchProperty['local_event_end_date'] = filterList['local_event_end_date'];

      var startDate = moment(intStartDate).format('MM/DD/YYYY');
      var endDate = moment(inteEndDate).format('MM/DD/YYYY');
      setTimeout(() => {
        $("#showDatePicker").show();
        this.zone.run(()=>{
        if($("#datePickerDemo").data('daterangepicker') != undefined){
          $('#datePickerDemo').val(startDate+' - '+endDate);
          $("#datePickerDemo").data('daterangepicker').setStartDate(startDate);
          $("#datePickerDemo").data('daterangepicker').setEndDate(endDate);
        }
        });
      },200);
    }
    if(JSON.parse(localStorage.getItem("recentSearches"))[0]['polygon'] != undefined){
      this.drawCustomPolyline(JSON.parse(localStorage.getItem("recentSearches"))[0]['polygon']);
      this.showCancelDraw = true;
      this.allowAddGeoJson = false;
      this.searchBarComponent.allowCallGetMapPolygons = false;
    }
    if(JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'] != undefined){
      let bounds = JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'];
      var center = new google.maps.LatLng(bounds['lat'],bounds['lng'])
      this.map.setCenter(new google.maps.LatLng(bounds['lat'],bounds['lng']));
      if(localStorage.getItem('zoomLevel')){
        this.map.setZoom(parseInt(localStorage.getItem('zoomLevel')))
      }
      else{
        this.map.setZoom(8)
      }
      localStorage.setItem('boundryZoom','false')
      if($(window).width() < 767){
        this.searchBarComponent.mapListView = false;
      }
      else{
        this.searchBarComponent.mapListView = true;
      }
      this.searchBarComponent.allowLocalStorageSearch();
      // this.searchBarComponent.allowMapIdle = true;
    }
  }

  zoom(map) {
    var self = this;
    var bounds = new google.maps.LatLngBounds();
    map.data.forEach(function(feature) {
      self.processPoints(feature.getGeometry(), bounds.extend, bounds);
    });
    map.fitBounds(bounds);
  }

   processPoints(geometry, callback, thisArg) {
    var self = this;
    if(geometry instanceof google.maps.LatLng) {
      callback.call(thisArg, geometry);
    }else if (geometry instanceof google.maps.Data.Point) {
      callback.call(thisArg, geometry.get());
    }else{
      geometry.getArray().forEach(function(g) {
        self.processPoints(g, callback, thisArg);
      });
    }
  }

  drawCustomPolyline(polygonsList){
    var polygons = [];
    if(polygonsList.length != 0){
      for(let i=0;i<JSON.parse(polygonsList).length;i++){
        let obj = {lng: JSON.parse(polygonsList)[i]['lon'], lat: JSON.parse(polygonsList)[i]['lat']}
        polygons.push(obj);
      }
    }

    if(polygons.length != 0){
      this.freeHandPolygons = polygons;
      this.mapPolygons = new google.maps.Polygon({
          paths: polygons,
          strokeColor: "#10B8A8",
          strokeOpacity: 0.8,
          strokeWeight: 3,
          fillOpacity: 0
      });
      const bounds = new google.maps.LatLngBounds();
      for (var i=0; i<this.mapPolygons.getPath().length; i++) {
        var point = new google.maps.LatLng(polygons[i]['lat'], polygons[i]['lng']);
        bounds.extend(point);
      }
      this.mapPolygons.setMap(this.map);
    }
  }

  propertyListView(){
    if(BaseComponent.baseselectedHeader == 'myOpenHouse'){
      this.router.navigate(['my-open-houses/my-open-houses-list']);
    }
    if(BaseComponent.baseselectedHeader == 'my-list'){
      this.router.navigate(['my-list/myList-open-house-list']);
    }
  }

  getCurrentLocation(){
    if(this.showMapLoading == false){
      if(this.geolocationPosition != undefined && this.geolocationPosition != null){
        this.searchBarComponent.ClearLocationSearch();
        this.clearGoogleMap(true);
        this.map.setCenter(this.geolocationPosition);
        this.map.setZoom(8);
      }
    }
  }

  setMapToViewPort(viewPort){
    var bounds = JSON.parse(viewPort['viewPort']);
    this.map.setCenter(new google.maps.LatLng(bounds['lat'],bounds['lng']));
    if(viewPort['zoomLevel'] != undefined && viewPort['zoomLevel'] != 0){
      this.map.setZoom(viewPort['zoomLevel']);
    }
  }

  setSaveSearchBoundry(boundry){
    this.drawCustomPolyline(boundry['boundry']);
  }

  showMap(){
    $(".display_none_map").addClass("show_map_mobile");
    $(".map_side_bar").addClass("hide_map_mobile");
    this.isMobileListView = false;

    if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
      if(this.mobileReZoom == true){
        this.mobileReZoom = false;
        if(this.openHouseMapGeoJson != undefined){
          this.zoom(this.map);
        }
      }
    }
    // if(this.searchBarComponent.searchLocation != undefined){
    //   this.zoom(this.map);
    // }
    this.searchBarComponent.mapListView = true;
  }

  showPropertyMarker(propertyObj){
    this.markers.filter((marker) => {
      if(marker.id == propertyObj.id){
        if(this.infoBubble.isOpen()){
          this.infoBubble.close();
        }
        this.multiInfoWindowList = [];
        var allMarkers = this.markerCluster.getMarkers();
        if (allMarkers.length != 0) {
          for (var j=0; j < allMarkers.length; j++) {
            var existingMarker = allMarkers[j];
            var pos = existingMarker.getPosition();
            if(marker.getPosition().equals(pos)) {
              this.multiInfoWindowList.push(this.markerSet[j]);
            }
          }
        }
        if(this.multiInfoWindowList.length > 1){
          this.html = this.InfoBubbleService.setHTML(this.multiInfoWindowList);
        }
        else{
          this.html = this.InfoBubbleService.changeHTML(propertyObj.property_file, propertyObj.home_price, propertyObj.bedroom, propertyObj.full_bath, propertyObj.living_area,propertyObj.street, propertyObj);
        }
        var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);

        if(pixelOffsetY != undefined && pixelOffsetY < 260){
          this.infoBubble = this.InfoBubbleService.mapInfoBubble(this.html,0,-260);
        }else{
          this.infoBubble = this.InfoBubbleService.mapInfoBubble(this.html,0,0);
        }

        this.infoBubble.open(this.map, marker);
        if(this.multiInfoWindowList.length > 1){
          setTimeout(() => {
            $(".box_on_map_event").parent().parent().parent().addClass('pop_div');
            $(".box_on_map_event").parent().parent().parent().attr('id','property_info');
          }, 20);
        }
        else{
          setTimeout(() => {
            $(".box_on_map").parent().parent().parent().addClass('pop_div');
            $(".box_on_map").parent().parent().parent().attr('id','property_info');
          }, 20);
        }
      }
    });
  }

  closeAllPorpertyMarkers(){
    if(this.infoBubble.isOpen()){
      this.infoBubble.close();
      var infowin = document.getElementsByClassName("pop_div");
      for(var i=0;i<infowin.length;i++)
      {
        infowin[i].innerHTML = ''
        $("#property_info").remove();
      }
    }
  }

  polygonErrorHandling(){
    if(this.searchBarComponent.handalMaptechError == true){
      this.clearGoogleMap(true);
      delete this.searchBarComponent.searchProperty['polygon'];
      if(this.searchBarComponent.searchProperty['geo_bounding_box'] != undefined){
        this.searchBarComponent.filterProperty();
        this.searchBarComponent.handalMaptechError = true;
      }
    }
  }

  removeSearchValue(){
    this.searchBarComponent.removeLocationValue();
  }
}
