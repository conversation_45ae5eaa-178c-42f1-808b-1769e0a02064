import { Injectable }     from '@angular/core';
import { CanActivate }    from '@angular/router';
import { AuthService } from '@app/auth/services/auth.service';
import { BaseComponent } from '@app/base/components/base.component';

@Injectable()
export class AuthGuard implements CanActivate {
    constructor(private authService: AuthService){}

    canActivate() {
        // console.log("canActivate");
        // this.authService.getUserDetails().subscribe(res=>{
        //     BaseComponent.user = res;
        //     console.log(BaseComponent.user);
        //     // return res;
        // });
        return true;
    }
}