.btn:hover{
    color: #10B8A8 !important;
}
.btn:focus{
    color: #10B8A8 !important;
}
.exportbtn:focus{
    color: #10B8A8 !important;
}
.btn1:hover{
    color: #ffffff !important;
}

.sharePropertyTitile{
    font-size: 25px !important;
    color: #676767 !important;
    line-height: 30px !important;
}
.sharable-Link{
    font-size: 18px !important;
    color: #10B8A8 !important;
    line-height: 30px !important;
    text-align: left;
}
.shareProperty{
    margin-top: 0px;
    position: absolute;
    display: none;
    right: 7px;
    top: 77px;
    width: 352px;
}
.boxzindex{
    z-index: 10 !important;
}
.shareProperty {
    background: #FFFFFF;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
    border-radius: 8px;
    width: 433px;
    display: table;
    margin: auto;
    /* padding: 15px 0px 0px 0px; */
    margin-top: 30px;
    margin-bottom: 30px;
    visibility: hidden;
}
.shareProperty .box{
    padding-left: 24px;
    padding-right: 39px;
    padding-bottom: 1px;
    padding-top: 15px;
}
.shareProperty label {
    font-size: 16px;
    color: #7A7A7A;
    letter-spacing: 0;
    font-weight: normal;
    margin: 0px;
    padding-top: 19px;
}
.shareProperty .dropdown{
    padding-top: 10px;
}
.shareProperty .line{
    padding-top: 22px;
}

.cilent-label{
    padding-top: 0px !important;
}
.shareProperty .sendbtn{
    margin-top: 15px;
}
.shareProperty i.fa.fa-facebook-square{
    color: #3B5998;
    font-size: 35px !important;
    cursor: pointer;
}
.shareProperty i.fa.fa-twitter{
    color: #42B6E7;
    font-size: 35px !important;
    margin-left: 30px;
    cursor: pointer;
}
.shareProperty .email-input{
    width: 100%;
    margin-top: 15px;
    height: 40px;
    padding: 6px;
    color: #8D8D8D;
    font-size: 16px;
    outline: 0 !important;
}
.shareProperty i.fa.fa-close{
    font-size: 28px !important;
    padding-left: 112px;
    color: #10B8A8;
    cursor: pointer;
    float: right;
}
.dot{
    font-size: 13px;
    padding-right: 2px;
    color: #71909d;
}
.dot1{
    font-size: 13px;
    padding-right: 2px;
    color: #37474f;
}
.visit-title{
    padding-left: 34px;
    color: #828282;
}
div.scrollmenu{
    width: 100% !important;
    height: 190px !important;
    overflow-x: auto !important;
    overflow-y: hidden !important;
    white-space: nowrap !important;
    padding: 0px;
    margin-top: 15px;
}
.event-box{
    display: inline-block !important;
    vertical-align: middle !important;
    width: 18% !important;
    position: relative !important;
    min-height: 1px !important;
    padding-left: 5px !important;
    padding-right: 15px !important;
    padding-top: 5px !important;
    padding-bottom: 5px !important;
}
.thusup-rating{
    margin-left: 9px !important;
    margin-bottom: 7px !important;
}
h2.bg_title{
    font-weight: 600 !important;
}
.property_des .sub_title {
    font-size: 16px;
    font-weight: 600 !important;
    color: #5A5A5A;
}
.title-bold{
    font-weight: 600;
    margin-bottom: 5px;
}
.search_submit input {
    background: #10B8A8;
    color: white;
    border: 0px;
    width: 131px !important;
    height: 32px !important;
    border-radius: 13px;
    padding: 0px;
}
.event-title-name{
    vertical-align: sub;
}
.property-view-padding{
    padding-left: 45px;
    padding-right: 45px;
}
.property-right-view-padding{
    padding-right: 50px;
}
.row-margin-right{
    margin-right: 0px;
}
.property-agent-info{
    width: 55%;
    float: right;
    margin-top: 13px !important;
}
.listing-date{
    float: right;
    color: #8D8D8D;
    padding-bottom: 24px;
}
.listing-link{
    margin-left: 12px;
    color: #3ab8a8;
    font-weight: bold;
}
.agent-broker{
    margin-bottom: 3px;
}
.new_form_group_agent{
    margin-bottom: 22px;
}
.property-agent-name{
    font-weight: bold;
    margin-bottom: 3px;
}
.property-header-right{
    margin-top: -7px !important;
}
.direction-icon{
    float: right;
}
.lender-contact .form_group{
    margin-bottom: 20px;
}
.contact-agent-btn{
    background: white !important;
    color: #3ab8a8 !important;
    border: 0px;
    width: 131px !important;
    height: 32px !important;
    border-radius: 13px;
    padding: 0px;
    border: 1px solid #3ab8a8 !important;
}
.participating_leader_img{
    height: 100px;
    width: 100px;
}
.lender{
    margin-left: 20px;
}
.lender-name{
    color: #8D8D8D;
}
.fhead-span{
    font-weight: 600 !important;
}
.listing-agent-ml{
    margin-left: -3px;
}
.direction-text{
    word-break: break-all;
}
.source-img{
  height: auto;
  width: 15rem;
  padding: 2rem 2rem 2rem 1rem;
}
.source-img img{
  width: 100%;
  height: auto;
}
.auto-align{
  padding: 0rem 2rem 3rem 1rem;
}

@media only screen and (max-width:767px) {
  .auto-align{
    padding: 0rem 2rem 0rem 1rem;
  }
  .source-img{
    padding: 0rem 2rem 0rem 1rem;
  }
}

.brokerImage {
    -webkit-filter: drop-shadow(1px 1px 0 white)
    drop-shadow(-1px -1px 0 white);
    filter: drop-shadow(1px 1px 0 white) 
    drop-shadow(-1px -1px 0 white);
}