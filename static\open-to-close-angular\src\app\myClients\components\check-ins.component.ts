import { Component, OnInit, ViewChild, On<PERSON><PERSON>roy} from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { MyClientService } from '@app/myClients/services/my-clients.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { PropertyEvent } from '@app/property-detail/models/event.model';
import { SearchService } from '@app/search/service/search.service';
import { Params } from '@angular/router/src/shared';
import * as moment from 'moment';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { ClientDetail } from '@app/myClients/model/clientdetail.model';
import { URLSearchParams } from '@angular/http';
import { ChatService } from '@app/messaging/service/chat-service';
import { AddEventComponent } from '@app/add-event/components/add-event.component';

declare var $;

@Component({
  selector: 'check-ins',
  templateUrl: '../views/check-ins.component.html',
  styleUrls: ['../css/my-clients.component.css']
})
export class CheckInsComponent extends BaseComponent implements OnInit,OnDestroy{

  @ViewChild(AddEventComponent) addEventModal: AddEventComponent;
  @ViewChild(EventModalComponent) eventModal: EventModalComponent;

  currentId: any;
  currentIndex: number;
  myClientService:MyClientService;
  searchService:SearchService;

  clientTitleObj : ClientDetail = new ClientDetail;
  public checkInList = [];
  public upComingList = [];
  public favoritesList = [];
  public mySavedSearchList = [];
  public myNoteList = [];
  public clientList = [];
  public clientId = ''; 

  updateNoteIndex;
  selectedEventObj: PropertyEvent = new PropertyEvent();
  meAgentType: boolean = false;
  brAgentType: boolean = false;
  saAgentType: boolean = false;
  closeBtn: boolean = false;
  disabledEvent: boolean = false;
  
  public checkTotalCount = 0;
  public upComTotalCount = 0;
  public favTotalCount = 0;
  public mySavedTotalCount = 0;
  public myNoteTotalCount = 0;

  public checkInIndex: number = 2;
  public upComInIndex: number = 2;
  public favoritesIndex: number = 2;
  public saveSearchIndex: number = 2;
  public myNoteIndex: number = 2;

  public checkItemPerPage:any;
  public upComItemPerPage:any;
  public favoritesItemPerPage:any;
  public saveSearchItemPerPage:any;
  public myNoteItemPerPage:any;

  public newNote :String = '';
  public disableNotebtn : Boolean = true;
  
  public chatService : ChatService;
  public routerSubscribe;

  public disableLoadMore : Boolean = false;
  public showCKLoader : Boolean = false;
  public showUPLoader : Boolean = false;
  public showFAVLoader : Boolean = false;
  public showSaveSearchLoader : Boolean = false;
  public showMNLoader : Boolean = false;

  //Upcoming Sorting
  public upSortObject : any = {};
  public upSortList :any[] = [];

  //Checkin Sorting
  public chSortObject : any = {};
  public chSortList :any[] = [];

  //Favorite Sorting
  public faSortObject : any = {};
  public faSortList :any[] = [];

  //Save Search Sorting
  public saSortObject : any = {};
  public saSortList :any[] = [];

  constructor() {
    super();
    this.myClientService = ServiceLocator.injector.get(MyClientService);
    this.searchService = ServiceLocator.injector.get(SearchService);
    this.chatService = ServiceLocator.injector.get(ChatService);
  }

  openMenu(index,id){
    this.currentIndex = index;
    this.currentId = id;
    $("#ckin_"+index+"_"+id).toggle();
  }

  ngOnInit() {
    this.loadClientDetail();
    let self = this;
    $(document).ready(function()
    {
      $(document).mouseup(function(e)
      {
        if(self.currentId != undefined && self.currentIndex != undefined){
          $("#ckin_"+self.currentIndex+"_"+self.currentId).hide();
          self.currentId = undefined;
          self.currentIndex = undefined;
        }
      });
    });
  }

  loadClientDetail(){
    if(this.myClientService.getClientObj() != undefined){
      this.clientTitleObj = this.myClientService.getClientObj();
      this.LoadData(this.clientTitleObj['clientId']);

      this.routerSubscribe = this.route.queryParams.subscribe((params:Params)=>{
        if(params['isNote'] != undefined || params['isNote'] != null){
            $('#clientTable .myclient_navbar li:eq(' + 4 + ')').tab('show');  
        }
        else{
          $('#clientTable .myclient_navbar li:eq(' + 0 + ')').tab('show');  
        }
        this.clientId = params['client'];
        this.setPreviousScreen('/my-clients/check-ins?client='+this.clientId);
      });
    }
    else{
      this.routerSubscribe = this.route.queryParams.subscribe((params:Params)=>{
        if(params['client'] != undefined || params['client'] != null){
          this.getClientDetail(params['client']);
          this.clientId = params['client'];
          this.setPreviousScreen('/my-clients/check-ins?client='+this.clientId);
        }
      });
    }
  }

  LoadData(clientId){
    let clientParams = new URLSearchParams();
    clientParams.set("page_no", "1");
    clientParams.set('client_id',clientId);
    clientParams.set("type", "CH");
    var todayDate = moment().utc().format('YYYY-MM-DD');
    clientParams.set('today_date',todayDate.toString());
    this.getClientActivityList(clientParams, "CH");
    this.showCKLoader = true;
    
    clientParams.set("type", "UP");
    this.getClientActivityList(clientParams, "UP");
    this.showUPLoader = true;
    
    clientParams.set("type", "FA");
    this.getClientActivityList(clientParams, "FA");
    this.showFAVLoader = true;
    
    clientParams.set("type", "SA");
    this.getClientActivityList(clientParams, "SA");
    this.showSaveSearchLoader = true;
    
    clientParams.set("type", "MN");
    this.getClientActivityList(clientParams, "MN");
    this.showMNLoader = true;
  }

  getClientActivityList(eventTypeParams, type){
    this.myClientService.getClientActivity(eventTypeParams).subscribe(res => {
      if(type == "CH"){
        this.showCKLoader = false;
        this.checkInList = res['result']['records'];
        this.checkTotalCount = res['result']['total_records_count'];
        this.checkItemPerPage = res['result']['items_per_page'];
      }else if(type == "UP"){
        this.showUPLoader = false;
        this.upComingList = res['result']['records'];
        this.upComTotalCount = res['result']['total_records_count'];
        this.upComItemPerPage = res['result']['items_per_page'];
      }else if(type == "FA"){
        this.showFAVLoader = false;
        this.favoritesList = res['result']['records'];
        this.favTotalCount = res['result']['total_records_count'];
        this.favoritesItemPerPage = res['result']['items_per_page'];
      }else if(type == "SA"){
        this.showSaveSearchLoader = false;
        this.mySavedSearchList = res['result']['records'];
        this.mySavedTotalCount = res['result']['total_records_count'];
        this.saveSearchItemPerPage = res['result']['items_per_page'];
      }else if(type == "MN"){
        this.showMNLoader = false;
        this.myNoteList = res['result']['records'];
        this.myNoteTotalCount = res['result']['total_records_count'];
        this.myNoteItemPerPage = res['result']['items_per_page'];
      }
    },err => {
      this.errorResponse(err.json());
    });
  }
 
  getClientDetail(client_id){
    let clientParams = new URLSearchParams();
    clientParams.set("client_id", client_id);
    this.myClientService.getSingleClient(clientParams).subscribe(res =>{
      var clientDetailObj = {
        'clientId' : res['result']['user_id'],
        'clientNameImage' : this.clientNameImage(res['result']['user_name']),
        'clientFullName' : res['result']['user_name'],
        'timeAsClient' : this.getClientTime(res['result']['time_as_client']),
        'messageName'  : this.getClientFirstName(res['result']['user_name']),
        'profileImage' : res['result']['profile_image']
       }
      this.clientTitleObj = clientDetailObj;
      this.LoadData(this.clientId);
      $('#clientTable .myclient_navbar li:eq(' + 0 + ')').tab('show');
    },err => {
      this.errorResponse(err.json());
    });
  }

  eventDetailView(event,eventType: string = ""){
    this.addEventModal.manageEventDetailView(event,eventType);
  }
  
  runSearch(search){
    this.searchService.setMyClientSearch(search);
    this.routeOnUrl('search');  
  }

  isValidNote(){
    if(this.newNote.trim().length != 0){
      this.disableNotebtn = false;
    }
    else{
      this.disableNotebtn = true;
    }
  }

  SaveNote(){
    let noteParams = new URLSearchParams();
    noteParams.set("note",this.newNote.toString());
    noteParams.set('client',this.clientId);
    this.myClientService.addNewNote(noteParams).subscribe(res =>{
      this.successResponse(res);
      var resObj ={
        'client' : res['result']['client'],
        'updated_date_time' : new Date(),
        'note' : res['result']['note'],
        'id' : res['result']['id'],
        'user' : res['result']['user']
      }
      this.myNoteList.push(resObj);
      this.newNote = '';
    },err => 
      this.errorResponse(err.json())
    )
  }

  utcDateFormat(date){
    return moment(date).format('MM.DD.YYYY');
  }

  reviewRatings(event){
    this.eventModal.openRatingModal(event,false);
  }

  showNote(note, manageType, index){
    if(manageType == "UPDATE"){
      this.updateNoteIndex = index;
    }
    else if(manageType == "DELETE"){
      let deleteNoteParams = new URLSearchParams();
      deleteNoteParams.set('note_id', note.id);
      this.myClientService.deleteNote(deleteNoteParams).subscribe(res => {
        this.successResponse(res);
        this.myNoteList.splice(index, 1);
      },err => this.errorResponse(err.json()));
    }
  }

  manageNote(selectedNote, type, updatedNotes, noteIndex){
    if(type == 'UPDATE'){
      let updateNoteParams = new URLSearchParams();
      updateNoteParams.set('note', updatedNotes);
      updateNoteParams.set('id', selectedNote.id);
      this.myClientService.updateNote(updateNoteParams).subscribe(res => {
        this.successResponse(res);
        this.myNoteList[noteIndex].note = updatedNotes;
        this.updateNoteIndex = undefined;
      }, err => this.errorResponse(err.json()));
    }
    else if(type == 'CANCEL'){
      this.updateNoteIndex = undefined;
    }
  }

  loadMoretList(listType, index){
    let sortList = [];
    let clientLoadMoreParams = new URLSearchParams();
    var todayDate = moment().utc().format('YYYY-MM-DD');
    clientLoadMoreParams.set('today_date',todayDate.toString());
    clientLoadMoreParams.set("page_no", index);
    clientLoadMoreParams.set('client_id',this.clientId);
    clientLoadMoreParams.set("type", listType);
    clientLoadMoreParams.delete("sort_list");
    this.disableLoadMore = true;

    if(listType == "CH"){
      if(this.chSortList.length !=0){
        sortList = this.chSortList;
        clientLoadMoreParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(listType == "UP"){
      if(this.upSortList.length !=0){
        sortList = this.upSortList;
        clientLoadMoreParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(listType == "FA"){
      if(this.faSortList.length !=0){
        sortList = this.faSortList;
        clientLoadMoreParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(listType == "SA"){
      if(this.saSortList.length !=0){
        sortList = this.saSortList;
        clientLoadMoreParams.set('sort_list', JSON.stringify(sortList));
      }
    }

    this.myClientService.getClientActivity(clientLoadMoreParams).subscribe(res => {
      this.disableLoadMore = false;
      if(listType == 'CH'){
        res['result']['records'].forEach(record => {
          this.checkInList.push(record);
        });
          this.checkTotalCount = res['result']['total_records_count'];
          this.checkItemPerPage = res['result']['items_per_page'];
          this.checkInIndex++;
      }
      if(listType == 'UP'){
        res['result']['records'].forEach(record => {
          this.upComingList.push(record);
        });
          this.upComTotalCount = res['result']['total_records_count'];
          this.upComItemPerPage = res['result']['items_per_page'];
          this.upComInIndex++;
      }
      if(listType == 'FA'){
        res['result']['records'].forEach(record => {
          this.favoritesList.push(record);
        });
          this.favTotalCount = res['result']['total_records_count'];
          this.favoritesItemPerPage = res['result']['items_per_page'];
          this.favoritesIndex++;
      }
      if(listType == 'SA'){
        res['result']['records'].forEach(record => {
          this.mySavedSearchList.push(record);
        });
          this.mySavedTotalCount = res['result']['total_records_count'];
          this.saveSearchItemPerPage = res['result']['items_per_page'];
          this.saveSearchIndex++;
      }
      if(listType == 'MN'){
        res['result']['records'].forEach(record => {
          this.myNoteList.push(record);
        });
          this.myNoteTotalCount = res['result']['total_records_count'];
          this.myNoteItemPerPage = res['result']['items_per_page'];
          this.myNoteIndex++;
      }
    },err => {
      console.log(err)
    });
  }

  messageClient(selectedClient){
    var client = {};
    client['user_name'] = selectedClient['clientFullName'];
    client['profile_image'] = selectedClient['profileImage'];
    client['chat_thread_id'] = selectedClient['clientId'];
    client['receiver_id'] = selectedClient['clientId'];
    client['last_message_time'] = "";
    client['last_message'] = '';
    this.chatService.setClientChatThread(client);
    this.router.navigate(['messaging']);
  }

  clientEventSortting(listingType,filedName){
    let sortList = [];
    if(listingType == 'CH'){
      this.checkInIndex = 2;
      if(this.chSortObject[filedName] == undefined){
        this.chSortObject[filedName] = true;
        $('#CH_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.chSortObject[filedName] = this.chSortObject[filedName] === true ? false : true;
       if(this.chSortObject[filedName]){
        $('#CH_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png'); 
       }else{
        $('#CH_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.chSortList[0] = this.chSortObject;
      sortList = this.chSortList;
    }

    if(listingType == 'UP'){
      this.upComInIndex = 2;
      if(this.upSortObject[filedName] == undefined){
        this.upSortObject[filedName] = true;
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.upSortObject[filedName] = this.upSortObject[filedName] === true ? false : true;
       if(this.upSortObject[filedName]){
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png'); 
       }else{
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.upSortList[0] = this.upSortObject;
      sortList = this.upSortList;
    }

    if(listingType == 'FA'){
      this.favoritesIndex = 2;
      if(this.faSortObject[filedName] == undefined){
        this.faSortObject[filedName] = true;
        $('#FA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.faSortObject[filedName] = this.faSortObject[filedName] === true ? false : true;
       if(this.faSortObject[filedName]){
        $('#FA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png'); 
       }else{
        $('#FA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.faSortList[0] = this.faSortObject;
      sortList = this.faSortList;
    }

    if(listingType == 'SA'){
      this.saveSearchIndex = 2;
      if(this.saSortObject[filedName] == undefined){
        this.saSortObject[filedName] = true;
        $('#SA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.saSortObject[filedName] = this.saSortObject[filedName] === true ? false : true;
       if(this.saSortObject[filedName]){
        $('#SA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png'); 
       }else{
        $('#SA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.saSortList[0] = this.saSortObject;
      sortList = this.saSortList;
    }

    let clientEventSorting = new URLSearchParams;
    
    clientEventSorting.set("type", listingType);
    clientEventSorting.set("client_id", this.clientId);
    clientEventSorting.set("page_no", "0");
    clientEventSorting.set('sort_list', JSON.stringify(sortList));
    var todayDate = moment().utc().format('YYYY-MM-DD');
    clientEventSorting.set('today_date',todayDate.toString());
    
    this.getClientActivityList(clientEventSorting, listingType);
  }

  ngOnDestroy(){
    if(this.routerSubscribe != undefined){
      this.routerSubscribe.unsubscribe();
    }
  }
}
