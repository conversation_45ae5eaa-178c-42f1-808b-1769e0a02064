import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { Brokerage } from '@app/profile/models/brokerage';
import { BrokerageDetail, Plan,BrokerageResponse} from '@app/profile/models/brokerage-detail';
import { HomeBuyer} from '@app/profile/models/home-buyer';
import { HomeBuyerDetail,HomeBuyerResponse} from '@app/profile/models/home-buyer-detail';
import { Agent } from '@app/profile/models/agent';
import { AgentDetail,AgentResponse} from '@app/profile/models/agent-detail';
import { Lender, LenderResponse } from '@app/profile/models/lender';
import { LenderDetail } from '@app/profile/models/lender-detail';
import { Message } from '@app/base/model/message';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { Observable } from 'rxjs/Observable';
import { ApiService } from '@app/base/services/api.service';
import { Plans,PlanResponse} from '@app/profile/models/plan'
import { BillingInfo,UpdateBillingInfoResponse} from '@app/profile/models/update-billininfo';
import { State, ZipCode, City } from '@app/profile/models/profile';
import { StateResponse } from '@app/profile/models/state-response';
import { ZipCodeResponse } from '@app/profile/models/zip-code-response';
import { CityResponse } from '@app/profile/models/city-response';
import { UpdatePaymentMethod, UpdatePaymentMethodResponse } from '@app/profile/models/update-payment';
import { PlansDetails} from '@app/admin/adminPlans/model/plans-details';
import { from } from 'rxjs/observable/from';
import { ApiResponse } from '@app/auth/models/api-response';
import { ResponseContentType, Http, Request, Headers  } from '@angular/http';
@Injectable()
export class ProfileService {

  public baseservice:BaseComponent;
  public apiService:ApiService;

  constructor(public http:Http) {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
   }

  public brokerageUpdate(brokerageUpdate:Brokerage): Observable <BrokerageResponse>{
    console.log("BROERAGE UPDATE", brokerageUpdate);
    let options =this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['profile']['update'],brokerageUpdate);
    return this.apiService.apiCall(options);
  }

  searchCity(cityName):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['searchCity'],{},cityName.toString());
    return this.apiService.apiCall(options);
  }

  searchZipCode(zipCode):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['search']['searchZipCode'],{},zipCode.toString());
    return this.apiService.apiCall(options);
  }

  public homeBuyerUpdate(homeBuyerUpdate): Observable <HomeBuyerResponse>{
    let options= this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['profile']['update'],homeBuyerUpdate);
    return this.apiService.apiCall(options);
  }
  
  public agentUpdate(agentUpdate:Agent): Observable <AgentResponse>{
    console.log("AGENT UPDATE", agentUpdate);
    let options= this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['profile']['update'],agentUpdate);
    return this.apiService.apiCall(options);
  }

  public brokerageDetails(): Observable <BrokerageResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['update'],{});
    return this.apiService.apiCall(options);
  }

  public homeBuyerDetail(): Observable <HomeBuyerResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['update'],{});
    return this.apiService.apiCall(options);
  }

  public agentDetail():Observable <AgentResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['update'],{});
    return this.apiService.apiCall(options);
  }

  public getStates(stateCode):Observable <StateResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['state']+stateCode,{});
    return this.apiService.apiCall(options);
  }

  public getCities(id):Observable <CityResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['city']+id,{});
    return this.apiService.apiCall(options);
  }

  public getZipCodes(id):Observable <ZipCodeResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['zipCode']+id,{});
    return this.apiService.apiCall(options);
  }

  public getPlans():Observable<PlanResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['purchase']['getPlans'],{});
    return this.apiService.apiCall(options);
  }

  public updatePlans(allPlans):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['purchase']['updatePlans'],allPlans);
    return this.apiService.apiCall(options);
  }

  public updateBillingInfo(billingUpdate:BillingInfo): Observable<UpdateBillingInfoResponse>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['profile']['billing-info'],billingUpdate);
    return this.apiService.apiCall(options);
  }

  public updatePaymentMethod(paymentMethod:UpdatePaymentMethod): Observable<UpdatePaymentMethodResponse>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['profile']['updatePaymentMethod'],paymentMethod);
    return this.apiService.apiCall(options);
  }

  public uploadUserImage(formData): Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['profile']['uploadProfileImage'],formData, null, null, null, true);
    return this.apiService.apiCall(options);
	}

  public cancelSubscription():Observable<ApiResponse>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['DELETE'],API_REFERENCE['profile']['cancelSubscription'],{});
    return this.apiService.apiCall(options);
  }

  public getInvitesAgent():Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['profile']['BRAgentInvites'],{},null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public UpdateAgentInvitesStatus(agent):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['profile']['updateAgentInviteStatus'],agent.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
  
  public agentInvite(email):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['profile']['inviteAgent'],email.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public lenderDetails(): Observable <LenderResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['update'],{});
    return this.apiService.apiCall(options);
  }
  
  public lenderUpdate(lenderUpdate:Lender): Observable <Message>{
    let options= this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['profile']['update'],lenderUpdate);
    return this.apiService.apiCall(options);
  }

  public lenderSearch(searchParams):Observable<any>{
    let options= this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['lenderSearch'], {},searchParams.toString());
    return this.apiService.apiCall(options);
  }

  public addRemoveLender(lenderId):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['profile']['manageLender'],lenderId.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public verifyAgentSatatus():Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['profile']['agentVerification'],{});
    return this.apiService.apiCall(options);
  }

  public getSubscriptionPurchaseHistory(getType):Observable<any>{
    let options= this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['subscriptionHistory']+'?get_all='+getType,{});
    return this.apiService.apiCall(options);
  }

  public genratePurchaseHistoryPdf(id):Observable<any>{
    let options= this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['downLoadSubscriptionHistory']+'?purchase_transaction_id='+id,{}, null, ResponseContentType.Blob);
    return this.apiService.downloadFile(options);
  }

  public getInvitesAgentSorting(params):Observable<Message>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['profile']['BRAgentInvites'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public getBrokerEmail(email):Observable<any>{
    let options= this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['profile']['getBrokerEmail']+'?broker_email='+email,{});
    return this.apiService.apiCall(options);
  }

  public wiseAgentCreateAccount(formValue):Observable<any>{
    let options= this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['wiseAgent']['signup'],{
      wise_agent_api_key: formValue.wiseAgentKey
    });
    return this.apiService.apiCall(options);
  }

}
