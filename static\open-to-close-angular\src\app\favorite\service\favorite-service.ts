import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ApiService } from '@app/base/services/api.service';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiResponse } from '@app/auth/models/api-response';

@Injectable()
export class FavoriteService {

  public baseservice:BaseComponent;
  public apiService:ApiService;
  
  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
   }

  getMyFavorites(urlParmas): Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['favorites']['getFavoritesProperty'],urlParmas.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);   
  }
  removeFavorite(property):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['saveToFavorite'],property.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
  manageFavorite(property):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['saveToFavorite'],property.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
  
  setFavourite(status:any, property_id: String, list: any[], index: any){
    let urlParams = new URLSearchParams();
    urlParams.set("is_favourite",status.toString());
    urlParams.set("property_id",property_id.toString());
    this.manageFavorite(urlParams).subscribe(res =>{
      this.baseservice.successResponse(res);
      list[index]['is_favourite'] = !list[index]['is_favourite'];
    },err =>{
      this.baseservice.errorResponse(err);
      list[index]['is_favourite'] = list[index]['is_favourite'];
    })
    return list[index]['is_favourite'];
  }

  getUpdatedPropertyInfo(propertyParams){
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['search']['getUpdatedInfo'],propertyParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  getPropertyFile(id):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['property']['propertyFile']+id.toString(),{});
    return this.apiService.apiCall(options);
  }
}