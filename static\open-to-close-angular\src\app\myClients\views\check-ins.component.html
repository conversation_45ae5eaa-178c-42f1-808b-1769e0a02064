<div>
	<header></header>
	</div>

	<div>
		<div class="myclient_page header_fix">
			<div class="container">
				<div class="ls_group mt-20">
					<span *ngIf="clientTitleObj.profileImage == ''">
						<div class="ls dis_inline">{{clientTitleObj.clientNameImage}}</div>
					</span>
					<span *ngIf="clientTitleObj.profileImage != ''">
						<img [src]="clientTitleObj.profileImage" class="client-profile-pic search-agent-event symbols-property-image dis_inline profile-single-img" alt="">
					</span>
					<div class="title_group">
						<div class="title">{{clientTitleObj?.clientFullName}}</div>
						<div class="sub_title time-client-w">Client for <span>{{clientTitleObj?.timeAsClient}}</span> </div>
					</div>
					<a (click)="messageClient(clientTitleObj)"><div class="Message_Lilly pull-right">Message {{clientTitleObj?.messageName}}</div></a>
				</div>
			<div id="clientTable" class="my_client_table_group">
				<div class="myclient_navbar">
					<ul>
					<li data-toggle="pill" href="#menu1">Past</li>
					<li data-toggle="pill" href="#menu2">Upcoming</li>
					<li data-toggle="pill" href="#menu3">Favorites</li>
					<li data-toggle="pill" href="#menu4">Saved Searches</li>
					<li data-toggle="pill" href="#menu5">My Notes</li>
					</ul>
				</div>
				<div class="tab-content">
					<div id="menu1"  class="event-list tab-pane fade in active table-responsive selected_saved">
						<div class="No_matches" *ngIf="showCKLoader == true">
							<div class="loader">
							<div class="message">Loading...</div>
							<div class="dots"><div class="center"></div></div>
							</div>
						</div>
						<div *ngIf="checkInList.length == 0 && showCKLoader == false" class="No_matches">
							<div class="title">No matches</div>
							<div class="text">You don’t have any listings that meet your search criteria. Try <br> running a new search or add a new listing.</div>
						</div>
						<div *ngIf="checkInList.length != 0" class="property-list-table">
							<table class="table">
								<thead>
									<tr>
									<th (click)="clientEventSortting('CH', 'PR')">Property <img id="CH_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									<th (click)="clientEventSortting('CH', 'ET')">Type <img id="CH_ET" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									<th (click)="clientEventSortting('CH', 'DT')">Date & Time <img id="CH_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									<th (click)="clientEventSortting('CH', 'LS')">Status<img id="CH_LS" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									<th (click)="clientEventSortting('CH', 'LA')">Listing Agent<img id="CH_LA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									<th (click)="clientEventSortting('CH', 'RA')" colspan="3">Rating<img id="CH_RA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									</tr>
								</thead>
								<tbody>
									<tr *ngFor="let event of checkInList; let ck = index">
											<td class="cursor-pointer" (click)="gotToPropertyDetail('my-clients/property-detail',event.property_id)">
											<span class="vertical-align-top" *ngIf="event.property_file != ''"><img [src]="event.property_file" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
											<span class="vertical-align-top" *ngIf="event.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
											<div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold new-line">{{event?.address}} <br></span>{{event?.location}}</div>
											</td>
											<td>
											<div [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">{{event?.event_type_msg}}</div>
											</td>
											<td><span class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}}<br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>
											<td> <div class="font_color font_semibold">{{event?.property_status}}</div> </td>
											<td>
											<span *ngIf="event?.open_house_agent_image != ''"><img src="{{event.open_house_agent_image}}" class="search-agent-event symbols-property-image dis_inline" alt=""></span>
											<span *ngIf="event?.open_house_agent_image == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
											<div class="dis_inline po_rel"><span class="dark font_semibold u-name-table">{{event?.open_house_agent_name}} <br></span></div>
											</td>
											<td>
												<div *ngIf="event.property_rate.rating == 1">
													<img src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png" class="symbols-glyph-checkin-thumbsup dis_inline" alt="">
												</div>
												<div *ngIf="event.property_rate.rating == 0">
													<img src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown.png" class="symbols-glyph-checkin-thumbsup dis_inline" alt="">
												</div>
											<td class="action-view">
											<div class="save_notes margin_zero" (click)="reviewRatings(event)">Review</div>
											</td>
											<td class="action-option">
											<div class="open_click_menu" (click)="openMenu(ck,event.event_id)">
												<img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
												<ul id="ckin_{{ck}}_{{event.event_id}}" class="click_menu_open events">
													<li class="cursor-pointer option-menu" (click)="gotToPropertyDetail('my-clients/property-detail',event.property_id)">Property Detail</li>
													<li class="cursor-pointer option-menu" (click)="eventDetailView(event,'past')">Event Detail</li>
												</ul>
											</div>
											</td>
									</tr>
								</tbody>
							</table>
							<div *ngIf="checkTotalCount > checkItemPerPage &&  checkTotalCount != checkInList.length" class="new_form_group load_more_btn">
								<input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoretList('CH', checkInIndex)" value="Load More">
							</div>
						</div>
					</div>

					<div id="menu2" class="tab-pane fade table-responsive selected_saved">
						<div class="No_matches" *ngIf="showUPLoader == true">
							<div class="loader">
							<div class="message">Loading...</div>
							<div class="dots"><div class="center"></div></div>
							</div>
						</div>
						<div *ngIf="upComingList.length == 0 && showUPLoader == false" class="No_matches">
							<div class="title">No matches</div>
							<div class="text">You don’t have any listings that meet your search criteria. Try <br> running a new search or add a new listing.</div>
						</div>
						<div *ngIf="upComingList.length != 0" class="property-list-table">
							<table class="table">
								<thead>
									<tr>
										<th (click)="clientEventSortting('UP', 'PR')">Property <img id="UP_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('UP', 'ET')">Type <img id="UP_ET" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('UP', 'DT')">Date & Time <img id="UP_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('UP', 'OA')" colspan="5">Open House Agent<img id="UP_OA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									</tr>
								</thead>
								<tbody>
									<tr *ngFor="let event of upComingList; let i = index">
										<td class="cursor-pointer" (click)="gotToPropertyDetail('my-clients/property-detail',event.property_id)">
												<span class="vertical-align-top" *ngIf="event.property_file != ''"><img [src]="event.property_file" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
												<span class="vertical-align-top" *ngIf="event.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline vertical-align-top" alt=""> </span>
												<div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold new-line">{{event?.address}} <br></span>{{event?.location}}</div>
										</td>
										<td>
												<div [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">{{event?.event_type_msg}}</div>
										</td>
										<td><span class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}} <br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>
										<td>
												<span *ngIf="event.open_house_agent_image != ''"><img [src]="event.open_house_agent_image" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
												<span *ngIf="event.open_house_agent_image == ''"><img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
												<div class="dis_inline po_rel"><span class="dark font_semibold u-name-table">{{event.open_house_agent_name}} <br></span></div>
										</td>
										<td><div class="bold_font going-text">{{event?.going_count}}</div></td>
										<td>going</td>
										<td class="action-view">
												<div class="save_notes margin_zero" (click)="eventDetailView(event,'past')">Event Detail</div>
										</td>
										<td class="action-option">
										<div class="open_click_menu"  (click)="openMenu(event.event_id,i)">
												<img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more" alt="">
												<ul id="ckin_{{event.event_id}}_{{i}}" class="click_menu_open events">
													<li class="cursor-pointer option-menu" (click)="gotToPropertyDetail('my-clients/property-detail',event.property_id)">Property Detail</li>
												</ul>
										</div>
										</td>
									</tr>
								</tbody>
							</table>
							<div *ngIf="upComTotalCount > upComItemPerPage &&  upComTotalCount != upComingList.length" class="new_form_group load_more_btn">
								<input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoretList('UP', upComInIndex)" value="Load More">
							</div>
						</div>
					</div>

					<div id="menu3" class="tab-pane fade table-responsive selected_saved">
						<div class="No_matches" *ngIf="showFAVLoader == true">
							<div class="loader">
							<div class="message">Loading...</div>
							<div class="dots"><div class="center"></div></div>
							</div>
						</div>
						<div *ngIf="favoritesList.length == 0 && showFAVLoader == false" class="No_matches">
							<div class="title">No matches</div>
							<div class="text">You don’t have any listings that meet your search criteria. Try <br> running a new search or add a new listing.</div>
						</div>
						<div *ngIf="favoritesList.length != 0" class="property-list-table">
							<table class="table">
								<thead>
									<tr>
										<th (click)="clientEventSortting('FA', 'PR')">Property <img id="FA_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('FA', 'HP')">Price <img id="FA_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('FA', 'HT')">Type <img id="FA_HT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('FA', 'LA')" colspan="2">Listing Agent <img id="FA_LA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									</tr>
								</thead>
								<tbody>
									<tr *ngFor="let favorite of favoritesList">
										<td class="cursor-pointer" (click)="gotToPropertyDetail('my-clients/property-detail',favorite.property_id)">
										<span class="vertical-align-top" *ngIf="favorite.property_file != ''"><img [src]="favorite.property_file" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
										<span class="vertical-align-top" *ngIf="favorite.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
										<div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold new-line">{{favorite?.address}} <br></span>{{favorite?.location}}</div>
										</td>
										<td><span class="font_semibold">{{favorite.property_price | currency:"":symbol:"1.0"}}</span></td>
										<td><span class="font_semibold">{{favorite.property_type}}</span></td>
										<td>
										<span *ngIf="favorite.open_house_agent_image != ''"><img [src]="favorite.open_house_agent_image" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
										<span *ngIf="favorite.open_house_agent_image == ''"><img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
										<div class="dis_inline po_rel"><span class="dark font_semibold u-name-table">{{favorite.open_house_agent_name}} <br></span></div>
										</td>
										<td class="action-view">
										<div class="save_notes margin_zero" (click)="gotToPropertyDetail('my-clients/property-detail',favorite.property_id)">Property Detail</div>
										</td>
									</tr>
								</tbody>
							</table>
							<div *ngIf="favTotalCount > favoritesItemPerPage && favTotalCount != favoritesList.length" class="new_form_group load_more_btn">
								<input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoretList('FA', favoritesIndex)" value="Load More">
							</div>
						</div>
					</div>

					<div id="menu4" class="tab-pane fade table-responsive selected_saved font_new_class">
						<div class="No_matches" *ngIf="showSaveSearchLoader == true">
							<div class="loader">
							<div class="message">Loading...</div>
							<div class="dots"><div class="center"></div></div>
							</div>
						</div>
						<div *ngIf="mySavedSearchList.length == 0 && showSaveSearchLoader == false" class="No_matches">
							<div class="title">No matches</div>
							<div class="text">You don’t have any listings that meet your search criteria. Try <br> running a new search or add a new listing.</div>
						</div>
						<div *ngIf="mySavedSearchList.length != 0" class="property-list-table">
							<table class="table">
								<thead>
									<tr>
										<th (click)="clientEventSortting('SA', 'LC')">Location <img id="SA_LC" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('SA', 'HP')">Price <img id="SA_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('SA', 'HT')">Home Type <img id="SA_HT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('SA', 'ST')">Status <img id="SA_ST" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('SA', 'BD')">Beds <img id="SA_BD" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('SA', 'BT')">Baths <img id="SA_BT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
										<th (click)="clientEventSortting('SA', 'SF')"colspan="2">Sqft <img id="SA_SF"src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
									</tr>
								</thead>
								<tbody>
									<tr *ngFor="let search of mySavedSearchList">
										<td><span class="font_semibold">{{search.location}}</span><br> </td>
										<td>
										<span class="font_semibold" *ngIf="search.max_price !=0 && search.max_price != -1">${{priceFormat(search.min_price)}}-${{priceFormat(search.max_price)}}</span>
										<span *ngIf="search.min_price ==0 && search.max_price ==0"></span>
										</td>
										<td class="td-width">
										<span *ngFor="let type of search.property_type; let i=index">
										<span class="font_semibold" *ngIf="search.property_type.length-1 != i">
											{{type}} |
										</span>
										<span class="font_semibold" *ngIf="search.property_type.length-1 == i">
											{{type}}
										</span>
										</span>
										</td>
										<td class="td-width">
										<span *ngFor="let type of search.property_status; let i=index">
											<span class="font_semibold" *ngIf="search.property_status.length-1 != i">
													{{type}} |
											</span>
											<span class="font_semibold" *ngIf="search.property_status.length-1 == i">
													{{type}}
											</span>
										</span>
										</td>
										<td>
										<span *ngIf="search.bedroom !=0 && search.bedroom != -1">
											<span class="font_semibold">{{search.bedroom}}</span>
											<img src="{{imagePrefix}}Bedroom.png" alt="">
										</span>
										</td>
										<td>
										<span *ngIf="search.bathroom !=0 && search.bathroom != -1">
										<span class="font_semibold">{{search.bathroom}}</span>
										<img src="{{imagePrefix}}Bathroom.png" alt="">
										</span>
										</td>
										<td>
										<span class="font_semibold" *ngIf="search.max_sqft !=0 && search.min_sqft != -1 && search.max_sqft != -1">
											{{search.min_sqft}}-{{search.max_sqft}} sqft
										</span>
										</td>
										<td class="action-view">
										<div class="save_notes margin_zero" (click)="runSearch(search)">Run Search</div>
										</td>
									</tr>
								</tbody>
							</table>
							<div *ngIf="mySavedTotalCount > saveSearchItemPerPage && mySavedTotalCount != mySavedSearchList.length" class="new_form_group load_more_btn">
								<input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoretList('SA', saveSearchIndex)" value="Load More">
							</div>
						</div>
					</div>

					<div id="menu5" class="tab-pane fade table-responsive selected_saved">
						<div class="my_client_table">
							<textarea name="" id="" cols="30" rows="10" (keyup)="isValidNote()" [(ngModel)]="newNote" class="my_client" placeholder="Type here to add a new note…."></textarea>
							<div *ngIf="disableNotebtn == true" class="save_notes submit-disable">Save Note</div>
							<div *ngIf="disableNotebtn == false" class="save_notes" (click)="SaveNote()">Save Note</div>
							<div class="row"></div>
							<div class="my_client_lists">
								<div class="No_matches my_client_load" *ngIf="showMNLoader == true">
									<div class="loader">
									<div class="message">Loading...</div>
									<div class="dots"><div class="center"></div></div>
									</div>
								</div>
								<div class="my_client_list" *ngFor="let note of myNoteList; let i = index">
									<div class="row">
									<div class="col-sm-4">
										<div class="date">{{utcDateFormat(note.date)}}</div>
									</div>
									<div class="col-sm-9">
										<div *ngIf="updateNoteIndex != i" class="text saved-note">{{note.note}}</div>
										<div *ngIf="updateNoteIndex == i">
												<textarea class="note-textarea" [value]="note.note" #noteValue id="note.id" cols="80" rows="3"></textarea>
												<div class="save_notes save-note" (click)="manageNote(note, 'UPDATE', noteValue.value, i)">Save Note</div>
												<a (click)="manageNote(note, 'CANCEL', '', i)"class="Cancel client cancel-note">Cancel</a>
										</div>
									</div>
									<div class="col-sm-3">
										<div class="edit">
												<i (click)="showNote(note, 'UPDATE', i)" class="fa fa-pencil edit_img"></i>
												<i (click)="showNote(note, 'DELETE', i)" class="fa fa-trash-o edit_img"></i>
										</div>
									</div>
									</div>
								</div>
								<div *ngIf="myNoteTotalCount > myNoteItemPerPage && myNoteTotalCount != myNoteList.length" class="new_form_group load_more_btn">
									<input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoretList('MN', myNoteIndex)" value="Load More">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		</div>
	</div>

	<add-event></add-event>
	<event-modal></event-modal>

	<div>
	<footer></footer>
	</div>
