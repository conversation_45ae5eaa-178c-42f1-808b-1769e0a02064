export class Plans{
    trial_period:String;
    currency_iso_code:String;
    created_at:String;
    id:String;
    billing_day_of_month:String;
    updated_at:String;
    description:String;
    name:string;
    add_ons:Add_ons[];
    trial_duration_unit:String;
    discounts:discounts[];
    price:String = '';
    merchant_id:String;
    billing_frequency:any;
    index:String;
    category:String;
    s_liscence:String;
    e_liscence:String;

}
export class Add_ons{

}
export class discounts{

}

export class PlanResponse{
    result: Plans[];
    message: String;
    status: any;
    statusCode: any;
}