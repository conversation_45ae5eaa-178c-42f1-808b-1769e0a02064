import { <PERSON>mpo<PERSON>,<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t,ViewChild,NgZ<PERSON> } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { MapInfoBubbleService } from '@app/base/services/map-info-bubble.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { SearchService } from '@app/search/service/search.service';
import { SearchBarComponent } from '@app/searchBar/component/search-bar.component';
import { HeaderComponent } from '@app/root/components/header.component';
import { FavoriteService } from '@app/favorite/service/favorite-service';
import { CommentStmt } from '@angular/compiler/src/output/output_ast';

declare var google;
declare var $;
declare var InfoBubble;
declare const MarkerClusterer: any;

@Component({
  selector: 'app-search',
  templateUrl: '../views/search.component.html',
  styleUrls: ['../css/search.component.css']
})
export class SearchComponent extends BaseComponent implements OnInit,DoCheck{

  @ViewChild(EventModalComponent) eventModal: EventModalComponent;
  @ViewChild(SearchBarComponent) searchBarComponent: SearchBarComponent;

  markerSet =[];

  itemsPerPage:any;

  public map;
  public poly;
  public mapPolygons: any;
  public freeHandPolygons = [];
  public matchMarker = [];
  public markers = [];
  public infoBubble: any;
  public lat=40.730610;
  public lng=-73.935242;
  public currentLatLng;
  public positionStatus:Boolean = true;
  infoBubbleService : MapInfoBubbleService;
  public geolocationPosition;
  houseImage = this.imagePrefix+ "symbols-map-hover.png";
  html:any = "";
  anyDateList = ['option 1','option 1','option 1'];
  public selectedEventDetails:String;
  public searchService : SearchService;
  favoriteService  : FavoriteService;
  public searchRequest = [];
  public propertyList = [];
  pageCount: number = 1;
  searchObj : String;
  public searchPageNo = 0;
  public searchListType = 0;
  public totalPorpertyCount = 0;
  public showSavedSearch:String = "listingAgent";
  public SaveSearchbutton : Boolean = true;
  public isLogin = false;
  public markerCluster;

  public autoMapPosition : Boolean = true;
  public showCancelDraw: Boolean = false;
  public showMapLoading: Boolean = false;
  public addLoaderClass: Boolean = true;
  public allowAddGeoJson : Boolean = true;
  public propertyFilterSubscription: any;
  public mobileReZoom : Boolean = true;
  public isMobileListView  : Boolean = true;

  public mapGeoJson;

  searchBoundry = {};

  constructor(private zone: NgZone) {
    super();
    this.infoBubbleService = ServiceLocator.injector.get(MapInfoBubbleService);
    this.searchService = ServiceLocator.injector.get(SearchService);
    this.favoriteService = ServiceLocator.injector.get(FavoriteService);
   }
  //  loadMarkerClustererScript() {
  //   const script = document.createElement('script');
  //   script.src = 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/markerclusterer.js';
  //   script.async = true;
  //   script.defer = true;
  //   script.onload = () => {
  //     console.log('MarkerClusterer script loaded.');
  //     this.addMarkerCluster(); // Call your method here
  //   };
  //   document.body.appendChild(script);
  // }
  
  // ngOnInit() {
  //   this.loadMarkerClustererScript();
  // }
  
  loadMarkerClustererScript() {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/@google/markerclustererplus@5.1.0/dist/markerclustererplus.min.js';
    script.async = true;
    script.onload = () => {
      this.addMarkerCluster(); // Call your method after the script is loaded
    };
    document.body.appendChild(script);
  }
  
  ngDoCheck() {
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      this.showSavedSearch = '';
      this.isLogin = true;
    }else{
      this.showSavedSearch = "listingAgent";
      this.isLogin = false;
    }
  }

  ngOnInit() {
         this.loadMarkerClustererScript();

    if(this.getPreviousScreen() != '/search'){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/search');
    let self = this;

    if($(window).width() < 767){
      this.searchBarComponent.mapListView = false;
    }

    this.initMap();
    this.searchBarComponent.setDefaultOpenHouseFilter();
    this.searchBarComponent.showopenHouseFilter = true;

    $(".show_map").click(function(){
      $(".display_none_map").addClass("show_map_mobile");
      $(".map_side_bar").addClass("hide_map_mobile");
    });

    $(".show_list").click(function(){
      $(".map_side_bar").removeClass("hide_map_mobile");
      $(".display_none_map").removeClass("show_map_mobile");
      self.zone.run(() => {
        self.isMobileListView = true;
        self.searchBarComponent.mapListView = false;
      });
    });
    // this.zoom(this.map);
  }

  showMap(){
    $(".display_none_map").addClass("show_map_mobile");
    $(".map_side_bar").addClass("hide_map_mobile");
    this.isMobileListView = false;

    if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
      if(this.mobileReZoom == true){
        this.mobileReZoom = false;
        if(this.mapGeoJson != undefined){
          this.zoom(this.map);
        }
      }
    }
    this.searchBarComponent.mapListView = true;
  }

  initMap(){
    if(localStorage.getItem('recentSearches')){
      this.searchBarComponent.allowMapIdle = false;
      this.autoMapPosition = false;
    }
    if(BaseComponent.currentUserLatitude != undefined && BaseComponent.currentUserLongitude != undefined){
      this.autoMapPosition = false;
      this.currentLatLng = new google.maps.LatLng(BaseComponent.currentUserLatitude,BaseComponent.currentUserLongitude);
    }
    else{
      this.currentLatLng = new google.maps.LatLng(this.lat,this.lng);
    }

    var mapOptions = {
      zoom:8,
      center: this.currentLatLng,
      zoomControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      mapTypeControlOptions: {
        position: google.maps.ControlPosition.LEFT_TOP
      }
    };

    var marker = new google.maps.Marker({
      position: this.currentLatLng,
      map: this.map,
      optimized:false
    });
    this.map = new google.maps.Map(document.getElementById('map'),mapOptions);

    if(localStorage.getItem('recentSearches')){
      this.setPreviousMapPositon();
    }

    var iconBase = 'https://maps.google.com/mapfiles/kml/shapes/';
    var icons = {
      parking: {
        name: 'Open Houses',
        icon: this.imagePrefix + 'OH.png'
      },
      library: {
        name: 'Coming Soon / PRE-Market Listing',
        icon: this.imagePrefix + 'AO.png'
      },
      info: {
        name: 'Active Listings - No Open House',
        icon: this.imagePrefix + 'no-event.png'
      }
      // parking: {
      //   name: 'Active on the market',
      //   icon: this.imagePrefix + 'Outlined.png'
      // },
      // library: {
      //   name: 'PRE MLS / Coming Soon',
      //   icon: this.imagePrefix + 'Solid.png'
      // }
    };

    var legend = document.getElementById('legend');
    for (var key in icons) {
      var type = icons[key];
      var name = type.name;
      var icon = type.icon;
      var div = document.createElement('div');
      div.style['padding-bottom'] = '6px';
      div.innerHTML = '<img style="width: 40px;" src="' + icon + '"> ' + '<span style="margin-left:10px;font-size: 14px;color: #696868;font-weight: 600;">' + name + '</span>';
      legend.appendChild(div);
    }

    this.map.controls[google.maps.ControlPosition.TOP_LEFT].push(legend);



   this.map.addListener('idle', function() {
    var lat0 = self.map.getBounds().getNorthEast().lat();
    var lng0 = self.map.getBounds().getNorthEast().lng();
    var lat1 = self.map.getBounds().getSouthWest().lat();
    var lng1 = self.map.getBounds().getSouthWest().lng();
    self.searchBoundry ={
      "location": {
        "top_right": {"lat":lat0,"lon": lng0},
        "bottom_left": {"lat": lat1,"lon": lng1}
      }
    };

    var southWest = new google.maps.LatLng(lat1,lng1);
    var northEast = new google.maps.LatLng(lat0,lng0);
    var bounds = new google.maps.LatLngBounds(southWest,northEast);
    self.searchBarComponent.searchProperty['gmap_bounds'] = bounds;

    var idleLatLng = {
      "lat" : self.map.data.map.center.lat(),
      "lng" : self.map.data.map.center.lng()
    }
    self.searchBarComponent.searchProperty['idleLatLng'] = idleLatLng;

    if($(window).width() < 767 && self.searchBarComponent.mapListView == false){
      if(localStorage.getItem('recentSearches') == null){
        self.searchBarComponent.searchProperty['geo_bounding_box'] = "{}";
      }
      else{
        let recentSearches = JSON.parse(localStorage.getItem('recentSearches'));
        self.searchBarComponent.searchProperty['geo_bounding_box'] = recentSearches[0]['geo_bounding_box'];
      }
      self.searchBarComponent.searchProperty['is_map_list'] = false;
      self.searchBarComponent.mapListView = false;
    }
    else{
      self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.searchBoundry);

      if($(window).width() < 767 && self.isMobileListView == true){
        self.searchBarComponent.searchProperty['geo_bounding_box'] = "{}";
      }

      self.searchBarComponent.searchProperty['is_map_list'] = true;
      self.searchBarComponent.mapListView = true;
    }

    self.searchBarComponent.searchProperty['request_type'] = 'WEB'
    self.searchBarComponent.pageNo = 0;

    self.zone.run(() => {
      self.showMapLoading = true;
    });
    self.disable();

    if(self.searchBarComponent.allowMapIdle == true){

      self.searchBarComponent.filterProperty();
      localStorage.setItem('zoomLevel',self.map.getZoom());
      self.searchBarComponent.allowMapIdle = false;
    }
    if(self.infoBubble.isOpen() == true){
      self.infoBubble.close();
    }
  });

 this.map.addListener('')
    var self=this;
    if(navigator.geolocation){
      /*
        * @Desc: Find current position
        * @Param:
        * @return:display infowindow on map with given string
        *
      */

      navigator.geolocation.getCurrentPosition((position)=>{
        var currentPosition;
        BaseComponent.currentUserLatitude = position.coords.latitude;
        BaseComponent.currentUserLongitude = position.coords.longitude;
          currentPosition= {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          self.geolocationPosition=currentPosition;

          if(this.positionStatus == true && this.autoMapPosition == true){
            setTimeout(() => {
              // if(localStorage.getItem("recentSearches") != undefined && JSON.parse(localStorage.getItem("recentSearches"))[0]['gmap_bounds'] != undefined){
              //   let bounds = JSON.parse(localStorage.getItem("recentSearches"))[0]['gmap_bounds'];
              //   this.map.fitBounds(bounds);
              // }
              // else{
                self.map.setCenter(currentPosition);
              // }
            },30);
          }
        },
        ()=>{
          this.handleLocationError(true,this.map.getCenter());
        });

      }
      else{
          this.handleLocationError(false,this.map.getCenter());
      }

    var self=this;
    $("#draw a").click((e)=>{
      if(self.showMapLoading == false){
        self.showCancelDraw = true;
        this.clearGoogleMap(true);
        delete this.searchBarComponent.searchProperty['polygon'];
        self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.searchBoundry);
        self.searchBarComponent.searchProperty['is_map_list'] = true;
        self.searchBarComponent.searchProperty['request_type'] = 'WEB'

        self.map.setOptions({ draggableCursor:'default'});
        /*
          * @Desc: Allow to draw polygon
          * @Param:
          * @return:
          *
        */
        e.preventDefault();
        self.disable();
        google.maps.event.addDomListener(self.map.getDiv(),'mousedown',(e)=>{
        self.drawFreeHand();
        // self.poly.setMap(null);
        });
      }else{
        e.preventDefault();
      }
    });

    $("#cancelDraw a").click((e)=>{
      self.showCancelDraw = false;
      self.clearGoogleMap(true);
      self.allowAddGeoJson = true;
      if(self.freeHandPolygons.length != 0){
        self.freeHandPolygons = [];
        delete this.searchBarComponent.searchProperty['polygon'];
        self.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.searchBoundry);
        self.searchBarComponent.searchProperty['is_map_list'] = true;
        self.searchBarComponent.searchProperty['request_type'] = 'WEB'
        self.searchBarComponent.filterProperty();
        self.zone.run(() => {
          self.showMapLoading = true;
        });
      }
      self.enable();
    });

  this.html = this.infoBubbleService.changeHTML("symbols-map-hover.png", "120000", "", "", "", "",{});
  this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
    var self=this;

    if($(window).width() < 767){
      google.maps.event.addListener(self.map,'click', function() {
        if(self.infoBubble.isOpen() == true){
          self.infoBubble.close();
          $("#property_info").remove();
        }
      });
      this.addLoaderClass = false;
      this.searchBarComponent.mapListView = false;
    }
    else{
      this.addLoaderClass = true;
      this.searchBarComponent.mapListView = true;
    }
}
  handleLocationError(browserHasGeolocation,pos){
    /*
      * @Desc:showing error message in infowindow if read location permission id block by user
      * @Param:
      * @return:error message.
      *
    */

  // infoWindow.setPosition(pos);
  // infoWindow.setContent(browserHasGeolocation ?'Error: The Geolocation service failed.' :'Error: Your browser doesn\'t support geolocation.');
  // infoWindow.open(this.gmap);
  }


  addMarkerCluster(){
    
    var cluster;
    let self = this;
    let j=1;
    var markers =this.markerSet.map(function(location, i) {
      var markerColorType = 'no-event';
      var markerColor = "#fffffff7";
      if(location['first_event_type'] == ''){
        markerColorType = 'no-event';
        markerColor = "#fffffff7";
      }
      else{
        if(location['listing_status']=="PRE-MLS/Coming Soon"){
          if(location['first_event_type']=="OH"){
            markerColorType = "AO";
          }
          else if(location['first_event_type']=="BO"){
            markerColorType = "BO";
          }
          else if(location['first_event_type']=="AO"){
            markerColorType = "AO";
            // -- added for coming soon
            markerColorType = "72_pin";
          }
          else if(location['first_event_type']==""){
            markerColorType = "no-event";
          }
          else {
            markerColorType = location['first_event_type'];
          }
        }
        else{
          if(location['first_event_type']=="AO"){
            markerColorType = "OH";
            markerColorType = "72_pin";
          }
          else if(location['first_event_type']=="OH"){
            markerColorType = "OH";
          }
          else{
            markerColorType = location['first_event_type'];
          }
        }

        // if(location['first_event_type']=="AO"){
        //   markerColorType = "OH";
        // }
        // else{
        //   markerColorType = location['first_event_type'];
        // }

        // if(location['listing_status'] != ''){
        //   if(location['first_event_type']=="OH" && location['listing_status']=="PreMLS/Coming Soon"){
        //     markerColorType = "AO";
        //   }
        // }
      }

      let iconImage = self.getIconImage(google, markerColorType)

      var priceLabel = self.priceFormat(self.markerSet[i]["home_price"]);
      cluster = new google.maps.Marker({
        position: {lat :self.markerSet[i]["latitude"],lng: self.markerSet[i]["longitude"]},
        // icon : self.imagePrefix+markerColorType+".png",
        icon : iconImage,
        map: self.map,
        label: {
          text: '$'+priceLabel,
          background: '#AD5FBF',
          color: markerColor,
          align: 'center',
          padding: '0',
          fontSize: "14px",
          fontWeight: "600",
          className: (markerColorType == "72_pin") ?'markerLabel' : '',
        },
        title:self.markerSet[i]["street"],
        id:self.markerSet[i]["id"]
      });
      self.markers.push(cluster);

      //hide all markers when map load
      //this.setMapHideAll(i,this.markerSet[i]["id"] -1 ,null);

      if($(window).width() < 767){
        google.maps.event.addListener(cluster, 'click',((marker,event)=>{
          return function(){
            if(self.markerSet[i]['property_file'] == ''){
              var property = self.propertyList.filter((propertyId) => propertyId.id == self.markerSet[i]['id']);
              if(property.length !=0){
                if(self.markerSet[i] != undefined){
                  self.markerSet[i]['property_file'] = property[0]['property_file'];
                  self.openMobilePropertyInfoBubble(i,marker);
                }
              }else{
                let url = new URLSearchParams();
                url.set('property_id',self.markerSet[i]['id'].toString());
                self.propertyFilterSubscription = self.favoriteService.getPropertyFile(url).subscribe(res =>{
                  self.zone.run(()=>{
                    if(self.markerSet[i] != undefined){
                      self.markerSet[i]['property_file'] = res['result']['property_file'];
                      self.openMobilePropertyInfoBubble(i,marker);
                    }
                  });
                },err=>{
                  console.log(err.json())
                });
              }
            }
            else{
              self.openMobilePropertyInfoBubble(i,marker);
            }
          }
        })(cluster));
        return cluster;

      }else{
        google.maps.event.addListener(cluster, 'mouseover',((marker,event)=>{
          return function(){
            if(self.markerSet[i]['property_file'] == ''){
              var property = self.propertyList.filter((propertyId) => propertyId.id == self.markerSet[i]['id']);
              if(property.length !=0){
                if(self.markerSet[i] != undefined){
                  self.markerSet[i]['property_file'] = property[0]['property_file'];
                  self.openWebPropertyInfoBubble(i,marker);
                }
              }else{
                let url = new URLSearchParams();
                url.set('property_id',self.markerSet[i]['id'].toString());
                self.propertyFilterSubscription = self.favoriteService.getPropertyFile(url).subscribe(res =>{
                  self.zone.run(()=>{
                    if(self.markerSet[i] != undefined){
                      self.markerSet[i]['property_file'] = res['result']['property_file'];
                      self.openWebPropertyInfoBubble(i,marker)
                    }
                  });
                },err=>{
                  console.log(err.json())
                });
              }
            }
            else{
              self.openWebPropertyInfoBubble(i,marker);
            }
          }
          })(cluster));

       google.maps.event.addListener(cluster, 'mouseout',((marker,event)=>{
          return function(){
            self.infoBubble.close();
            if(self.propertyFilterSubscription){
              self.propertyFilterSubscription.unsubscribe();
            }
            $("div.box_on_map", self.infoBubble.bubble_).on("click",function(){
              self.gotToPropertyDetail('search/property-detail',marker['id']);
            });
          }
        })(cluster));
        return cluster;
      }
    });

  this.markerCluster = new MarkerClusterer(this.map, markers,
    {
      maxZoom: 8,
      styles: this.mapMarkerCluster[0]
    });

    google.maps.event.addListener(this.markerCluster, 'clusterclick', function(clust) {
      self.mobileReZoom = false;
      if(self.searchBarComponent.searchMapSubscription){
        self.searchBarComponent.allowMapIdle = true;
        self.searchBarComponent.searchMapSubscription.unsubscribe();
      }
    });
  }

  openWebPropertyInfoBubble(index,marker){
    var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);

    if(this.infoBubble.isOpen() == true){
      this.infoBubble.close();
    }
    this.html = this.infoBubbleService.changeHTML(this.markerSet[index]['property_file'], this.markerSet[index]['home_price'], this.markerSet[index]['bedroom'], this.markerSet[index]['full_bath'], this.markerSet[index]['living_area'],this.markerSet[index]['street'], this.markerSet[index]);
    if(pixelOffsetY != undefined && pixelOffsetY < 260){
      this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
    }else{
      this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
    }
    this.infoBubble.open(this.map, marker);
    setTimeout(() => {
      $(".box_on_map").parent().parent().parent().addClass('pop_div');
      $(".box_on_map").parent().parent().parent().attr('id','property_info');
    },20);
  }

  openMobilePropertyInfoBubble(i,marker){
    let self = this;
    var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);
    if(this.infoBubble.isOpen() == false){
      this.html = this.infoBubbleService.changeHTML(this.markerSet[i]['property_file'], this.markerSet[i]['home_price'], this.markerSet[i]['bedroom'], this.markerSet[i]['full_bath'], this.markerSet[i]['living_area'],this.markerSet[i]['street'], this.markerSet[i]);
      if(pixelOffsetY != undefined && pixelOffsetY < 260){
        this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
      }else{
        this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
      }
      this.infoBubble.open(this.map, marker);
      setTimeout(() => {
        $(".box_on_map").parent().parent().parent().addClass('pop_div');
        $(".box_on_map").parent().parent().parent().attr('id','property_info');
        $('div.box_on_map', self.infoBubble.bubble_).on('click',function(){
          self.gotToPropertyDetail('search/property-detail',marker['id']);
        });
      }, 20);
    }else{
      self.infoBubble.close();
      $("#property_info").remove();
      self.openMobilePropertyInfoBubble(i,marker);
    }
  }

  mapZoomOut(){
    if(this.showMapLoading == false){
      var getZoom = this.map.getZoom();
      this.map.setZoom(getZoom - 1);
    }
  }

  mapZoomIn(){
    if(this.showMapLoading == false){
      var getZoom = this.map.getZoom();
      this.map.setZoom(getZoom + 1);
    }
  }

  disable(){
    /*
      * @Desc:disable map controls.
    */
    this.map.setOptions({
      draggable: false,
      zoomControl: false,
      scrollwheel: false,
      disableDoubleClickZoom: false,
      clickable:false
    });
  }

  drawFreeHand(){
    if(this.showMapLoading == false){
      //the polygon
      var self=this;
      self.freeHandPolygons = [];
      this.poly=new google.maps.Polyline({map:this.map,clickable:false,strokeColor: "#10B8A8"});
      //move-listener
      var move=google.maps.event.addListener(this.map,'mousemove',(e)=>{
        self.poly.getPath().push(e.latLng);
      });

      //mouseup-listener
      google.maps.event.addListenerOnce(this.map,'mouseup',(e)=>{
        google.maps.event.removeListener(move);
        var path=self.poly.getPath();
        self.poly.setMap(null);
        self.poly=new google.maps.Polygon({map:self.map,path:path, strokeColor: "#10B8A8",
          strokeOpacity: 0.8,
          strokeWeight: 2,
          fillColor: "#10B8A8",
          fillOpacity: 0
        });
        var len = path.getLength();
        var latlist = [];
        for (var i = 0; i < len; i++) {
          latlist.push("new google.maps.LatLng(" + path.getAt(i).toUrlValue(5) + "), ");
          self.freeHandPolygons.push({lat:path.getAt(i).lat(), lon:path.getAt(i).lng()});
        }

        this.searchBarComponent.searchProperty['geo_bounding_box'] = JSON.stringify(self.searchBoundry);

        if(self.freeHandPolygons.length == 0){
          delete self.searchBarComponent.searchProperty['polygon'];
        }else{
          this.searchBarComponent.searchProperty['polygon'] = JSON.stringify(self.freeHandPolygons);
        }
        this.searchBarComponent.searchProperty['is_map_list'] = true;
        this.searchBarComponent.searchProperty['request_type'] = 'WEB'
        this.searchBarComponent.filterProperty();

        self.zone.run(() => {
          self.showMapLoading = true;
        });
        self.disable();

        google.maps.event.clearListeners(self.map.getDiv(), 'mousedown');
        self.enable();

          setTimeout(function(){
            for (var j = 0; j <self.markerSet.length; j++){
              var currentMarkerPosition=new google.maps.LatLng(self.markerSet[j]["latitude"],self.markerSet[j]["longitude"]);
              var resultColor = google.maps.geometry.poly.containsLocation(currentMarkerPosition,self.poly)
                if(resultColor){
                  /*
                    * @desc:if marker available
                    */
                  self.matchMarker.push({lat:self.markerSet[j]["latitude"],lng:self.markerSet[j]["longitude"],id:self.markerSet[j]["id"]});

                //  self.setMapOnAll(j,self.markerSet[j]["id"] - 1,self.map);
                }
                else{
                  self.setMapHideAll(j,self.markerSet[j]["id"],null);
                }
            }
            for(let i=0; i< self.matchMarker.length; i++){
              self.setMapOnAll(i,self.matchMarker[i]['id'],self.map);
            }
          });
      });
    }
  }

  enable(){
    /*
      * @Desc:enable map controls.
    */
    this.map.setOptions({
      draggable: true,
      zoomControl: false,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      clickable:false
    });
  }

  setMapHideAll(index,id,map){
    /*
      * @Desc: set markers hide in map
      * @Param:
                index:number
                map:current map
      * @return:
      *
    */
    let markerCluster = this.markerCluster.getMarkers().filter(marker => marker['id'] == id);
    this.markerCluster.removeMarker(markerCluster[0])
    let marker = this.markers.filter(marker => marker['id'] == id);
    marker[0].setMap(map);
  }

  setMapOnAll(index,id,map){
    /*
      * @Desc: Set markers show in map
      * @Param:
                index:number
                map:current map
      * @return:
      *
    */
    let marker = this.markers.filter(marker => marker['id'] == id);
    marker[0].setMap(map);
  }

  selectedEvent(type,event){
    if(this.isLogin == true){
      this.eventModal.openEventModal(type,event,false);
    }else{
      // this.warningMessage("Please sign into your account to see this property's Open Houses");
      $("#authModal").modal("show");
      $('#authModal .modal-body .nav-pills a:eq(' + 1 + ')').tab('show');
    }
  };

  getSearchObj(event){
    this.searchObj = event;
    this.searchBarComponent.allowMapIdle = true;
    this.zone.run(() => {
      if(event['error'] == "false"){
        this.removeInfoBubble();
        this.propertyList = event['result'];
        this.totalPorpertyCount = event['totalPage'];
        this.itemsPerPage = event['itemsPerPage'];
        this.pageCount = event['currentPageNumber'];
        if(this.searchBarComponent.searchProperty['is_map_list'] == true || event['currentPageNumber'] == 0){
          this.deleteMarkers(event['map_record_list']);
          this.zone.run(() => {
            this.showMapLoading = false;
          });
          this.map.setOptions({draggable: true, zoomControl: false, scrollwheel: true, disableDoubleClickZoom: false});
        }
      }
      else{
        this.zone.run(() => {
          this.showMapLoading = false;
        });
        this.map.setOptions({draggable: true, zoomControl: false, scrollwheel: true, disableDoubleClickZoom: false});
      }
    });
  }

  removeInfoBubble(){
    var infowin = document.getElementsByClassName("pop_div");
    for(var i=0;i<infowin.length;i++)
    {
      infowin[i].innerHTML = ''
      $("div.pop_div").remove();
    }
  }

  deleteMarkers(property: any) {
    if (this.infoBubble && this.infoBubble.isOpen) {
      this.infoBubble.close();
    }
  
    if (this.markerCluster) {
      this.markerCluster.clearMarkers();
      this.markerCluster = null;
    }
  
    this.markers.forEach(marker => marker.setMap(null));
    this.markers = [];
    this.markerSet = [];
    
    this.propertyOnMap(property); // Ensure this method handles an empty state properly
  }
  

  propertyOnMap(property){
    for(let i=0;i<property.length;i++){
      if(property[i]['latitude'] != 0 && property[i]['longitude'] !=0){
        property[i]['property_file'] = '';
        this.markerSet.push(property[i]);
      }
    }
    this.addMarkerCluster();
  }

  addToFavorite(id,item){
    if(this.isLogin == true){
      let index = this.propertyList.indexOf(item);
      this.propertyList[index]['is_favourite'] = this.favoriteService.setFavourite(!this.propertyList[index]['is_favourite'], id, this.propertyList, index);
    }
    else
    {
      // this.warningMessage("Please sign into your account to save this property as a favorite.");
      $("#authModal").modal("show");
      $('#authModal .modal-body .nav-pills a:eq(' + 1 + ')').tab('show');
    }
  }

  getpage(pageNumber :number){
    var setScroll = document.getElementById('scroll');
    this.pageCount = pageNumber;
    this.searchBarComponent.pageNo = pageNumber;
    this.searchBarComponent.filterProperty();
    setScroll.scrollTop = 0;
    if(pageNumber != 1){
      this.searchBarComponent.mapListView = false;
      this.searchBarComponent.searchProperty['is_map_list'] = false;
    }
    else{
      this.searchBarComponent.mapListView = true;
    }
  }

  setMapPosition(position){
    this.searchBarComponent.allowMapIdle = false;
    this.zone.run(
      () => {
        var timeOut = 150;
        if(this.geolocationPosition != undefined && this.geolocationPosition != null){
          timeOut = 70;
        }
        setTimeout(() => {

          // if(localStorage.getItem("sear") != undefined && localStorage.getItem("sear") == "false" && JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'] != undefined){
          //   this.zoom(this.map);
          // }
          // else{
            if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
              this.map.setCenter(new google.maps.LatLng(position['lat'],position['lng']));
              if(position['setAutoPosition'] == true){
                this.map.setZoom(8);
              }
            }

          // }
          // this.currentLatLng = new google.maps.LatLng(position['lat'],position['lng']);
          this.autoMapPosition = false;
          this.allowAddGeoJson = true;
        },timeOut);
      });
  }

  clearGoogleMap(clearMarker){
    if(clearMarker == true){
      if(this.poly != undefined){
        this.poly.setMap(null);
      }
      if(this.mapPolygons != undefined){
        this.mapPolygons.setMap(null);
      }
      if(this.markerSet.length !=0){
        this.markerCluster.clearMarkers();
      }
      for (var i = 0; i < this.markers.length; i++) {
        this.markers[i].setMap(null);
      }
      this.markers = [];
      this.markerSet = [];
    }

    if(this.mapGeoJson != undefined){
      for (var i = 0; i < this.mapGeoJson.length; i++){
        this.map.data.remove(this.mapGeoJson[i])
      }
    }
    // delete this.searchBarComponent.searchProperty['polygon'];
  }

  drawPolygons(polygonObj){
    this.clearGoogleMap(false);
    this.mobileReZoom = true;

    if(polygonObj['isError'] == true){
      if(this.mapPolygons != undefined){
        this.mapPolygons.setMap(null);
      }
    }
    else{
      if(this.mapGeoJson != undefined){
        for (var i = 0; i < this.mapGeoJson.length; i++){
          this.map.data.remove(this.mapGeoJson[i])
        }
      }
      if(this.allowAddGeoJson == true){
        this.mapGeoJson = this.map.data.addGeoJson(polygonObj);
      this.map.data.setStyle({
        strokeColor: "#10B8A8",
        strokeWeight: 2,
        strokeOpacity: 0.8,
        fillColor: "#10B8A8",
        fillOpacity: 0
      });
      }

      if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
        this.zoom(this.map);
        delete this.searchBarComponent.searchProperty['polygon'];
        if(this.poly != undefined){
          this.poly.setMap(null);
        }
        if(this.mapPolygons != undefined){
          this.mapPolygons.setMap(null);
        }
        this.showCancelDraw = false;
      }
      this.searchBarComponent.allowMapIdle = true;
    }
  }

  setPreviousMapPositon(){
    this.searchBarComponent.setDefaultOpenHouseFilter();
    var bound = new google.maps.LatLngBounds();
    if(JSON.parse(localStorage.getItem("recentSearches"))[0]['polygon'] != undefined){
      this.drawCustomPolyline(JSON.parse(localStorage.getItem("recentSearches"))[0]['polygon']);
      this.showCancelDraw = true;
      this.allowAddGeoJson = false;
      this.searchBarComponent.allowCallGetMapPolygons = false;
    }
    if(JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'] != undefined){
      let bounds = JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'];
      var center = new google.maps.LatLng(bounds['lat'],bounds['lng'])
      this.map.setCenter(new google.maps.LatLng(bounds['lat'],bounds['lng']));
      if(localStorage.getItem('zoomLevel')){
        this.map.setZoom(parseInt(localStorage.getItem('zoomLevel')));
      }
      else{
        this.map.setZoom(8)
      }
      localStorage.setItem('boundryZoom','false');
      if($(window).width() < 767){
        this.searchBarComponent.mapListView = false;
      }
      else{
        this.searchBarComponent.mapListView = true;
      }
      this.searchBarComponent.allowLocalStorageSearch();
      // this.searchBarComponent.allowMapIdle = true;
    }
  }

  zoom(map) {
    var self = this;
    var bounds = new google.maps.LatLngBounds();
    map.data.forEach(function(feature) {
      self.processPoints(feature.getGeometry(), bounds.extend, bounds);
    });
    map.fitBounds(bounds);
  }

  processPoints(geometry, callback, thisArg) {
    var self = this;
    if(geometry instanceof google.maps.LatLng) {
      callback.call(thisArg, geometry);
    }else if (geometry instanceof google.maps.Data.Point) {
      callback.call(thisArg, geometry.get());
    }else{
      geometry.getArray().forEach(function(g) {
        self.processPoints(g, callback, thisArg);
      });
    }
  }

  drawCustomPolyline(polygonsList){
    var polygons = [];
    if(polygonsList.length != 0){
      for(let i=0;i<JSON.parse(polygonsList).length;i++){
        let obj = {lng: JSON.parse(polygonsList)[i]['lon'], lat: JSON.parse(polygonsList)[i]['lat']}
        polygons.push(obj);
      }
    }

    if(polygons.length != 0){
      this.freeHandPolygons = polygons;
      this.mapPolygons = new google.maps.Polygon({
          paths: polygons,
          strokeColor: "#10B8A8",
          strokeOpacity: 0.8,
          strokeWeight: 3,
          fillOpacity: 0
      });
      const bounds = new google.maps.LatLngBounds();
      for (var i=0; i<this.mapPolygons.getPath().length; i++) {
        var point = new google.maps.LatLng(polygons[i]['lat'], polygons[i]['lng']);
        bounds.extend(point);
      }
      this.mapPolygons.setMap(this.map);
    }
  }

  setMapToViewPort(viewPort){
    var bounds = JSON.parse(viewPort['viewPort']);
    this.map.setCenter(new google.maps.LatLng(bounds['lat'],bounds['lng']));
    if(viewPort['zoomLevel'] != undefined && viewPort['zoomLevel'] != 0){
      this.map.setZoom(viewPort['zoomLevel']);
    }
  }

  setSaveSearchBoundry(boundry){
    this.drawCustomPolyline(boundry['boundry']);
  }

  UpdatePropertyInfo(propertyInfo){
    var property = this.propertyList.filter((propertyId) => propertyId.id == propertyInfo['property']);
    var propertyIndex = this.propertyList.indexOf(property[0]);
    if(property.length !=0){
      let updatedPropertyParams = new URLSearchParams();
      updatedPropertyParams.set('property_id',propertyInfo['property']);
      updatedPropertyParams.set('list_type',this.searchListType.toString());

      this.favoriteService.getUpdatedPropertyInfo(updatedPropertyParams).subscribe(res =>{
        this.propertyList[propertyIndex] = res['result'];
      },err=>{
        this.errorResponse(err.json());
      });
    }
  }

  getCurrentLocation(){
    if(this.showMapLoading == false){
      if(this.geolocationPosition != undefined && this.geolocationPosition != null){
        this.searchBarComponent.ClearLocationSearch();
        this.clearGoogleMap(true);
        this.map.setCenter(this.geolocationPosition);
        this.map.setZoom(8);
      }
    }
  }

  showPropertyMarker(propertyObj){
    this.markers.filter((marker) => {
      if(marker.id == propertyObj.id){
        if(this.infoBubble.isOpen()){
          this.infoBubble.close();
        }
        var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);
        this.html = this.infoBubbleService.changeHTML(propertyObj.property_file, propertyObj.home_price, propertyObj.bedroom, propertyObj.full_bath, propertyObj.living_area,propertyObj.street, propertyObj);
        if(pixelOffsetY != undefined && pixelOffsetY < 260){
          this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
        }else{
          this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
        }
        this.infoBubble.open(this.map, marker);
        setTimeout(() => {
          $(".box_on_map").parent().parent().parent().addClass('pop_div');
          $(".box_on_map").parent().parent().parent().attr('id','property_info');
        }, 20);
      }
    });
    var markerSet = this.markerSet.filter((propertyId) => propertyId.id == propertyObj.id);
    if(markerSet.length !=0){
      if(markerSet[0]['property_file'] == ''){
        markerSet[0]['property_file'] = propertyObj.property_file;
      }
    }
  }

  closeAllPorpertyMarkers(){
    if(this.infoBubble.isOpen()){
      this.infoBubble.close();
      var infowin = document.getElementsByClassName("pop_div");
      for(var i=0;i<infowin.length;i++)
      {
        infowin[i].innerHTML = ''
        $("#property_info").remove();
      }
    }
  }


  polygonErrorHandling(){
    if(this.searchBarComponent.handalMaptechError == true){
      this.clearGoogleMap(true);
      delete this.searchBarComponent.searchProperty['polygon'];
      if(this.searchBarComponent.searchProperty['geo_bounding_box'] != undefined){
        this.searchBarComponent.filterProperty();
        this.searchBarComponent.handalMaptechError = true;
      }
    }
  }

  removeSearchValue(){
    this.searchBarComponent.removeLocationValue();
  }

}
