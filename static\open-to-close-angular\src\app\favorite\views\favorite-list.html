<div>
    <header></header>
</div>

<div>
    <search-bar (searchObjEvent)="getSearchObj($event)" [searchFrom]="'favoriteListView'" [currentPage]="'listingAgent'" [isListViewScreen]='true'></search-bar>
    <div class="myclient_page My_Listings">
         <div class="container">
            <div class="ls_group Favorites_page">
               <div class="title_group ml_zero">
                  <div class="title pull-left">Favorites</div>
                  <a (click)="routeOnUrl('favorites')"><img src="{{imagePrefix}}Icon.png" class="Icon_png cursor-pointer" alt=""></a>
               </div>
            </div>
            <div class="my_client_table_group">
               <div class="myclient_navbar">
                  <ul>
                    <li (click)="setSelectedTab('On the market')" class="active" data-toggle="pill" href="#menu1" >On the market</li>
                    <li (click)="setSelectedTab('Coming soon')" data-toggle="pill" href="#menu3" >Coming soon</li>
                    <li (click)="setSelectedTab('Off market')" data-toggle="pill" href="#menu4" >Off market</li>
                  </ul>
               </div>
               <div class="tab-content">
                <div id="menu1" class="event-list tab-pane fade in active table-responsive selected_saved">
                    <div class="No_matches" *ngIf="showONMLoader == true">
                        <div class="loader">
                        <div class="message">Loading...</div>
                        <div class="dots"><div class="center"></div></div>
                        </div>
                    </div>
                    <div *ngIf="favoritesPropertyList.length == 0 && showONMLoader == false" class="No_matches">
                        <div class="title">No matches</div>
                        <div class="text">You don’t have any listings that meet your search criteria. Try <br> running a new search or add a new listing.</div>
                    </div>

                    <div *ngIf="favoritesPropertyList.length != 0" class="property-list-table">
                        <table class="table">
                            <thead>
                            <tr>
                                <th (click)="favouriteSortting('ONM','PR')">Property <img id="ONM_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('ONM','HP')">Price <img id="ONM_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('ONM','BD')">Beds <img id="ONM_BD" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('ONM','BT')">Baths<img id="ONM_BT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('ONM','SF')">Sqft <img id="ONM_SF" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('ONM','DT')" colspan="5">Events  <img id="ONM_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let property of favoritesPropertyList;let j = index">
                                    <td class="cursor-pointer" (click)="gotToPropertyDetail('favorites/property-detail',property.id)">
                                        <span class="vertical-align-top"><img *ngIf="property.property_file != ''" src="{{property.property_file}}"  class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""> </span>
                                        <span class="vertical-align-top"><img *ngIf="property.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""> </span>
                                        <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{property.street}}<br></span>{{property.location}}</div>
                                    </td>

                                    <td class="font_semibold list-table-digit">{{property.home_price | currency:"":symbol:"1.0"}}</td>
                                    <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bedroom.png" alt=""> {{property.bedroom}} beds</td>
                                    <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bathroom.png" alt=""> {{property.full_bath}} baths</td>
                                    <td class="font_semibold list-table-digit fav-living">{{property.living_area}}</td>
                                    <td>
                                    <span *ngFor="let event of property.event_list">
                                        <span *ngIf="event.event_type == 'BO'" (click)="openEventModel('brokerOpen',event)">
                                                <div class="month"> <span class="bgcolor1 font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                        </span>
                                        <span *ngIf="event.event_type == 'AO'" (click)="openEventModel('appointmentOnly',event)">
                                                <div class="month"> <span class="month_2_color font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                        </span>
                                        <span *ngIf="event.event_type == 'OH'" (click)="openEventModel('openHouse',event)">
                                                <div class="month"> <span class="month_3_color font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                        </span>
                                    </span>
                                    </td>
                                    <td>
                                        <span *ngIf="property.total_event_count != 0" class="pluse-more">
                                            + {{property.total_event_count}} more
                                        </span>
                                    </td>
                                    <td class="action-view">
                                    <a (click)="gotToPropertyDetail('favorites/property-detail',property.id)"><div class="save_notes margin_zero">Property Details</div> </a>
                                    </td>
                                    <td class="action-option">
                                        <div class="open_click_menu" (click)="openMenu(j,property.id)">
                                            <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                            <ul id="fav_{{j}}_{{property.id}}" class="click_menu_open events">
                                                <li class="cursor-pointer option-menu" (click)="remove(property.id,property,'ONM')">Remove</li>
                                                <li *ngIf="currentUserId != property.user" class="cursor-pointer option-menu" (click)="messageListingAgent(property)">Contact Listing Agent</li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div *ngIf="faviouriteTotalCount > faviouriteItemsPerPage && faviouriteTotalCount != favoritesPropertyList.length" class="new_form_group load_more_btn">
                        <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreFaviouriteList(faviouriteIndex,'ONM')" value="Load More">
                        </div>
                     </div>
                  </div>


                <div id="menu3" class="tab-pane table-responsive selected_saved">
                    <div class="No_matches" *ngIf="showCSLoader == true">
                        <div class="loader">
                        <div class="message">Loading...</div>
                        <div class="dots"><div class="center"></div></div>
                        </div>
                    </div>
                    <div *ngIf="csFavoritesPropertyList.length == 0 && showCSLoader == false" class="No_matches">
                        <div class="title">No matches</div>
                        <div class="text">You don’t have any listings that meet your search criteria. Try <br> running a new search or add a new listing.</div>
                    </div>
                    <div *ngIf="csFavoritesPropertyList.length != 0" class="property-list-table">
                        <table class="table">
                            <thead>
                            <tr>
                                <th (click)="favouriteSortting('CS','PR')">Property <img id="CS_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('CS','HP')">Price <img id="CS_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('CS','BD')">Beds <img id="CS_BD" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('CS','BT')">Baths<img id="CS_BT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('CS','SF')">Sqft <img id="CS_SF" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('CS','DT')" colspan="3">Events  <img id="CS_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let property of csFavoritesPropertyList; let cs = index">
                                    <td class="cursor-pointer" (click)="gotToPropertyDetail('favorites/property-detail',property.id)">
                                        <span class="vertical-align-top"><img *ngIf="property.property_file != ''" src="{{property.property_file}}"  class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                                        <span class="vertical-align-top"><img *ngIf="property.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                                        <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{property.street}}<br></span>{{property.location}}</div>
                                    </td>

                                    <td class="font_semibold list-table-digit">{{property.home_price | currency:"":symbol:"1.0"}}</td>
                                    <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bedroom.png" alt=""> {{property.bedroom}} beds</td>
                                    <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bathroom.png" alt=""> {{property.full_bath}} baths</td>
                                    <td class="font_semibold list-table-digit fav-living">{{property.living_area}}</td>
                                    <td>
                                    <span *ngFor="let event of property.event_list">
                                        <span *ngIf="event.event_type == 'BO'" (click)="openEventModel('brokerOpen',event)">
                                                <div class="month"> <span class="bgcolor1 font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                        </span>
                                        <span *ngIf="event.event_type == 'AO'" (click)="openEventModel('appointmentOnly',event)">
                                                <div class="month"> <span class="month_2_color font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                        </span>
                                        <span *ngIf="event.event_type == 'OH'" (click)="openEventModel('openHouse',event)">
                                                <div class="month"> <span class="month_3_color font_semibold">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span><div class="font_semibold">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</div> </div>
                                        </span>
                                    </span>
                                    </td>
                                    <td class="action-view">
                                    <a (click)="gotToPropertyDetail('favorites/property-detail',property.id)"><div class="save_notes margin_zero">Property Details</div> </a>
                                    </td>
                                    <td class="action-option">
                                        <div class="open_click_menu" (click)="openMenu(cs,property.id)">
                                            <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                            <ul id="fav_{{cs}}_{{property.id}}" class="click_menu_open events">
                                                <li class="cursor-pointer option-menu" (click)="remove(property.id,property,'CS')">Remove</li>
                                                <li *ngIf="currentUserId != property.user" class="cursor-pointer option-menu" (click)="messageListingAgent(property)">Contact Listing Agent</li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div *ngIf="csFaviouriteTotalCount > csFaviouriteItemsPerPage && csFaviouriteTotalCount != csFavoritesPropertyList.length" class="new_form_group load_more_btn">
                        <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreFaviouriteList(csFaviouriteIndex,'CS')" value="Load More">
                        </div>
                    </div>
                  </div>

                <div id="menu4" class="tab-pane  table-responsive selected_saved">
                    <div class="No_matches" *ngIf="showOFMLoader == true">
                        <div class="loader">
                        <div class="message">Loading...</div>
                        <div class="dots"><div class="center"></div></div>
                        </div>
                    </div>
                    <div *ngIf="ofmFavoritesPropertyList.length == 0 && showOFMLoader == false" class="No_matches">
                        <div class="title">No matches</div>
                        <div class="text">You don’t have any listings that meet your search criteria. Try <br> running a new search or add a new listing.</div>
                    </div>
                    <div *ngIf="ofmFavoritesPropertyList.length != 0" class="property-list-table">
                        <table class="table">
                            <thead>
                            <tr>
                                <th (click)="favouriteSortting('OFM','PR')">Property <img id="OFM_PR"  src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('OFM','HP')">Price <img id="OFM_HP" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('OFM','BD')">Beds <img id="OFM_BD" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('OFM','BT')">Baths<img id="OFM_BT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                <th (click)="favouriteSortting('OFM','SF')" colspan="3">Sqft <img id="OFM_SF" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let property of ofmFavoritesPropertyList;let ofm = index">
                                    <td class="cursor-pointer" (click)="gotToPropertyDetail('favorites/property-detail',property.id)">
                                        <span class="vertical-align-top"><img *ngIf="property.property_file != ''" src="{{property.property_file}}"  class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                                        <span class="vertical-align-top"><img *ngIf="property.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt=""></span>
                                        <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{property.street}}<br></span>{{property.location}}</div>
                                    </td>
                                    <td class="font_semibold list-table-digit">{{property.home_price | currency:"":symbol:"1.0"}}</td>
                                    <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bedroom.png" alt=""> {{property.bedroom}} beds</td>
                                    <td class="font_semibold list-table-digit"> <img src="{{imagePrefix}}Bathroom.png" alt=""> {{property.full_bath}} baths</td>
                                    <td class="font_semibold list-table-digit fav-living">{{property.living_area}}</td>
                                    <td class="action-view">
                                    <a (click)="gotToPropertyDetail('favorites/property-detail',property.id)"><div class="save_notes margin_zero">Property Details</div> </a>
                                    </td>
                                    <td class="action-option">
                                        <div class="open_click_menu" (click)="openMenu(ofm,property.id)">
                                            <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more " alt="">
                                            <ul id="fav_{{ofm}}_{{property.id}}" class="click_menu_open events">
                                                <li class="cursor-pointer option-menu" (click)="remove(property.id,property,'OFM')">Remove</li>
                                                <li *ngIf="currentUserId != property.user" class="cursor-pointer option-menu" (click)="messageListingAgent(property)">Contact Listing Agent</li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div *ngIf="ofmFaviouriteTotalCount > ofmFaviouriteItemsPerPage && ofmFaviouriteTotalCount != ofmFavoritesPropertyList.length" class="new_form_group load_more_btn">
                        <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreFaviouriteList(ofmFaviouriteIndex,'OFM')" value="Load More">
                        </div>
                    </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
</div>

<event-modal (setPropertyLatestInfo)="UpdatePropertyInfo($event)"></event-modal>

<div>
    <footer></footer>
</div>
