import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';

import { Subscription } from 'rxjs';

import { ServiceLocator } from '@app/base/components/service-locator';
import { PurchaseService } from '@app/purchase/service/purchase.service';
import { GetPlansService } from '@app/base/services/get.plans.service';

import { BaseComponent } from '@app/base/components/base.component';
import { ProfileComponent } from '@app/profile/component/profile.component';
import { AuthService } from '@app/auth/services/auth.service';

import { Plans } from '@app/profile/models/plan';

declare var $;

@Component({
  selector: 'broker-upgrade',
  templateUrl: '../views/broker-upgrade.component.html',
  styleUrls: ['../css/upgrade.component.css', '../../landing-pages/css/landing-page.component.css']
})
export class BrokerUpgradeComponent extends BaseComponent implements OnInit, OnDestroy {

  public purchaseService: PurchaseService;
  public getPlansService: GetPlansService;
  public authService: AuthService;

  public plansAPISubscription: Subscription;

  public plansList = [];

  public selectedPlan: String = "";

  public subscribed: boolean = false;

  constructor() {
    super();
    super();
    this.getPlansService = ServiceLocator.injector.get(GetPlansService);
    this.purchaseService = ServiceLocator.injector.get(PurchaseService);
    this.authService = ServiceLocator.injector.get(AuthService);
  }

  ngOnInit() {

    $(document).ready(function ($) {
      $(this).scrollTop(0);
    });

    this.getCurrentPlans();
    this.getPlans();
  }

  getCurrentPlans(): void {
    if (BaseComponent.user != undefined || Object.keys(BaseComponent.user).length != 0) {
      if (Object.keys(ProfileComponent.listingAgent).length == 0) {
        this.authService.agentDetail().subscribe(agentRes => {
          if (BaseComponent.user.is_paid_account) {
            this.selectedPlan = agentRes.result.selected_plan_id;
          }
          this.subscribed = true;
        });
      }
    }
  }

  getPlans(): any {
    if (this.plansList.length <= 0) {
      if (this.plansAPISubscription) {
        this.plansAPISubscription.unsubscribe();
      }
      this.plansAPISubscription = this.getPlansService.getPlans().subscribe(res => {
        let tempList = [];
        this.plansList = res.result.filter(plan => {
          if (plan.id.substr(0, 1) == 'A') {
            tempList.push(plan);
          }
          if (plan.id.substr(0, 2) == 'BR') {
            tempList.push(plan);
          }
          return tempList;
        });
      });
    }
  }

  upgradeAccount(plan: Plans): void {
    this.purchaseService.setPlan(plan);
    if (BaseComponent.user != undefined || Object.keys(BaseComponent.user).length != 0) {
      if (Object.keys(ProfileComponent.brokerageUser).length == 0) {
        this.authService.brokerageDetails().subscribe(brokerRes => {
          ProfileComponent.brokerageUser = brokerRes.result;
          ProfileComponent.brokerageUser.billing_info = brokerRes.result.billing_info;
          ProfileComponent.brokerageUser.payment_method = brokerRes.result.payment_method;
          this.routeOnUrl('/purchase');
        });
      } else {
        this.routeOnUrl('/purchase');
      }
    }
  }

  ngOnDestroy() {
    this.subscribed = false;
  }

}
