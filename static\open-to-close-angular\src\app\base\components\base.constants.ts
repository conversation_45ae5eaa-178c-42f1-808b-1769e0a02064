import { environment } from "../../../environments/environment";

// export var PATH='http://192.168.2.16:8000/api/v1';
// export var PATH='https://20180730-dot-staging-api-demo-dot-high-apricot-196023.appspot.com/api/v1';
// export var PATH='https://staging-api-demo-dot-high-apricot-196023.appspot.com/api/v1';
export var PATH= environment.apiUrl;

// export var IMAGEPATH = 'assets/images/';
export var IMAGEPATH = environment.imagePrefix;

export var API_REFERENCE ={

    'auth':{
        'signin':'/auth/login',
        'signup':'/auth/signup',
        'password':'/auth/password/',
        'logout':'/auth/logout',
        'verification':'/auth/email/confirmation',
        'agentSearch':'/profile/agent/search/',
        'brokerageAgentSearch':'/property/agent/search/',
        'getUserDetail':'/auth/user',
        'forgotPassword':'/auth/forgot/password',
        'resetPassword':'/auth/reset/password',
        'getAgentDetails' : '/profile/agent/get',
        'sendOtp' : '/auth/send/otp',
        'verifyOtp' : '/auth/verify/otp',
    },

    //wiseAgent

    'wiseAgent':{
        'signup': '/auth/wise_agent/register'
    },

    'profile':{
        'update':'/profile/update/',
        'state': '/base/states/',
        'city': '/base/cities/',
        'zipCode':'/base/zipcodes/',
        'billing-info':'/subscription/billing-info/',
        'updatePaymentMethod':'/subscription/payment-methods/',
        'uploadProfileImage': '/profile/update/photo',
        'cancelSubscription':'/subscription/cancel/',
        'BRAgentInvites' : '/profile/invite/agent',
        'updateAgentInviteStatus' : '/profile/brokerage/agent/invite/',
        'inviteAgent' : '/profile/brokerage/agent/invite/',
        'lenderSearch' : '/profile/mortgage/search',
        'manageLender' : '/profile/user/attach',
        'agentVerification' : '/profile/agent/verification',
        'subscriptionHistory' : '/subscription/purchase/history/',
        'downLoadSubscriptionHistory' : '/subscription/invoice',
        'getBrokerEmail' : '/profile/broker/get/',
        'getGuestUser': '/profile/guest/user/get'
    },

    'search':{
        'searchSuggestions':'/base/search/suggestion/',
        'searchResults':'/search',
        'searchPropertiesByFilters':'/search',
        'searchByMapCustomShape':'/search/custom_shape',
        'saveSearch':'/search/save/',
        'getSavedSearch':'/search/get/all/',
        'getOpenHouseAgent' : '/profile/brokerage/agent/list/',
        'eventSearch' : '/property/event/search/',
        'getUpdatedInfo' : '/property/get/single/',
        'searchCity':'/base/search/cities/',
        'searchZipCode':'/base/search/zipcode/',
    },

    'purchase':{
        'getPlans':'/subscription/plans/',
        'subscriptionToken':'/subscription/client/token/',
        'creditCard':'/purchase/billing_info',
        'creditCardList':'/subscription/payment-methods/1',
        'purchasePlanId':'/purchase/',
        'purchasePlan':'/subscription/purchase/',
        'updatePlans': '/subscription/plans/update/'
    },

    'property':{
        'addProperty':'/property/add/',
        'getAllProperty':'/property/get/all/',
        'getProperty':'/property/get/',
        'updateProperty':'/property/update/',
        'addPropertyExternal':'/property/external/save/',
        'addPropertyBuilding':'/property/building/save/',
        'addPropertyLocation':'/property/location/save/',
        'addPropertyTaxeExpsenses':'/property/taxes/expences/save/',
        'propertyImages':'/property/image/upload/',
        'searchProperty':'/property/search/',
        'saveToFavorite' : '/favourite/save/',
        'addPropertyEvent': '/property/event/manage',
        'addToMyOpenHouse' : '/property/myopenhouse/add/',
        'getEventManagerList': '/property/event/manage',
        'cancelEvent': '/property/event/manage',
        'updatePropertyEvent': '/property/event/manage',
        'acceptRequest': '/property/event/request/manage',
        'addPropertyRating' : '/property/rate/property/',
        'searchPolygons':'/search/search/polygons',
        'contactListingAgent' : '/chats/contact/agent',
        'getPropertyOverview' : '/property/agent/overview',
        'getPropertyEvent' : '/property/agent/view',
        'runEvent' : '/property/event/runtool/manage',
        'removeProperty' : '/property/myopenhouse/remove/',
        'shareProperty' : '/chats/share/property',
        'oveviewPdf' : '/property/agent/overview/export/pdf',
        'propertyFile' : '/property/file/?',
        'removeListing' : '/property/remove/listing',
        'addPropertyListingById' : '/property/fetch/property/',
    },

    'favorites':{
        'getFavoritesProperty' : '/favourite/get/all/web/',
    },

    'myClient':{
        'getMyClients' : '/clients/detail/',
        //Check in Screen
        'getClientActivity' : '/clients/client/manage',
        'addNewNote' : '/clients/save/',
        'deleteNote' : '/clients/remove/',
        'updateNote' : '/clients/update/',
        'getSingleClientInfo' : '/clients/client/detail/',
        'searchClients' : '/clients/manage/client/list',
        'exportCSV' : '/clients/export/client',
        'getClientListSP' : '/clients/get/share/property/client/',
        'getInviteLink' : '/clients/invite/link',

    },

    'myOpenHouse':{
        'getAllOpenHouse' : '/property/myopenhouse/get',
        'getOpenHouseDetailView' : '/property/myopenhouse/detail',
    },

    'chat':{
        'myClientList' : '/chats/approved/user',
        'getAllChatThread' : '/chats/get/chat/thread',
        'getAllChat' : '/chats/manage/chat',
        'sendMessage' : '/chats/manage/chat',
        'getRecent':'/chats/get/recent',
        'markReaded':'/chats/mark/message/readed',
        'getAllChatWithTimestamp' : '/chats/manage/chat',
    },

    'eventManager':{
        'getRunEventDetail' : '/property/event/runtool/detail',
        'addToCheckIn' : '/property/event/runtool/checkin/users',
        'getEventChatThread' : '/chats/event/chat/thread',
        'getUserList' : '/chats/event/chat/user',
        'checkInWithEmail' : '/property/event/runtool/checkin',
        'endOpenHouse' : '/property/event/runtool/manage',
    },

    'notification':{
        'getAllNotification' : '/notification/get/all/',
        'getChatMessageStatus' : '/chats/chat/remaining',
        'readNotification': '/notification/update/',
        'getNewNotificationStatus' : '/notification/remaining/',
    },

    'myLeads':{
        'getAllMyLeads' : '/lead/get/',
        'addRemoveArchive' : '/lead/archive/',
        'leadAdd' : '/lead/add/',
        'search' : '/lead/search/',
        'exportCSV' : '/lead/export/lead/',
        'addNewNote' : '/lead/notes/add/',
        'deleteNote' : '/lead/notes/update/',
        'updateNote' : '/lead/notes/update/',
        'getNote' : '/lead/notes/get/',
        'updateLeadProfile' : '/lead/update/lead/',
    },

    //admin
    'admin':{
        'getAllUsers' : '/auth/listuser',
        'getHBUser' : '/auth/super/user/HB',
        'getLAUser' : '/auth/super/user/LA',
        'getBRUser' : '/auth/super/user/BR',
        'manageUserAccountStatus' : '/auth/deactivate/user',
        'searchBrokerage' : '/profile/brokerage/search/',
        'impersonateUser' : '/auth/impersonate/user/login',
        'reSendConfimEmail' : '/auth/resend/email/confirm',
        'searchListing':'/property/admin/property/search',
        'downloadCSV' : '/auth/download/users/csv',
        'upgradeUser' : '/auth/permanent/premium/subscription',
        'downgradeUser' : '/auth/remove/permanent/premium',
    },
    
    'mapBoundary' : {
        'MapBoundryUsingState' : '/base/boundary/state',
        'MapBoundryUsingGoId' : '/base/boundary/city',
        'MapBoundryUsingZipcodeId' : '/base/boundary/zipcode',
    }

};

export var METHOD_REFERENCE={
    'GET':'GET',
    'POST':'POST',
    'PUT':'PUT',
    'DELETE':'DELETE',
}
