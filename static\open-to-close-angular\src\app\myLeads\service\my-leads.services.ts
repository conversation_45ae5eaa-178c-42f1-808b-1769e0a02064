import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ApiService } from '@app/base/services/api.service';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiResponse } from '@app/auth/models/api-response';

@Injectable()
export class MyLeadsService {

  public baseservice:BaseComponent;
  public apiService:ApiService;
  
  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
  }

  getMyLeads(urlParams):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['myLeads']['search'],urlParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  manageArchive(leadParams):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['myLeads']['addRemoveArchive'],leadParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  searchLeads(userName):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['myLeads']['search'],userName.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  exportCSV(leadType):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['myLeads']['exportCSV'],leadType.toString(), null, null, null, false, true);
    return this.apiService.downloadFile(options);
  }


  public addNewNote(params):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['myLeads']['addNewNote'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  public updateNote(params):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['myLeads']['updateNote'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
  
  public deleteNote(params):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['DELETE'],API_REFERENCE['myLeads']['deleteNote'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  getMyNotes(id):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['myLeads']['getNote']+'?lead_id='+id,{});
    return this.apiService.apiCall(options);
  }

  public updateLeadProfile(params):Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['myLeads']['updateLeadProfile'],params.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

}