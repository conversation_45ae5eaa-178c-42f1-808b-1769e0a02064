/* You can add global styles to this file, and also import other style files */
/* @import "~@angular/material/prebuilt-themes/indigo-pink.css"; */

.cursor-pointer{
    cursor: pointer !important;
}
.form_group input[type="button"] {
    background: #10B8A8 ;
    color: white;
    border-radius: 20px ;
    width: 100px ;
    padding: 8px 2px ;
    font-size: 12px ;
    color: #FFFFFF ;
    letter-spacing: 0 ;
}
.input-group.stylish-input-group input.form-control.search-input-layout{
    margin-bottom: 0px !important;
     text-align: left;
   }
.form-validation {
    color: red;
    margin: 0px 0 0px 0 !important;
    font-size: 11px !important;
    font-family: 'Source Sans Pro', sans-serif !important;
  }
.submit-disable{
    opacity: 0.4;
}
.nav.navbar-nav.navbar-right.logout{
    margin: 0px -15px !important;
}
.navbar-nav{
    margin: 0px -15px !important;
}

.upgrade-account-a{
    color: #FFFFFF;
    text-decoration: none !important;
}
.upgrade-account-a:hover{
    color: #FFFFFF;
    text-decoration: none !important;
}
.new_profile_title{
    padding-top: 80px !important;
}
.search-agent-padding{
    padding-top: 10px;
    padding-bottom: 10px;
}

tag{
    background: #10B8A8 100% !important;
}
.bootstrap[_ngcontent-c2] tag[_ngcontent-c2]{
    opacity: 1 !important;
    background: #10B8A8 100% !important;
    border-radius: 4px !important;
    height: 31px !important;
    padding: 0px !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
    letter-spacing: 1px !important;
    line-height: 30px !important;
}

.bootstrap.ng2-tag-input.ng2-tag-input--focused[_ngcontent-c2]{
    border-bottom: 1px solid #bcbbbb !important;
}

.ng2-dropdown-menu .ng2-dropdown-menu---width--4 .ng-trigger .ng-trigger-fade .ng2-dropdown-menu--open{
    width: 1000px !important;
}

.ng2-dropdown-menu[_ngcontent-c5]{
    width: 350px !important;
}

.ng2-tag-input__text-input{
    height: 23px !important;
}

.tag-dropdown-icon{
    top: 3px !important;
}
tag-input-form{
    width: 100%;
}

.header{
    z-index: 2 !important;
}

.bootstrap[_nghost-c7] svg[_ngcontent-c7], .bootstrap [_nghost-c7] svg[_ngcontent-c7]{
    height: 26px !important;
}

.optWrapper .options{
    max-height: 550px !important;
}
.selected-plan{

    background: #10B8A8 !important;
    color: #F0F2F4 !important;
 }
 .selected-plan-margin{
     margin-top: 0px;
 }

 .broker-plan-font{
    font-size: 19px !important;
    padding: 7px !important;
}

.br-upgrade-acc{
    padding: 6px 12px 5px 12px !important;
}

.ng-select.custom {
    border:0px;
    min-height: 0px;
    border-radius: 0;
}
.ng-select.custom .ng-control  {
    min-height: 0px;
    border-radius: 0;
}
.ng-select.custom .ng-control .ng-value-container .ng-input > input{
    cursor: pointer !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.marked{
    background-color: #10B8A8;
    color: white !important;
}

.ng-select.focused:not(.opened)>.ng-control{
    border: 1px solid #dbdbdb;
    border-color:none !important;
    box-shadow: none !important;
    /* border-color:#10B8A8;
    box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 0 3px rgba(0,126,255,0.1) */
}
.ng-select.agent-dropdown .ng-control{
    border: none !important;
    border-radius: 0px !important;
    border-bottom: 1px solid #bbbaba !important;
    font-size: 16px !important;
    color: #5A5A5A;
}

.ng-select.signup-dropdown .ng-control{
    border: none !important;
    border: 1px solid #DBDBDB !important;
    padding: 20px !important;
    padding-left: 0px !important;

}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.selected .ng-option-label{
    font-weight: normal !important;
}

.ng-select .ng-control .ng-value-container .ng-placeholder{
    font-size: 16px !important;
    color: #8D8D8D !important;
    font-weight: normal !important;
    margin-left: -9px !important;
}

.ng-select.signup-dropdown  .ng-control .ng-value-container .ng-placeholder{
    font-size: 16px !important;
    color: #8D8D8D !important;
    font-weight: normal !important;
    margin-left: 0px !important;
}

.ng-select .ng-arrow-zone{
    border: solid #10B8A8 !important;
    border-width: 0 1px 1px 0 !important;
    display: inline-block !important;
    padding: 5px !important;
    transform: rotate(45deg) !important;
    -webkit-transform: rotate(45deg) !important;

    width: 0 !important;
}

.ng-select.date-drop-down  .ng-control .ng-value-container .ng-placeholder{
    font-size: 16px !important;
    color: #8D8D8D !important;
    font-weight: normal !important;
    margin-left: 0px !important;
}

.ng-select.share-property-email  .ng-control .ng-value-container .ng-placeholder{
    font-size: 16px !important;
    color: #8D8D8D !important;
    font-weight: normal !important;
    margin-left: 0px !important;
}

.ng-select.share-property-email .ng-control{
    border: 1px solid #a9a9a9;
}

.ng-select.date-drop-down .ng-arrow-zone{
    border: solid #e0e0e0 !important;
    border-width: 0 2px 2px 0 !important;
    display: inline-block !important;
    padding: 5px !important;
    transform: rotate(45deg) !important;
    -webkit-transform: rotate(45deg) !important;
    width: 0 !important;
    margin: 16px 15px 20px 0px !important;
}

.ng-select.date-drop-downEvent .ng-arrow-zone{
    border: solid #e0e0e0 !important;
    border-width: 0 2px 2px 0 !important;
    display: inline-block !important;
    padding: 5px !important;
    transform: rotate(45deg) !important;
    -webkit-transform: rotate(45deg) !important;
    width: 0 !important;
    margin: 16px 8px 20px 0px !important
}

.ng-select.date-drop-down .ng-control{
    border: 1px solid #dbdbdb;
    width: 313px;
    height: 44px;
}
.ng-select.o-h-a .ng-control{
    border: 1px solid #dbdbdb;
    width: 199px;
    height: 45px;
}
.ng-select.o-h-a .ng-arrow-zone{
    border: solid #e0e0e0 !important;
    border-width: 0 1px 1px 0 !important;
    display: inline-block !important;
    padding: 7px !important;
    transform: rotate(45deg) !important;
    -webkit-transform: rotate(45deg) !important;
    width: 0 !important;
    margin: 14px 15px 20px 0px !important;
}
.o-h-a .ng-dropdown-panel{
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: absolute;
    width: 199px;
    z-index: 1050;
}
.ng-select.o-h-a .ng-control .ng-value-container .ng-placeholder {
    font-size: 16px !important;
    color: #8D8D8D !important;
    font-weight: normal !important;
    margin-left: -4px !important;
    margin-top: 1px;
}
.ng-select.date-drop-down1 .ng-control{
    border: 1px solid #dbdbdb;
    width: 298px !important;
    height: 44px;
}

.ng-select.date-drop-downEvent .ng-control{
    border: 1px solid #ccc;
    width: 308px !important;
    height: 44px;
    border-radius: 5px !important;
}

.ng-select.shareProrty .ng-control{
    width: 371px !important;
    height: 44px;
}
.ng-select.share-property-email .ng-control{
    height: 45px !important;
}

.ng-dropdown-panel.date-drop-down .scroll-host {
    height: 100% !important;
    max-height: 100% !important;
}
.date-drop-down .ng-dropdown-panel{
    width: 313px !important;
}

.date-drop-down.shareProrty .ng-dropdown-panel{
    width: 371px !important;
}

.date-drop-downEvent .ng-dropdown-panel{
    width: 308px !important;
    height: 324% !important;
}

.date-drop-down1 .ng-dropdown-panel{
    width: 298px !important;
}

.ng-select.eventChat .ng-control{
    border: 1px solid #dbdbdb;
    width: 284px !important;
    height: 44px;
}
.eventChat .ng-dropdown-panel{
    width: 284px !important;
}
.ng-select .ng-arrow-zone .ng-arrow{
    border-color: none !important;
    border-style: none !important;
    border-width: none !important;
    display: none !important;
}

.ng-select .ng-control .ng-value-container{
    flex: 0.98 !important;
}
input[type="file"] {
    display: none;
}
.custom-file-upload {
    border: 1px solid #566D77;
    border-radius: 100px;
    cursor: pointer;
    color: #566D77;
    width: 16%;
    padding-bottom: 5px;
}

.file-upload-text{
    font-size: 13px;
    color: #566D77;
    vertical-align: middle;
    margin-left: 15px;
    margin-right: 15px;
    font-weight: 100;
    font-family: 'Source Sans Pro', sans-serif !important;
    letter-spacing: 0px;
}

.file-upload-title{
    width: 90% !important;
}

.file-upload-label{
    width: 21% !important;
    vertical-align: text-top !important;
    font-size: 27px !important;
    margin-left: 10px;
    margin-top: 4px;
}
.tab-margin{
    margin: unset !important;
}
.tab-title{
    width: 1120px !important;
}

.bootstrap.ng2-tag-input.ng2-tag-input--focused[_ngcontent-c3]{
    border-bottom: 1px solid #bcbbbb !important;
}

.new_profile_title img.new_symbols-avatar{
    border-radius: 50px;
    object-fit: cover;
}

.verification-text{
    text-align: center;
}

.header{
    z-index: 16 !important;
}

.ng-select.agent-dropdown.ng-multiple .ng-control .ng-value-container .ng-value{
    font-size: 0.9em;
    margin-right: 5px !important;
    margin-bottom: 0px !important;
    padding: 0px;
    padding-right: 0px;
    padding-left: 6px;
    padding-top: 1px;
    padding-bottom: 1px;
    background-color: #1fb8a8;
    border-radius: 6px;
    border: none !important;
    color: white;
}

.ng-select.ng-multiple .ng-control .ng-value-container .ng-value .ng-value-icon.right{
    border-left: 0px !important;
}

.ng-select.ng-multiple .ng-control .ng-value-container .ng-value .ng-value-icon:hover{
    background-color: #1fb8a8 !important;
}

.ng-select.ng-multiple .ng-control .ng-value-container .ng-value .ng-value-label{
    padding: 2px 0px 2px 1px !important;
}

.ng-select.agent-dropdown.ng-multiple .ng-control .ng-value-container{
    padding-left: 8px !important;
    padding-top: 0px !important;
}

.agent-dropdown .ng-dropdown-panel{
    z-index: 1000 !important
}
.ng-select.opened .ng-control{
    z-index: 1000 !important
}

.ng-select.ng-multiple .ng-control .ng-value-container .ng-value .ng-value-icon{
    padding: 1px 3px !important;
}

.ng-select.search-dropdown.ng-multiple .ng-control .ng-value-container .ng-value{
    font-size: 0.9em;
    margin-right: 5px !important;
    margin-bottom: 0px !important;
    padding: 0px;
    padding-right: 0px;
    padding-left: 6px;
    padding-top: 1px;
    padding-bottom: 1px;
    background-color: #1fb8a8;
    border-radius: 6px;
    border: none !important;
    color: white;
}
.ng-select.search-dropdown .ng-control{
    width: 126px !important;
    height: 45px !important;
    cursor: pointer !important;
}

.ng-select.search-dropdown .ng-control .ng-value-container .ng-placeholder{
    font-size: 16px !important;
    color: #8D8D8D !important;
    font-weight: normal !important;
    margin-left: -3.5px !important;
}

.ng-select.search-dropdown .ng-arrow-zone{
    border: solid #e0e0e0 !important;
    border-width: 0 2px 2px 0 !important;
    display: inline-block !important;
    padding: 5px !important;
    transform: rotate(45deg) !important;
    -webkit-transform: rotate(45deg) !important;
    width: 0 !important;
    margin: 16px 15px 20px 0px !important;
}

.ng-select.search-dropdown .ng-control{
    border: 1px solid #dbdbdb;
}

.ng-select.search-dropdown .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{
    font-size: 16px !important;
    color: #5A5A5A;
    padding: 6px 10px;
}

.ng-select.ad-user-dropdown.ng-multiple .ng-control .ng-value-container .ng-value{
    font-size: 0.9em;
    margin-right: 5px !important;
    margin-bottom: 0px !important;
    padding: 0px;
    padding-right: 0px;
    padding-left: 6px;
    padding-top: 1px;
    padding-bottom: 1px;
    background-color: #1fb8a8;
    border-radius: 6px;
    border: none !important;
    color: white;
}
.ng-select.ad-user-dropdown .ng-control{
    width: 100% !important;
    height: 36px !important;
    cursor: pointer !important;
    border: 1px solid #a9a9a9;
}

.ng-select.ad-user-dropdown .ng-control .ng-value-container .ng-placeholder{
    font-size: 16px !important;
    color: #8D8D8D !important;
    font-weight: normal !important;
    margin-left: -3.5px !important;
}

.ng-select.ad-user-dropdown .ng-arrow-zone{
    border: solid #a9a9a9 !important;
    border-width: 0 2px 2px 0 !important;
    display: inline-block !important;
    padding: 5px !important;
    transform: rotate(45deg) !important;
    -webkit-transform: rotate(45deg) !important;
    width: 0 !important;
    margin: 16px 15px 20px 0px !important;
}

.ng-select.listing-mg-status.ng-multiple .ng-control .ng-value-container .ng-value{
    font-size: 0.9em;
    margin-right: 5px !important;
    margin-bottom: 0px !important;
    padding: 0px;
    padding-right: 0px;
    padding-left: 6px;
    padding-top: 1px;
    padding-bottom: 1px;
    background-color: #1fb8a8;
    border-radius: 6px;
    border: none !important;
    color: white;
}
.ng-select.listing-mg-status .ng-control{
    width: 100% !important;
    height: 36px !important;
    cursor: pointer !important;
    border: 1px solid #a9a9a9;
}

.ng-select.listing-mg-status .ng-control .ng-value-container .ng-placeholder{
    font-size: 16px !important;
    color: #8D8D8D !important;
    font-weight: normal !important;
    margin-left: -3.5px !important;
}

.ng-select.listing-mg-status .ng-arrow-zone{
    border: solid #a9a9a9 !important;
    border-width: 0 2px 2px 0 !important;
    display: inline-block !important;
    padding: 5px !important;
    transform: rotate(45deg) !important;
    -webkit-transform: rotate(45deg) !important;
    width: 0 !important;
    margin: 16px 15px 20px 0px !important;
}

.listing-mg-status .ng-dropdown-panel{
    width: 129% !important;
}

.ng-select.search-dropdown .ng-control{
    border: 1px solid #dbdbdb;
}

.ng-select.save-dropdown.ng-multiple .ng-control .ng-value-container .ng-value{
    font-size: 0.9em;
    margin-right: 5px !important;
    margin-bottom: 0px !important;
    padding: 0px;
    padding-right: 0px;
    padding-left: 6px;
    padding-top: 1px;
    padding-bottom: 1px;
    background-color: #1fb8a8;
    border-radius: 0px;
    border: none !important;
    color: white;
}
.ng-select.save-dropdown .ng-control{
    border: none !important;
    border-radius: 0px !important;
    font-size: 16px !important;
    color: #5A5A5A;
}

.ng-select.save-dropdown .ng-control{
    background-color: rgb(240, 242, 245) !important;
}

.save-dropdown .ng-select.opened>.ng-control{
    background: #F0F2F4 !important;
}

.save-dropdown .ng-dropdown-panel{
    width: 100% !important;
}

.ng-select.any-date .ng-control{
    border: none !important;
    border-radius: 0px !important;
    font-size: 16px !important;
    color: #5A5A5A;
}

.ng-select.save-dropdown .ng-control{
    width: 100% !important;
    height: 44px !important;
}
.ng-select.save-dropdown .ng-arrow-zone{
    border: solid #e0e0e0 !important;
    border-width: 0 2px 2px 0 !important;
    display: inline-block !important;
    padding: 5px !important;
    transform: rotate(45deg) !important;
    -webkit-transform: rotate(45deg) !important;
    width: 0 !important;
    margin: 16px 2px 20px 0px !important;
}
.ng-dropdown-panel .scroll-host {
    height: 100% !important;
    /* max-height: 100% !important; */
}

.modal-openn{
    padding: 0px !important;
  }
.searched-agent-image{
    height: 70px !important;
    width: 70px !important;
    border-radius: 85px !important;
}

.remove-span{
    margin-right: -28px !important;
  }

.remove-span1{
    margin-right: 14px !important;
}

/* The location pointed to by the popup tip. */
.popup-tip-anchor {
    height: 0;
    position: absolute;
    /* The max width of the info window. */
    width: 200px;
  }
  /* The bubble is anchored above the tip. */
  .popup-bubble-anchor {
    position: absolute;
    width: 100%;
    bottom: /* TIP_HEIGHT= */ 8px;
    left: 0;
  }
  /* Draw the tip. */
  .popup-bubble-anchor::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    /* Center the tip horizontally. */
    transform: translate(-50%, 0);
    /* The tip is a https://css-tricks.com/snippets/css/css-triangle/ */
    width: 0;
    height: 0;
    /* The tip is 8px high, and 12px wide. */
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: /* TIP_HEIGHT= */ 8px solid white;
  }
  /* The popup bubble itself. */
  .popup-bubble-content {
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(-50%, -100%);
    /* Style the info window. */
    background-color: white;
    padding: 5px;
    border-radius: 5px;
    font-family: 'Source Sans Pro', sans-serif;
    overflow-y: auto;
    max-height: 60px;
    box-shadow: 0px 2px 10px 1px rgba(0,0,0,0.5);
  }

  #styles, #add-tab {
    float: left;
    margin-top: 10px;
    width: 400px;
  }
  #styles label,
  #add-tab label {
    display: inline-block;
    width: 130px;
  }

.width-100{
    width: 100% !important;
}

.btn:hover{
    color: #FFFFFF !important;
}

.btn:focus{
    color: #FFFFFF !important;
}
.my-custom-class-for-label {
    width: 50px;
    height: 20px;
    border: 1px solid #eb3a44;
    border-radius: 5px;
    background: #fee1d7;
    text-align: center;
    line-height: 20px;
    font-weight: bold;
    font-size: 14px;
    color: #eb3a44;
  }
.resetPwd{
    margin-left: -75px !important;
}
.resetPwdText{
    text-align: center !important;
}
.resetPwdPosition{
    margin-top: 40px !important;
}
.err-text-align{
    text-align: start !important;
}
.upgrade-box{
    padding-bottom: 30px !important;
    vertical-align: top !important;
    margin-top: 45px !important;
}
.label-space{
    padding-bottom: 7px !important;
}
.chat_on_bottom{
    height: calc(100vh - 199px) !important;
}

.new-label{
    top: 10px !important;
    font-size: 16px !important;
}

.location-input{
    padding-bottom: 25px !important;
}

.new_form_label label :active{
    background-color: #1fb8a8 !important;
}
.new_form_label test:hover{
    background-color: #10B8A8 !important
}
.new_form_label test:active{
    background-color: #10B8A8 !important
}

/* .map_side_bar {
    position: absolute;
    top: 0px;
    right: 0;
    width: 350px;
    background: white;
    box-shadow: -2px 2px 4px 0 rgba(0,0,0,0.11);
    height: 100%;
    padding: 10px 4px 0px 4px !important;
}

.map_listing {
    margin-top: 7px !important;
    float: left;
    height: 93% !important;
    overflow: scroll;
    width: 100%;
} */

.map_listing .home_group {
    float: left;
    width: 100%;
    margin-top: 7px !important;
    margin-bottom: 50px !important;
}

.addeventdate{
    margin-top: 1px !important;
}
.addeventdatebox{
    height: 44px !important;
    padding: 8px !important;
}

.eventType{
    margin-top: 1px !important;
    width : 95% !important;
}
/* .right_side_event_agent.right_side_overflow{
    height: calc(100vh - 54px) !important;
} */

.chat_message .left_side.height_auto{
    height: calc(100vh - 117px) !important;
}

.chat_session.Recipient_Chosen_chat.message_list_with_name{
    height: calc(100vh - 349px) !important;
}
/* .meassage_event{
    margin: 0px auto 19px 20px !important;
} */

.left_side input.meassage_event{
    margin: 2px auto 19px 20px !important;
}

.right_side_event_agent{
    width: 77% !important;
}

.file-upload-image{
    width: 41% !important;
    vertical-align: text-top !important;
    font-size: 27px !important;
    margin-top: -10px;
    margin-bottom: 0px;
}
.custom-file-upload-image {
    border: 1px solid #10B8A8;
    border-radius: 100px;
    cursor: pointer;
    color: #FFFFFF !important;
    width: 16%;
    background: #10B8A8 !important;
    padding-bottom: 3px;
}
.upload-image-text{
    color: #FFFFFF !important;
}

/* @media (min-width: 768px){
.dropimage .col-sm-8{
    width: 102% !important;
    }
} */
.videosize{
margin-left: -55px !important
}

.upload-img{
    height: auto;
    max-height: 174px;
    width: 334px;
    object-fit: cover;
    /* height: 130px; */
}
.upload-img-2{
    width: 169px;
    height: 130px;
}

.image-scroll{
 overflow-x: hidden;
 overflow-y: auto;
 min-height: 2px !important;
 max-height: 421px !important;
 width: 358px !important;
}

.calander-btn:hover{
    color: #8D8D8D !important;
}
.theme_img_modal{
    top: 22% !important;
    left: 18% !important;
}
.theme_img_modal img{
    margin-right: 22px !important;
}
/* input.mini :active{
    border: 1px solid #10B8A8 !important
} */

.rate-modal-row{
    margin-left: -79px !important;
    margin-right: -123px !important;
}
/* .sp-arrow.sp-next-arrow{
    height: 64px !important;
} */
.sp-full-screen .sp-image-container{
    display: flex;
    justify-content: center;
}
.sp-full-screen .sp-image-container img{
    /* height: 630px !important; */
    height: auto !important;
}
.sp-full-screen .sp-bottom-thumbnails{
    width: 100% !important;
    padding-left: 15% !important;
}

.sp-next-arrow-big{
    height: 60px !important;
}
.sp-previous-arrow-big{
    height: 60px !important;
}

.prtitle-font-color{
    color: #5A5A5A !important;
    margin-bottom: 25px;
}

.pro-upload-font-color{
    color: #5A5A5A !important;
}

.caption-img{
    width: 50%;
}
.rounded-img{
    border-radius: 50px !important;
}

.ngx-pagination .disabled {
    padding: 1.1875rem 0.625rem !important;
    color: #cacaca;
    cursor: default;
}

.ngx-pagination .current {
    padding: 0.1875rem 0.625rem !important;
    background: none !important;
    color: #10B8A8 !important;
    cursor: default;
    border-bottom: solid 2px !important;
}

.ngx-pagination .pagination-next a::after, .ngx-pagination .pagination-next.disabled::after {
    content: ' ' !important;
    display: inline-block;
    margin-left: 0.5rem;
}
.ngx-pagination .pagination-previous a::before, .ngx-pagination .pagination-previous.disabled::before {
    content: ' ' !important;
    display: inline-block;
    margin-right: 0.5rem;
}
.ngx-pagination li {
    display: inline-block;
    padding-top: 1.200rem !important;
}
.ngx-pagination a, .ngx-pagination button{
    color:#5A5A5A !important;
}
.header {
    z-index: 1002 !important;
}
.search-agent-event{
    height: 73px !important;
    border-radius: 41px;
    width: 73px !important;
}
.searched-agents{
    margin: 0 !important;
    margin-top: 5px !important;
}
.search-agent-list{
    width: 98%;
    height: 300px;
    overflow-x: auto;
}
.check_group.mt-10.found_agent_add_p input[type="checkbox"]{
    top: -55px !important;
}
.check_group.mt-10.found_agent_add_p input[type="radio"]{
    top: -48px !important;
}
textarea.new_form{
    background-color: #FFFFFF;
    border: 1px solid #DBDBDB;
    border-radius: 2px;
    width: 100%;
    padding: 10px 7px;
    margin-top: 14px;
    outline: 0;
    margin-bottom: 2px;
    font-size: 16px;
    color: #5A5A5A;
}
.textarea-input-height{
    height: 100px;
}
.datepicker-days{
    cursor: pointer;
}
.error-validation{
    font-size: 13px;
    color: #ff0000;
}

.event-type.date-drop-downEvent .ng-dropdown-panel {
    width: 308px !important;
    height: auto !important;
}
.event-list.table-responsive{
    overflow-x: auto !important;
}

.option-menu{
    padding: 10px !important;
}
.option-menu:hover{
    background: #10B8A8;
    color: #ffffff;
}
.click_menu_open.events{
    padding: 0px !important
}
.open_b{
    background: #566d77;
    display: inline-block;
    color: white;
    padding: 1px 4px;
    font-size: 12px;
    border-radius: 5px;
}
.open_a{
    background: #BD3430;
    display: inline-block;
    color: white;
    padding: 1px 4px;
    font-size: 12px;
    border-radius: 5px;
}
.modal-body.bor_top_bg{
    width: 100% !important;
}
.property-list-table{
    padding-bottom: 25px !important;
    min-width: 1170px;
}
.load_more{
    margin: 30px auto 0px auto;
}
.going-text{
    font-weight: bold;
    color: #1fb8a8;
    padding-right: 0px !important;
}
.list-filter .SumoSelect > .optWrapper{
    width: 146px !important;
    left: -15px !important;
    top: 50px !important;
}
.list-filter .SumoSelect > .optWrapper li label{
    font-weight: normal !important;
}
.list-filter .SumoSelect > .optWrapper li{
    color: #868686 !important;
}
.list-filter .SumoSelect > .optWrapper ul li:hover{
    color: white !important;
}
.SumoSelect > .optWrapper > .options li.opt:hover{
    color: white !important;
}
.list-filter .SumoSelect > .optWrapper ul li label:hover{
    color:  white !important;
}
.list-filter .SumoSelect > .optWrapper.multiple > .options li.opt.selected span i, .SumoSelect .select-all.selected > span i, .SumoSelect .select-all.partial > span i{
    background-image: url('../assets/images/symbols-glyph-form-check.png') !important;
    background-size: 100% 100% !important;
    background-color: #DBDBDB !important;
}
.SumoSelect > .CaptionCont > span{
    cursor:pointer  !important;
}

img.small-image{
    /* width: 50px; */
    /* height: 50px; */
    width: 63px;
    height: 85%;
    float: left;
}
.form-control:focus{
    border-color: #3ab8a8 !important;
}
.map-listing-custom{
    margin-top: 7px !important;
    float: left;
    height: 87% !important;
    overflow: scroll;
    width: 100%;
}
.box_on_map_event{
  /* box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2); */
  transition: 0.3s;
  width: 100%;
  /* border-radius: 5px; */
   height: auto;
   min-width: 200px;
   min-height: 192px;
   /* max-height: 192px; */
    /* width: 203px; */
    overflow: auto;
    /* overflow-x: hidden; */
    background: #FFFFFF;
    /* box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30); */
}
.on_map_details{
    margin-top: 9%;
    margin-left: 10%;
}

#map_info_bubble::-webkit-scrollbar {
    width: 5px;
}

#map_info_bubble::-webkit-scrollbar-thumb {
    background: #10B8A8;
    border-radius: 4px;
}

.event-list-map{
    height: 75px;
    padding: 0px 0px 0px 0px;
    /* border-top: 4px solid #10B8A8; */
}
.event-map-detail{
    padding-left: 3px;
    width: 65%;
    float: left;
}
.chat-left-align{
    float: left;
}
.chat-thread-img{
    vertical-align: -webkit-baseline-middle;
}
.chat-thread-last{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-height: 16px;     /* fallback */
    max-height: 32px;      /* fallback */
    -webkit-line-clamp: 2; /* number of lines to show */
    -webkit-box-orient: vertical;
}
.chat-user-image{
    height: 50px !important;
    width: 50px !important;
    border-radius: 31px !important;
    object-fit: cover;
}
.street-title{
    font-size: 12px;
    color: #676767;
    line-height: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.street-title::-webkit-scrollbar {
    display: none;
}
.event-map-time{
    color: #707070;
    font-size: 11px;
    margin-bottom: 0px !important;
}
.event-map-date{
    color: #4d4d4d;
    font-size: 13px;
    margin-top: -4px;
    margin-bottom: 0px !important;
}
.save-note{
    float: left;
}
.cancel-note{
    margin-top: 16px !important;
    margin-left: 16px !important;
    cursor: pointer;
}
.saved-note{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-height: 20px;
    max-height: 108px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.note-textarea{
    padding: 6px;
}
.client-profile-pic{
    height: 60px !important;
    width: 61px !important;
}
.event-hide-msg-box{
    position: absolute;
    font-size: 16px;
    color: #FFFFFF;
    top: 27% !important;
    left: 3%;
    width: 95%;
}
span.message_time {
    font-size: 12px;
    background: #10B8A8;
    color: #FFFFFF;
    padding: 3px 6px 1px 5px;
    border-radius: 11px;
    margin-left: 10px !important;
}
.unread-msg-dot{
    font-size: 11px;
    padding-right: 2px;
    color: #f19632d8;
    vertical-align: super;
}
.selected-client{
    border-left: 6px solid white;
    padding-left: 14px !important;
    background: #5c6467;
}
input.meassage_search {
    background: #455A64 !important;
    border: 1px solid #636363;
    border-radius: 2px;
    font-size: 16px;
    color: #ffffff !important;
    height: 40px;
    margin: 20px auto 20px 20px;
    width: 90%;
    padding-left: 10px;
    outline: 0 !important;
}
.box-pagging{
    margin: 20px 0px !important;
}
.chat_session.chat_over_flow {
    height: calc(100vh - 276px);
    overflow-y: scroll;
    overflow-x: hidden !important;
}
.chat-profile-img{
    height: 60px !important;
    width: 62px !important;
    margin-top: 11px;
    border-radius: 31px !important;
    object-fit: cover;
}
.chat-profile-img-span{
    vertical-align: -webkit-baseline-middle;
    vertical-align: middle;
}
.new-chat-div{
    margin-top: 18px;
    height: calc(100vh - 189px);
    overflow-y: auto;
    overflow-x: hidden !important;
}
.new-chat-name-msg[_ngcontent-c4] {
    padding: 0px 0px 4px 10px !important;
}
.new-chatmessage_name{
    font-size: 16px;
    color: black;
}
.new-client-list{
    cursor: pointer;
    padding: 8px 0px 7px 0px;
}
.rating-span{
    padding-left: 5px;
    padding-right: 5px;
}
.rating-img{
    background: #E4F8F8;
    height: 38px !important;
    padding: 6px;
    border-radius: 30px;
    margin-right: 7px;
}
/* .your_open_agent {
    position: absolute;
    top: 96% !important;
    left: 35px;
    width: 97.1%;
} */

.image-gradient{
    position:relative;
    display:inline-block;
  }
.image-gradient:after {
    content:'';
    position:absolute;
    left:0; top:0;
    width:100%; height:100%;
    display:inline-block;
    background: -moz-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(0,0,0,0.41) 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.41)), color-stop(100%,rgba(0,0,0,0))); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, rgba(0,0,0,0) 0%,rgba(0,0,0,0.41) 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, rgba(0,0,0,0) 0%,rgba(0,0,0,0.41) 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, rgba(0,0,0,0) 0%,rgba(0,0,0,0.41) 100%); /* IE10+ */
    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%,rgba(0,0,0,0.41) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a6000000', endColorstr='#00000000',GradientType=0 ); /* IE6-9 */
  }

.event-card-image-gradient:after{
    left:0; top:0;
    width:100%;
    display:inline-block;
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(#0d0e0e96,#0d0e0e96);
    opacity: .6;
  }
.card-image:after{
    height: 192px;
}

.run-tool-image-gradient:after{
    height: 330px;
    background-image: linear-gradient(#0d0e0e8a,#0d0e0e78) !important;
}


.pluse-more{
    font-size: 14px !important;
    border-bottom: 2px solid #3ab8a8 !important;
    color: #3ab8a8 !important;
    cursor: pointer;
}

.homeType-width .SumoSelect > .optWrapper{
    width: 184% !important;
    height: 289px;
    overflow: auto;
}
.all_home_type .SumoSelect > .optWrapper{
    width: 100% !important;
    height: 289px;
    overflow: auto;
}
.property-status .SumoSelect > .optWrapper{
    width: 200% !important;
}
.event-type .SumoSelect > .optWrapper{
    /* width: 210% !important; */
    /* width: 170% !important; */
    width: 100% !important;
}

.gmnoprint_2 {
    height: 38px !important;
    top: 20px !important;
    width: 300px !important;
    left: -290px !important;
}
/* $(".gmnoprint").addClass("gmnoprint_2"); */
.info-bubble-top{
    top: 15px;
    z-index: 1500;
}

.info-bubble-marker{
    width: 200px !important;
    height: 200px !important;
    top: -204px !important;
}

.pop_div{
  margin-top: 5px;
  border-radius: 5px;
  box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
}

.pop_div:hover {
    display: block !important;
}
.pop_div_mo{
    display: block !important;
}
.search-span{
    position: absolute;
    width: 373px;
}
.checkmark {
    position: absolute !important;
    top: 3px !important;
    left: 0px;
    height: 16px !important;
    width: 16px !important;
    border: 1px solid #eee;
}
.check_group.profile_checkbox .form_group label.width_auto {
    margin-left: 1px;
}
.sign_modal .modal-body{
    border-radius: 4px !important
}
.sign_modal .modal_content{
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.modal_footer{
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.event-type-msg{
    font-weight: 600;
    vertical-align: text-top;
}
.event-title-logo{
    height: 30px !important;
}
.facts-row{
    border-top: 1px #ccc solid;
    margin-left: -2px;
}
.facts-bottom-border{
    border-bottom: 1px #ccc solid;
    margin-left: -2px;
}
.sp-thumbnails-container{
    margin: initial !important;
    width: 100% !important;
}
.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{
    border-bottom: 0px !important;
}

@media only screen and (max-width: 767px) {
    /* .display_none_map.show_map_mobile .map_icons_location{
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 111;
        display: BLOCK !important;
        left: 85% !important;
    }
    .display_none_map.show_map_mobile .map_icons{
        position: absolute;
        right: 0;
        left: 85%;
        bottom: 0;
        z-index: 111;
        display: BLOCK !important;
    } */
    .map_listing .home_group {
        margin-top: 0px !important;
        margin-bottom: -25px !important;
    }
    .show_map_button{
        position: fixed;
        z-index: 100;
        margin-bottom: 39px!important;
        margin-left: 14px!important;
        text-align: center;
        box-shadow: 0px 1px 5px 1px #80808082;
    }
    .show_list{
        margin-bottom: 50px !important;
        margin-left: 14px !important;
    }
    .map_listing{
        float: left;
        height: 100%;
        overflow: hidden;
        width: 100%;
        margin-bottom: 30px;
    }
    .ng-select.search-dropdown .ng-control{
        width: 100% !important;
    }
    .ng-select.date-drop-down .ng-control{
        width: 100% !important;
        margin-bottom: 6px !important;
    }
    .date-drop-down .ng-dropdown-panel{
        width: 100% !important;
    }
    .footer-class{
        bottom: 0 !important;
        position: fixed !important;
    }
    .date-box{
        padding-bottom: 5px !important;
        padding-left: 0px !important;
        padding-right: 0px !important;
    }
    .ngx-pagination {
        margin-left: 0;
        margin-bottom: 2rem !important;
    }
    .my-listing-header{
        margin-bottom: 15px;
    }
    .add_new_list{
        float: right;
    }
    .noti_text{
        width: 76% !important;
        overflow: hidden;
        margin-left: 84px !important;
    }
    .msg-title{
        margin-top: -90px !important;
        margin-left: 84px !important;
    }
    .notification_div {
        padding-left: 6px;
        padding-right: 6px;
        height: 100px;
        overflow: hidden;
    }
    .notification_div span.dark{
        font-size: 14px !important;
        color: #676767d6 !important;
        line-height: 22px !important;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .notification-time{
        margin-top: 2px !important;
    }
    .padding-0{
        padding: 0 !important;
    }
    .invoice_history {
        width: 100% !important;
        display: inline-block;
    }

    /*contact agent*/
    .property_body .border {
        border: 1px solid #D8D8D8;
        border-radius: 4px;
        margin: 30px 0px 42px 0px !important;
        padding: 30px 0px 15px !important;
    }

    .property-right-view-padding{
        padding-right: 0px !important;
    }
}

@media only screen and (min-width: 767px) {
    .search-result[_ngcontent-c1] {
        width: 373px;
    }
}

@media only screen and (max-width:767px) {
    body .date_boxes .event-box {
         width: 55%!important;
    }
    body .col-sm-10.property-view-padding div.scrollmenu {
        overflow: unset !important;
    }
    .search_button{
        margin-top: 20px;
    }
    .location-box{
        font: inherit !important;
        width: 100%;
        height: 45px !important;
    }
    .property-view-padding {
           padding-left: 15px;
           padding-right: 15px;
    }
    body .bg-white .property-view-padding {
        padding-left: 15px;
        padding-right: 15px;
    }
    .date_boxes .event-box {
        width: 55% !important;
    }
    .date_boxes.event-box {
        width: 100%;
    }
    .col-xs-16.scrollmenu::-webkit-scrollbar {
        display: none;
    }
    .property_des.row div#accordion {
        width: 100%;
    }
    body .col-sm-10.property-view-padding div.date_boxes::-webkit-scrollbar {
        width: 0px;
        background: transparent;
    }
    .sp-thumbnails-container.sp-bottom-thumbnails {
        display: none;
    }
    .sp-full-screen-button.sp-fade-full-screen {
        display: none;
    }
    .add-listing-footer{
        min-width: 700px;
    }
    .add-listing-property-images{
        min-width: 650px;
    }
    .new_profile_group{
        padding: 2px 29px 10px 21px;
    }
    .event-card-responsive{
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
    .event_footer .event_save.dis_inline{
        margin-bottom: 8px;
        margin-left: 127px !important;
        margin-top: 8px !important;
    }
    .rate-thumb{
        top: 17% !important;
        left: 17% !important;
    }
    .rate-thumb img{
        margin-right: 6px !important;
    }
    .rate{
        left: 28%;
    }
    .rate-modal-row{
        margin-left: 21% !important;
        margin-right: 21% !important;
    }
    .rating-check-img{
        margin-left: 6px !important;
    }
    div#Agent_View .bg-white{
        padding-left: 15px !important;
        padding-right: 18px !important;
        background: white !important;
    }
    #Property_View .bg-white{
        padding-left: 15px !important;
        padding-right: 18px !important;
        background: white !important;
    }
    .new_profile_group{
        padding: 0px !important;
    }
    .mobile-ui-padding-0{
        padding: 0px !important;
    }
    .agent-overview-right{
        margin-left: 25px;
    }
    .sp-mask.sp-grab{
        width: 100% !important;
    }
    .sp-mask.sp-grab{
        width: 100% !important;
    }
    .sp-image-container{
        width: 100% !important;
    }
    .add_event_mt{
        margin-top: 17px !important;
        margin-right: 15px !important;
    }
    #Agent_View .overview-title{
        margin-left: 15px;
    }
    .edit_listing{
        margin-top: 0px !important;
    }
}

/* .datepicker .datepicker-days table tr td:nth-child(1){
    padding-left: 10px !important;
} */

.color-black{
    color: #000 !important;
}
.map-property-address{
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    width: 80%;
}
.event-search-agent-title{
    padding-left: 15px;
    font-weight: bold;
    padding-bottom: 15px;
}
.event-text-bold{
    font-weight: bold;
}
.event-search-agent-mt-10{
    margin-top: 10px !important;
}
.timeError{
    margin-top: 9px !important;
}
.datepicker td, .datepicker th{
    text-align: center !important;
    width: 20px;
    height: 20px !important;
    -webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    border-radius: 0px !important;
    border: none !important;
}
/* .datepicker table .table-condensed>thead>tr>th, .table-condensed>tbody>tr>th, .table-condensed>tfoot>tr>th, .table-condensed>thead>tr>td, .table-condensed>tbody>tr>td, .table-condensed>tfoot>tr>td{
    padding: 5px !important;
} */
.datepicker table tr th:nth-child(1), .datepicker table tr td:nth-child(1){
    padding-left: 5px !important;
}
.datepicker-dropdown.datepicker-orient-top:before {
    bottom: -7px;
    border-bottom: 0;
    border-top: 0px solid #999 !important;
}
.datepicker-dropdown.datepicker-orient-top:after {
    bottom: -6px;
    border-bottom: 0;
    border-top: 0px solid #fff !important;
}
.datepicker .datepicker-switch:hover, .datepicker .prev:hover, .datepicker .next:hover, .datepicker tfoot tr th:hover {
    background: #627a82 !important;
}

.text-width{
    width: 130%;
}
.my-open-house-text-width{
    width: 117%;
}

.property-text-ellipsis{
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    width: 89%!important;
    white-space: inherit;
    overflow: hidden!important;
    text-overflow: ellipsis!important;
}
.property-td{
    width: 17% !important;
}
.av-property-td{
    width: 18% !important;
}
.property-po_rel {
    position: relative;
    top: 0px;
    left: -23px;
}
.av-po_rel {
    position: relative;
    top: 0px;
    left: 5px;
}
.my-list-property-po_rel {
    position: relative;
    top: 0px;
    left: -10px;
}
.cm-property-po_rel {
    position: relative;
    top: 0px;
    left: -36px;
}
.ck-property-po_rel {
    position: relative;
    top: 0px;
    left: -54px;
}
.my-list-om-po_rel {
    position: relative;
    top: 0px;
    left: -20px;
}
.my-list-om-text-width{
    width: 122%;
}
.vertical-align-top{
    vertical-align: top !important;
}
.text-line-clamp{
    width: 242px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.event-title-clamp{
    width: 542px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.drop_down_label{
    font-size: 12px !important;
    color: #8D8D8D;
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 0px;
    top: -17px !important;
    transition: 0.2s ease all;
    -moz-transition: 0.2s ease all;
    -webkit-transition: 0.2s ease all;
}
.drop-down-label-pos{
    position: relative;
}

.search-drop-down-result{
    overflow: auto;
    list-style-type: none;
    text-align: left;
    padding: 0px;
    /* width: 372px; */
    max-height: 145px;
    background: white;
    font-size: 17px;
    color: #5A5A5A;
  }
.search-drop-down-li{
    /* border: 1px solid #CCCCCC;     */
    text-align: left;
    padding: 3px 7px 3px 9px;
    cursor: pointer;
  }
.search-drop-down{
    position: absolute;
    width: 100%;
    z-index: 500;
}
.search-bg{
    background-color: #f0f2f5;
}
.upload-button-area{
    text-align: center;
    margin: 10px 0px 1px 0px;
}
.c100-width{
    width: 0.99em !important;
}
.property-status-icon{
    height: 17px;
    width: 17px;
}
.property-status-dropdown-icon{
    height: 17px;
    width: 17px;
    margin-right: 15px;
}
.marker-legend-dropdown-icons {
    height: 17px !important;
    width: 32px !important;
    margin-right: 15px;
}
.marker-dropdown-bo-icon {
    height: 20px !important;
    width: 35px !important;
    margin-right: 15px;
}
.marker-dropdown-no-event-icon {
    height: 18px !important;
    width: 35px !important;
    margin-right: 15px;
}
.marker-legend-dropdown-mobile {
    height: 15px !important;
    width: 29px !important;
    margin-right: 15px;
}
.marker-dropdown-bo-icon-mobile {
    height: 20px !important;
    width: 35px !important;
    margin-right: 15px;
}
.marker-dropdown-no-event-icon-mobile {
    height: 18px !important;
    width: 35px !important;
    margin-right: 15px;
}
.list-pro-img{
    height: 73px !important;
    width: 73px !important;
}
.link-message{
    color: #fff;
    text-decoration: underline !important;
}
.link-message-right a{
    color:black !important;
    text-decoration: underline !important;
}
.link-message-right a:hover{
    color:#23527c !important;
}
.ngx-pagination{
    margin-bottom: 3rem !important;
    padding-left: 0;
    width: 105%;
    float: left;
    text-align: center;
}
.load_more_btn{
    margin-bottom: 1px !important;
}
.chat-text-break{
    overflow-wrap: break-word;
}
.add-event-bro{
    border-bottom: 1px solid #C2C2C2;
    margin-left: 15px;
    width: 96%;
}
.add-event-top{
    margin-bottom: 20px;
}

@media screen and (max-width: 767px) {
    span.add-event-bro.col-sm-16 {
        display: block;
        width: 90%;
    }
    .modal-dialog.modal-md.add_property_empty {
        margin: 10px 0px;
    }
    .new_form_label .bar
    {
        width: 100%;
    }
    .col-sm-9.overviewRight.agent-overview-right p.pmain {
        margin-bottom: 3px;
    }
    .col-sm-9.overviewRight.agent-overview-right .col-xs-8.col-sm-4.relative {
        margin-top: 20px;
    }
    .event_footer .event_save.dis_inline
    {
        margin-left: 20px !important;
    }
    .event_footer {
        margin-bottom: 55px;
        padding-bottom: 20px
    }
    .myclient_navbar ul li {
        margin-right: 0px;
        margin-left: 15px;
    }
    div#eventModal .modal-content
    {
        margin-bottom: 20px;
    }
    textarea.event_modal_lorem {
        width: 94%;
    }
    body .sp-full-screen-button.sp-fade-full-screen, .sp-thumbnails-container.sp-bottom-thumbnails {
        display: block !important;
    }
    .sp-full-screen .sp-image-container img {
        height: auto !important;
        width: 100% !important;
    }

    /* css for property share and save in image slider */

    .sp-full-screen p.sp-layer.Manage.manage3 , p.sp-layer.Manage.manage2 , p.sp-layer.Manage.manage1 {
        display: none !important;
    }

    /* css for property share and save in image slider */

    .property_des.row h2.bg_title {
        padding-top: 17px;
        padding-bottom: 17px;
    }

    .features.col-sm-16 {
        margin-bottom: 30px;
    }

    .facts-row div {
        width: 44%;
        display: inline-block;
        float: left;
        padding: 15px 0px;
    }

    h2.bg_title.drop_down_icon
    {
        cursor: pointer;
    }

    .row.new_shadow.visible-xs.mobile_search_menu .form_group.text-center input.new_form {
        border: 0px;
    }

    .filter_page .mo_fi_price .form_group label.width_auto {
        margin-left: 15px;
    }

    #Agent_View .overview-title {
        margin-left: 0px !important;
    }

    #Agent_View .exportpdf {
        margin-right: 0px;
    }

    .col-sm-9.overviewRight.agent-overview-right .row.mb-24 {
        margin-bottom: 0px;
    }

    .rate-thumb {
        top: 17%!important;
        left: auto !important;
        right: auto !important;
        width: 100%;
    }

    .rating-check-img {
        margin-left: 10px !important;
    }


    .new_profile_title
    {
        width:100% !important;
    }

    .file-upload-label
    {
        width:50% !important;
    }
    .new_profile_group
    {
        min-width: 100% !important;
        padding: 15px 22px !important;
    }

    .width_350 {
        width: 100%;
    }
    ul.nav.nav-pills li a {
        padding: 10px 10px;
    }

    .group_1 .title2 {
        width: 100%;
        margin-bottom: 20px;
    }
    .my-market-div{
        width: 100% !important;
        margin-bottom: 20px !important;
    }
    .my-market-text{
        max-width: 100% !important;
        margin-bottom: 20px !important;
    }

    .new_form
    {
        width:100%
    }

    .new_profile_group_wrap.add_listing {
        display: block;
    }

    .save_notes.export_csv {
        top: 20px;
        left: 10px;
    }

    .fullSize-input{
        width: 100% !important;
        margin-left: 0px !important;
    }

    /* .new_form_group.dis_inline .group.new_form_label { */
    .new_form_group.dis_inline .group.new_form_label.fullSize{
        width: 100% !important;
    }

    .new_form_group.dis_inline .fullSize input[type="text"]{
        width: 100% !important;
    }

    /* .save_notes.export_csv {
        top: 20px;
        left: 10px;
    } */

    .myclient_navbar {
        padding: 0px;
    }

    .myclient_navbar ul {
        padding: 0px 0px 10px 0px;
    }

    .myclient_navbar ul li {
        padding-bottom: 0px !important;
        margin-top: 8px;
        margin-left: 10px;
        display: -webkit-box;
    }
    .my_client_table_group .myclient_navbar ul {
        overflow-x: scroll;
    }

    .my_client_table_group .myclient_navbar ul::-webkit-scrollbar {
        width: 0px;  /* remove scrollbar space */
        background: transparent;  /* optional: just make scrollbar invisible */
    }

    .modal-dialog.modal-md.add_property_empty
    {
        width: 100% !important
    }

    .event_gr_de label.ad_e {
        display: block;
    }

    .modal-dialog.modal-md.add_property_empty .new_form.eventType {
        width: 100% !important;
    }

    .modal-dialog.modal-md.add_property_empty .modal-content
    {
        width: 90% !important
    }

    .modal-dialog.modal-md.add_property_empty .event-title-clamp {
        width: auto !important;
    }

    .file-upload-label
    {
        margin-left: 0px !important
    }

    .new_profile_title img.new_symbols-avatar.dis_inline {
        margin-left: 22px;
    }

    .pl_detail {
        margin: 0px;
        max-width: 100%;
    }

    .new_profile_group.dis_inline i.fa.fa-search.search_button {
        position: absolute;
        top: -7px;
        left: 95% !important;
    }

    .property_header h1.prtitle
    {
        margin-left:0px;
    }

    .artboard6_sidebar.artboard7_side.dis_inline.add-listing-property-images {
        max-width: 89% !important;
        min-width: 89% !important;
    }

    .add-listing-footer
    {
        min-width: 100% !important;
    }

    .new_profile_group_wrap.add_listing  ul.nav.nav-pills li {
        height: 44px;
    }

    .search_button.search-mt span.search-span {
        width: 94%;
    }
    .image-scroll
    {
        width: 100%;
    }

    nav.mobile_new_menu li a
    {
        cursor: pointer;
    }
}

div#scroll {
    margin-top: 15px;
}

.daterangepicker td.available:hover, .daterangepicker th.available:hover
{
    padding-top: 5px;
    padding-left: 3px;
}

.col-sm-16.mb-5.padding-0.my-listing-header {
    margin-top: 3px;
    margin-bottom: 0px;
}

p.sp-layer.Manage.manage1 {
    left: 80% !important;
}
div#myModalrate .modal-content {
    overflow: hidden
}

/* .table-responsive.selected_saved tbody td span{
    font-weight: bold;
} */

.map_side_bar .form_group.col-sm-16.hidden-xs {
    padding-bottom: 0px;
}

.SumoSelect > .optWrapper.multiple > .options li.opt span i, .SumoSelect .select-all > span i {
    box-shadow: none !important;
}

button.applyBtn.btn.btn-sm.btn-default.calander-btn {
    font-weight: bold;
}

/* .form_group.col-sm-16.hidden-xs input#datePicker {
    border: 0px !important;
    box-shadow: none !important;
}

input.new_form
{
    border-radius: 4px !important;
}

.ng-select.search-dropdown .ng-control {
     border-radius: 4px !important;
}

input#sqfttxt {
    border-radius: 4px !important;
} */


.bold_font{
    font-weight:600 !important
}


label.file-upload-image {
    float: left;
}

.location-input {
    padding-bottom: 35px !important;
}


/* .new_form .fa-plus-circle:before {
    content: "\f067";
    font-size: 21px;
    color: #10b8a8!important;
    border: 1px solid #10b8a8;
    padding: 7px 9px;
    border-radius: 50px;
} */

.rate_modal span.checkmark {
    left: -6px;
}

img.rating-check-img {
    height: 32px;
    width: auto;
}

.rate_modal .form_group.text-center.mb-20 {
    margin-top: 10px;
    margin-bottom: 10px !important;
}

.rate_modal .modal-content
{
    margin-bottom: 30px;
}

.property_body.mt-20 .new_profile_group.dis_inline.col-sm-16 {
    padding: 15px !important;
}

.font_semibold
{
    font-weight:600 !important
}

.font_bold
{
    font-weight:700 !important
}

.disable-option-menu{
    padding: 10px !important;
    cursor: not-allowed !important;
}
.map_icons_location{
    bottom: 0;
    left: -20%;
    position: absolute;
}
.list-table-digit{
        font-family: 'Source Sans Pro', sans-serif !important;
        font-size: 18px !important;
        color: #5A5A5A !important;
}
.my-list-status{
    color: #5A5A5A !important;
    font-family: 'Source Sans Pro', sans-serif !important;
}

.cancel-icon{
    height: 43px !important;
    margin-left: 9px !important;
    border-radius: 35px !important;
}

.gm-style > div:nth-child(10){
    display:none;
}

@media screen and (max-width: 767px) {
.gm-style > div:nth-child(10){
    display:none;
    }
}

@media screen and (min-width: 767px) {
    .display_none_map {
        width: calc(100% - 350px);
    }
}

@media screen and (max-width: 767px) {
    .map_icons_location {
        display: BLOCK !important;
        position: absolute;
        left: -40%;
        z-index: 111;
    }
    .display_none_map.show_map_mobile {
        position: relative;
    }
    div#draw {
        position: absolute;
        right: 0;
        z-index: 111;
        bottom: 0;
    }
    .show_list {
        margin-bottom: 39px!important;
        margin-left: 14px!important;
        text-align: center;
    }
}


body, input::-webkit-input-placeholder , input, select, label, span, p, .ng-select.custom .ng-control .ng-value-container .ng-input>input, div  {
    font-family: 'Source Sans Pro', sans-serif !important;
}

div#scroll {
    margin-top: 15px;
}

.col-sm-16.mb-5.padding-0.my-listing-header {
    margin-top: 3px;
    margin-bottom: 0px;
}

p.sp-layer.Manage.manage1 {
    left: 80% !important;
}

div#myModalrate .modal-content {
    overflow: hidden
}

.map_side_bar .form_group.col-sm-16.hidden-xs {
    padding-bottom: 0px !important;
}

.SumoSelect > .optWrapper.multiple > .options li.opt span i, .SumoSelect .select-all > span i {
    box-shadow: none !important;
}

button.applyBtn.btn.btn-sm.btn-default.calander-btn {
    font-weight: bold;
}

.ng-select.search-dropdown .ng-control {
     border-radius: 4px !important;
}

input#sqfttxt {
    border-radius: 4px !important;
}


.bold_font{
    font-weight:600 !important
}


span.custom-file-upload-image
{
    height: 34px;
    margin-bottom: 1px;
}

label.file-upload-image {
    float: left;
}
.file-upload-image
{
    width:43% !important
}

.location-input {
    padding-bottom: 35px !important;
}

.new_form .fa-plus-circle:before {
    content: "\f067";
    font-size: 21px;
    color: #10b8a8!important;
    border: 1px solid #10b8a8;
    padding: 7px 9px;
    border-radius: 50px;
}

.rate_modal span.checkmark {
    left: -6px;
}

img.rating-check-img {
    height: 32px;
    width: auto;
}

.rate_modal .form_group.text-center.mb-20 {
    margin-top: 10px;
    margin-bottom: 10px !important;
}

.rate_modal .modal-content
{
    margin-bottom: 30px;
}


.property_body.mt-20 .new_profile_group.dis_inline.col-sm-16 {
    padding: 15px !important;
}

.font_semibold
{
    font-weight:600 !important
}

.font_bold
{
    font-weight:700 !important
}

.daterangepicker .calendar td, .daterangepicker .calendar td:hover {
    padding-top: 7px;
    padding-left: 4px !important;
}



span.custom-file-upload-image {
    float: left;
    width: 67%;
}

span.file-upload-text.upload-image-text {
    position: relative;
    top: -5px;
}


@media screen and (max-width: 767px) {

    span.add-event-bro.col-sm-16 {
        display: block;
        width: 90%;
    }

    .modal-dialog.modal-md.add_property_empty {
        margin: 10px 0px;
    }

    .new_form_label .bar
    {
        width: 100%;
    }
    .col-sm-9.overviewRight.agent-overview-right p.pmain {
        margin-bottom: 3px;
    }


    .col-sm-9.overviewRight.agent-overview-right .col-xs-8.col-sm-4.relative {
        margin-top: 20px;
    }

    .event_footer .event_save.dis_inline
    {
        margin-left: 20px !important;
    }
    .event_footer {
        margin-bottom: 55px;
        padding-bottom: 20px
    }

    .myclient_navbar ul li {
        margin-right: 0px;
        margin-left: 15px;
    }

    div#eventModal .modal-content
    {
        margin-bottom: 20px;
    }

    textarea.event_modal_lorem {
        width: 94%;
    }


    body .sp-full-screen-button.sp-fade-full-screen, .sp-thumbnails-container.sp-bottom-thumbnails {
        display: block !important;
    }


    .sp-full-screen .sp-image-container img {
        height: auto !important;
    }


    /* css for property share and save in image slider */

    .sp-full-screen p.sp-layer.Manage.manage3 , p.sp-layer.Manage.manage2 , p.sp-layer.Manage.manage1 {
        display: none !important;
    }

    /* css for property share and save in image slider */



    .property_des.row h2.bg_title {
        padding-top: 17px;
        padding-bottom: 17px;
    }

    .features.col-sm-16 {
        margin-bottom: 30px;
    }

    .facts-row div {
        width: 44%;
        display: inline-block;
        float: left;
        padding: 15px 0px;
    }

    h2.bg_title.drop_down_icon
    {
        cursor: pointer;
    }

    .row.new_shadow.visible-xs.mobile_search_menu .form_group.text-center input.new_form {
        border: 0px;
    }


    .filter_page .mo_fi_price .form_group label.width_auto {
        margin-left: 15px;
    }

    #Agent_View .overview-title {
        margin-left: 0px !important;
    }

    #Agent_View .exportpdf {
        margin-right: 0px;
    }

    .col-sm-9.overviewRight.agent-overview-right .row.mb-24 {
        margin-bottom: 0px;
    }

    .rate-thumb {
        top: 17%!important;
        left: auto !important;
        right: auto !important;
        width: 100%;
    }

    .rating-check-img {
        margin-left: 10px !important;
    }


    .new_profile_title
    {
        width:100% !important;
    }

    .file-upload-label
    {
        width:50% !important;
    }
    .new_profile_group
    {
        min-width: 100% !important;
        padding: 15px 15px !important;
    }

    .width_350 {
        width: 100%;
    }
    ul.nav.nav-pills li a {
        padding: 10px 10px;
    }

    .group_1 .title2 {
        width: 100%;
        margin-bottom: 20px;
    }

    .new_form
    {
        width:100%
    }

    .new_profile_group_wrap.add_listing {
        display: block;
    }

    .save_notes.export_csv {
        top: 20px;
        left: 10px;
    }

    .save_notes.export_csv {
        top: 20px;
        left: 10px;
    }

    .myclient_navbar {
        padding: 0px;
    }

    .myclient_navbar ul {
        padding: 0px 0px 10px 0px;
    }

    .myclient_navbar ul li {
        padding-bottom: 0px !important;
        margin-top: 8px;
        margin-left: 10px;
        display: inherit;
    }


    .my_client_table_group .myclient_navbar ul {
        overflow-x: scroll;
        display: -webkit-box;
    }

    .my_client_table_group .myclient_navbar ul::-webkit-scrollbar {
        width: 0px;  /* remove scrollbar space */
        background: transparent;  /* optional: just make scrollbar invisible */
    }

    .modal-dialog.modal-md.add_property_empty
    {
        width: 100% !important
    }

    .event_gr_de label.ad_e {
        display: block;
    }

    .modal-dialog.modal-md.add_property_empty .new_form.eventType {
        width: 100% !important;
    }

    .modal-dialog.modal-md.add_property_empty .modal-content
    {
        width: 90% !important
    }

    .modal-dialog.modal-md.add_property_empty .event-title-clamp {
        width: auto !important;
    }

    .file-upload-label
    {
        margin-left: 0px !important
    }

    .new_profile_title img.new_symbols-avatar.dis_inline {
        margin-left: 22px;
    }

    .pl_detail {
        margin: 0px;
        max-width: 100%;
    }

    .new_profile_group.dis_inline i.fa.fa-search.search_button {
        position: absolute;
        top: -7px;
        left: 95% !important;
    }

    .property_header h1.prtitle
    {
        margin-left:0px;
    }

    .artboard6_sidebar.artboard7_side.dis_inline.add-listing-property-images {
        max-width: 89% !important;
        min-width: 89% !important;
    }

    .add-listing-footer
    {
        min-width: 100% !important;
    }

    .new_profile_group_wrap.add_listing  ul.nav.nav-pills li {
        height: 62px;
    }

    .search_button.search-mt span.search-span {
        width: 94%;
    }

    .image-scroll
    {
        width: 100% !important;
    }

    nav.mobile_new_menu li a
    {
        cursor: pointer;
    }

    .check_group.profile_checkbox.width_350.newWidth.flex_container
    {
        width: 100% !important
    }

    .new_profile_group_wrap .new_profile_group.dis_inline li {
        height: 60px;
        vertical-align: middle;
        display: flex;
        align-items: center;
    }
    .artboard6_sidebar.side_bar_height.upgrade-box.ng-star-inserted {
        width: 93%;
        margin-left: 15px;
    }

    .new_profile_title
    {
        display: flex;
    }
      .new_profile_group ul.nav.nav-pills {
        border: 0px;
        display: flex;
        text-align: center;
        width: 344px;
        overflow: scroll;
    }
    .artboard6_sidebar.side_bar_height.upgrade-box.ng-star-inserted {
        width: calc(100% - 30px);
        margin: 0px 15px !important;
        padding: 15px 15px;
        float: left;
    }
    .tab-content div#Profile .ml-10 {
        margin-left: 0px;
    }
    .new_profile_title .new_title {
        font-size: 36px;
    }

    .share-property .modal-dialog.modal-lg{
        width: 100% !important;
        padding-right: 15px;
    }
}

@media only screen and (min-width:768px) and (max-width:991px) {

}

.open_new_page .section_1 .banner_title{
    font-weight: 600 !important;
    width: 90%;
}
.property-detail-fav-icon{
    height: 20px;
    margin-top: -6px;
}
.property-detail-share-icon{
    height: 20px;
    margin-top: -12px;
}
.car-icon{
    height: 68px !important;
}
/* .optWrapper.multiple , .select_mate .square_footage , .select_mate .square_footage2{
    position: relative;
    top: 44px;
} */
.tax_plus{
    height: 32px;
}
.tax_minus{
    height: 32px;
    margin-left: 10px;
}
.white-thumbup-img{
    height: 130px;
}

@media screen and (max-width: 767px) {
	.homepage.header_fix .chat_message.check_event {
		display: block;
	}
	.homepage.header_fix .chat_message.check_event .left_side.height_auto.eventchatwidth {
		width: 100% !important;
		display: block;
		height: calc(100vh - 0px)!important;
	}
	.homepage.header_fix .chat_message.check_event .left_side.height_auto.eventchatwidth a.check_in_link {
		top: 12px;
		position: relative;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .col-sm-9.hidden-xs {
		display: block !important;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_text .agent_details_check {
		margin-top: 16px;
		padding-left: 15px;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow {
		width: 100% !important;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_text {
		left: 12px;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_text a.cursor-pointer div {
		width: 110px;
		margin-top: 10px;
		margin-left: 8px !important;
		padding: 7px 1px !important;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_text {
		left: 0px;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow {
		width: 100% !important;
		height: 100%;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_event div#Agent_View .col-sm-7.overviewLeft .col-sm-3.events {
		display: inline-block;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_event div#Agent_View .col-sm-7.overviewLeft .col-sm-3.guests {
		display: inline-block;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_event div#Agent_View .col-sm-7.overviewLeft .col-sm-5.represented {
		display: inline-block;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_event div#Agent_View .col-sm-7.overviewLeft .col-sm-5.unrepresented {
		display: inline-block;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_event div#Agent_View .bg-white {
		padding: 0px !important;
	}
	.check_shown_tables {
		display: block;
	}
	.check_shown_tables .check_table1.check-table {
		height: 100%;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_text .button_group {
		position: relative;
		left: 20px;
	}
	div.message_list_with_name#scroll,
	.homepage.header_fix .chat_message.check_event .left_side.height_auto.eventchatwidth {
		height: auto !important;
	}
}

.glyphicon{
    font-family: 'Glyphicons Halflings' !important;
}
.fa {
    font: normal normal normal 14px/1 FontAwesome !important;
}
.right_property_bar i{
    font-size: 20px !important;
}
@media only screen and (max-width: 767px) {
    .date_boxes {
        position: relative;
        z-index: 2;
    }
    .property_des .description{
        position: relative;
        z-index: 0;
    }
}

.square_footage2 {
    top: 43px !important;
}.square_group {
    box-shadow: none;
    border: 1px solid #ddd;
}.SumoSelect.open > .optWrapper {    top: 44px;    display: block;}.square_footage .square_group {    border: 0px;}.square_footage {    top: 44px;}


@media only screen and (max-width: 767px) {
    .open_new_page .section_1 {
        padding:57px 10px 0px 10px;
        background-size: cover;
        background-size: auto 100%;
        background-repeat: no-repeat;
        background-position: center center;
    }
    .content-pending{
        padding-top: 50px !important;
    }
    .open_new_page .section_1_agent {
      padding: 5% 9% 11% 9%;
      background-size: auto 100%;
      background-repeat: no-repeat;
      background-position: center center;
    }

    .open_new_page .section_1_broker{
        padding: 11% 4% 7% 5%;
        background-size: auto 100%;
        background-repeat: no-repeat;
        background-position: center center;
    }

    .open_new_page .section_1 .banner_title
    {
        width:100%;
    }

    .baths_group.price_ul ul.search-result {
        width: calc(100% - 12px);
    }

    .baths_group.price_ul ul.search-result::-webkit-scrollbar {
        width: 0px;  /* remove scrollbar space */
        background: transparent;  /* optional: just make scrollbar invisible */
    }

    .map_icons_location {
        left: 100%;
        bottom: 19%;
        margin-right: 0px;
    }

    .map_icons_location .map_icon1 {
        right: 0%;
        position: absolute;
        top: -31px;
    }

    .map_icons {
        left: 100%;
        position: absolute;
        top: 14%;
    }

    .map_side_bar .map_icons_location {
        display: none !important;
    }

    #map {
        height: calc(100vh - 228px) !important;
    }

    /* img.caption_toogle.upload-img {
        width: 100%;
    }

    img.img-delete-sm {
        left: 90% !important;
    }
    img.caption_toogle {
        width: 100%;
    }

    .property_header + .new_profile_group_wrap.add_listing .new_profile_group.dis_inline ul.nav.nav-pills {
        width: 100%;
    }
    .property_header+.new_profile_group_wrap.add_listing .new_profile_group.dis_inline ul.nav.nav-pills, img.caption_toogle {
        width: 344px !important;
    }
    .property_page.header_fix .property_header + .new_profile_group_wrap.add_listing .new_profile_group.dis_inline ul.nav.nav-pills {
        width: 344px !important;
    } */

    footer {
        position: absolute;
    }

    body {
        margin-bottom: 124px !important;
    }

    div#originEvent .modal-content {
        width: auto;
    }

    .width_383{
      width: auto;
    }

 }

 .header_icon-sm{
    height: 19px !important;
    opacity: 0.9;
 }
 .header_icon-sm-no{
    opacity: 0.9;
 }
 .img-cover{
     object-fit: cover;
 }

 .slider-pro img.sp-image, .slider-pro img.sp-thumbnail{
     object-fit: cover;
 }



 .loader {
        color: #4e4e4e;
        font-size: 30px;
        text-align: center;
        position: relative;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 250px;
    }
    .subMessage {
        font-size: 20px;
    }

    @keyframes dots {
    30%
    { transform: scale(1); }
    50%
    { transform: scale(1.8); }
    70%
    { transform: scale(1); }
    }
    .dots {
        position: relative;
        margin: 20px 0 20px;
    }
    .dots:before {
        content: "";
        position: absolute;
        display: inline-block;
        width: 0px;
        height: 0px;
        top: calc(50% - 5px);
        left: calc(50% - 40px);
        border-radius: 50%;
        border: 8px solid #10B8A8;
        animation: dots 1.5s 0s linear infinite;
    }
    .dots .center {
        background: white;
        width: 0px;
        height: 0px;
        border: 8px solid #10B8A8;
        border-radius: 50%;
        display: inline-block;
        animation: dots 1.5s 0.3s linear infinite;
    }
    .dots:after {
        content: "";
        position: absolute;
        display: inline-block;
        width: 0px;
        height: 0px;
        top: calc(50% - 5px);
        right: calc(50% - 40px);
        border-radius: 50%;
        border: 8px solid #10B8A8;
        animation: dots 1.5s 0.6s linear infinite;
    }


/*property image responsive*/
@media only screen and (max-width: 767px) {
    .img-delete{
        right: 0px !important;
    }
    .first-img{
        width: 100% !important;
    }
    img.img-delete-sm {
        left: 87% !important;
    }
    .upload-img-2 {
        width: 100% !important;
        height: 130px;
    }
    .upload-img{
        /* width: 334px !important; */
    }
    span.custom-file-upload-image{
        width: 90%;
    }
}
.row.img_upload.flex_container.image-scroll::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}
.width-99{
    width: 99% !important;
}



/*
admin
*/

.center_logo{
    margin: 0 auto;
}
.log-mt{
    margin-top: 4%;
}
.top-bar{
    height: 129px;
    background: #eceff1;
    margin-top: 34px;
}
.user-box{
    width: 80%;
    height: auto;
    z-index: 1;
    margin: auto;
    display: block;
    border: white;
    position: relative;
    background: #FFFFFF;
    -webkit-box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
    border-radius: 8px;
    padding: 20px 10px;
}
.user-box-pd{
    padding-top: 6.2%;
}
.box-title{
    margin-top: -40px;
    padding-left: 40px;
    font-size: 30px;
    color: #10B8A8;
    font-weight: 600;
}
.box-title-admin{
    margin-top: -40px;
    padding-left: 25px;
    font-size: 30px;
    color: #10B8A8;
    font-weight: 600;
}
.box-input{
    padding-left: 25px;
}
.custom-col-sm-3{
    width: 21.75%;
    float: left;
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
}
.custom-col-sm-3 input{
    width: 100%;
    height: 36px;
    outline: none;
    padding: 10px 7px;
    font-size: 16px;
    color: #5A5A5A
}
.user-search{
    width: 100%;
    height: 36px;
    outline: none;
    padding: 10px 7px;
    font-size: 16px;
    color: #5A5A5A
}
.ad-search-btn{
    width: 100%;
    margin-top: 3px !important;
    padding: 6px 15px 6px 15px !important;
}
.user-search-btn{
    width: 43%;
    margin-top: 3px !important;
    padding: 6px 15px 6px 15px !important;
}
.in-pdt{
    margin-top: 18px;
}
.admin-body{
    background: white !important;
}
.table>thead>tr>th{
    cursor: pointer;
}
.table tr:first-child>td{
    border-top: 0px solid #ddd;
}
.tweet-btn{
    display: none;
}
.pre-style{
    font-weight: 700;
}
.pre-style-contain{
    margin-left: 3px;
    color: #10B8A8;
    font-weight: 700;
}
.app-link-contain{
    text-align: center;
    margin-top: 40px;
}
.built-text{
    width: 47%;
    color: white;
    line-height: 47px;
}
.built-text span{
    font-weight: 700;
}
.built-text sup{
    font-size: 60%;
}
.built-text-read-btn{
    background: #10B8A8;
    font-size: 15px;
    font-weight: 600;
    color: #FFFFFF;
    vertical-align: bottom;
    border-radius: 21px;
    padding: 8px 12px 8px 12px;
    width: 27%;
    text-align: center;
    margin-top: 9%;
}
.pre-style-modal{
    color: #10B8A8 !important;
    font-weight: 600;
}
.pre-style-white{
  color: #FFFFFF !important;
  font-weight: 600;
}
/* @media only screen and (min-width: 1024px) {
    div#menu1 {
        overflow-x: visible !important;
    }
} */
.property-list-table .table tr:last-child>td .open_click_menu ul.click_menu_open.events{
    top: -2em;
}

.u-name-table{
    width: 140px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

@media (min-width: 768px) and (max-width: 1023px) {

	.ng-select.eventChat .ng-control {
		width: auto !important;
    }
    .eventChat .ng-dropdown-panel{
        width: 100% !important;
    }
}

@media only screen and (max-width: 767px) {
    .date-drop-downEvent.mobile .ng-dropdown-panel{
        width: 100% !important;
        background-color: black !important;
    }
}

.mobile_cancelDraw{
    position: absolute;
    right: 8px;
    z-index: 111;
    bottom: 0;
    top: -60px;
}

.header-upgrade{
  font-weight: 600 !important;
  font-size: 15px !important;
  padding: 8px 25px 8px 25px !important;
}

/* #property_info{ */
  /* padding-top: 3px !important; */
  /* top: -423.853px !important */
/* } */
@media (min-width: 768px) and (max-width: 1024px) {
  .all_home_type .SumoSelect > .optWrapper{
    min-width: 230px !important;
  }
  .input.mini{
    width: 40% !important;
  }
  .square_footage{
    min-width: 230px;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .all_home_type .SumoSelect > .optWrapper{
    min-width: 230px !important;
  }
  .input.mini{
    width: 40% !important;
  }
  .square_footage{
    min-width: 230px;
  }
}

.action-option{
  position: -webkit-sticky;
  position: sticky;
  right: 0;
}
.action-view{
  position: -webkit-sticky;
  position: sticky;
  right: 3%;
  padding-right: 1%;
  z-index: auto !important;
}


@media only screen and (max-width: 767px) {
  .action-view{
    right: 6%;
  }
  .action-lead-view{
    right: 20%;
  }
  .action-agent-view{
    right: 10%;
  }
  .action-event-view{
    right: 25% !important;
  }
  .action-client-view{
    right: 10% !important;
  }
}
.action-client-view{
  right: 5%;
}
.action-event-view{
    right: 13%;
  }

.action-btn{
  background: #ffffff !important;
}
