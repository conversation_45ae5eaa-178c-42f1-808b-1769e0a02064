.drop-down-spc{
   margin-left:  1px !important;
}

.btnh :hover{
    color: #FFFFFF !important
}

input.select_box_price2 {
    width: 100%;
    height: 43px;
    border: 0px;
    padding-left: 8px;
    outline: 0 !important;
    color: #333;
}
input.number-of-going{
    width: 94%;
    height: 43px;
    border: 0px;
    padding-left: 8px;
    outline: 0 !important;
}

.square_footage2 {
    position: absolute;
    z-index: 11;
    width: 223px;
    display: none;
    top: 57px;
}

input.mini2{
    background: #FFFFFF;
    border: 1px solid #dbdbdb;
    border-radius: 2px;
    display: inline-block;
    width: 89px;
    padding: 6px 6px 6px 6px;
    font-size: 15px;
    color: #8D8D8D;
    cursor: pointer;
    outline: #10B8A8 !important;
}
input.mini2:focus{
    border: 1px solid #10B8A8 !important;
    cursor: pointer;
    outline: #10B8A8 !important;
}

.baths_group2 ul {
    margin: 0px;
    padding: 0px;
}
.baths_group2 ul li {
    list-style-type: none;
    padding: 5px 16px 4px 16px;
    font-size: 16px;
    color: #5A5A5A;
}
.baths_group2 {
    background: #FFFFFF;
    /* box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30); */
    padding: 0px;
}
.baths_group2 ul li
{
    cursor: pointer;
}
.baths_group2 ul li:hover {
    background: #10B8A8;
    color: #FFFFFF;
    transition: all 275ms ease-in-out;
}
input.mini {
    border: 1px solid #dbdbdb;
    cursor: pointer;
    outline: #10B8A8 !important;
}
input.mini:focus{
    border: 1px solid #10B8A8 !important;
    cursor: pointer;
    outline: #10B8A8 !important;
}

.miniborder{
    border: 1px solid #5BC1BA !important;
}

.search-result{
    overflow: auto;
    list-style-type: none;
    text-align: left;
    padding: 0px;
    width: 190px;
    max-height: 300px;
    background: white;
    font-size: 20px;
    position: absolute;
    z-index: 1000;
}
.search-result-li{
    /* border: 1px solid #CCCCCC;     */
    text-align: left;
    padding: 3px 7px 3px 9px;
    line-height: 30px !important;
}
  .search-result-a{
    text-decoration: none !important;
    color: #676767 !important;
    font-size: 16px;
    cursor: pointer;
}
.search-result-a:focus,a:hover{
    text-decoration: none !important;
    color: #FFFFFF;
}
/* For Firefox */
input[type='number'] {
    -moz-appearance:textfield;
}
/* Webkit browsers like Safari and Chrome */
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.agent-drop-down{
    width: 36%;
    margin-top: 0px !important;
    display: inline-block;
}
.agent-align{
    float: left !important;
}
.no-of-going{
    float: left;
    width: 189px;
    padding-left: 8px;
}
.goingGroup ul {
    margin: 0px;
    padding: 0px;
}
.goingGroup ul li {
    list-style-type: none;
    padding: 5px 16px 4px 16px;
    font-size: 16px;
    color: #5A5A5A;
}
.goingGroup {
    background: #FFFFFF;
    /* box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30); */
    padding: 0px;
}
.goingGroup ul li
{
    cursor: pointer;
}
.goingGroup ul li:hover {
    background: #10B8A8;
    color: #FFFFFF;
    transition: all 275ms ease-in-out;
}
.eventType-drop-down {
    margin-top: 1px !important;
    width: 100% !important;
}
.searchModel .modal-body{
    border-radius: 4px;
}
.save-search-border{
    border-radius: 4px;
    border: 1px solid !important;
}
.save-search-btn-top{
    padding-right: 1.5%;
    padding-top: 6px;
    vertical-align: super;
}

.filter-word-break{
    word-break: inherit;
    width: 142px !important;
}
.filter-status-break{
    word-break: inherit;
    width: 100px !important;
}
.price-list-res{
    height: 282px;
    overflow: auto;
}
.check-mob{
    vertical-align: top !important;
}

.active-icon {
    background-image: url(../../../assets/images/active.png) !important;
    background-repeat: no-repeat !important;
    background-size: 18px !important;
    background-position: 97% 14px;
 }

 .opt-wrapper-width {
    width: 200% !important;
 }

.event_type_filter{
  max-width: 10%;
}

.map-search{
  position: relative;
  display: block;
  width: 100%;
}
.search-result{
  width: 100%;
}

/* More  */
@media (min-width: 1281px) {
  .search_location.tablet_search_filter .form_group.col-sm-3 {
    max-width: 20% !important;
    width: 100%;
  }

  .search_location.tablet_search_filter .form_group.col-sm-2.price_select{
    width: 100%;
    max-width: 20%;
  }
  .square_footage{
      width: 100%;
  }
  input.mini2{
    width: 45%;
  }
  input.mini{
    width: 44%;
  }
  .all_home_type{
    max-width: 20% !important;
  }
  .show_more_search_filter{
    width: 100%;
  }
  .save-dropdown .ng-dropdown-panel{
    width: 100% !important;
  }
  .save_search_box.pull-right{
    width: 100%;
  }
  .tablet_search_location{
    padding: 78px 10px 11px 10px !important;
  }
  .new_shadow{
    box-shadow: 0 0px 0px 0 rgba(0,0,0,0.14) !important;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .input.mini{
    width: 40% !important;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .input.mini{
    width: 40% !important;
  }
}

