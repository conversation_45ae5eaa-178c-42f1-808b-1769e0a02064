<div class="message_right_side chat">
	<div class="live_chat">
			<img _ngcontent-c1="" alt="" class="white_leftarrow_image cursor-pointer_2" src="{{imagePrefix}}symbols-glyph-arrow-line-down.png">
		<div class="chat_name">{{selectedClient.user_name}}</div>
	</div>
</div>

<div class="chat_on_bottom">
	<div class="chat_session chat_over_flow" id="scrollMe">
        <div *ngFor="let message of chatList">
			<div class="chat_time">{{getMessageTime(message.date_time)}}</div>
            <div id={{message.chat_id}} *ngIf="message.sender_id == senderId" class="col-sm-16">
				<div *ngIf="(message.property_detail | json) != '{}' && message.is_link == true" class="col-sm-16 chat-image">
          <div class="chat-box-file">
            <div (click)="propertyDetailInNewTab(message.property_detail.property_id)" class="box_on_map position_reletive pull-right chat_session cursor-pointer">
              <div class="map_group">
                <img src="{{message?.property_detail?.property_file}}" class="box_on_image_image" alt="">
                <div class="on_map_details">
                <div class="on_map_price title">{{message?.property_detail?.home_price | currency:"":symbol:"1.0"}}</div>
                <div class="on_map_detail">{{message?.property_detail?.bedroom}}bds {{message?.property_detail?.full_bath}}bths {{message?.property_detail?.lot_size}}</div>
                </div>
              </div>
              <div class="on_map_address">{{message?.property_detail?.street}}</div>
            </div>
          </div>
				</div>
				<div *ngIf="(message.property_detail | json) == '{}' && message.is_link == true" class="your_chat chat-text-break" [innerHTML]="checkMessageAsLink(message.message)"></div>
				<div *ngIf="message.is_link == false" class="your_chat chat-text-break" [innerHTML]="checkMessageAsLink(message.message)"></div>
			</div>

            <div id={{message.chat_id}} *ngIf="message.sender_id !== senderId">
				<span class="other_chat container box-pagging">
					<div *ngIf="selectedClient.profile_image == ''" class="message_short_name">{{clientNameImage(selectedClient.user_name)}}</div>
					<img *ngIf="selectedClient.profile_image !='' " class="chat-user-image" src="{{selectedClient.profile_image}}">

					<div *ngIf="(message.property_detail | json) != '{}' && message.is_link == true" class="col-sm-4 chat-image chat-co-sm-4">
              <div class="chat-box-file">
                <div (click)="propertyDetailInNewTab(message.property_detail.property_id)" class="box_on_map position_reletive pull-right chat_session cursor-pointer">
                  <div class="map_group">
                    <img src="{{message?.property_detail?.property_file}}" class="box_on_image_image" alt="">
                    <div class="on_map_details">
                    <div class="on_map_price title">{{message?.property_detail?.home_price | currency:"":symbol:"1.0"}}</div>
                    <div class="on_map_detail">{{message?.property_detail?.bedroom}}bds {{message?.property_detail?.full_bath}}bths {{message?.property_detail?.lot_size}}</div>
                    </div>
                  </div>
                  <div class="on_map_address">{{message?.property_detail?.street}}</div>
                </div>
              </div>
					</div>
					<span *ngIf="(message.property_detail | json) == '{}' && message.is_link == true" class="chat-text-break link-message-right" [innerHTML]="checkMessageAsLink(message.message)"></span>
					<span class="chat-text-break link-message-right" *ngIf="message.is_link == false" [innerHTML]="checkMessageAsLink(message.message)"></span>
				</span>
			</div>
		</div>
	</div>
	<div class="type_a_message">
		<textarea *ngIf="(selectedClient | json) != '{}'" name="" class="type_a_text" id="msgtxtbox" #message (keyup.enter)="SentMessage(message.value)" cols="30" rows="10" placeholder="Type a mesage…"></textarea>
	</div>
</div>
