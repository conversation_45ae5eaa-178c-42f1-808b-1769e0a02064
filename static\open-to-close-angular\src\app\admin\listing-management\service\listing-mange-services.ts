import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ApiService } from '@app/base/services/api.service';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiResponse } from '@app/auth/models/api-response';

@Injectable()
export class ListingManagementService {

  public baseservice:BaseComponent;
  public apiService:ApiService;
  
  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
  }

  public searchAgent(name): Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['auth']['agentSearch']+'?mls_agent_id='+name,{});
    return this.apiService.apiCall(options);    
  }

  public searchBrokerage(name): Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['admin']['searchBrokerage']+name,{});
    return this.apiService.apiCall(options);    
  }

  public filterListingSearch(listingUrlParams): Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['searchListing'],listingUrlParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);   
  }

  public getImpersonateUserToken(email):Observable<ApiResponse>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['admin']['impersonateUser'],email.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

}