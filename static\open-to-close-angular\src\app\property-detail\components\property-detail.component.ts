import { <PERSON>mpo<PERSON>,<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t,ViewChild } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { FormGroup, FormControl,Validators,NgForm } from '@angular/forms';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ChatService } from '@app/messaging/service/chat-service';
import { Params } from '@angular/router/src/shared';
import { MyListingService } from '@app/myListings/service/my-listing-services';
import { AuthService } from '@app/auth/services/auth.service';
import { Agent } from '@app/profile/models/agent';
import { Event,EventError, PropertyEvent } from '@app/property-detail/models/event.model';
import { URLSearchParams } from '@angular/http';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { FavoriteService } from '@app/favorite/service/favorite-service';
import { RatingOverview, PropertyOverview } from '@app/event-manager/model/run-tool-info.model';
import * as moment from 'moment';
import { AddEventComponent } from '@app/add-event/components/add-event.component';
import * as FileSaver from 'file-saver';
import { EventManagerService } from '@app/event-manager/services/event-manager.service';
import { ClipboardService } from 'ngx-clipboard'
import { HeaderComponent } from '@app/root/components/header.component';

declare var $;
declare function set_listing_key_value_obj(arg): any;
declare var FB;

@Component({
  selector: 'property-detail',
  templateUrl: '../views/property-detail.component.html',
  styleUrls: ['../css/property-detail.component.css']
})
export class PropertyDetailComponent extends BaseComponent implements OnInit,OnDestroy {

  @ViewChild(EventModalComponent) eventModal: EventModalComponent;
  @ViewChild(AddEventComponent) addEventModal: AddEventComponent;
  @ViewChild(HeaderComponent) headerComponent: HeaderComponent;

  eventMangerService: EventManagerService;

  private propertySubscription;
  message:any;
  sharPropertyFormGroup:FormGroup;
  contactListingAgentFormGroup:FormGroup;
  contactLenderFormGroup:FormGroup;
  favoriteService: FavoriteService;
  public ratingOverview : RatingOverview = new RatingOverview();
  public propertyOverview : PropertyOverview = new PropertyOverview();
  public upcomingEventList = [];
  public pastEventList = [];
  currentId: any;
  disabledEvent: boolean = false;
  isShow72Hour: boolean = false;
  closeBtn : Boolean = true;
  selectedEventObj: PropertyEvent = new PropertyEvent();

  public showBackButton : Boolean = false;
  public isTabSelected : Boolean = false;
  public isSelectPropertyTab : Boolean = false;

  upcIndex: any = 2;
  upcTotalCount: any = 0;
  public upcItemPerPage: any;
  pastIndex: any = 2;
  pastTotalCount: any = 0;
  public pastItemPerPage:any;

  disableLoadMore : Boolean = false;
  isMobileScreen: Boolean = true;
  disableExportPdf : Boolean = false;

  public showButton : Boolean = true;

  public timeList = [
    {"key": "00:00:00", "value" : "12:00 AM"}, {"key": "00:30:00", "value" : "12:30 AM"},{"key": "01:00:00", "value" : "01:00 AM"}, {"key": "01:30:00", "value" : "01:30 AM"},{"key": "02:00:00", "value" : "02:00 AM"}, {"key": "02:30:00", "value" : "02:30 AM"},
    {"key": "03:00:00", "value" : "03:00 AM"}, {"key": "03:30:00", "value" : "03:30 AM"},{"key": "04:00:00", "value" : "04:00 AM"}, {"key": "04:30:00", "value" : "04:30 AM"},{"key": "05:00:00", "value" : "05:00 AM"}, {"key": "05:30:00", "value" : "05:30 AM"},
    {"key": "06:00:00", "value" : "06:00 AM"}, {"key": "06:30:00", "value" : "06:30 AM"},{"key": "07:00:00", "value" : "07:00 AM"}, {"key": "07:30:00", "value" : "07:30 AM"},{"key": "08:00:00", "value" : "08:00 AM"}, {"key": "08:30:00", "value" : "08:30 AM"},
    {"key": "09:00:00", "value" : "09:00 AM"}, {"key": "09:30:00", "value" : "09:30 AM"},{"key": "10:00:00", "value" : "10:00 AM"}, {"key": "10:30:00", "value" : "10:30 AM"},{"key": "11:00:00", "value" : "11:00 AM"}, {"key": "11:30:00", "value" : "11:30 AM"},
    {"key": "12:00:00", "value" : "12:00 PM"}, {"key": "12:30:00", "value" : "12:30 PM"},{"key": "13:00:00", "value" : "01:00 PM"}, {"key": "13:30:00", "value" : "01:30 PM"},{"key": "14:00:00", "value" : "02:00 PM"}, {"key": "14:30:00", "value" : "02:30 PM"},
    {"key": "15:00:00", "value" : "03:00 PM"}, {"key": "15:30:00", "value" : "03:30 PM"},{"key": "16:00:00", "value" : "04:00 PM"}, {"key": "16:30:00", "value" : "04:30 PM"},{"key": "17:00:00", "value" : "05:00 PM"}, {"key": "17:30:00", "value" : "05:30 PM"},
    {"key": "18:00:00", "value" : "06:00 PM"}, {"key": "18:30:00", "value" : "06:30 PM"},{"key": "19:00:00", "value" : "07:00 PM"}, {"key": "19:30:00", "value" : "07:30 PM"},{"key": "20:00:00", "value" : "08:00 PM"}, {"key": "20:30:00", "value" : "08:30 PM"},
    {"key": "21:00:00", "value" : "09:00 PM"}, {"key": "21:30:00", "value" : "09:30 PM"},{"key": "22:00:00", "value" : "10:00 PM"}, {"key": "22:30:00", "value" : "10:30 PM"},{"key": "23:00:00", "value" : "11:00 PM"}, {"key": "23:30:00", "value" : "11:30 PM"},
  ]
  public eventTypeList = ['option 1','option 2','option 3','option 4'];
  eventTypes: any = [
    {"key" : "OH", "value" : "Open House"},
    {"key" : "BO", "value" : "Broker Open"},
    // {"key" : "AO", "value" : "72 Hour Home Sale"}
  ];

  public startTime:any;
  public endTime:any;
  public timeError: String;
  public clientList = ['Christian Keller'];
  public chatService : ChatService;
  public myListingService : MyListingService;
  public singelProperty;
  agentImageUrl: any = this.imagePrefix + "default-placeholder.png";
  public mainurl;
  public selectedHeader;
  public contactListingAgentId;

  // Services
  public authService: AuthService;


  // Parameters for add event
  meAgentType: boolean = false;
  brAgentType: boolean = false;
  saAgentType: boolean = false;
  searchAgentList: Agent[] = [];
  event: Event = new Event();
  eventError: EventError = new EventError();
  propertyId: any;

  // list of events
  eventList: any[] = [];

  //user
public currentUserId = '';
public isUserLogin : Boolean = false;

public isMyProperty : Boolean = false;
public showLenderContact : Boolean = false;

public sharePropertyClientList = [];

//Share property
public selectedSharePropertyClient = {};
public showShareErrorMsg : Boolean = false;
public sharePropertyErrorMSg = '';
public showEmailMessage : Boolean = false;

public currentUrl: any;

  constructor(private _clipboardService: ClipboardService) {
    super();
    this.eventMangerService = ServiceLocator.injector.get(EventManagerService);
    this.chatService = ServiceLocator.injector.get(ChatService);
    this.myListingService = ServiceLocator.injector.get(MyListingService);
    this.authService = ServiceLocator.injector.get(AuthService);
    this.favoriteService = ServiceLocator.injector.get(FavoriteService);
  }

  ngOnInit() {
    this.findLocation();
    $('#AddNewEvent').on('hidden.bs.modal', function () {
      self.event = new Event();
      self.eventError = new EventError();
      self.timeError = '';
      self.event.updatedDate = '';
      self.disabledEvent = false;
      self.closeBtn = false;
    });

    this.currentUrl = window.location.origin;
    $('.popup').click(function(event) {
      var width  = 575,
          height = 400,
          left   = ($(window).width()  - width)  / 2,
          top    = ($(window).height() - height) / 2,
          url    = this.href,
          opts   = 'status=1' +
                    ',width='  + width  +
                    ',height=' + height +
                    ',top='    + top    +
                    ',left='   + left;

      window.open(url, 'twitter', opts);

      return false;
    });

    if(BaseComponent.user != undefined && BaseComponent.user != null){
      this.currentUserId = BaseComponent.user.id;
    }
    this.propertySubscription = this.route.queryParams.subscribe((params:Params)=>{
      this.mainurl = this.router.routerState.snapshot.url;
        if(this.mainurl.includes('search')){
          this.selectedHeader = 'search';
        }
      if(params['propertyId'] != undefined || params['propertyId'] != null){
          this.propertyId = params['propertyId'];
          this.myListingService.getProperty(params['propertyId']).subscribe(res =>{

            set_listing_key_value_obj(res['result']['basic_info']['listing_key']);

            if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
              this.currentUserId = BaseComponent.user.id;
              this.isUserLogin = true;
              if(res['result']['basic_info']['property_status'] == "Off Market"){
                this.showButton = false;
              }
            }

            this.getAgenClientListForShareProperty();

            this.singelProperty = res['result'];
            console.log('Property Details', this.singelProperty)
            if(this.singelProperty && this.singelProperty.event_list) {
              let indexEvent = 0;
              this.singelProperty.event_list.forEach(eventObj => {
                indexEvent++;
                if(eventObj.event_type === 'AO'){
                  this.isShow72Hour = true
                }
              });
            }

            this.isMyProperty = res['result']['agent_info']['is_myproperty'];
            if(this.isMyProperty == true){
              this.getOverView();
              this.setPropertyEventType();
            }

            this.contactListingAgentId = res['result']['agent_info']['id'];
            if(res['result']['agent_info']['profile_photo'] != ''){
              this.agentImageUrl = res['result']['agent_info']['profile_photo'];
            }

            $(document).ready(function($) {
              $(".thumb").each(function(){
                var attr = $(this).attr('data-image-src');
                if(typeof attr !== typeof undefined && attr !== false){
                  $(this).css('background', 'url('+attr+')');
                }
              });

              $('#example3').sliderPro({
                width: 1200,
                height: 700,
                fade: false,
                arrows: true,
                buttons: false,
                fullScreen: true,
                autoScaleLayers :false,
                waitForLayers :true,
                shuffle: false,
                smallSize: 500,
                mediumSize: 1000,
                largeSize: 3000,
                thumbnailArrows: true,
                autoplay: false
              });

              var slider = $( '#example3' ).data( 'sliderPro' );
              if(slider != undefined){
                var total_ans = slider.getTotalSlides();
                var total_ans2 = slider.getSelectedSlide();
                total_ans2++;
                var total_slider = total_ans2 + "/" + total_ans;
                $(".manage3").html(total_slider);
                setTimeout(() => {
                  $(".sp-thumbnails.sp-grab").children("span").remove();
                }, 500);
              }

              $('#example3').on( 'gotoSlide', function( event ) {
                var slider = $( '#example3' ).data( 'sliderPro' );
                if(slider != undefined){
                  var total_ans = slider.getTotalSlides();
                  var total_ans2 = slider.getSelectedSlide();
                  total_ans2++;
                  var total_slider = total_ans2 + "/" + total_ans;
                  $(".manage3").html(total_slider);
              }
            });
          });
        },err => {
        this.errorResponse(err.json());
      });
    }
    else{
      // this.router.navigate(['']);
    }
  })

    this.sharPropertyFormGroup = new FormGroup({
      email : new FormControl('',[Validators.email]),
      client : new FormControl(null),
      message : new FormControl('')
    });

    this.contactListingAgentFormGroup = new FormGroup({
      first_name : new FormControl('',Validators.required),
      last_name : new FormControl('',Validators.required),
      email : new FormControl ('',[Validators.email,Validators.required]),
      subject : new FormControl('',Validators.required),
      message : new FormControl('',Validators.required),
      contact_user : new FormControl(null),
      property_id : new FormControl(''),
    });

    this.contactLenderFormGroup = new FormGroup({
      first_name : new FormControl('',Validators.required),
      last_name : new FormControl('',Validators.required),
      email : new FormControl ('',[Validators.email,Validators.required]),
      subject : new FormControl('',Validators.required),
      message : new FormControl('',Validators.required),
      contact_user : new FormControl(null),
      agent_id : new FormControl(null),
      property_id : new FormControl(''),
    });

    let self = this;
    $(document).ready(function($) {
      $(".shareProperty").css("visibility", "hidden");
      $(this).scrollTop(0);
      $('#example3').sliderPro({
        width: 120,
        height: 700,
        fade: false,
        arrows: true,
        buttons: false,
        fullScreen: true,
        autoScaleLayers :false,
        waitForLayers :true,
        shuffle: false,
        smallSize: 500,
        mediumSize: 1000,
        largeSize: 3000,
        thumbnailArrows: true,
        autoplay: false
     });

     $("#share").click(function() {
      $(".shareProperty").css("visibility", "visible");
      });

    $('#eventDatePicker').datepicker({
      autoclose: true,
      format: 'mm/dd/yyyy'
    }).on('changeDate', function(e){
      let date = e.target.value;
      if(date != ""){
        let dates = date.split("/");
        if(dates.length != 0){
          date = dates[2]+ "-" +dates[0]+ "-" +dates[1];
          self.event.date = date;
          // self.event.updatedDate = date;
        }
      }
      else{
        self.event.date = undefined;
      }
    });

      $('.sp-full-screen-button').click(function(){
        if($('.sp-arrow.sp-next-arrow').hasClass('sp-next-arrow-big')){
          $('.sp-arrow.sp-next-arrow').removeClass('sp-next-arrow-big');
          $('.sp-arrow.sp-previous-arrow').removeClass('sp-previous-arrow-big');
        }
        else{
          $('.sp-arrow.sp-next-arrow').addClass('sp-next-arrow-big');
          $('.sp-arrow.sp-previous-arrow').addClass('sp-previous-arrow-big');
        }
      });

      if ($(window).width() >= 1000) {
        $('.panel-collapse').addClass("in");
        $('h2.bg_title').off("click");

        $("h2.bg_title").click(function(event) {
            event.stopPropagation();
        });
      }
    });

    $(document).ready(function () {
      $(document).on("click",function() {
        if(self.currentId !=undefined){
          // $(document).not(".click_menu_open").hide();
          if(BaseComponent.user != undefined && BaseComponent.user != null){
            this.currentUserId = BaseComponent.user.id;
          }
        }

      });
    });

    $(document).ready(function()
    {
        $(document).mouseup(function(e)
        {
          if(BaseComponent.user != undefined && BaseComponent.user != null){
            this.currentUserId = BaseComponent.user.id;
          }
          var subject = $(".open_click_menu");
          if(self.currentId != undefined){
            $("#"+self.currentId).hide();
            self.currentId = undefined;
          }
        });
    });

    var windowSize = $(window).width();

    if (windowSize >= 1023) {
      self.isMobileScreen = false;
    }
    else{
      self.isMobileScreen = true;
    }
  }

  showSharePropertyCard(){
    $(".shareProperty").css("visibility", "visible");
    $("#link_copy").css("display", "none");
  }

  copyToClipboard(){
    var rootUlr = ''
    if(window.location.origin.toString().includes('openhousedirect')){
      rootUlr =  'https://share.openhousesdirect.com/share/p/'+this.propertyId;
      // rootUlr =  'https://wwww.openhousesdirect.com/search/property-detail?propertyId='+this.propertyId;
    } else {
      rootUlr =  'https://share.openhousesdirect.com/share/p/'+this.propertyId;
      // rootUlr =  'https://www.openhousesdirect.com/share/s/'+this.propertyId;
      // rootUlr =  'https://www.openhousesdirect.com/search/property-detail?propertyId='+this.propertyId;
    }
    this._clipboardService.copyFromContent(rootUlr)
    $("#link_copy").css("display", "block");
  }


  // Add Event methods start here...

  openEventModel(){
    var file_url = '';
    if(this.singelProperty['property_file'] != undefined && this.singelProperty['property_file'].length !=0){
      file_url = this.singelProperty['property_file'][0]['file_url'];
    }
    var property = {
      'property_file' : file_url,
      'street': this.singelProperty['basic_info']['street'],
      'address' :this.singelProperty['basic_info']['address'],
      'location': this.singelProperty['basic_info']['location']
    }
    this.addEventModal.openAddEventModel(this.propertyId,property);
  }

  updateEventList(event){
    if(event['is_updated'] != undefined && event['is_updated'] == true){
      var event_list = this.upcomingEventList.filter((eventObj) => eventObj.event_id == event['event_id']);
      var propertyIndex = this.upcomingEventList.indexOf(event_list[0]);
      if(event_list.length !=0){
        this.upcomingEventList[propertyIndex]['date'] = event.event_date;
        this.upcomingEventList[propertyIndex]['description'] = event.description;
        this.upcomingEventList[propertyIndex]['end_time'] = event.end_time;
        this.upcomingEventList[propertyIndex]['start_time'] = event.start_time;
        this.upcomingEventList[propertyIndex]['event_agent_type'] = event.event_agent_type;
        this.upcomingEventList[propertyIndex]['open_house_agent_id'] = event.open_house_agent_id;
        this.upcomingEventList[propertyIndex]['open_house_agent_image'] = event.open_house_agent_image;
        this.upcomingEventList[propertyIndex]['open_house_agent_name'] = event.open_house_agent_name;
        if(event.event_type){
          this.upcomingEventList[propertyIndex]['event_type'] = event.event_type;
        }
        if(event.event_type_msg){
          this.upcomingEventList[propertyIndex]['event_type_msg'] = event.event_type_msg;
        }
      }
    }else{
      this.setPropertyEventType();
    }
  }

  // Add Event methods end here...

  editProperty(){
   this.propertySubscription = this.route.queryParams.subscribe(params=>{
      if(params['propertyId'] != undefined)
      {
        this.router.navigate(['my-listing/edit-property'],{queryParams:{propertyId:params['propertyId']}});
      }
    });
    this.unSubscription();
  }

  unSubscription(): void {
      this.propertySubscription.unsubscribe();
  }

  eventInfoView(event,eventType){
    event['is_running'] = event['is_runnig'];
    this.addEventModal.manageEventDetailView(event,eventType);
  }

  closeShareProperty(){
    $(".shareProperty").css("visibility", "hidden");
    this.sharPropertyFormGroup.reset();
    this.showShareErrorMsg = false;
    $("#link_copy").css("display", "none");
  }

  shareProperty(form:FormGroup){
    let sharePropertyParms = new URLSearchParams;
    this.showShareErrorMsg = false;

    sharePropertyParms.set('property_id',this.propertyId);
    form.value['email'] = form.value['email'] == null ? '' : form.value['email'];
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0 && BaseComponent.user.user_type == 'LA'){
      this.showEmailMessage = false;
      if(form.value['email'] == '' && Object.keys(this.selectedSharePropertyClient).length == 0){
        this.showShareErrorMsg = true;
        this.sharePropertyErrorMSg = 'Please select client or add email for share this property';
      }else if(form.value['email'] != '' && Object.keys(this.selectedSharePropertyClient).length != 0){
        this.showShareErrorMsg = true;
        this.sharePropertyErrorMSg = 'Please select only client or add only email for share property';
      }

      else if(form.value['email'] != '' && this.sharPropertyFormGroup.controls.email.invalid == false){
        sharePropertyParms.set('is_email','true');
        sharePropertyParms.set('user_email',form.value['email']);
        this.makeSharePropertyAPICall(sharePropertyParms);
      }
      else if(Object.keys(this.selectedSharePropertyClient).length != 0){
        if(form.value['message'] == ''){
          this.showShareErrorMsg = true;
          this.sharePropertyErrorMSg = 'Please enter message';
        }
        else{
          sharePropertyParms.set('is_email','false');
          sharePropertyParms.set('user_email',this.selectedSharePropertyClient['client_email']);
          sharePropertyParms.set('message',form.value['message']);
          sharePropertyParms.set('receiver_id',this.selectedSharePropertyClient['client_id']);
          this.makeSharePropertyAPICall(sharePropertyParms);
        }
      }
    }else{
      if(form.value['email'] == ''){
        this.sharPropertyFormGroup.controls.email.setErrors({'email': true});
        this.showEmailMessage = true;
      }else if(form.value['email'] != '' && this.sharPropertyFormGroup.controls.email.invalid == false){
        sharePropertyParms.set('is_email','true');
        sharePropertyParms.set('user_email',form.value['email']);
        this.makeSharePropertyAPICall(sharePropertyParms);
      }
    }
  }

  makeSharePropertyAPICall(sharePropertyParms){
    this.myListingService.shareProperty(sharePropertyParms).subscribe(res =>{
    this.successResponse(res);
    $(".shareProperty").css("visibility", "hidden");
    this.sharPropertyFormGroup.reset();
    this.showShareErrorMsg = false;
    this.selectedSharePropertyClient = {};
      if(res['result']['is_email'] == false){
        if(BaseComponent.user.is_paid_account){
          this.router.navigate(['messaging']);
        }
      }
    },err=> this.errorResponse(err.json()))
  }

  onClientChange(selectedClient){
    this.showShareErrorMsg = false;
    this.selectedSharePropertyClient = selectedClient;
  }

  eventDetail(type,event){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      if(type == 'OH'){
        this.eventModal.openEventModal('openHouse',event,false);
      }
      if(type == 'AO'){
        this.eventModal.openEventModal('appointmentOnly',event,false);
      }
      if(type == 'BO'){
        this.eventModal.openEventModal('brokerOpen',event,false);
      }
    }else{
      // this.warningMessage("Please signin to see property event detail");
      $("#authModal").modal("show");
      this.headerComponent.OpenAuthModal('signIn');
      localStorage.setItem("returnurl",  this.router.url);
      // this.headerComponent.deeplinkUrl = this.router.url
      // console.log(this.router.url)
    }
  }

  isUserPaid(){
    if(BaseComponent.user !=undefined && Object.keys(BaseComponent.user).length !=0){
      if(BaseComponent.userType == 'listingAgent' && this.isMyProperty == true){
        if(BaseComponent.user.is_paid_account){
          if(!this.isTabSelected){
            this.isTabSelected = true;
            setTimeout(() => {
              $("#tabMenu li a:eq(" + 1 + ")").tab("show");
            }, 100);
          }
          return true;
        }
      }
      return false;
    }
  }

  public reFreshImageView() :void {
    if(this.isSelectPropertyTab == false && this.isTabSelected == true){
      $('.sp-image').css({
        "margin-left": "auto",
        'margin-top' : 'auto',
        'width' : '100%',
        'height' : 'auto'
      });
      $('.sp-thumbnail').css({
        "margin-left": "auto",
        'margin-top' : 'auto',
        'width' : 'auto',
        'height' : '100%'
      });
      this.isSelectPropertyTab = true;
    }
  }

  userIsLogin(){
    if(BaseComponent.user !=undefined && Object.keys(BaseComponent.user).length !=0){
      return true;
    }
    else{
      return false;
    }
  }

  contactListingAgent(from:FormGroup){
    let contactParams = new  URLSearchParams();
    contactParams.set('first_name',from.value['first_name']);
    contactParams.set('last_name',from.value['last_name']);
    contactParams.set('email',from.value['email']);
    contactParams.set('subject',from.value['subject']);
    contactParams.set('message',from.value['message']);
    contactParams.set('contact_user',this.contactListingAgentId);
    contactParams.set('property_id',this.propertyId);

    this.myListingService.contactListingAgent(contactParams).subscribe(res =>{
      this.successResponse(res);
      from.reset();
    },err =>{
      this.errorResponse(err.json());
    });
  }

  contactLenderAgent(from:FormGroup){
    let contactParams = new  URLSearchParams();
    contactParams.set('first_name',from.value['first_name']);
    contactParams.set('last_name',from.value['last_name']);
    contactParams.set('email',from.value['email']);
    contactParams.set('subject',from.value['subject']);
    contactParams.set('message',from.value['message']);
    contactParams.set('contact_user',this.contactListingAgentId);
    contactParams.set('agent_id',this.contactListingAgentId);
    contactParams.set('property_id',this.propertyId);

    this.myListingService.contactListingAgent(contactParams).subscribe(res =>{
      this.successResponse(res);
      from.reset();
      this.showLenderContact = false;
    },err =>{
      this.errorResponse(err.json());
    });
  }

  runEvent(event_id,type){
    if(type == 'newEvent'){
      let runEventParams = new URLSearchParams();
      runEventParams.set('event_status','RU');
      runEventParams.set('event_id',event_id);
      this.myListingService.runEvent(runEventParams).subscribe(res =>{
        // this.successResponse(res);
        this.router.navigate(['event-manager/run-event-manager'],{queryParams:{eventId:event_id}});
      },err=>{
        this.errorResponse(err.json());
      });
    }
    if(type == 'runningEvent'){
      this.router.navigate(['event-manager/run-event-manager'],{queryParams:{eventId:event_id}});
    }
  }

  manageToFavorite(status,propertyId){
    let urlParams = new URLSearchParams();
    urlParams.set("is_favourite",status);
    urlParams.set("property_id",propertyId);
    this.favoriteService.manageFavorite(urlParams).subscribe(res=>{
      this.singelProperty.is_favourite = status;
      this.successResponse(res);
    },err=>{
      this.errorResponse(err.json());
    });
  }

  openMenu(id){
    this.currentId = id;
    $("#"+id).toggle();
  }

  getOverView(){
    let overViewParams = new URLSearchParams();
    overViewParams.set('property_id',this.propertyId);
    this.myListingService.getPropertyOverview(overViewParams).subscribe(res =>{
      this.propertyOverview = res['result']['property_overview'];
      this.ratingOverview = res['result']['rating_overview'];
    },err=>{
      console.log(err.json());
    });
  }

  setPropertyEventType(){
    let eventParams = new URLSearchParams();
    var todayDate = new Date();
    var date = moment(todayDate).utc().format('YYYY-MM-DD');
    eventParams.set('property_id',this.propertyId);
    eventParams.set('today_date', date.toString());
    //past
    eventParams.set('type', 'PA');
    this.getEventList(eventParams,'PA');
    //upcoming
    eventParams.set('type', 'UP');
    this.getEventList(eventParams,'UP');

  }

  getEventList(eventParams,type){
    this.myListingService.getPropertyEvents(eventParams).subscribe(res =>{
      if(type == 'UP'){
        this.upcomingEventList = res['result']['records'];
        this.upcTotalCount = res['result']['total_records_count'];
        this.upcItemPerPage = res['result']['items_per_page'];
      }
      if(type == 'PA'){
        this.pastEventList = res['result']['records'];
        this.pastTotalCount = res['result']['total_records_count'];
        this.pastItemPerPage = res['result']['items_per_page'];
      }
    },err=>{
      console.log(err)
    });
  }

  cancelEvent(eventId: any, eventType: string){
    let canEventUrlParams = new URLSearchParams();
    canEventUrlParams.set('event_id', eventId);

    this.eventMangerService.cancelEvent(canEventUrlParams).subscribe(res => {
      this.successResponse(res);
      if(eventType == "UP"){
        this.upcomingEventList.splice((this.upcomingEventList.findIndex(index => index === eventId)), 1);
      }
      else{
        this.warningMessage("Invalid Event Type")
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  loadMoreEvent(pageNo: any, eventType: string){
    let eventTypeParams = new URLSearchParams()
    eventTypeParams.set("page_no", pageNo);
    var todayDate = new Date();
    var date = moment.utc(todayDate).local().format('YYYY-MM-DD');
    eventTypeParams.set('property_id',this.propertyId);
    eventTypeParams.set('today_date', date.toString());
    eventTypeParams.set('type',eventType);
    this.disableLoadMore = true;

    this.myListingService.getPropertyEvents(eventTypeParams).subscribe(res => {
      this.disableLoadMore = false;
      if(eventType == "UP"){
        if(res['result']['records'].length != 0){
          res['result']['records'].forEach(record => {
            this.upcomingEventList.push(record);
          });
        }
        this.upcIndex += 1;
      }else if(eventType == "PA"){
        if(res['result']['records'].length != 0){
          res['result']['records'].forEach(record => {
            this.pastEventList.push(record);
          });
        }
        this.pastIndex += 1;
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  getCountProgress(positiveVlaue,negativeValue, type){
    var total = positiveVlaue + negativeValue;
    var cal = (positiveVlaue/total)*100;
    cal = parseInt(cal.toString());
    return "c100 p" + cal + " " + type;
  }

  getCountPosition(positiveVlaue,negativeValue){
    var total = positiveVlaue + negativeValue;
    var cal = 90 + ((positiveVlaue)*3.6);
    return "rotate("+cal+"deg)";
  }

  contactOpenHouseAgent(selectedClient){
    var client = {};
    client['user_name'] = selectedClient['open_house_agent_name'];
    client['profile_image'] = selectedClient['open_house_agent_image'];
    client['chat_thread_id'] = selectedClient['open_house_agent_id'];
    client['receiver_id'] = selectedClient['open_house_agent_id'];
    client['last_message_time'] = " ";
    client['last_message'] = ' ';
    this.chatService.setClientChatThread(client);
    this.router.navigate(['messaging']);
  }

  dayCount(dateTime){
    var todayDate = new Date();
    var createdDate = moment(dateTime).local();
    return (this.dateDifference(todayDate,createdDate))
  }

  dateDifference(current, previous){
    var msPerMinute = 60 * 1000;
    var msPerHour = msPerMinute * 60;
    var msPerDay = msPerHour * 24;
    var msPerMonth = msPerDay * 30;
    var msPerYear = msPerDay * 365;

    var elapsed = current - previous;
    return Math.round(elapsed/msPerDay)
  }

  listingDateFormat(date){
    return moment(date).format('MM/DD/YYYY');
  }

  listingModificationDate(date){
    return moment.utc(date).local().format('MM/DD/YYYY');
  }

  backToScreen(){
    this.router.navigateByUrl(localStorage.getItem('bts'));
  }

  UpdatePropertyInfo(propertyInfo){
    let updatedPropertyParams = new URLSearchParams();
    updatedPropertyParams.set('property_id',propertyInfo['property']);
    updatedPropertyParams.set('list_type','0');
    updatedPropertyParams.set('get_all','true');

    this.favoriteService.getUpdatedPropertyInfo(updatedPropertyParams).subscribe(res =>{
      this.singelProperty['event_list'] = res['result']['event_list'];
    },err=>{
      this.errorResponse(err.json());
    });
  }

  getScreenName(){
    if(this.getPreviousScreen()){
      var bts = this.getPreviousScreen();
    var displayName:any;

    this.showBackButton = true;

    displayName = this.UrlsInfo.filter(di => {
      return di['screenName'] == bts.toString();
    });
    return displayName[0];
    }
    else{
      this.showBackButton = false;
      return "";
    }
  }

  showHideLenderContact(){
    if(this.showLenderContact == false){
      this.showLenderContact = true;
    }
    else if(this.showLenderContact == true){
      this.showLenderContact = false;
    }
  }

  isValidForShareProperty(){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0){
      if(this.getUserType() == 'listingAgent'){
        return true;
      }
      else{
        return false;
      }
    }
    else{
      return false;
    }
  }

  getAgenClientListForShareProperty(){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      if(BaseComponent.user.user_type == 'LA'){
        this.myListingService.getClientListForShareProperty().subscribe(res =>{
          this.sharePropertyClientList =  res['result'];
        },err=>{
          console.log(err.json());
        });
      }
    }
  }

  generateAgentViewPdf(){
    let propertyParams = new URLSearchParams;
    propertyParams.set('property_id',this.propertyId);
    propertyParams.set('generated_date',moment().format('MM/DD/YYYY').toString());
    this.disableExportPdf = true;
    this.myListingService.getPropertyOverViewPdf(propertyParams).subscribe(res =>{
      let fileBlob = res.blob();
      let blob = new Blob([fileBlob], {
         type: 'application/pdf'
      });
      let filename = 'agent_overview.pdf';
      FileSaver.saveAs(blob, filename);
      this.disableExportPdf = false;
    },err=>{
      this.disableExportPdf = false;
      console.log(err.json());
    });
  }

  ngOnDestroy(): void {
    if(this.propertySubscription != undefined){
      this.propertySubscription.unsubscribe();
    }
  }

  shareFBProperty(e){
    var self = this;
    e.preventDefault();
    FB.ui({
      method: 'share',
      name: 'Open Houses Direct',
      app_id : '854805355265204',
      redirect_uri: 'https://share.openhousesdirect.com/share/p/'+this.propertyId,
      href: 'https://share.openhousesdirect.com/share/p/'+this.propertyId,
      // href: "https://high-apricot-196023.appspot.com.storage.googleapis.com:443/property_file/staging/23333/20190729112939076994.jpg?Signature=rU9gvh4j9AwsWOgTwyNeh8pOhoY%3D&Expires=1567085050&GoogleAccessId=GOOGDKB2B4RRIUZIEVZV",
      hashtag: window.location.origin.toString,
      quote: 'https://share.openhousesdirect.com/share/p/'+this.propertyId,
    });
  }
}
