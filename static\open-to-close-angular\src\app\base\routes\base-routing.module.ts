import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { ProfileComponent } from "@app/profile/component/profile.component";
import { SearchComponent } from "@app/search/component/search.component";
import { PurchaseComponent } from "@app/purchase/component/purchase.component";
import { AuthComponent } from "@app/auth/components/auth.component";
import { BrokerageProfileComponent } from "@app/profile/component/brokerage-profile.component";
import { HomeBuyerProfileComponent } from "@app/profile/component/home-buyer-profile.component";
import { Component } from "@angular/core/src/metadata/directives";
import { ListingAgentProfileComponent } from "@app/profile/component/listing-agent-profile.component";
import { LenderProfileComponent } from "@app/profile/component/lender-profile.component";
import { VerificationComponent } from "@app/auth/components/verification.component";
import { PurchaseSuccessComponent } from "@app/purchase/component/purchase-success.component";
import { ListingComponent } from "@app/myListings/components/listing.component";
import { ListingDetailComponent } from "@app/myListings/components/listing-detail.component";
import { ListingAddNewPropertyComponent } from "@app/myListings/components/listing-add-new-property.component";
import { AuthGuard } from "../services/auth.guard.service";
import { SearchBarComponent } from "@app/searchBar/component/search-bar.component";
import { MyClientsComponent } from "@app/myClients/components/my-clients.component";
import { CheckInsComponent } from "@app/myClients/components/check-ins.component";
import { PropertyDetailComponent } from "@app/property-detail/components/property-detail.component";
import { MessagingComponent } from "@app/messaging/components/messaging.component";
import { EventManaegerComponent } from "@app/event-manager/component/event-manager.component";
import { EventListComponent } from "@app/event-manager/component/event-list.component";
import { RunEventManagerComponent } from "@app/event-manager/component/run-event-manager.component";
import { GuestBookModeComponent } from "@app/event-manager/component/guest-book-mode.component";
import { MyOpenHousesComponent } from "@app/my-open-houses/component/my-open-houses.component";
import { MyOpenHousesListComponent } from "@app/my-open-houses/component/my-open-houses-list.component";
import { NotificationComponent } from "@app/notification/component/notification.component";
import { ResetPasswordComponent } from "@app/auth/components/reset-password.component";
import { MyLeadsComponent } from "@app/myLeads/component/my-leads.component";
import { FavoriteComponent } from "@app/favorite/component/favorite.component";
import { FavoriteListComponent } from "@app/favorite/component/favorite-list";
import { AboutUsComponent } from "@app/about-us/component/about-us.component";
import { ContactUsComponent } from "@app/contact-us/component/contact-us.component";
import { TermsofUseComponent } from "@app/terms-of-use/component/terms-of-use.component";
import { HBAuthGuard } from "@app/base/services/auth.HB.service";
import { LAauthguard } from "@app/base/services/auth.LA.service";
import { BRAuthGuard } from "@app/base/services/auth.BR.service";
import { MLAuthGuard } from "@app/base/services/auth.ML.service";
import { eventManagerAuthguard } from "@app/base/services/auth.event-manager.service";
import { SearchBarFilterComponent } from "@app/searchBar/component/filter.component";
import { UserAccountStatusAuthGuard } from "@app/base/services/user.account.status.service";
import { AdminPlansComponent } from "@app/admin/adminPlans/component/admin-plans.component";
import { UsersComponent } from "@app/admin/users/component/users.component";
import { ListingManagementComponent } from "@app/admin/listing-management/component/listing-management.component";
import { AdminLoginComponent } from "@app/admin/admin-login/component/admin-login.component";
import { AdminForgotPasswordComponent } from "@app/admin/admin-forgot-password/component/admin-forgot-password.component";
import { AdminResetPasswordComponent } from "@app/admin/admin-reset-password/component/admin-reset-password.component";
import { ClientLeadsAuthGuard } from "@app/base/services/client-leads.auth.service";
import { ChatAuthguard } from "@app/base/services/chat.auth.service";
import { AgentUpgradeComponent } from "@app/upgrade/component/agent-upgrade.component";
import { BrokerUpgradeComponent } from "@app/upgrade/component/broker-upgrade.component";
import { BrokerLandingPageComponent } from "@app/landing-pages/component/broker-landing-page.component";
import { AgentLandingPageComponent } from "@app/landing-pages/component/agent-landing-page.component";
import { LPauthguard } from "../services/auth.LP.service";
import { HomeBuyerComponent } from "@app/admin/home-buyer/component/home-buyer.component";
import { ListingAgentComponent } from "@app/admin/listing-agent/component/listing-agent.component";
import { BrokerUserComponent } from "@app/admin/broker-user/component/broker-user.component";
import { RegisterComponent } from "@app/agent-register/components/agent-register.component";
import { PrivacyPolicyComponent } from "@app/privacy-policy/component/privacy-policy.component";

// import { MetaGuard } from 'ng2-meta';


const routes: Routes = [
  {
    path: "profile",
    children: [
      { path: "", component: ProfileComponent },
      {
        path: "brokerage",
        component: BrokerageProfileComponent,
        canActivate: [BRAuthGuard]
      },
      {
        path: "homeBuyer",
        component: HomeBuyerProfileComponent,
        canActivate: [HBAuthGuard]
      },
      {
        path: "listingAgent",
        component: ListingAgentProfileComponent,
        canActivate: [LAauthguard]
      },
      {
        path: "mortgageLender",
        component: LenderProfileComponent,
        canActivate: [MLAuthGuard]
      }
    ]
  },
  { path: "share/s/:propertyId", component: PropertyDetailComponent },

  { path: "search", component: SearchComponent },
  { path: "search-bar-filter", component: SearchBarFilterComponent },

  {
    path: "purchase",
    children: [
      { path: "", component: PurchaseComponent },
      { path: "purchase-success", component: PurchaseSuccessComponent }
    ]
  },

  { path: "emailconfirm", component: VerificationComponent },
  { path: "forgotpassword", component: ResetPasswordComponent },
  { path: "ref", component: AuthComponent },
  { path: "agent-invite", component: AuthComponent },
  { path: "join-us", component: AuthComponent },
  { path: "agent-register", component: RegisterComponent },
  // { path: "term-use", component: TermsofUseComponent },


  {
    path: "my-listing",
    children: [
      { path: "", component: ListingComponent },
      { path: "listing-detail", component: ListingDetailComponent },
      { path: "add-new-property", component: ListingAddNewPropertyComponent },
      { path: "edit-property", component: ListingAddNewPropertyComponent }
    ],
    canActivate: [UserAccountStatusAuthGuard]
  },

  {
    path: "my-clients",
    children: [
      { path: "", component: MyClientsComponent },
      { path: "check-ins", component: CheckInsComponent },
      { path: "property-detail", component: PropertyDetailComponent }
      // {path: 'add-new-property',component:ListingAddNewPropertyComponent}
    ],
    canActivate: [ClientLeadsAuthGuard]
  },

  { path: "property-detail", component: PropertyDetailComponent },
  { path: "search/property-detail", component: PropertyDetailComponent },

  {
    path: "my-open-houses/property-detail",
    component: PropertyDetailComponent
  },
  { path: "event-manager/property-detail", component: PropertyDetailComponent },
  { path: "my-listing/property-detail", component: PropertyDetailComponent },
  { path: "event-manager/property-detail", component: PropertyDetailComponent },

  {
    path: "messaging",
    component: MessagingComponent,
    canActivate: [ChatAuthguard]
  },

  {
    path: "event-manager",
    children: [
      {
        path: "",
        component: EventManaegerComponent,
        canActivate: [eventManagerAuthguard]
      },
      {
        path: "event-list",
        component: EventListComponent,
        canActivate: [eventManagerAuthguard]
      },
      { path: "run-event-manager", component: RunEventManagerComponent },
      { path: "guest-book-mode", component: GuestBookModeComponent }
    ],
    canActivate: [eventManagerAuthguard]
  },

  {
    path: "my-open-houses",
    children: [
      { path: "", component: MyOpenHousesComponent },
      { path: "my-open-houses-list", component: MyOpenHousesListComponent }
    ]
  },

  { path: "all-notification", component: NotificationComponent },

  {
    path: "my-list",
    children: [
      { path: "", component: MyOpenHousesComponent },
      { path: "myList-open-house-list", component: MyOpenHousesListComponent },
      { path: "property-detail", component: PropertyDetailComponent }
    ]
  },

  {
    path: "my-leads",
    children: [
      { path: "", component: MyLeadsComponent },
      { path: "property-detail", component: PropertyDetailComponent }
    ],
    canActivate: [ClientLeadsAuthGuard]
  },

  {
    path: "favorites",
    children: [
      { path: "", component: FavoriteComponent },
      { path: "property-detail", component: PropertyDetailComponent },
      { path: "favorites-list", component: FavoriteListComponent }
    ]
  },

  { path: "about-us", component: AboutUsComponent },
  { path: "contact-us", component: ContactUsComponent },
  { path: "terms-of-use", component: TermsofUseComponent },
  { path: "privacy-policy", component: PrivacyPolicyComponent },
  
  {
    path: "agentupgrade",
    component: AgentUpgradeComponent,
    canActivate: [LPauthguard]
  },
  {
    path: "brokerupgrade",
    component: BrokerUpgradeComponent,
    canActivate: [LPauthguard]
  },
  {
    path: "agent",
    component: AgentLandingPageComponent,
    canActivate: [LPauthguard]
  },
  {
    path: "broker",
    component: BrokerLandingPageComponent,
    canActivate: [LPauthguard]
  },

  //admin
  {
    path: "admin",
    children: [
      { path: "", component: AdminLoginComponent },
      { path: "forgot-password", component: AdminForgotPasswordComponent },
      { path: "reset-password", component: AdminResetPasswordComponent },
      // { path: "admin-plans", component: AdminPlansComponent },
      { path: "admin-users", component: UsersComponent },
      { path: "listing-management", component: ListingManagementComponent },
      { path: "home-buyer", component: HomeBuyerComponent },
      { path: "listing-agent", component: ListingAgentComponent },
      { path: "brokerage", component: BrokerUserComponent }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BaseRoutingModule {}
