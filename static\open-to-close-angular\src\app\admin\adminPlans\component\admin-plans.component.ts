import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ProfileService } from '@app/profile/service/profile.service';
import { ServiceLocator } from '@app/base/components/service-locator';

declare var $;

@Component({
  selector: 'admin-plans',
  templateUrl: '../views/admin-plans.component.html',
  styleUrls: ['../css/admin-plans.css']
})
export class AdminPlansComponent extends BaseComponent implements OnInit {

  profileService: ProfileService;

  plansList = [];
  categoryList = ['BR','LA'];
  indexList=['1','2','3','4','5'];

  constructor() {
    super();
    this.profileService = ServiceLocator.injector.get(ProfileService);
   }

  ngOnInit() {
    $("body").addClass("admin-body");
    this.profileService.getPlans().subscribe(res =>{
      this.plansList = res['result'];
    }, err => this.errorResponse(err.json()));
  }

  Updateplans(){
    this.profileService.updatePlans(this.plansList).subscribe(res => {
      this.sucMessageResponse('Plans Updated Successfully')
    },err =>{
      this.errorResponse(err.json());
    })
  }

}
