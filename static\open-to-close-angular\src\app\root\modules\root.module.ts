import { BrowserModule } from '@angular/platform-browser';
import { NgModule, Injector, APP_ID } from '@angular/core';
import { RootComponent } from '@root/components/root.component';
import { BaseModule } from '@app/base/modules/base.module';
import { ServiceLocator } from '@app/base/components/service-locator';
import { RootRoutingModule } from '@root/routes/root.routing';
import { AuthModule } from '@app/auth/modules/auth.module';
import { ProfileModule } from '@app/profile/module/profile.module';
import { SearchModule } from '@app/search/module/search.module';
import { PurchaseModule } from '@app/purchase/module/purchase.module';
import { ListingModule } from '@app/myListings/modules/listing.module';
import { AdminPlansModule } from '@app/admin/adminPlans/module/admin-plans.module';
import { UsersModule } from '@app/admin/users/module/users.module';
import { AdminHeaderModule } from '@app/admin/admin-header/module/admin-header.module';
import { ListingManagementModule } from '@app/admin/listing-management/module/listing-management.module';
import { AdminResetPasswordModule } from '@app/admin/admin-reset-password/module/admin-reset-password.module';
import { AdminLoginModule } from '@app/admin/admin-login/module/admin-login.module';
import { SearchBarModule } from '@app/searchBar/module/search-bar.module';
import { MyClientsModule } from '@app/myClients/module/my-clients.module';
import { PropertyDetailModule } from '@app/property-detail/module/property-detail.module';
import { MessagingModule }  from '@app/messaging/module/messaging.module';
import { EventManagerModule } from '@app/event-manager/module/event-manager.module';
import { MyOpenHousesModule } from '@app/my-open-houses/module/my-open-houses.module';
import { NotificationModule } from '@app/notification/module/notification.module';
import { AboutUsModule } from '@app/about-us/module/about-us.module';
import { ContactUsModule } from '@app/contact-us/module/contact-us.module';
import { TermsofUseModule } from '@app/terms-of-use/module/terms-of-use.module';
import { UpgradeModule } from '@app/upgrade/module/upgrade.module'
import { LandingPageModule } from "@app/landing-pages/module/landing-page.module";
import { FavoriteModule } from '@app/favorite/module/favorite.module';
import { MyLeadsModule } from '@app/myLeads/module/my-leads.module';
import { MessagingService } from '@app/messaging.service';
import { AngularFireModule, FirebaseApp } from 'angularfire2';
import { AngularFireDatabaseModule, AngularFireDatabase } from 'angularfire2/database';
import { AngularFireAuthModule } from 'angularfire2/auth';
import * as firebase from 'firebase';
import { Http } from '@angular/http';
import { InterceptorService } from '@app/base/services/interceptor-service';
import { AuthService } from '@app/auth/services/auth.service';
import { environment } from '@env/environment.prod';
import { ServiceWorkerModule } from '@angular/service-worker';
import { AdminForgotPasswordModule } from '@app/admin/admin-forgot-password/module/admin-forgot-password.module';
import { agentRegisterModule } from '@app/agent-register/modules/agent-register.module';
import { PrivacyPolicyModule } from '@app/privacy-policy/module/privacy-policy.module';

// var config = {
//   apiKey: 'AIzaSyD8LWf23j18_wUSZHfyLlIHUdCJ7uzTmE4',
//   authDomain: 'koochoolooapp.firebaseapp.com',
//   projectId: 'koochoolooapp',
//   messagingSenderId: '************'
// };

var config = {
  apiKey: 'AIzaSyAwhIfsRk3lcYUFI8kMoDGN007hOJBmtLQ',
  authDomain: 'high-apricot-196023.firebaseapp.com',
  projectId: 'high-apricot-196023',
  messagingSenderId: '***********'
};

firebase.initializeApp(config);

@NgModule({
  declarations: [
    RootComponent,
  ],
  imports: [
    BrowserModule,
    BaseModule,
    AuthModule,
    RootRoutingModule,
    ProfileModule,
    SearchModule,
    PurchaseModule,
    ListingModule,
    AdminPlansModule,
    MyClientsModule,
    PropertyDetailModule,
    MessagingModule,
    EventManagerModule,
    MyOpenHousesModule,
    NotificationModule,
    MyLeadsModule,
    FavoriteModule,
    AboutUsModule,
    ContactUsModule,
    TermsofUseModule,
    UpgradeModule,
    LandingPageModule,
    UsersModule,
    ListingManagementModule,
    AdminLoginModule,
    AdminForgotPasswordModule,
    AdminResetPasswordModule,
    agentRegisterModule,
    PrivacyPolicyModule
    // MetaModule.forRoot()
  ],
  providers: [FirebaseApp,AngularFireDatabase,AuthService,{ provide: Http, useClass: InterceptorService }],
  bootstrap: [RootComponent],
})

export class RootModule {

  constructor(private injector: Injector){
    ServiceLocator.injector = this.injector;
  }
}
