<div>
  <header></header>
</div>

<div>
    <div class="homepage header_fix">
        <div class="chat_message">
           <div class="left_side">
              <button type="button" class="btn add_new_list dis_inline new-left-msg" (click)="newMessage()">New Message</button>
              <input type="text" #text (keyup)="filterUser(text.value)" placeholder="Search" class="meassage_search">
              <div id="scroll" class="message_list_with_name">
                <div *ngFor="let chatThread of chatThreadList">
                 <div id={{chatThread.chat_thread_id}} (click)="oldChat(chatThread)" class="message_list cursor-pointer" [ngClass]="{'selected-client':selectedClientId == chatThread.chat_thread_id}">
                    <div *ngIf="chatThread.profile_image =='' " class="ls dis_inline po_rel_1">{{clientNameImage(chatThread.user_name)}}</div>
                    <span *ngIf="chatThread.profile_image !='' ">
                      <img class="chat-profile-img" src="{{chatThread.profile_image}}">
                    </span>
                    <div class="meaasge_details chat-thread-img">
                       <a >
                          <div class="chat-msg message_name">{{chatThread.user_name}}
                            <i *ngIf="chatThread.is_read == false" class="fa fa-circle unread-msg-dot"></i>
                            <span *ngIf="chatThread.last_message_time != ''" class="message_time">{{getLastTime(chatThread.last_message_time)}}</span>
                          </div>
                          <div class="message_text">
                            <p class="chat-thread-last">{{chatThread.last_message}}</p>
                          </div>
                       </a>
                    </div>
                 </div>
                </div> 
              </div>
           </div>
            
           
            <div *ngIf="oldChatView == true" class="message_right message_new_width">
              <chat [selectedClient]='selectedClient'></chat>
            </div>

            <div *ngIf="newChatView == true" class="message_right_side send_message_to_group full-right">
                <div class="send_message_to">
                 <new-chat></new-chat>                  
                </div>
            </div>        
        </div>
     </div>
</div>

<div>
  <footer></footer>
</div>