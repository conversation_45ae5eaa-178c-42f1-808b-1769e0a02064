import { Component, OnInit,ViewChild,EventEmitter,Output,Input,NgZone} from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { MyListingService } from '@app/myListings/service/my-listing-services';
import { ServiceLocator } from '@app/base/components/service-locator';
import { MapInfoBubbleService } from '@app/base/services/map-info-bubble.service';
import { ProfileService } from '@app/profile/service/profile.service';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { SearchBarComponent } from '@app/searchBar/component/search-bar.component';
import { FavoriteService } from '@app/favorite/service/favorite-service';

declare var $;
declare var google;
declare var MarkerClusterer;

@Component({
  selector: 'my-listing',
  templateUrl: '../views/listing.html',
  styleUrls: ['../css/listing.component.css']
})
export class ListingComponent extends BaseComponent implements OnInit {

  @ViewChild(EventModalComponent) eventModal: EventModalComponent;
  @ViewChild(SearchBarComponent) SearchBar: SearchBarComponent;

  public mapPolygons: any;
  markerSet =[];
  public map;
  public poly;
  public bermudaTriangle = [];
  public freeHandPolygons = [];
  public matchMarker = [];
  public markers = [];
  public infoBubble: any;
  public lat=40.730610;
  public lng=-73.935242;
  public currentLatLng;
  public positionStatus:Boolean =true;
  public infoBubbleService : MapInfoBubbleService;
  public geolocationPosition;
  houseImage = this.imagePrefix+ "symbols-map-hover.png";
  html:any = "";
  propertysList = [];
  public totalPorpertyCount;
  public paidAccount:boolean;

  myListingService:MyListingService;
  profileService:ProfileService;
  favoriteService :FavoriteService;

  pageCount: number = 1;
  public isPropertySearch = false;
  public searchPageNo = 0;
  public searchListType = 2;
  public itemsPerPage:any;
  public markerCluster;

  public autoMapPosition : Boolean = true;
  public showCancelDraw: Boolean = false;
  public showMapLoading: Boolean = false;
  public addLoaderClass: Boolean = true;
  public allowAddGeoJson : Boolean = true;
  public mobileReZoom : Boolean = true;
  public isMobileListView  : Boolean = true;

  public listingmapGeoJson;
  public listingViewPort;
  public propertyFilterSubscription: any;

  constructor(public zone: NgZone) {
    super();
    this.profileService = ServiceLocator.injector.get(ProfileService);
    this.myListingService=ServiceLocator.injector.get(MyListingService);
    this.infoBubbleService = ServiceLocator.injector.get(MapInfoBubbleService);
    this.favoriteService = ServiceLocator.injector.get(FavoriteService);
  }

  ngOnInit(){
    this.SearchBar.showStatusFilter = true;
    this.SearchBar.mapListView = true;
    this.SearchBar.openSearch = true;
    this.SearchBar.listType = this.searchListType;
    var self = this;
    if(this.getPreviousScreen() != '/my-listing'){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/my-listing');

    if(localStorage.getItem('recentSearches') == null){
      // this.initData();
    }
    if($(window).width() < 767){
      this.SearchBar.mapListView = false;
    }

    this.initMap();
    $(document).ready(function () {
     $('#datePickerDemo').daterangepicker({
           "opens": "left"
      }, function(start, end, label) {
      });
    });

    $(".price_group select.new_form.drop_down_icon").click(function(){
      $(".square_footage.price_group_box").toggle();
     });

    $(".square_footage.price_group_box ul li").click(function(){
      $(".square_footage.price_group_box").css("display" , "none");
    });

    $(".show_map").click(function(){
      $(".display_none_map").addClass("show_map_mobile");
      $(".map_side_bar").addClass("hide_map_mobile");
    });

    $(".show_list").click(function(){
      $(".map_side_bar").removeClass("hide_map_mobile");
      $(".display_none_map").removeClass("show_map_mobile");
      self.zone.run(() => {
        self.isMobileListView = true;
        self.SearchBar.mapListView = false;
      });
    });

    if(BaseComponent.user != undefined){
      this.paidAccount = BaseComponent.user.is_paid_account;
      if(BaseComponent.user.user_type == 'BR'){
        this.paidAccount = false;
      }
    }
    else{
      setTimeout(() => {
        this.paidAccount = BaseComponent.user.is_paid_account;
        if(BaseComponent.user.user_type == 'BR'){
          this.paidAccount = false;
        }
      }, 5000);
    }
  }

  initMap(){
    if(localStorage.getItem('recentSearches')){
      this.SearchBar.allowMapIdle = false;
      this.autoMapPosition = false;
    }
    if(BaseComponent.currentUserLatitude != undefined && BaseComponent.currentUserLongitude != undefined){
      this.autoMapPosition = false;
      this.currentLatLng = new google.maps.LatLng(BaseComponent.currentUserLatitude,BaseComponent.currentUserLongitude);
    }
    else{
      this.currentLatLng = new google.maps.LatLng(this.lat,this.lng);
    }

    var mapOptions = {
      zoom:8,
      center: this.currentLatLng,
      zoomControl: false,
      streetViewControl: false,
      fullscreenControl: false,
    };

    var marker = new google.maps.Marker({
      position: this.currentLatLng,
      map: this.map,
      optimized:false
    });
    this.map = new google.maps.Map(document.getElementById('map'),mapOptions);

    if(localStorage.getItem('recentSearches')){
      this.setPreviousMapPositon();
    }

    this.map.addListener('idle', function() {
      var lat0 = self.map.getBounds().getNorthEast().lat();
      var lng0 = self.map.getBounds().getNorthEast().lng();
      var lat1 = self.map.getBounds().getSouthWest().lat();
      var lng1 = self.map.getBounds().getSouthWest().lng();
      self.listingViewPort ={
        "location": {
          "top_right": {"lat":lat0,"lon": lng0},
          "bottom_left": {"lat": lat1,"lon": lng1}
        }
      };

      var southWest = new google.maps.LatLng(lat1,lng1);
      var northEast = new google.maps.LatLng(lat0,lng0);
      var bounds = new google.maps.LatLngBounds(southWest,northEast);
      self.SearchBar.searchProperty['gmap_bounds'] = bounds;

      var idleLatLng = {
        "lat" : self.map.data.map.center.lat(),
        "lng" : self.map.data.map.center.lng()
      }
      self.SearchBar.searchProperty['idleLatLng'] = idleLatLng;

      if($(window).width() < 767 && self.SearchBar.mapListView == false){
        if(localStorage.getItem('recentSearches') == null){
          self.SearchBar.searchProperty['geo_bounding_box'] = "{}";
        }
        else{
          let recentSearches = JSON.parse(localStorage.getItem('recentSearches'));
          self.SearchBar.searchProperty['geo_bounding_box'] = recentSearches[0]['geo_bounding_box'];
        }
        self.SearchBar.searchProperty['is_map_list'] = false;
        self.SearchBar.mapListView = false;
      }
      else{
        self.SearchBar.searchProperty['geo_bounding_box'] = JSON.stringify(self.listingViewPort);

        if($(window).width() < 767 && self.isMobileListView == true){
          self.SearchBar.searchProperty['geo_bounding_box'] = "{}";
        }
        self.SearchBar.searchProperty['is_map_list'] = true;
        self.SearchBar.mapListView = true;
      }

      self.SearchBar.searchProperty['request_type'] = 'WEB'
      self.SearchBar.pageNo = 0;

      if(self.SearchBar.allowMapIdle == true){
        self.SearchBar.filterProperty();
        localStorage.setItem('zoomLevel',self.map.getZoom());
        self.SearchBar.allowMapIdle = false;
        self.zone.run(() => {
          self.showMapLoading = true;
        });
        self.disable();
      }
      if(self.infoBubble.isOpen() == true){
        self.infoBubble.close();
      }
     });

    var self=this;
    if(navigator.geolocation){
      /*
        * @Desc: Find current position
        * @Param:
        * @return:display infowindow on map with given string
        *
      */

    navigator.geolocation.getCurrentPosition((position)=>{
      var currentPosition
      BaseComponent.currentUserLatitude = position.coords.latitude;
      BaseComponent.currentUserLongitude = position.coords.longitude;
        currentPosition= {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        self.geolocationPosition=currentPosition;

        if(this.positionStatus == true && this.autoMapPosition == true){
          self.map.setCenter(currentPosition);
        }
      },
      ()=>{
        this.handleLocationError(true,this.map.getCenter());
      });

    }
    else{
        this.handleLocationError(false,this.map.getCenter());
    }


    var self=this;
    $("#draw a").click((e)=>{
    if(self.showMapLoading == false){
      self.showCancelDraw = true;
      this.clearGoogleMap(true);
      delete this.SearchBar.searchProperty['polygon'];
      self.SearchBar.searchProperty['geo_bounding_box'] = JSON.stringify(self.listingViewPort);
      self.SearchBar.searchProperty['is_map_list'] = true;
      self.SearchBar.searchProperty['request_type'] = 'WEB'
      self.map.setOptions({ draggableCursor:'default'});
      /*
        * @Desc: Allow to draw polygon
        * @Param:
        * @return:
        *
      */
      e.preventDefault();
      self.disable();
      google.maps.event.addDomListener(self.map.getDiv(),'mousedown',(e)=>{
      self.drawFreeHand();
      });
    }else{
      e.preventDefault();
    }
    });

    $("#cancelDraw a").click((e)=>{
      self.showCancelDraw = false;
      self.clearGoogleMap(true);
      self.allowAddGeoJson = true;
      if(self.freeHandPolygons.length != 0){
        self.freeHandPolygons = [];
        delete this.SearchBar.searchProperty['polygon'];
        self.SearchBar.searchProperty['geo_bounding_box'] = JSON.stringify(self.listingViewPort);
        self.SearchBar.searchProperty['is_map_list'] = true;
        self.SearchBar.searchProperty['request_type'] = 'WEB'
        self.SearchBar.filterProperty();
        self.zone.run(() => {
          self.showMapLoading = true;
        });
      }
      self.enable();
    });

    this.html = this.infoBubbleService.changeHTML("symbols-map-hover.png", "120000", "", "", "", "", {});
    this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);

    if($(window).width() < 767){
      google.maps.event.addListener(self.map,'click', function() {
        if(self.infoBubble.isOpen() == true){
          self.infoBubble.close();
          $("#property_info").remove();
        }
      });
      this.addLoaderClass = false;
      this.SearchBar.mapListView = false;
    }
    else{
      this.addLoaderClass = true;
      this.SearchBar.mapListView = true;
    }
  }

  handleLocationError(browserHasGeolocation,pos){
    /*
      * @Desc:showing error message in infowindow if read location permission id block by user
      * @Param:
      * @return:error message.
      *
    */
  // infoWindow.setPosition(pos);
  // infoWindow.setContent(browserHasGeolocation ?'Error: The Geolocation service failed.' :'Error: Your browser doesn\'t support geolocation.');
  // infoWindow.open(this.gmap);
  }

  addMarkerCluster(){
    var cluster;
    let self = this;
    let j=1;
    var markers =this.markerSet.map(function(location, i) {
      var markerColorType = 'no-event';
      var markerColor = "#fffffff7";
      if(location['first_event_type'] == ''){
        markerColorType = 'no-event';
        markerColor = "#fffffff7";
      }
      else{
        if(location['listing_status']=="PRE-MLS/Coming Soon"){
          if(location['first_event_type']=="OH"){
            markerColorType = "AO";
          }
          else if(location['first_event_type']=="BO"){
            markerColorType = "BO";
          }
          else if(location['first_event_type']=="AO"){
            markerColorType = "AO";
          }
          else if(location['first_event_type']==""){
            markerColorType = "no-event";
          }
          else {
            markerColorType = location['first_event_type'];
          }
        }
        else{
          if(location['first_event_type']=="AO"){
            markerColorType = "OH";
          }
          else if(location['first_event_type']=="OH"){
            markerColorType = "OH";
          }
          else{
            markerColorType = location['first_event_type'];
          }
        }
      }

      let iconImage = self.getIconImage(google, markerColorType)

      var priceLabel = self.priceFormat(self.markerSet[i]["home_price"]);
      cluster = new google.maps.Marker({
          position: {lat :self.markerSet[i]["latitude"],lng: self.markerSet[i]["longitude"]},
          // icon : self.imagePrefix+markerColorType+".png",
          icon: iconImage,
          map: self.map,
          label: {
            text: '$'+priceLabel,
            background: '#AD5FBF',
            color: markerColor,
            // align: 'center',
            // padding: '0',
            fontSize: "14px",
            fontWeight: "600"
          },
          title:self.markerSet[i]["street"],
          id:self.markerSet[i]["id"]
        });
      self.markers.push(cluster);
      //hide all markers when map load
      //this.setMapHideAll(i,this.markerSet[i]["id"] -1 ,null);

      if($(window).width() < 767){
        google.maps.event.addListener(cluster, 'click',((marker,event)=>{
          return function(){
            if(self.markerSet[i]['property_file'] == ''){
              var property = self.propertysList.filter((propertyId) => propertyId.id == self.markerSet[i]['id']);
              if(property.length !=0){
                if(self.markerSet[i] != undefined){
                  self.markerSet[i]['property_file'] = property[0]['property_file'];
                  self.openMobileListingPropertyInfoBubble(i,marker);
                }
              }else{
                let url = new URLSearchParams();
                url.set('property_id',self.markerSet[i]['id'].toString());
                self.propertyFilterSubscription = self.favoriteService.getPropertyFile(url).subscribe(res =>{
                  self.zone.run(()=>{
                    if(self.markerSet[i] != undefined){
                      self.markerSet[i]['property_file'] = res['result']['property_file'];
                      self.openMobileListingPropertyInfoBubble(i,marker);
                    }
                  });
                },err=>{
                  console.log(err.json())
                });
              }
            }
            else{
              self.openMobileListingPropertyInfoBubble(i,marker);
          }
        }
        })(cluster));
        return cluster;

      }else{
        google.maps.event.addListener(cluster, 'mouseover',((marker,event)=>{
          return function(){
            if(self.markerSet[i]['property_file'] == ''){
              var property = self.propertysList.filter((propertyId) => propertyId.id == self.markerSet[i]['id']);
              if(property.length !=0){
                if(self.markerSet[i] != undefined){
                  self.markerSet[i]['property_file'] = property[0]['property_file'];
                  self.openWebPropertyInfoBubble(i,marker);
                }
              }else{
                let url = new URLSearchParams();
                url.set('property_id',self.markerSet[i]['id'].toString());
                self.propertyFilterSubscription = self.favoriteService.getPropertyFile(url).subscribe(res =>{
                  self.zone.run(()=>{
                    if(self.markerSet[i] != undefined){
                      self.markerSet[i]['property_file'] = res['result']['property_file'];
                      self.openWebPropertyInfoBubble(i,marker);
                    }
                  });
                },err=>{
                  console.log(err.json())
                });
              }
            }
            else{
              self.openWebPropertyInfoBubble(i,marker);
            }
          }
        })(cluster));

      google.maps.event.addListener(cluster, 'mouseout',((marker,event)=>{
          return function(){
            self.infoBubble.close();
            $("div.box_on_map", self.infoBubble.bubble_).on("click",function(){
              self.gotToPropertyDetail('my-listing/property-detail',marker['id']);
            });
          }
        })(cluster));
      return cluster;
      }
    });

    this.markerCluster = new MarkerClusterer(this.map, markers,
      {
        maxZoom: 12,
        styles: this.mapMarkerCluster[0]
      });

    google.maps.event.addListener(this.markerCluster, 'clusterclick', function(clust) {
      self.mobileReZoom = false;
      if(self.SearchBar.searchMapSubscription){
        self.SearchBar.allowMapIdle = true;
        self.SearchBar.searchMapSubscription.unsubscribe();
      }
    });
  }

  openWebPropertyInfoBubble(index,marker){
    if(this.infoBubble.isOpen() == true){
      this.infoBubble.close();
    }
    var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);
    this.html = this.infoBubbleService.changeHTML(this.markerSet[index]['property_file'], this.markerSet[index]['home_price'], this.markerSet[index]['bedroom'], this.markerSet[index]['full_bath'], this.markerSet[index]['living_area'],this.markerSet[index]['street'], this.markerSet[index]);
    if(pixelOffsetY != undefined && pixelOffsetY < 260){
      this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
    }else{
      this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
    }
    this.infoBubble.open(this.map, marker);
    setTimeout(() => {
      $(".box_on_map").parent().parent().parent().addClass('pop_div');
      $(".box_on_map").parent().parent().parent().attr('id','property_info');
    },20);
  }

  openMobileListingPropertyInfoBubble(i,marker){
    let self = this;
    var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);
    if(this.infoBubble.isOpen() == false){
      this.html = this.infoBubbleService.changeHTML(this.markerSet[i]['property_file'], this.markerSet[i]['home_price'], this.markerSet[i]['bedroom'], this.markerSet[i]['full_bath'], this.markerSet[i]['living_area'],this.markerSet[i]['street'], this.markerSet[i]);
      if(pixelOffsetY != undefined && pixelOffsetY < 260){
        this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
      }else{
        this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
      }
      this.infoBubble.open(this.map, marker);
      setTimeout(() => {
        $(".box_on_map").parent().parent().parent().addClass('pop_div');
        $(".box_on_map").parent().parent().parent().attr('id','property_info');
        $('div.box_on_map', self.infoBubble.bubble_).on('click',function(){
          self.gotToPropertyDetail('my-listing/property-detail',marker['id']);
        });
      }, 20);
    }else{
      self.infoBubble.close();
      $("#property_info").remove();
      self.openMobileListingPropertyInfoBubble(i,marker);
    }
  }

  mapZoomOut(){
    if(this.showMapLoading == false){
      var getZoom = this.map.getZoom();
      this.map.setZoom(getZoom - 1);
    }
  }

  mapZoomIn(){
    if(this.showMapLoading == false){
      var getZoom = this.map.getZoom();
      this.map.setZoom(getZoom + 1);
    }
  }

  enable(){
    /*
      * @Desc:enable map controls.
    */
    this.map.setOptions({
      draggable: true,
      zoomControl: false,
      scrollwheel: true,
      disableDoubleClickZoom: true,
      clickable:false
    });
  }

  disable(){
    /*
      * @Desc:disable map controls.
    */
    this.map.setOptions({
      draggable: false,
      zoomControl: false,
      scrollwheel: false,
      disableDoubleClickZoom: false,
      clickable:false
    });
  }

  drawFreeHand(){
    //the polygon
    var self=this;
    self.freeHandPolygons = [];
    this.poly=new google.maps.Polyline({map:this.map,clickable:false,strokeColor: "#10B8A8"});

    //move-listener
    var move=google.maps.event.addListener(this.map,'mousemove',(e)=>{
      self.poly.getPath().push(e.latLng);
    });

    //mouseup-listener
    google.maps.event.addListenerOnce(this.map,'mouseup',(e)=>{
      google.maps.event.removeListener(move);
      var path=self.poly.getPath();
      self.poly.setMap(null);
      self.poly=new google.maps.Polygon({map:self.map,path:path, strokeColor: "#10B8A8",
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: "#10B8A8",
      fillOpacity: 0
    });
      var len = path.getLength();
      var latlist = [];
      for (var i = 0; i < len; i++) {
        latlist.push("new google.maps.LatLng(" + path.getAt(i).toUrlValue(5) + "), ");
        self.freeHandPolygons.push({lat:path.getAt(i).lat(), lon:path.getAt(i).lng()});
      }

      this.SearchBar.searchProperty['geo_bounding_box'] = JSON.stringify(self.listingViewPort);
      if(self.freeHandPolygons.length == 0){
        delete self.SearchBar.searchProperty['polygon'];
      }else{
        this.SearchBar.searchProperty['polygon'] = JSON.stringify(self.freeHandPolygons);
      }
      this.SearchBar.searchProperty['is_map_list'] = true;
      this.SearchBar.searchProperty['request_type'] = 'WEB'
      this.SearchBar.filterProperty();

      self.zone.run(() => {
        self.showMapLoading = true;
      });
      self.disable();

      google.maps.event.clearListeners(self.map.getDiv(), 'mousedown');
      self.enable();

        setTimeout(function(){
          for (var j = 0; j <self.markerSet.length; j++){
            var currentMarkerPosition=new google.maps.LatLng(self.markerSet[j]["latitude"],self.markerSet[j]["longitude"]);
            var resultColor = google.maps.geometry.poly.containsLocation(currentMarkerPosition,self.poly)
              if(resultColor){
                 /*
                  * @desc:if marker available
                  */
                self.matchMarker.push({lat:self.markerSet[j]["latitude"],lng:self.markerSet[j]["longitude"],id:self.markerSet[j]["id"]});

              //  self.setMapOnAll(j,self.markerSet[j]["id"] - 1,self.map);
              }
              else{
                self.setMapHideAll(j,self.markerSet[j]["id"],null);
              }
          }
          for(let i=0; i< self.matchMarker.length; i++){
            self.setMapOnAll(i,self.matchMarker[i]['id'],self.map);
          }
        });
    });
  }

  setMapHideAll(index,id,map){
    /*
      * @Desc: set markers hide in map
      * @Param:
                index:number
                map:current map
      * @return:
      *
    */
    let markerCluster = this.markerCluster.getMarkers().filter(marker => marker['id'] == id);
    this.markerCluster.removeMarker(markerCluster[0])
    let marker = this.markers.filter(marker => marker['id'] == id);
    marker[0].setMap(map);
  }

  setMapOnAll(index,id,map){
    /*
      * @Desc: Set markers show in map
      * @Param:
                index:number
                map:current map
      * @return:
      *
    */
    let marker = this.markers.filter(marker => marker['id'] == id);
    marker[0].setMap(map);
  }

  showListingDetail(){
    this.routeOnUrl('/my-listing/listing-detail');
  }

  addNewProperty(){
    this.routeOnUrl('/my-listing/add-new-property');
  }

  initData(){
    this.myListingService.getAllProperty(1).subscribe(res =>{
      this.propertysList = res['result']['property_list'];
      this.totalPorpertyCount = res['result']['total_proeprty_count'];
      this.itemsPerPage = res['result']['items_per_page'];
      // this.deleteMarkers();
    })
  }

  selectedEvent(type,event){
    this.eventModal.openEventModal(type,event,false);
  };

  addToFavorite(id,item){
    let index = this.propertysList.indexOf(item);
    this.propertysList[index]['is_favourite'] = this.favoriteService.setFavourite(!this.propertysList[index]['is_favourite'], id, this.propertysList, index);
  }

  getpage(pageNumber :number){
    var setScroll = document.getElementById('scroll');
    this.pageCount = pageNumber;
    this.SearchBar.searchProperty['is_map_list'] = false;
    this.SearchBar.pageNo = pageNumber;
    this.SearchBar.filterProperty();

    if(pageNumber != 1){
      this.SearchBar.mapListView = false;
      this.SearchBar.searchProperty['is_map_list'] = false;
    }
    else{
      this.SearchBar.mapListView = true;
    }

    if(this.isPropertySearch == false){
      setScroll.scrollTop = 0;
      this.myListingService.getAllProperty(pageNumber).subscribe(res =>{
        this.propertysList = res['result']['property_list'];
        this.totalPorpertyCount = res['result']['total_proeprty_count'];
        this.itemsPerPage = res['result']['items_per_page'];
        // this.deleteMarker/s();
      });
    }
    if(this.isPropertySearch == true){
      setScroll.scrollTop = 0;
    }
  }

  getSearchObj(event){
    this.SearchBar.allowMapIdle = true;
    this.zone.run(
      () => {
    if(event['error'] == "false"){
      this.removeInfoBubble();
      this.isPropertySearch = true;
      this.propertysList = event['result'];
      this.totalPorpertyCount = event['totalPage'];
      this.itemsPerPage = event['itemsPerPage'];
      this.pageCount = event['currentPageNumber'];
      if(this.SearchBar.searchProperty['is_map_list'] == true || event['currentPageNumber'] == 0){
        this.deleteMarkers(event['map_record_list']);
        this.zone.run(() => {
          this.showMapLoading = false;
        });
        this.map.setOptions({draggable: true, zoomControl: false, scrollwheel: true, disableDoubleClickZoom: false});
      }
    }
    else{
      this.zone.run(() => {
        this.showMapLoading = false;
      });
      this.map.setOptions({draggable: true, zoomControl: false, scrollwheel: true, disableDoubleClickZoom: false});
    }
    });
  }

  propertyOnMap(property){
    for(let i=0;i<property.length;i++){
      if(property[i]['latitude'] != 0 && property[i]['longitude'] !=0){
        property[i]['property_file'] = '';
        this.markerSet.push(property[i]);
      }
    }
    this.addMarkerCluster();
  }

  removeInfoBubble(){
    var infowin = document.getElementsByClassName("pop_div");
    for(var i=0;i<infowin.length;i++)
    {
      infowin[i].innerHTML = ''
      $("div.pop_div").remove();
    }
  }

  deleteMarkers(property) {
    if(this.markerSet.length !=0 && this.markerSet != undefined){
      this.markerCluster.clearMarkers();
    }
    for (var i = 0; i < this.markers.length; i++) {
        this.markers[i].setMap(null);
    }
    this.markers = [];
    this.markerSet = [];
    this.propertyOnMap(property);
  };

  UpdatePropertyInfo(propertyInfo){
    var property = this.propertysList.filter((propertyId) => propertyId.id == propertyInfo['property']);
    var propertyIndex = this.propertysList.indexOf(property[0]);
    if(property.length !=0){
      let updatedPropertyParams = new URLSearchParams();
      updatedPropertyParams.set('property_id',propertyInfo['property']);
      updatedPropertyParams.set('list_type',this.searchListType.toString());

      this.favoriteService.getUpdatedPropertyInfo(updatedPropertyParams).subscribe(res =>{
        this.propertysList[propertyIndex] = res['result'];
      },err=>{
        this.errorResponse(err.json());
      });
    }
  }

  setMapPosition(position){
    this.SearchBar.allowMapIdle = false;
    if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
      this.map.setCenter(new google.maps.LatLng(position['lat'],position['lng']));
      if(position['setAutoPosition'] == true){
        this.map.setZoom(8);
      }
      this.currentLatLng = new google.maps.LatLng(position['lat'],position['lng']);
      this.allowAddGeoJson = true;
      this.autoMapPosition = false;
    }
  }

  clearGoogleMap(clearMarker){
    if(clearMarker == true){
      if(this.poly != undefined){
        this.poly.setMap(null);
      }
      if(this.mapPolygons != undefined){
        this.mapPolygons.setMap(null);
      }
      if(this.markerSet.length !=0){
        this.markerCluster.clearMarkers();
      }
      for (var i = 0; i < this.markers.length; i++) {
        this.markers[i].setMap(null);
      }
      this.markers = [];
      this.markerSet = [];
    }

    if(this.listingmapGeoJson != undefined){
      for (var i = 0; i < this.listingmapGeoJson.length; i++){
        this.map.data.remove(this.listingmapGeoJson[i])
      }
    }
    // delete this.searchBarComponent.searchProperty['polygon'];
  }


  drawPolygons(polygonObj){
    this.clearGoogleMap(false);
    this.mobileReZoom = true;
    if(polygonObj['isError'] == true){
      if(this.mapPolygons != undefined){
        this.mapPolygons.setMap(null);
      }
    }
    else{
      if(this.listingmapGeoJson != undefined){
        for (var i = 0; i < this.listingmapGeoJson.length; i++){
          this.map.data.remove(this.listingmapGeoJson[i])
        }
      }
      if(this.allowAddGeoJson == true){
        this.listingmapGeoJson = this.map.data.addGeoJson(polygonObj);
        this.map.data.setStyle({
        strokeColor: "#10B8A8",
        strokeWeight: 2,
        strokeOpacity: 0.8,
        fillColor: "#10B8A8",
        fillOpacity: 0
        });
      }

      if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
        this.zoom(this.map);
        delete this.SearchBar.searchProperty['polygon'];
        if(this.poly != undefined){
          this.poly.setMap(null);
        }
        if(this.mapPolygons != undefined){
          this.mapPolygons.setMap(null);
        }
        this.showCancelDraw = false;
      }
      this.SearchBar.allowMapIdle = true;
    }
  }

  setPreviousMapPositon(){
    this.SearchBar.listType = 2;
    var bound = new google.maps.LatLngBounds();
    if(JSON.parse(localStorage.getItem("recentSearches"))[0]['polygon'] != undefined){
      this.drawCustomPolyline(JSON.parse(localStorage.getItem("recentSearches"))[0]['polygon']);
      this.showCancelDraw = true;
      this.allowAddGeoJson = false;
      this.SearchBar.allowCallGetMapPolygons = false;
    }
    if(JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'] != undefined){
      let bounds = JSON.parse(localStorage.getItem("recentSearches"))[0]['idleLatLng'];
      var center = new google.maps.LatLng(bounds['lat'],bounds['lng'])
      this.map.setCenter(new google.maps.LatLng(bounds['lat'],bounds['lng']));
      if(localStorage.getItem('zoomLevel')){
        this.map.setZoom(parseInt(localStorage.getItem('zoomLevel')));
      }
      else{
        this.map.setZoom(8)
      }
      localStorage.setItem('boundryZoom','false');
      if($(window).width() < 767){
        this.SearchBar.mapListView = false;
      }
      else{
        this.SearchBar.mapListView = true;
      }
      this.SearchBar.allowLocalStorageSearch();
      // this.searchBarComponent.allowMapIdle = true;
    }
  }

  zoom(map) {
    var self = this;
    var bounds = new google.maps.LatLngBounds();
    map.data.forEach(function(feature){
      self.processPoints(feature.getGeometry(),bounds.extend,bounds);
    });
    map.fitBounds(bounds);
  }

  processPoints(geometry,callback,thisArg){
    var self = this;
    if(geometry instanceof google.maps.LatLng){
      callback.call(thisArg, geometry);
    }else if (geometry instanceof google.maps.Data.Point){
      callback.call(thisArg, geometry.get());
    }else{
      geometry.getArray().forEach(function(g){
        self.processPoints(g, callback, thisArg);
      });
    }
  }

  drawCustomPolyline(polygonsList){
    var polygons = [];
    if(polygonsList.length != 0){
      for(let i=0;i<JSON.parse(polygonsList).length;i++){
        let obj = {lng: JSON.parse(polygonsList)[i]['lon'], lat: JSON.parse(polygonsList)[i]['lat']}
        polygons.push(obj);
      }
    }

    if(polygons.length != 0){
      this.freeHandPolygons = polygons;
      this.mapPolygons = new google.maps.Polygon({
          paths: polygons,
          strokeColor: "#10B8A8",
          strokeOpacity: 0.8,
          strokeWeight: 3,
          fillOpacity: 0
      });
      const bounds = new google.maps.LatLngBounds();
      for (var i=0; i<this.mapPolygons.getPath().length; i++) {
        var point = new google.maps.LatLng(polygons[i]['lat'], polygons[i]['lng']);
        bounds.extend(point);
      }
      this.mapPolygons.setMap(this.map);
    }
  }

  getCurrentLocation(){
    if(this.showMapLoading == false){
      if(this.geolocationPosition != undefined && this.geolocationPosition != null){
        this.SearchBar.ClearLocationSearch();
        this.clearGoogleMap(true);
        this.map.setCenter(this.geolocationPosition);
        this.map.setZoom(8);
      }
    }
  }

  showMap(){
    $(".display_none_map").addClass("show_map_mobile");
    $(".map_side_bar").addClass("hide_map_mobile");
    this.isMobileListView = false;

    if(localStorage.getItem('boundryZoom') && localStorage.getItem('boundryZoom') == 'true'){
      if(this.mobileReZoom == true){
        this.mobileReZoom = false;
        if(this.listingmapGeoJson != undefined){
          this.zoom(this.map);
        }
      }
    }
    // if(this.SearchBar.searchLocation != undefined){
    //   this.zoom(this.map);
    // }
    this.SearchBar.mapListView = true;
  }

  showPropertyMarker(propertyObj){
    this.markers.filter((marker) => {
      if(marker.id == propertyObj.id){
        if(this.infoBubble.isOpen()){
          this.infoBubble.close();
        }
        var pixelOffsetY = this.getMapPixelOffsetY(this.map,marker);
        this.html = this.infoBubbleService.changeHTML(propertyObj.property_file, propertyObj.home_price, propertyObj.bedroom, propertyObj.full_bath, propertyObj.living_area,propertyObj.street, propertyObj);
        if(pixelOffsetY != undefined && pixelOffsetY < 260){
          this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,-260);
        }else{
          this.infoBubble = this.infoBubbleService.mapInfoBubble(this.html,0,0);
        }
        this.infoBubble.open(this.map, marker);
        setTimeout(() => {
          $(".box_on_map").parent().parent().parent().addClass('pop_div');
          $(".box_on_map").parent().parent().parent().attr('id','property_info');
        }, 20);
      }
    });
    var markerSet = this.markerSet.filter((propertyId) => propertyId.id == propertyObj.id);
    if(markerSet.length !=0){
      if(markerSet[0]['property_file'] == ''){
        markerSet[0]['property_file'] = propertyObj.property_file;
      }
    }
  }

  closeAllPorpertyMarkers(){
    if(this.infoBubble.isOpen()){
      this.infoBubble.close();
      var infowin = document.getElementsByClassName("pop_div");
      for(var i=0;i<infowin.length;i++)
      {
        infowin[i].innerHTML = ''
        $("#property_info").remove();
      }
    }
  }

  polygonErrorHandling(){
    if(this.SearchBar.handalMaptechError == true){
      this.clearGoogleMap(true);
      delete this.SearchBar.searchProperty['polygon'];
      if(this.SearchBar.searchProperty['geo_bounding_box'] != undefined){
        this.SearchBar.filterProperty();
        this.SearchBar.handalMaptechError = true;
      }
    }
  }

  removeSearchValue(){
    this.SearchBar.removeLocationValue();
  }
}
