import { Component, OnInit, NgZone, ViewChild } from '@angular/core';
import { ListingComponent } from '@app/myListings/components/listing.component';
import { BaseComponent } from '@app/base/components/base.component';
import { URLSearchParams } from '@angular/http';
import { EventError, ProeprtyEvent } from '@app/property-detail/models/event.model';
import { Agent } from '@app/profile/models/agent';
import { AuthService } from '@app/auth/services/auth.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { AddEventComponent } from '@app/add-event/components/add-event.component';
import { FormGroup, FormControl, Validators } from '@angular/forms';

declare var google;
declare var $;
declare var FB;

@Component({
  selector: 'listing-detail',
  templateUrl: '../views/listing-detail.html',
  styleUrls: ['../css/listing.component.css']
})
export class ListingDetailComponent extends ListingComponent implements OnInit {

  @ViewChild(AddEventComponent) addEventModal: AddEventComponent;

  sharPropertyFormGroup: FormGroup;

  public onTheMarketList = [];
  public soldPropertyList = [];
  public commingSoonPropertyList = [];
  public offMarketPropertyList = [];

  armlsId: any
  isArmlsView: boolean = false
  currentId: any;
  currentIndex: number;

  ONMCount: any = 0;
  ONMItemsPerPage: any = 0;
  ONMIndex: any = 2;

  CSCount: any = 0;
  CSItemsPerPage: any = 0;
  CSIndex: any = 2;

  OFMCount: any = 0;
  OFMItemsPerPage: any = 0;
  OFMIndex: any = 2;

  public indexOfProperty;

  public currentUrl: any;

  // Parameters for add event
  meAgentType: boolean = false;
  brAgentType: boolean = false;
  saAgentType: boolean = false;
  searchAgentList: Agent[] = [];
  event: ProeprtyEvent = new ProeprtyEvent();
  eventError: EventError = new EventError();
  propertyId: any;
  timeError: String;
  startTime: any;
  endTime: any;

  public listUrlParams = new URLSearchParams();

  public authService: AuthService;

  public currentSelectedTab = 'onTheMarketList';
  disableLoadMore: Boolean = false;

  public showONMLoader: Boolean = false;
  public showCSLoader: Boolean = false;
  public showOFMLoader: Boolean = false;
  public showArmlLoader: Boolean = false;
  

  //ONM Sorting
  public onmSortObject: any = {};
  public onmSortList: any[] = [];

  //CS Sorting
  public csSortObject: any = {};
  public csSortList: any[] = [];

  //OFM Sorting
  public ofmSortObject: any = {};
  public ofmSortList: any[] = [];

  //Share property
  public clientList = [];
  public selectedSharePropertyClient = {};
  public showShareErrorMsg: Boolean = false;
  public sharePropertyErrorMSg = '';
  public sharePropertyId: number;

  public isPaidAccount: boolean;

  constructor(zone: NgZone) {
    super(zone);
    this.authService = ServiceLocator.injector.get(AuthService);
  }

  aherf: any;

  openMenu(index, id) {
    this.currentIndex = index;
    this.currentId = id;
    $("#ml_" + index + "_" + id).toggle();
  }

  ngOnInit() {
    this.SearchBar.showStatusFilter = true;
    this.SearchBar.listType = this.searchListType;
    this.currentUrl = window.location.origin;

    $('.popup').click(function (event) {
      var width = 575,
        height = 400,
        left = ($(window).width() - width) / 2,
        top = ($(window).height() - height) / 2,
        url = this.href,
        opts = 'status=1' +
          ',width=' + width +
          ',height=' + height +
          ',top=' + top +
          ',left=' + left;

      window.open(url, 'twitter', opts);

      return false;
    });

    $('#AddNewEvent').on('hidden.bs.modal', function () {
      self.event = new ProeprtyEvent();
      self.eventError = new EventError();
      self.timeError = '';
    });

    $('#shareProperty').on('hidden.bs.modal', function () {
      $(this).find('form').trigger('reset');
      self.sharPropertyFormGroup.reset();
      self.sharePropertyId = 0;
      self.showShareErrorMsg = false;
      self.selectedSharePropertyClient = {};
    });

    if (this.getPreviousScreen() != '/my-listing/listing-detail') {
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/my-listing/listing-detail');

    if (localStorage.getItem('recentSearches') == null) {
      this.initListingData();
      this.showONMLoader = true;
      this.showCSLoader = true;
      this.showOFMLoader = true;
    } else {
      this.SearchBar.allowCallGetMapPolygons = false;
      this.SearchBar.allowLocalStorageSearch();
      this.showONMLoader = true;
      this.showCSLoader = true;
      this.showOFMLoader = true;
    }

    this.getClientList();

    let self = this;
    $(document).ready(function () {
      $(document).mouseup(function (e) {
        var subject = $(".open_click_menu");
        if (self.currentId != undefined && self.currentIndex != undefined) {
          $("#ml_" + self.currentIndex + "_" + self.currentId).hide();
          self.currentId = undefined;
          self.currentIndex = undefined;
        }
      });
    });

    this.sharPropertyFormGroup = new FormGroup({
      email: new FormControl('', [Validators.email]),
      client: new FormControl(null),
      message: new FormControl('')
    });

    if (BaseComponent.user != undefined) {
      this.isPaidAccount = BaseComponent.user.is_paid_account;
      if (BaseComponent.user.user_type == 'BR') {
        this.isPaidAccount = false;
      }
    }
    else {
      setTimeout(() => {
        this.isPaidAccount = BaseComponent.user.is_paid_account;
        if (BaseComponent.user.user_type == 'BR') {
          this.isPaidAccount = false;
        }
      }, 5000);
    }
  }

  initListingData() {
    this.listUrlParams.set("page_no", "1");
    this.listUrlParams.set("is_map_list", false.toString());
    this.listUrlParams.set("request_type", 'WEB');
    this.listUrlParams.set("geo_bounding_box", '{}');

    this.listUrlParams.set("listing_status", "ONM")
    this.getMyListing(this.listUrlParams, "ONM");

    this.listUrlParams.set("listing_status", "CS")
    this.getMyListing(this.listUrlParams, "CS");

    this.listUrlParams.set("listing_status", "OFM")
    this.getMyListing(this.listUrlParams, "OFM");
  }

  addNewProperty() {
    this.routeOnUrl('/my-listing/add-new-property');
  }

  loadMoreListings(listIndex, type) {
    let sortList = [];
    this.listUrlParams.delete("sort_list");
    this.listUrlParams.set("page_no", listIndex);
    this.listUrlParams.set("listing_status", type);

    if (type == "ONM") {
      if (this.onmSortList.length != 0) {
        sortList = this.onmSortList;
        this.listUrlParams.set('sort_list', JSON.stringify(sortList));
      }
    } else if (type == "CS") {
      if (this.csSortList.length != 0) {
        sortList = this.csSortList;
        this.listUrlParams.set('sort_list', JSON.stringify(sortList));
      }
    }
    else if (type == "OFM") {
      if (this.ofmSortList.length != 0) {
        sortList = this.ofmSortList;
        this.listUrlParams.set('sort_list', JSON.stringify(sortList));
      }
    }
    this.disableLoadMore = true;

    this.myListingService.getAllPropertyListSearch(this.listUrlParams).subscribe(res => {
      this.disableLoadMore = false;
      if (res['result']['property_list'].length != 0) {
        if (type == "ONM") {
          this.ONMCount = res['result']['total_proeprty_count'];
          this.ONMItemsPerPage = res['result']['items_per_page'];

          res['result']['property_list'].forEach(record => {
            this.onTheMarketList.push(record);
          });
          this.ONMIndex += 1;
        }
        if (type == "CS") {
          this.CSCount = res['result']['total_proeprty_count'];
          this.CSItemsPerPage = res['result']['items_per_page'];
          res['result']['property_list'].forEach(record => {
            this.commingSoonPropertyList.push(record);
          });
          this.CSIndex += 1;
        }
        if (type == "OFM") {
          this.OFMCount = res['result']['total_proeprty_count'];
          this.OFMItemsPerPage = res['result']['items_per_page'];
          res['result']['property_list'].forEach(record => {
            this.offMarketPropertyList.push(record);
          });
          this.OFMIndex += 1;
        }
      }
    });
  }

  getMyListing(listUrlParams, type) {
    this.myListingService.getAllPropertyListSearch(listUrlParams).subscribe(res => {
      if (type == "ONM") {
        this.showONMLoader = false;
        this.onTheMarketList = res['result']['property_list'];
        this.ONMCount = res['result']['total_proeprty_count'];
        this.ONMItemsPerPage = res['result']['items_per_page'];
      }
      if (type == "CS") {
        this.showCSLoader = false;
        this.commingSoonPropertyList = res['result']['property_list'];
        this.CSCount = res['result']['total_proeprty_count'];
        this.CSItemsPerPage = res['result']['items_per_page'];
      }
      if (type == "OFM") {
        this.showOFMLoader = false;
        this.offMarketPropertyList = res['result']['property_list'];
        this.OFMCount = res['result']['total_proeprty_count'];
        this.OFMItemsPerPage = res['result']['items_per_page'];
      }
    });
  }

  showLisingDetail(propertyId) {
    this.router.navigate(['my-listing/edit-property'], { queryParams: { propertyId: propertyId } });
  }

  removeListingDetail(property) {
    let sharePropertyParams = new URLSearchParams;
    sharePropertyParams.set('property_id', property.id);
    this.myListingService.removeListing(sharePropertyParams).subscribe(res => {
      let propertyIndex = this.commingSoonPropertyList.indexOf(property);
      this.commingSoonPropertyList.splice(propertyIndex, 1);
      this.listUrlParams.set("listing_status", "OFM")
      this.getMyListing(this.listUrlParams, "OFM");
    });
  }

  showNewPropertyDialog(property, listType) {
    this.currentSelectedTab = listType;
    if (listType == 'onTheMarketList') {
      this.indexOfProperty = this.onTheMarketList.indexOf(property);
    }
    else if (listType == 'commingSoonPropertyList') {
      this.indexOfProperty = this.commingSoonPropertyList.indexOf(property);
    }
    var file_url = '';
    if (property['property_file'] != undefined && property['property_file'] != '') {
      file_url = property['property_file'];
    }
    var propertyEvetnt = {
      'property_file': file_url,
      'street': property['street'],
      'address': property['location'],
      'location': property['location']
    }
    this.addEventModal.openAddEventModel(property['id'], property);
  }


  addEvent(res) {
    if (this.currentSelectedTab == 'commingSoonPropertyList') {
      var propertyObj = {
        property: this.commingSoonPropertyList[this.indexOfProperty]['id']
      }
      this.UpdatePropertyInfo(propertyObj);
    }
    else if (this.currentSelectedTab == 'onTheMarketList') {
      var propertyObj = {
        property: this.onTheMarketList[this.indexOfProperty]['id']
      }
      this.UpdatePropertyInfo(propertyObj);
    }
  }

  currentTab(tab) {
    this.currentSelectedTab = tab;
  }

  getSearchObj(ListingParams) {
    this.zone.run(
      () => {
        this.ONMIndex = 2;
        this.CSIndex = 2;
        this.OFMIndex = 2;

        this.listUrlParams = ListingParams;
        this.listUrlParams.set("is_map_list", false.toString());
        this.listUrlParams.set("request_type", 'WEB');
        this.listUrlParams.set("geo_bounding_box", '{}');

        this.listUrlParams.set("page_no", "1");
        this.listUrlParams.set('list_type', '2');

        this.listUrlParams.set("listing_status", "ONM")
        this.getMyListing(this.listUrlParams, "ONM");

        this.listUrlParams.set("listing_status", "CS")
        this.getMyListing(this.listUrlParams, "CS");

        this.listUrlParams.set("listing_status", "OFM")
        this.getMyListing(this.listUrlParams, "OFM");

        this.listUrlParams.delete("sort_list");
        this.removeSorting();
      });
  }

  removeSorting() {
    var sort = 'ONM';

    for (let i = 0; i < 3; i++) {
      if (i == 0) {
        sort = 'ONM';
        this.onmSortObject = {};
        this.onmSortList = [];
      }
      else if (i == 1) {
        sort = 'CS';
        this.csSortObject = {};
        this.csSortList = [];
      }
      else if (i == 2) {
        sort = 'OFM';
        this.ofmSortObject = {};
        this.ofmSortList = [];
      }
      $('#' + sort + '_PR').attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
      $('#' + sort + '_HP').attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
      $('#' + sort + '_BD').attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
      $('#' + sort + '_BT').attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
      $('#' + sort + '_SF').attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
      $('#' + sort + '_DT').attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');

    }
  }

  openSharePropertyModal(propertyId) {
    $('#shareProperty').modal('show');
    this.sharePropertyId = propertyId;
  }

  openEventModel(type, event) {
    this.eventModal.openEventModal(type, event, false);
  };

  UpdatePropertyInfo(propertyInfo) {
    var property: any = '';
    var propertyIndex: any = '';
    var eventIndex: any = '';
    if (this.currentSelectedTab == 'onTheMarketList') {
      property = this.onTheMarketList.filter((propertyId) => propertyId.id == propertyInfo['property']);
      propertyIndex = this.onTheMarketList.indexOf(property[0]);
    }
    if (this.currentSelectedTab == 'commingSoonPropertyList') {
      property = this.commingSoonPropertyList.filter((propertyId) => propertyId.id == propertyInfo['property']);
      propertyIndex = this.commingSoonPropertyList.indexOf(property[0]);
    }
    if (property.length != 0) {
      let updatedPropertyParams = new URLSearchParams();
      updatedPropertyParams.set('property_id', propertyInfo['property']);
      updatedPropertyParams.set('list_type', this.searchListType.toString());

      this.favoriteService.getUpdatedPropertyInfo(updatedPropertyParams).subscribe(res => {
        if (this.currentSelectedTab == 'onTheMarketList') {
          this.onTheMarketList[propertyIndex]['event_list'] = res['result']['event_list'];
          this.onTheMarketList[propertyIndex]['total_event_count'] = res['result']['total_event_count'];
        }
        if (this.currentSelectedTab == 'commingSoonPropertyList') {
          this.commingSoonPropertyList[propertyIndex]['event_list'] = res['result']['event_list'];
          this.commingSoonPropertyList[propertyIndex]['total_event_count'] = res['result']['total_event_count'];
        }
      }, err => {
        this.errorResponse(err.json());
      });
    }
  }

  listingSort(listingType, filedName) {
    let sortList = [];

    if (listingType == 'ONM') {
      this.ONMIndex = 2;
      if (this.onmSortObject[filedName] == undefined) {
        this.onmSortObject[filedName] = true;
        $('#ONM_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
      }
      else {
        this.onmSortObject[filedName] = this.onmSortObject[filedName] === true ? false : true;
        if (this.onmSortObject[filedName]) {
          $('#ONM_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
        } else {
          $('#ONM_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
        }
      }
      this.onmSortList[0] = this.onmSortObject;
      sortList = this.onmSortList;
    }
    else if (listingType == 'CS') {
      this.CSIndex = 2;
      if (this.csSortObject[filedName] == undefined) {
        this.csSortObject[filedName] = true;
        $('#CS_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
      }
      else {
        this.csSortObject[filedName] = this.csSortObject[filedName] === true ? false : true;
        if (this.csSortObject[filedName]) {
          $('#CS_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
        } else {
          $('#CS_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
        }
      }
      this.csSortList[0] = this.csSortObject;
      sortList = this.csSortList;
    }
    else if (listingType == 'OFM') {
      this.OFMIndex = 2;
      if (this.ofmSortObject[filedName] == undefined) {
        this.ofmSortObject[filedName] = true;
        $('#OFM_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
      }
      else {
        this.ofmSortObject[filedName] = this.ofmSortObject[filedName] === true ? false : true;
        if (this.ofmSortObject[filedName]) {
          $('#OFM_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line-up.png');
        } else {
          $('#OFM_' + filedName).attr('src', this.imagePrefix + 'symbols-glyph-arrow-line.png');
        }
      }
      this.ofmSortList[0] = this.ofmSortObject;
      sortList = this.ofmSortList;
    }

    this.listUrlParams.set("page_no", "0");
    this.listUrlParams.set("list_type", "2");
    this.listUrlParams.set("listing_status", listingType);
    this.listUrlParams.set('sort_list', JSON.stringify(sortList));
    this.listUrlParams.set('geo_bounding_box', "{}");

    this.getMyListing(this.listUrlParams, listingType);
  }

  getClientList() {
    this.myListingService.getClientListForShareProperty().subscribe(res => {
      this.clientList = res['result'];
    }, err => {
      console.log(err.json());
    });
  }

  onClientChange(selectedClient) {
    this.showShareErrorMsg = false;
    this.selectedSharePropertyClient = selectedClient;
  }

  shareProperty(form: FormGroup) {
    let sharePropertyParms = new URLSearchParams;
    this.showShareErrorMsg = false;
    sharePropertyParms.set('property_id', this.sharePropertyId.toString());
    form.value['email'] = form.value['email'] == null ? '' : form.value['email'];

    if (form.value['email'] == '' && Object.keys(this.selectedSharePropertyClient).length == 0) {
      this.showShareErrorMsg = true;
      this.sharePropertyErrorMSg = 'Please select client or add email for share this property';
    } else if (form.value['email'] != '' && Object.keys(this.selectedSharePropertyClient).length != 0) {
      this.showShareErrorMsg = true;
      this.sharePropertyErrorMSg = 'Please select only client or add only email for share property';
    }

    else if (form.value['email'] != '' && this.sharPropertyFormGroup.controls.email.invalid == false) {
      sharePropertyParms.set('is_email', 'true');
      sharePropertyParms.set('user_email', form.value['email']);
      this.makeSharePropertyAPICall(sharePropertyParms);
    }
    else if (Object.keys(this.selectedSharePropertyClient).length != 0) {
      if (form.value['message'] == '' || form.value['message'].trim().length == 0) {
        this.showShareErrorMsg = true;
        this.sharePropertyErrorMSg = 'Please enter message';
      }
      else {
        sharePropertyParms.set('is_email', 'false');
        sharePropertyParms.set('user_email', this.selectedSharePropertyClient['client_email']);
        sharePropertyParms.set('message', form.value['message']);
        sharePropertyParms.set('receiver_id', this.selectedSharePropertyClient['client_id']);
        this.makeSharePropertyAPICall(sharePropertyParms);
      }
    }
  }

  makeSharePropertyAPICall(sharePropertyParms) {
    this.myListingService.shareProperty(sharePropertyParms).subscribe(res => {
      this.successResponse(res);
      this.sharPropertyFormGroup.reset();
      $("#shareProperty").modal("hide");
      this.showShareErrorMsg = false;
      this.selectedSharePropertyClient = {};
      if (res['result']['is_email'] == false) {
        if (BaseComponent.user != undefined && BaseComponent.user.is_paid_account) {
          this.router.navigate(['messaging']);
        }
      }
    }, err => this.errorResponse(err.json()))
  }

  shareFBProperty(e) {
    e.preventDefault();
    // FB.ui({
    //   method: 'feed',
    //   link: window.location.origin + '/my-listing/property-detail?propertyId='+this.sharePropertyId,
    // });
    FB.ui({
      method: 'share',
      name: 'Open Houses Direct',
      app_id: '***************',
      redirect_uri: 'https://share.openhousesdirect.com/share/p/' + this.sharePropertyId,
      href: 'https://share.openhousesdirect.com/share/p/' + this.sharePropertyId,
      // href: "https://high-apricot-196023.appspot.com.storage.googleapis.com:443/property_file/staging/23333/20190729112939076994.jpg?Signature=rU9gvh4j9AwsWOgTwyNeh8pOhoY%3D&Expires=**********&GoogleAccessId=GOOGDKB2B4RRIUZIEVZV",
      hashtag: window.location.origin.toString,
      quote: 'https://share.openhousesdirect.com/share/p/' + this.sharePropertyId,
    });
  }

  getUrl() {
    return window.location.origin + '/my-listing/property-detail?propertyId=' + this.sharePropertyId;
  }

  shareTwitterProperty() {

  }

  showArmls() {
    this.isArmlsView = true
    this.armlsId = ''
  }
  closeArmls() {
    this.isArmlsView = false
    this.armlsId = ''
  }

  addArmls() {
    const armlsUrlParams = new URLSearchParams();
    armlsUrlParams.set("listing_key", this.armlsId);
    console.log(armlsUrlParams)
    this.showArmlLoader = true
    this.myListingService.addPropertyListingById(armlsUrlParams).subscribe(
      (res) => {
        this.toastService.addToast('success', 'Success', res['message']);
        this.armlsId = ''
        this.ngOnInit()
        this.isArmlsView = false
        this.showArmlLoader = false
      },
      (err) => {
        this.showArmlLoader = false
        this.toastService.addToast('error', 'Error', 'MLSID not found');
      }
    );

  }

}
