export class Event{
    property_id: any;
    date: any;
    event_type: string;
    start_time: string;
    end_time: string;
    description: string;
    event_agent_type: string;
    agent_id: string;
    updatedDate: string;
    open_house_agent_image : string;
    open_house_agent_name : string;
    brokerage_name : string;
}

export class EventError{
    property_id_error: any = "";
    date_error: any = "";
    event_type_error: string = "";
    start_time_error: string = "";
    end_time_error: string = "";
    description_error: string = "";
    event_agent_type_error: string = "";
    agent_id_error: string = "";
}

export class PropertyEvent{
    address: string;
    brokerage_name: string;
    property_id: any;
    event_id: any;
    event_date: any;
    date: any;
    event_type: string;
    event_type_msg: string;
    start_time: string;
    end_time: string;
    open_house_agent_id : string;
    open_house_agent_image: string;
    open_house_agent_name: string;
    listing_agent_image: string;
    listing_agent_name: string;
    listing_agent_id: any;
    description: string = '';
    property_file: string;
    property_price: string;
    property_status: string;
    event_agent_type: string;
    agent_id: string;
    updatedDate: string;
    location :string;
    street : string;
    is_running : Boolean;
    is_listhub_event : Boolean;
}

export class ProeprtyEvent{
    property_file : String = '';
    property_id: any = 0;
    date: any;
    event_type: string = '';
    start_time: string = '';
    end_time: string = '';
    description: string = '';
    event_agent_type: string = '';
    agent_id: string = '';
    builder: string = '';
    street: string = '';
}