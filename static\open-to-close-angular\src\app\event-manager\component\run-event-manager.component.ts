import { Component, OnInit,<PERSON>Z<PERSON>,ViewChild,OnDestroy } from '@angular/core';
import { EventManaegerComponent } from '@app/event-manager/component/event-manager.component';
import { BaseComponent } from '@app/base/components/base.component';
import { Params } from '@angular/router/src/shared';
import { BasicInfo, OpenHouseOverview, RatingOverview } from '@app/event-manager/model/run-tool-info.model';
import * as moment from 'moment';
import { ChatService } from '@app/messaging/service/chat-service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { CheckedInCountPipes } from '@app/event-manager/pipes/checked-in-count-pipe';
import { NotificationService } from '@app/notification/service/notification-service';
import { MyLeadDialog } from '@app/myLeads/model/my-lead-dialog.model';
import { EventModalComponent } from '@app/event-modal/component/event-modal.component';
import { EventModal } from '@app/event-modal/models/event-modal';
import { HeaderComponent } from '@app/root/components/header.component';

declare var $;

@Component({
  selector: 'run-event-manager',
  templateUrl: '../views/run-event-manager.component.html',
  styleUrls: ['../css/event-manager.component.css'],
})
export class RunEventManagerComponent extends EventManaegerComponent  implements OnInit,OnDestroy {


  @ViewChild(EventModalComponent) eventCardComponent: EventModalComponent;
  @ViewChild(HeaderComponent) headerComponent: HeaderComponent;


  public myNoteList = [];
  updateNoteIndex;
  public newNote: String = '';
  public noteParams: any = {};
  public disableNotebtn: Boolean = true;
  public myNoteTotalCount = 0;
  public myNoteItemPerPage: any;
  public myNoteIndex: number = 2;
  public showMNLoader : Boolean = false;

  public showEventChat : boolean = true;
  public showRecipientChat : boolean = false;
  public showSingeEventChat : boolean = false;
  public filterDropDown = ['Represented','Unrepresented'];
  public recipientUserList = [];
  public messageList= [];

  public eventBasicInfo : BasicInfo = new BasicInfo();
  public openHouseOverview : OpenHouseOverview = new OpenHouseOverview();
  public ratingOverview : RatingOverview = new RatingOverview();
  public leadDialog : MyLeadDialog = new MyLeadDialog();
  public guestList = [];
  public representedGuestList = [];
  public unrepresentedGuestList = [];
  public ratingList = [];
  public representedRatingList = [];
  public unrepresentedRatingList = [];
  public selectAllGuest : Boolean = false;
  public selectAllRatingList : Boolean = false;
  public currentEventId = '';
  public currentLoginUserId : any;

  public selectedGusetList = [];

  //Chat
  chatService : ChatService;
  public eventChatThreadList = [];
  public selectedClient = {};
  public chatList = [];
  public senderId = '';
  public tempChatThreadList = [];
  public eventFirebaseSubscription;
  public chatFirebaseSubscription;

  //filter
  public guestListFilterType = [];
  public ratingListFilterType = [];
  public tempAllGuestList = [];
  public tempAllRatingList = [];

  //hide checkin
  public hideChecked = false;

  //message
  public selectedRating = [];

  //disable all page
  public disableEventEdit : Boolean = false;

  public notificationService : NotificationService;

  public disableGuestMsgBtn : Boolean = false;
  public disableCheckInMsgBtn : Boolean = false;
  public isTabletScreen : Boolean = false;

  public isPaidAccount : boolean;
  
  constructor(zone: NgZone) { 
    super(zone);
    this.chatService = ServiceLocator.injector.get(ChatService);
    this.notificationService = ServiceLocator.injector.get(NotificationService);
  }

  ngOnInit() {
    let self = this;
    $(document).ready(function($){
      window['filter'] = $('.filter').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});
      window['ratings'] = $('.ratings').SumoSelect({forceCustomRendering: true,triggerChangeCombined: true,isClickAwayOk: true,selectAll:false});

      if($(window).width() > 767 && $(window).width() < 1025){
        self.isTabletScreen = true;
      }

        $(this).scrollTop(0);       
        if($(window).width() >= 1000) {
          $('.panel-collapse').addClass("in");
          $('.check_table1 .title2').off("click");
          $( ".check_table1 .title2" ).click(function( event ) {
            event.stopPropagation();
          });    
        }
        $('.group_acc.panel-collapse.collapse.guest-list-table-col').on('shown.bs.collapse', function (e) {
          let newHeight = $(".check_shown_tables").height() + ($(".table-position").height() - 70 );
          $(".check_table1.check-table .group_acc.panel-collapse").height(newHeight);
        }).on('hidden.bs.collapse', function (e) {
          $(".check_table1.check-table .group_acc.panel-collapse").height(0);
          $(".check_table1.check-table").height('auto');
        });
        $('.filter').click(function(){
          self.hideChecked = false;
          var value = []
          $('.sumoGuestEvent option:selected').each(function(i) {
              value.push($(this).val());
          });
          self.guestListFilterType = value;
          self.filterGuestList();
        });

        $('.rating-filter').click(function(){
          var value = []
          $('.sumoEvent option:selected').each(function(i) {
              value.push($(this).val());
          });
          self.ratingListFilterType = value;
          self.filterRatingList();
        });

        $("img.open_chat").click(function(){
          $(".homepage.header_fix .chat_message.check_event .left_side.height_auto.eventchatwidth").toggle();
          $(".chat_message.check_event .right_side_event_agent.right_side_overflow").toggle();
        });

        $(document).on( "click", "img.white_leftarrow_image.cursor-pointer", function() {
          $('body .homepage.header_fix .chat_message.check_event .left_side.height_auto.eventchatwidth').show();
        });

        var windowSize = $(window).width();
        if(windowSize <= 1023){
          $(document).on( "click", "a.check_in_link button.btn.btn1.add_new_list.dis_inline", function() {
            $('.left_side.height_auto.Recipient_Chosen').show();
            $('.left_side.height_auto.eventchatwidth').hide();
          });
        }
      });

    this.eventMangerService.updateRunningEvent.subscribe(res =>{
      this.updateRuningEvent();
    },err=>{
      console.log(err);
    });

    this.eventFirebaseSubscription = this.chatService.onRunToolFirebase.subscribe(res =>{
      let chatParam = new URLSearchParams();
      chatParam.set('last_timestamp',res['data']['last_timestamp']);
      chatParam.set('sender_id',res['data']['sender_id']);
      var is_active_thread = false;

      if(res['data']['sender_id'] == this.selectedClient['receiver_id']){
        is_active_thread = true;
      }
      this.chatService.getRecentChat(res['data']['last_timestamp'], res['data']['sender_id'],is_active_thread).subscribe(msgRes => {
        msgRes['result'].forEach(record => {
          if(record['sender_id'] == this.selectedClient['receiver_id']){
            this.zone.run(()=>{
              this.chatList.push(record);
              $(document).ready(function() {
                $(".chat_session").scrollTop($(".chat_session")[0].scrollHeight);
              });
            });
            record['is_read'] = true;
            this.updateChatThread(record);
          }
          else{
            this.updateChatThread(msgRes['result'][0]);
          }
        });
      },err => {
        console.log(err);
      });
    },err=>{
      console.log(err)
    });
    
    this.chatFirebaseSubscription = this.route.queryParams.subscribe((params:Params)=>{      
      if(params['eventId'] != undefined || params['eventId'] != null){
        this.currentEventId = params['eventId'];        
        this.setPreviousScreen('/event-manager/run-event-manager?eventId='+this.currentEventId);
        let eventParams = new URLSearchParams();
        eventParams.set("event_id",params['eventId']);
        //chat_Thread
        if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
          if(BaseComponent.user.user_type == 'LA' && BaseComponent.user.is_paid_account){            
            this.getEventChatThread(eventParams);
          }
        }
       //Basic_info
        eventParams.set("type","DE");
        this.getEventDetail(eventParams,"DE");
        //Overview
        eventParams.set("type","OV");
        this.getEventDetail(eventParams,"OV");
        //CheckIn_List
        eventParams.set("type","CH");
        this.getEventDetail(eventParams,"CH");
        //Guestlist
        eventParams.set("type","GU");
        this.getEventDetail(eventParams,"GU");
      }
    });

    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      // this.isPaidAccount = BaseComponent.user.is_paid_account;
      if(BaseComponent.user.user_type == 'LA' && !BaseComponent.user.is_paid_account){
        this.isPaidAccount = false;
      }
      else if(BaseComponent.user.user_type == 'LA' && BaseComponent.user.is_paid_account){
        this.isPaidAccount = true;
      }
      else{
        this.isPaidAccount = true;
      }
    }
  }


  updateRuningEvent(){
    let eventParams = new URLSearchParams();
    eventParams.set("event_id",this.currentEventId);
    //chat_thread
    // this.getEventChatThread(eventParams);
    //user_list
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user)){
      if(BaseComponent.user.user_type == 'LA' && BaseComponent.user.is_paid_account){
        this.recipientList();
      }
    }
    //Basic_info
    eventParams.set("type","DE");
    this.getEventDetail(eventParams,"DE");
    //Overview
    eventParams.set("type","OV");
    this.getEventDetail(eventParams,"OV");
    //CheckIn_List
    eventParams.set("type","CH");
    this.getEventDetail(eventParams,"CH");
    //Guestlist
    eventParams.set("type","GU");
    this.getEventDetail(eventParams,"GU");
}

  getEventDetail(eventParams,type){
    this.eventMangerService.getRunEventDetails(eventParams).subscribe(res =>{
      if(BaseComponent.user != undefined && Object.keys(BaseComponent.user)){
        this.currentLoginUserId = BaseComponent.user.id;
        this.senderId = BaseComponent.user.id;
      }
      
      if(type == "DE"){
        this.eventBasicInfo = res['result'];
        // if(res['result']['event_status'] != 'RU'){
        //   this.disableEventEdit = true
        // }
        // else{
        //   this.disableEventEdit = false;
        // }
      }
      if(type == "OV"){
        this.openHouseOverview = res['result']['open_house_overview'];        
        this.ratingOverview = res['result']['rating_overview'];
      }
      if(type == "CH"){
        this.ratingList = res['result']['all_list'];
        this.tempAllRatingList = res['result']['all_list'];
        this.representedRatingList = res['result']['represented_list'];
        this.unrepresentedRatingList = res['result']['unrepresented_list'];
      }
      if(type == "GU"){
        this.guestList = res['result']['all_list'];
        this.tempAllGuestList = res['result']['all_list'];
        this.representedGuestList = res['result']['represented_list'];
        this.unrepresentedGuestList = res['result']['unrepresented_list'];
        
        setTimeout(() => {
          let newHeight = $(".check_table1.check-table table tbody").height() + 250;
          if($(window).width() > 1023){
            $(".check_table1.check-table").height(newHeight);    
          }
        }, 100);
      }
    },err=>{
      console.log(err);
    });
  }

  getEventChatThread(eventParams){
    this.eventMangerService.getEventChatThread(eventParams).subscribe(res =>{      
      let array = res['result'];
      this.eventChatThreadList = array.reverse();
      this.tempChatThreadList = res['result'];
      this.recipientList();
    },err=>{
      console.log(err)
    });
  }

  recipientList(){
    let userParams = new URLSearchParams();
    userParams.set("event_id",this.currentEventId);
    this.eventMangerService.getEventChatUserList(userParams).subscribe(res =>{
      this.recipientUserList = res['result'];
    },err=>{
      console.log(err)
    });
  }

  getCountProgress(positiveVlaue,negativeValue, type){
    var total = positiveVlaue + negativeValue;
    var cal = (positiveVlaue/total)*100;
    cal = parseInt(cal.toString());
    return "c100 p" + cal + " " + type;
  }

  onSingleGuestSelect(type,user){
    this.selectAllGuest = false;
    if(type.target.checked == true){
      this.selectedGusetList.push(user);
    }
    else
    {
      let lastIndex = this.selectedRating.indexOf(user);
      this.selectedGusetList.splice(lastIndex,1);
    }
  }

  onAllGuestSelect(type){
    if(type.target.checked == true){
      for(let i=0;i<this.guestList.length;i++){
        if(this.guestList[i]['is_checkin'] == false){
          this.selectedGusetList.push(this.guestList[i]);
        }
      }
    }
    else
    {
      this.selectedGusetList = [];  
    }
  }

  startMessage(type){
    this.senderId = BaseComponent.user.id;
    if(type == 'guestList'){
      if(this.selectedGusetList.length == 1){
        let user = this.recipientUserList.filter((user=> user.user_id == this.selectedGusetList[0]['user_id']));
        if(user.length != 0){
          this.chatList = [];
          this.disableGuestMsgBtn = true;
          // this.startMessageWithThumbnail(this.selectedGusetList[0]['user_id'],user);
        }
      }
      else if(this.selectedGusetList.length != 0){
        this.warningMessage('Can not send message to multiple guests');
      }
    }
    if(type == 'ratingList'){
      if(this.selectedRating.length == 1){
        let user = this.recipientUserList.filter((user=> user.user_id == this.selectedRating[0]['user_id']));
        if(user.length != 0){
          this.chatList = [];
          this.disableCheckInMsgBtn = true;
          // this.startMessageWithThumbnail(this.selectedRating[0]['user_id'],user);
        }
      }
      else if(this.selectedRating.length != 0){
        this.warningMessage('Can not send message to multiple guests');
      }
    }
  }
  
  checkInsMsg(selectedUser){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0){
      if(BaseComponent.user.user_type == "LA" && BaseComponent.user.is_paid_account == false){
        this.openPlansModal();
      }
      else{
        let userList = this.recipientUserList.filter((user=> user.user_id == selectedUser['user_id']));
        this.chatList = [];
        $("#checkIn_"+userList[0]['user_id']).prop("disabled",true);
        this.startMessageWithThumbnail(userList[0]['user_id'],userList[0],'checkIns');    
      }
    }
  }

  guestMsg(selectedUser){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0){
      if(BaseComponent.user.user_type == "LA" && BaseComponent.user.is_paid_account == false){        
        this.openPlansModal();
      }
      else{        
        this.chatList = [];
        let userList = this.recipientUserList.filter((user=> user.user_id == selectedUser['user_id']));
        $("#guest_"+userList[0]['user_id']).prop("disabled",true);
        this.startMessageWithThumbnail(userList[0]['user_id'],userList[0],'guest');
      }
    }
  }

  startMessageWithThumbnail(userId,user,origin){
    let msgParams = new URLSearchParams();
    msgParams.set('message',this.staticWebUrl+'search/property-detail?propertyId='+this.eventBasicInfo.property_id);
    msgParams.set('receiver_id',userId);
    msgParams.set('is_link','true');
    
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user)){
      this.currentLoginUserId = BaseComponent.user.id;
      this.senderId = BaseComponent.user.id;
    }

    this.chatService.sendMessage(msgParams).subscribe(res=>{
      this.onRecipientSelect(user);
      if(origin == 'checkIns'){
        $("#checkIn_"+user['user_id']).prop("disabled",false);
      }
      if(origin == 'guest'){
        $("#guest_"+user['user_id']).prop("disabled",false);
      }
      res['result']['sender_id'] = res['result']['receiver_id'];
      this.updateChatThread(res['result']) 
    });
  }

  onSingleRatingSelect(type,user){
    this.selectAllRatingList = false;
    if(type.target.checked == true){
      this.selectedRating.push(user);
    }
    else{
      let lastIndex = this.selectedRating.indexOf(user);
      this.selectedRating.splice(lastIndex,1);
    }
  }

  onAllRatingSelect(type){
    if(type.target.checked == true){
      for(let i=0;i<this.ratingList.length;i++){
        this.selectedRating.push(this.ratingList[i]);
      }
    }
    else
    {
      this.selectedRating = [];  
    }
  }

  checkIn(){
    let checkInParams = new URLSearchParams();
    checkInParams.set('event_id',this.currentEventId);
    if(this.selectedGusetList.length !=0){
      for(let i=0;i<this.selectedGusetList.length;i++){
        if(this.selectedGusetList[i]['is_checkin'] == false){
          checkInParams.set('checkin_user_list['+[i]+']',this.selectedGusetList[i]['user_id']);
        }
      }
      this.eventMangerService.addToCheckIn(checkInParams).subscribe(res =>{
        this.successResponse(res);
        let eventParams = new URLSearchParams();
        eventParams.set("event_id",this.currentEventId);
        //CheckIn_List
        eventParams.set("type","CH");
        this.getEventDetail(eventParams,"CH");
        //Guestlist
        eventParams.set("type","GU");
        this.getEventDetail(eventParams,"GU");
      },err =>{
        this.errorResponse(err.json());
      });
    }
  }

  getTime(dateTime){
    if(dateTime != ''){
      return  moment.utc(dateTime).local().format('hh:mm a');
    }
  }

  getLastTime(dateTime){
    return  moment.utc(dateTime).local().format('hh:mm a');
  }

  onRecipientSelect(user){
    var customChatThread={
      "user_name" : user['user_name'],
      "chat_thread_id" : user['user_id'],
      "is_read" : true,
      "last_message" : "",
      "last_message_time" : "",
      "profile_image" : user['profile_image'],
      "receiver_id" :user['user_id'],
      "user_initial" :user['user_initial']
    }
    this.selectedClient = customChatThread;
    this.getChat();

    this.showSingeEventChat = true;
    this.showRecipientChat = false;
    this.showEventChat = false;
  }

  backToChat(){
    this.showSingeEventChat = false;
    this.showRecipientChat = false;
    this.showEventChat = true;
    this.filterUser('');
    $('img.open_chat').removeClass('hide_open_chat');
  }

  newChat(){
    this.showSingeEventChat = false;
    this.showRecipientChat = true;
    this.showEventChat = false;
    $('img.open_chat').addClass('hide_open_chat');
  }

  showSingelChat(chatThread){
    this.showSingeEventChat = true;
    this.showRecipientChat = false;
    this.showEventChat = false;
    this.selectedClient = chatThread;
    this.getChat();
    let index = this.eventChatThreadList.indexOf(chatThread);
    this.eventChatThreadList[index]['is_read'] = true;
    this.senderId = BaseComponent.user.id;
    $('img.open_chat').addClass('hide_open_chat');
  }

  getChat(){
    this.zone.run(
      () => {
      let self = this;
        $(document).ready(function() {
          $('.chat_session').scroll(function() {
            var getScroll = $('.chat_session').scrollTop()
            if(getScroll == 0 && self.chatList[0]!=undefined)
            {
              var lastTimestamp = self.chatList[0]['date_time'];
              var receiverId = self.chatList[0]['receiver_id'];
              var chatId = self.chatList[0]['chat_id']
              self.chatService.getAllChat(lastTimestamp,receiverId).subscribe(res =>{
                self.zone.run(
                  () => {
                    res['result'].forEach(record => {
                      self.chatList.unshift(record);
                    });                
                  })            
                var position = ($("#"+chatId).offset())
                if(position !=undefined || position != null){
                  $(".chat_session").scrollTop(position['top']-230);
                }
              },err=>console.log(err))
            }
          });
        });
      });

    let clientParams = new URLSearchParams();
    clientParams.set('receiver_id',this.selectedClient['receiver_id']);
    this.chatService.getClientChat(clientParams).subscribe(res =>{
      let array = res['result'];
      this.chatList = array.reverse();
      $(document).ready(function() {
        $(".chat_session").scrollTop($(".chat_session")[0].scrollHeight);
      });
    },err=>{
      console.log(err)
    });
    this.notificationService.setMessageDotIcon.emit('getStatusFormAPI');
  }

  sendMessage(msg){
    if(msg.trim().length !=0){
      var isLink = false;
      var messageType = msg.split('?');
      if(messageType.length > 0){
        if(messageType[1] != undefined){
          var property = messageType[1].split('=');
          if(property[0] == 'propertyId' && property[1] != undefined){
            isLink = true;
          }
        }
      }
      let msgParams = new URLSearchParams();
      msgParams.set('message',msg);
      msgParams.set('receiver_id',this.selectedClient['receiver_id']);
      msgParams.set('is_link',isLink.toString());
      
      var msgObj={
      "sender_id": this.currentLoginUserId,
      "receiver_id": this.selectedClient['receiver_id'],
      "message": msg,
      "date_time": new Date(),
      "is_link": isLink
      }
      
      this.chatList.push(msgObj);
      let indexOfLastMsg = this.chatList.indexOf(msgObj);
      this.chatService.sendMessage(msgParams).subscribe(res =>{
        if(res['result']['is_link'] == true){
          this.chatList[indexOfLastMsg]['property_detail'] = res['result']['property_detail'];
        }
        res['result']['sender_id'] = res['result']['receiver_id'];
        this.updateChatThread(res['result']) 
      },err=>{
        this.chatList.splice(indexOfLastMsg,1);
        this.errorResponse(err.json())
      });

      $(document).ready(function() {
        $("#msgtxtbox").val('');
        $(".chat_session").scrollTop($('.chat_session')[0].scrollHeight)
      });
    }
  }

  filterUser(userName){
    if(userName.trim().length !=0){
      this.eventChatThreadList = this.tempChatThreadList.filter(name => {
        return name['user_name'].toLowerCase().includes(userName.toLowerCase());
      });
    }else{
      this.eventChatThreadList = this.tempChatThreadList;
    }
  }

  updateChatThread(res){
    let chatThread = this.eventChatThreadList.filter((chatThread) => chatThread.receiver_id == res['sender_id']);
    if(chatThread.length !=0){
      let chatThreadIndex = this.eventChatThreadList.indexOf(chatThread[0]);
      this.eventChatThreadList[chatThreadIndex]['last_message'] = res['message'];
      this.eventChatThreadList[chatThreadIndex]['is_read'] = res['is_read'];
      this.eventChatThreadList[chatThreadIndex]['last_message_time'] = res['date_time'];
      chatThread = this.eventChatThreadList[chatThreadIndex];
      this.eventChatThreadList.splice(chatThreadIndex,1);
      this.eventChatThreadList.unshift(chatThread);
      
      if(res['is_read'] == true){
        this.notificationService.setMessageDotIcon.emit('getStatusFormAPI');
      }

      $(document).ready(function() {
        $("#scroll").scrollTop(0);
      });
    }
    else{
      let eventParams = new URLSearchParams();
      eventParams.set("event_id",this.currentEventId);
      this.getEventChatThread(eventParams);
    }
  }

  filterGuestList(){
    this.zone.run(()=>{
      this.guestList = [];
    if(this.guestListFilterType.length !=0){
      for(let i=0;i<this.guestListFilterType.length;i++){
        if(this.guestListFilterType[i] == 'Represented'){
          this.representedGuestList.forEach(record =>{
            this.guestList.push(record);
          });
        }
        else if(this.guestListFilterType[i] == 'Unrepresented'){
          this.unrepresentedGuestList.forEach(record =>{
            this.guestList.push(record);
          });
        }
      }
    }
    else{
      this.tempAllGuestList.forEach(record =>{
        this.guestList.push(record);
      });
    }
    });
  }
  
  filterRatingList(){
    this.zone.run(()=>{
      this.ratingList = [];
      if(this.ratingListFilterType.length !=0){
        for(let i=0;i<this.ratingListFilterType.length;i++){
          if(this.ratingListFilterType[i] == 'Represented'){
            this.representedRatingList.forEach(record =>{
              this.ratingList.push(record);
            });
          }
          else if(this.ratingListFilterType[i] == 'Unrepresented'){
            this.unrepresentedRatingList.forEach(record =>{
              this.ratingList.push(record);
            });
          }
        }
      }
      else{
        this.tempAllRatingList.forEach(record =>{
          this.ratingList.push(record);
        });
      }
    });
  }

  showCheckedList(){
    this.hideChecked = true;
    setTimeout(() => {
      let newHeight = $(".check_table1.check-table table tbody").height() + 250;
      // let newHeight = $(".check_table1.check-table .group_acc.panel-collapse").height() + $(".table-position").height();
      $(".check_table1.check-table").height(newHeight);
    }, 100);
  }

  hideCheckedList(){
    this.hideChecked = false;
    setTimeout(() => {
      let newHeight = $(".check_table1.check-table table tbody").height() + 250;
    //   let newHeight = $(".check_table1.check-table .group_acc.panel-collapse").height() + $(".table-position").height();
      $(".check_table1.check-table").height(newHeight);
    }, 100);
  }

  getCheckedInCount(){
    var pipe = new CheckedInCountPipes();
    return pipe.transform(this.guestList);
  }

  openGuestBookMode(){
    this.router.navigate(['event-manager/guest-book-mode'],{queryParams:{eventId:this.currentEventId}});
  }

  endOpenHouse(){
    let endOpenHouseParams = new URLSearchParams();
    endOpenHouseParams.set('event_status','COM');
    endOpenHouseParams.set('event_id',this.currentEventId);
    this.eventMangerService.endOpenHouse(endOpenHouseParams).subscribe(res =>{
      this.successResponse(res);
      this.disableEventEdit = true;
      $("#endOpenHouseModel").modal("hide");
    },err=>{
      this.errorResponse(err.json());
    });
  }

  getMessageTime(dateTime){
    return moment.utc(dateTime).local().format('MM/DD/YYYY hh:mm a');
  }

  contactListingAgent(agent){
    let user = this.recipientUserList.filter((user=> user.user_id == agent['listing_agent_id']));
      if(user.length != 0){
        this.chatList = [];
        this.onRecipientSelect(user[0])
      }
  }

  addToMyLeads(){
    if(this.selectedRating.length != 0){
      let propertyRatesParams = new URLSearchParams();
      for(let i=0;i<this.selectedRating.length;i++){
        propertyRatesParams.set('property_rate_list['+i+']', this.selectedRating[i].property_rate_id);
      }
      this.eventMangerService.addToMyLeads(propertyRatesParams).subscribe(res => {
        this.successResponse(res);
      }, err => {
        console.log(err);
      });
    }
  }

  openViewLeadModal(userInfo){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0){
      if(BaseComponent.user.user_type == "LA" && BaseComponent.user.is_paid_account == false){
        this.openPlansModal();
      }
      else{
        this.leadDialog = userInfo;
        $('#viewLeadModal').modal('show');
      }
    }
    else{
      this.leadDialog = userInfo;
      $('#viewLeadModal').modal('show');
    }
  }

  showUserPropertyRating(rating){
    var rate = new EventModal();
    rate.property_rate.bathroom_rating = rating['bathroom_rating'];
    rate.property_rate.bedroom_rating = rating['bedroom_rating'];
    rate.property_rate.finishes_rating = rating['finishes_rating'];
    rate.property_rate.floorplan_rating = rating['floorplan_rating'];
    rate.property_rate.kitchen_rating = rating['kitchen_rating'];
    rate.property_rate.landscaping_rating = rating['landscaping_rating'];
    rate.property_rate.neighbourhood_rating = rating['neighbourhood_rating'];
    rate.property_rate.price_rating = rating['price_rating'];
    rate.property_rate.rating = rating['rating'];
    rate.event_type_msg = rating['event_type_msg'];
    rate.property_file = rating['property_file'];
    rate.property_rate.notes = rating['notes'];
    this.eventCardComponent.openRatingModal(rate,false);
  }

  exportMyLeads(){
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0){
      if(BaseComponent.user.user_type == "LA" && BaseComponent.user.is_paid_account == false){
        this.openPlansModal();
      }else{
        var leadParms = new URLSearchParams();
        leadParms.set('event_id',this.currentEventId);
        if(this.selectedRating.length != 0){
          for(let i=0;i<this.selectedRating.length;i++){
            leadParms.set('user_list['+i+']', this.selectedRating[i].user_id);
          }
          this.generateExportLeads(leadParms);
        }
        else if(this.selectedRating.length == 0){
          for(let i=0;i<this.ratingList.length;i++){
            leadParms.set('user_list['+i+']', this.ratingList[i].user_id);
          }
          this.generateExportLeads(leadParms);
        }
      }
    }
  }

  generateExportLeads(leadParms){
    this.eventMangerService.exportLeads(leadParms).subscribe(res =>{
      this.downloadFile(res,"leads.csv");
    },err => this.errorResponse(err.json()));
  }

  openPlansModal(){
    this.headerComponent.getPlans();
    this.headerComponent.listingAgent = true;
    this.headerComponent.openHeaderPlansModal();
  }

  ngOnDestroy(){
    if(this.eventFirebaseSubscription != undefined){
      this.eventFirebaseSubscription.unsubscribe();
    }
    if(this.chatFirebaseSubscription != undefined){
      this.chatFirebaseSubscription.unsubscribe();
    }
  }
  // -- NOTES RELATED FUNCTIONALITY

  startNote(lead) {
    console.log('lead', lead)
    this.leadDialog = lead;
    $("#noteEvent").modal("show");
    this.newNote = '';
    this.myNoteList = []
    if(Object.keys(this.leadDialog['lead_notes']).length === 0 ){
      this.leadDialog['lead_notes'] = []
    }
    this.myNoteList = this.leadDialog['lead_notes'] || []
    // this.myLeadsService.getMyNotes(this.leadDialog.lead_id).subscribe(res => {
    //   console.log('Final Result', res)
    //   this.myNoteList = res.result.records
    //   this.newNote = '';
    // }, err =>
    //   this.errorResponse(err.json())
    // )
  }

  SaveNote() {
    console.log('Save notes')
    let noteParams = new URLSearchParams();
    noteParams.set("note", this.newNote.toString());
    noteParams.set('lead_id', this.leadDialog.lead_id);
    console.log(noteParams)
    this.eventMangerService.addNewNote(noteParams).subscribe(res => {
      this.successResponse(res);
      var resObj = {
        'lead_id': res['result']['lead_id'],
        'updated_date_time': new Date(),
        'note': res['result']['note'],
        'id': res['result']['id'],
        'user': res['result']['user']
      }
      this.myNoteList.push(resObj);
      this.newNote = '';
      if(Object.keys(this.leadDialog['lead_notes']).length ===0){
        this.leadDialog['lead_notes']=[]
      }
      this.leadDialog['count'] = this.leadDialog['count'] + 1
      // this.disableNotebtn = true;
      // this.leadDialog['notes'].push(resObj)
    }, err =>
      this.errorResponse(err.json())
    )
  }
  showNote(note, manageType, index) {
    if (manageType == "UPDATE") {
      this.updateNoteIndex = index;
    }
    else if (manageType == "DELETE") {
      let deleteNoteParams = new URLSearchParams();
      deleteNoteParams.set('note_id', note.id);
      this.eventMangerService.deleteNote(deleteNoteParams).subscribe(res => {
        this.successResponse(res);
        this.myNoteList.splice(index, 1);
        this.leadDialog['count'] = this.leadDialog['count'] - 1
        // this.leadDialog['notes'].splice(index, 1);
      }, err => this.errorResponse(err.json()));
    }
  }

  manageNote(selectedNote, type, updatedNotes, noteIndex) {
    if (type == 'UPDATE') {
      let updateNoteParams = new URLSearchParams();
      updateNoteParams.set('note', updatedNotes);
      updateNoteParams.set('id', selectedNote.id);
      this.eventMangerService.updateNote(updateNoteParams).subscribe(res => {
        this.successResponse(res);
        this.myNoteList[noteIndex].note = updatedNotes;
        this.updateNoteIndex = undefined;
      }, err => this.errorResponse(err.json()));
    }
    else if (type == 'CANCEL') {
      this.updateNoteIndex = undefined;
    }
  }
  isValidNote() {
    if (this.newNote.trim().length != 0) {
      this.disableNotebtn = false;
    }
    else {
      this.disableNotebtn = true;
    }
  }
  utcDateFormat(date) {
    return moment(date).format('MM.DD.YYYY');
  }
}