

.rate_modal .modal-dialog.modal-lg .modal-body {
	padding: 0px;
	overflow: inherit;
}

.right_property_bar i {
	font-size: 20px !important;
}

@media (min-width: 768px) and (max-width: 1023px) {
	.notification-time{
        margin-top: 0px !important;
	}
	.invoice_history {
		width: 100% !important;
		display: inline-block;
	}
	.open_new_page .section_1 .banner_group {
		max-width: 100%;
	}
	.open_new_page .section_1 .banner_title {
		font-weight: 600!important;
		width: 100% !important;
	}
	.open_new_page .section2_group {
		max-width: 93%;
		margin: auto;
	}
	.open_new_page .section3 .sec_3_video {
		margin: 20px 0px 50px 0px;
		height: 316px;
	}
	.built-text{
		width: 70% !important;
   	}
   	.built-text-read-btn{
        width: 30% !important;
	}
	.open_new_page .section4{
		padding-top: 64%;
	}
	.open_new_page .section3 {
		margin-bottom: 120px;
		padding-bottom: 340px;
	}
	.open_new_page .section3 .sec_3_bottom {
		padding: 40px 30px;
		width: 90%;
	}
	footer {
		height: 100px;
	}
	a.footer_logo.cursor-pointer {
		display: table;
		margin: auto;
		padding-top: 10px;
	}
	.save_notes.export_csv {
		margin-right: 0px;
		left: 0px;
	}
	.myclient_navbar {
		width: 100%;
		overflow: scroll;
	}
	.myclient_navbar ul {
		white-space: nowrap;
	}
	.new_profile_title {
		width: 100%;
	}
	.new_profiles {
		padding: 0px 25px;
	}
	.new_profile_title {
		display: flex;
	}
	.artboard6_sidebar.side_bar_height {
		margin-bottom: 70px;
	}
	.property_header .col-sm-10 {
		display: block;
		width: 70%;
	}
	.col-sm-7.overviewLeft {
		width: 100%;
		padding-right: 40px;
		margin-bottom: 20px;
	}
	.col-sm-9.overviewRight.agent-overview-right {
		width: 100%;
	}
	.modal-dialog.modal-lg.Upgrade-Agent {
		float: left;
		width: 100% !important;
		margin: auto;
	}
	.agent_plan_upgrade.upgrade-contain {
		float: left;
		width: 57% !important;
		margin: auto;
		display: table;
		height: auto !important;
	}
	/* mobile tab css */
	#Property_View .bg-white {
		padding-left: 0px;
	}
	.col-sm-10.property-view-padding {
		width: 100%;
		padding: 30px !important;
	}
	.col-sm-6.property-right-view-padding {
		width: 100%;
		padding: 30px !important;
	}
	div#accordion .panel.panel-default {
		border: 0px;
	}
	div#accordion h2.bg_title.drop_down_icon {
		background: #fff;
		padding: 12px 0px;
		border-top: 1px solid #ccc;
		border-bottom: 1px solid #ccc;
		background-position: 98%;
	}
	.artboard6_sidebar.artboard7_side.dis_inline.upgrade-box.ng-star-inserted {
		margin-bottom: 70px;
	}
	li.visible-xs.mobile_logo_menu.back_white_menu {
		display: block !important;
	}
	nav.navbar.navbar-inverse.hidden-xs {
		display: none;
	}
	img.img-responsive.mobile_white_logo {
		height: 29px;
		padding-left: 20px;
		margin-bottom: 10px;
		margin-top: 10px;
		display: inline-block;
		position: relative;
		z-index: 11;
	}
	.back_white_menu button.click_mobile_menu.btn.btn-info {
		margin-top: 5px;
		margin-right: 3px;
		display: block !important;
	}
	button.btn.btn-info.menu_button {
		background: transparent;
		border: 0px;
		box-shadow: none;
		float: right;
		margin: 8px 10px 0px 0px;
		outline: 0 !important;
	}
	.back_white_menu ul.mobile_icon {
		display: inline-block;
		width: 24%;
		top: 16px;
		position: relative;
		float: right;
	}
	.back_white_menu ul.mobile_icon li {
		width: 20%;
		float: left;
	}
	ul.mobile_icon li {
		display: inline-block;
		width: calc(73vw / 4);
	}
	ul.mobile_icon li a {
		padding: 0px !important;
		text-align: center !important;
	}
	li.visible-xs.mobile_logo_menu.back_white_menu {
		background: #fff !important;
		position: relative;
		z-index: 11;
	}
	li.visible-xs.mobile_logo_menu.back_white_menu i.fa.fa-bars {
		/* border: 1px solid #00B7A6; */
		padding: 6px 8px;
		color: #00B7A6;
		font-size: 21px;
	}
	nav.mobile_new_menu ul {
		float: left;
		width: 104%;
		padding-left: 0px;
		height: 85vh;
	}
	/* nav#menu_group_mobile.mobile_new_menu.collapse.in {
        display: block;
    } */
	.mobile_new_menu {
		background: #00B8A7;
		float: left;
		width: 100%;
		top: -50px;
		position: relative;
		padding-top: 50px;
	}
	nav.mobile_new_menu li a {
		font-size: 16px;
		color: white;
		padding: 9px 20px;
		font-weight: normal !important;
		float: left;
		width: 100%;
	}
	nav.mobile_new_menu li a {
		cursor: pointer;
	}
	.property_header+.property_body.mt-20 .container {
		width: 100%;
		margin: 0px !important;
		padding: 0;
		overflow: hidden;
	}
	.col-sm-7.overviewLeft .col-sm-4.events,
	.col-sm-7.overviewLeft .col-sm-4.guests {
		width: 50%;
	}
	.col-sm-7.overviewLeft .col-sm-4.represented,
	.col-sm-7.overviewLeft .col-sm-4.unrepresented {
		width: 50%;
		margin-top: 10px;
	}
	.row.events {
		padding: 0px 25px;
	}
	#map {
		height: calc(100vh - 194px) !important;
		width: 100%;
	}
	footer {
		position: absolute;
	}
	body {
		margin-bottom: 100px !important;
	}
}

@media screen and (max-width: 767px) {

    body {
        float: left;
		width: 100%;
		margin-bottom: 141px !important;
    }


	.new_profile_group.dis_inline.col-sm-16 ul.nav.nav-pills {
		overflow: visible;
	}

	.modal-dialog.modal-lg.Upgrade-Agent {
		width: 95% !important;
		margin: auto;
	}
	.agent_plan_upgrade.upgrade-contain {
		float: left;
		width: 100% !important;
		margin: auto;
		display: table;
		height: auto !important;
	}
}

@media screen and (min-width: 1024px) {
	#menu_group_mobile {
		/* display: none; */
	}
}

.search_location.tablet_search_filter .form_group.col-sm-3 {
	max-width: 40% !important;
	width: 100%;
}

.search_location.tablet_search_filter .form_group.col-sm-2.price_select {
	width: 100%;
	max-width: 20%;
}

.all_home_type {
	width: 100%;
	max-width: 20%;
}

.more_filter.all_home_type_2 input.drop_down_icon {
	width: 100%;
	height: 44px;
	border: 1px solid #dbdbdb;
	box-shadow: none;
	padding: 0px;
	padding-left: 10px;
	background-position: 96%;
	margin-left: 10px;
}

.more_filter.all_home_type_2 {
	display: inline-block;
	width: 20%;
	position: relative;
}

.more_filter.all_home_type_2 input.drop_down_icon {
	color: #5A5A5A;
	outline: 0 !important;
	cursor: pointer;
}

.show_more_search_filter {
	float: left;
	width: 300px;
	position: absolute;
	box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.14);
	background: #f0f2f5;
	top: 44px;
	padding: 10px;
	right: -10px;
	z-index: 1000;
}

.show_more_search_filter .form_group.col-sm-2 {
	width: 100%;
	margin: 10px 0px;
	max-width: 100%;
}

.show_more_search_filter {
	display: none;
}

.tablet_search_location {
	padding: 70px 10px 11px 10px !important;
}

.ng-select.search-dropdown.ng-multiple .ng-control .ng-value-container .ng-value {
	font-size: 0.9em;
	margin-right: 5px !important;
	margin-bottom: 0px !important;
	padding: 0px;
	padding-right: 0px;
	padding-left: 6px;
	padding-top: 1px;
	padding-bottom: 1px;
	background-color: #1fb8a8;
	border-radius: 6px;
	border: none !important;
	color: white;
}

.ng-select.search-dropdown-tablet .ng-control {
	width: 126px !important;
	height: 45px !important;
	cursor: pointer !important;
}

.ng-select.search-dropdown-tablet .ng-control .ng-value-container .ng-placeholder {
	font-size: 16px !important;
	color: #8D8D8D !important;
	font-weight: normal !important;
	margin-left: -3.5px !important;
}

.ng-select.search-dropdown-tablet .ng-arrow-zone {
	border: solid #e0e0e0 !important;
	border-width: 0 2px 2px 0 !important;
	display: inline-block !important;
	padding: 5px !important;
	transform: rotate(45deg) !important;
	-webkit-transform: rotate(45deg) !important;
	width: 0 !important;
	margin: 16px 15px 20px 0px !important;
}

.ng-select.search-dropdown-tablet .ng-control {
	border: 1px solid #dbdbdb;
}

.ng-select.search-dropdown-tablet .ng-control {
	width: 100% !important;
}

.ng-select.search-dropdown-tablet .ng-control {
	border-radius: 4px !important;
}

.sqft-mt {
	margin-left: 0px !important;
	border: 0px !important;
}

.sqft-width {
	max-width: 100% !important;
}

.sqft-box {
	width: 100% !important;
}

.price-range {
	z-index: 1000 !important;
}

.square_border_tab {
	margin: 0px 6px !important;
}

@media (min-width: 768px) and (max-width: 1023px) {
	.property_header h1.prtitle{
		margin-left: 3%;
	}
	.mobile_close {
		display: none;
	}
	.new_profile_group.dis_inline {
		width: 100%;
	}
	.artboard6_sidebar.side_bar_height.upgrade-box.ng-star-inserted {
		margin-left: 0px;
	}
	.check_group.profile_checkbox.width_350 .form_group.flex_none span.checkmark {
		left: -6px;
  }
  .show_more_search_filter {
    width: 300px !important;
  }
}


/* 09-08 css*/

.chat_message.check_event .meaasge_details {
	width: 70%;
}

img.open_chat {
	opacity: 0;
	display: none;
}

@media (min-width: 768px) and (max-width: 1023px) {}

@media only screen and (max-width: 767px) {
	.agent_details_check button.btn.add_new_list.dis_inline {
		float: left;
	}
	.agent_title2.long-text-dot {
		line-height: normal;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_text a.cursor-pointer div {
		width: 108px !important;
	}
	.checked-text {
		/* position: relative !important; */
		position: absolute !important;
		bottom: 0;
		text-align: center;
		left: 0 !important;
		margin-top: 20px;
		display: table;
		margin: 10px auto;
		margin-left: 38%;
	}
	/* chat css */
	.chat_message {
		display: block;
	}
	.chat_message .left_side {
		width: 100%;
		padding-right: 0px;
	}
	.message_list {
		display: flex;
	}
	.right_side_event_agent.right_side_overflow .right_agent_event .right_agent_text .col-sm-9.hidden-xs.agent-user-info-in {
		display: none !important;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow .right_agent_text .agent_details_check {
		margin-top: 120px;
	}
	.right_agent_text+.col-sm-9.hidden-xs.agent-user-info {
		background: #EFF2F4;
		padding: 20px 10px;
	}
	.right_agent_text+.col-sm-9.hidden-xs.agent-user-info .open_agent_name_group {
		margin-top: 16px;
	}
	.right_agent_text+.col-sm-9.hidden-xs.agent-user-info .white_button.dis_inline {
		margin-top: 19px !important;
	}
	.right_agent_text+.col-sm-9.hidden-xs.agent-user-info .open_agent_name_group * {
		color: #a8aaaa;
	}
	.right_agent_text+.col-sm-9.hidden-xs.agent-user-info .white_button.dis_inline {
		border-color: #00B8A7;
		color: #00B8A7;
	}
	img.open_chat {
		bottom: 20px;
		position: fixed;
		z-index: 111;
		right: 20px;
		cursor: pointer;
		opacity: 1;
		display: block;
	}
	body .homepage.header_fix .chat_message.check_event .left_side.height_auto.eventchatwidth {
        display: none;
        height: calc(100vh - 194px) !important;
	}
	.chat_message.check_event .right_side_event_agent.right_side_overflow {
		display: block;
	}
	/* end of chat css */
}

.Event_manager_add {
	display: block !important;
}

.Event_manager_add.small-grid .add_event_image_group {
	display: inline-block;
	width: 70%;
	height: 100%;
}

.Event_manager_add.small-grid .event_agent_group {
	display: inline-block;
	width: 29%;
	vertical-align: top;
}


@media (min-width: 768px) and (max-width: 1023px) {


	.chat_message {
		display: block;
	}

	.chat_message .left_side {
		width: 100%;
		padding-right: 10px;
		padding-bottom: 30px;
	}
	.chat_message .left_side input.meassage_search {
		width: 97.6%;
	}

	.chat_message .left_side + .message_right.message_new_width {
		display: none;
		width: 100%;
	}

	.chat_on_bottom {
		position: relative;
	}

	img.white_leftarrow_image.cursor-pointer_2 {
		display: inline-block;
		height: 20px;
		vertical-align: middle;
		position: absolute;
		top: 30px;
		transform: rotate(90deg);
	}

	body .homepage.header_fix .chat_message.check_event .left_side.height_auto.eventchatwidth {
		display: none;
		height: calc(100vh - 194px) !important;
	}

	.left_side.height_auto.eventchatwidth + .right_side_event_agent.right_side_overflow
	{
		width:100% !important;
	}

	.right_agent_text .col-sm-9.hidden-xs.agent-user-info-in {
		display: none;
	}

	.right_agent_text .col-sm-6.hidden-xs.agent-user-info-in {
		display: none;
	}

	.right_agent_text .col-sm-4.agent-user-info-in {
		width: 50%;
		float: left;
	}

	.right_agent_event .right_agent_text .col-sm-6 {
		float: right;
	}

	.right_agent_event .right_agent_text .col-sm-7 {
		float: right;
	}

		.right_agent_text .agent_details_check {
			position: relative;
			bottom: -100px;
		}

		div#Agent_View {
			width: 96%;
			margin: 0px;
			margin-left: 10px;
			/* margin-top: 13px; */
			margin-bottom: 11px;
			box-shadow: none;
			position: relative;
			/* margin-top: 113px; */
		}

		.right_agent_text+.col-sm-9.hidden-xs.agent-user-info {
			background: #EFF2F4;
			padding: 20px 10px;
			float: left;
			width: 100%;
		}
		.right_agent_text+.col-sm-9.hidden-xs.agent-user-info .open_agent_name_group {
			margin-top: 16px;
		}
		.right_agent_text+.col-sm-9.hidden-xs.agent-user-info .white_button.dis_inline {
			margin-top: 19px !important;
		}
		.right_agent_text+.col-sm-9.hidden-xs.agent-user-info .open_agent_name_group * {
			color: #a8aaaa;
		}
		.right_agent_text+.col-sm-9.hidden-xs.agent-user-info .white_button.dis_inline {
			border-color: #00B8A7;
			color: #00B8A7;
		}

		.col-sm-9.overviewRight {
			width: 100%;
		}

		.check_shown_tables {
			display: block;
		}

		.check_table1.check-table {
			padding-top: 10px;
			padding-bottom: 10px;
			font-size: 16px;
			margin: 0px;
			box-shadow: none;
			width: 100%;
			border-top: 1px solid lightgray;
			height: auto;
		}

		.check_table1.check_table2.checkins_table {
			padding-bottom: 10px;
			font-size: 16px;
			margin: 0px;
			box-shadow: none;
			width: 100%;
			border-top: 1px solid lightgray;
			height: auto;
		}

		body div#collapse1 .checked-text {
			position: absolute;
			left: 46%;
			margin: auto;
			display: table;
			/* margin: 10px auto -26px auto; */
		}

		.check_table1 .title2 {
			font-size: 21px;
			color: #676767;
			line-height: 25px;
			display: inline-block;
		}



}

@media only screen and (max-width: 767px) {


   .filter_page {
	   float: left;
	   width: 100%;
   }

   .left_side + .message_right.message_new_width {
	   width: 100%;
   }

   .message_right.message_new_width .chat_on_bottom {
	   position: relative;
   }

   .message_right.message_new_width .chat_on_bottom div#scrollMe {
	   margin-left: 0px;
   }

   .other_chat span {
	   width: 100%;
   }

   .your_chat.chat-text-break {
	   width: 100%;
	   max-width: 100%;
   }

   body div#scroll.message_list_with_name {
	   height: calc(100vh - 230px) !important;
   }

   .chat_message.check_event .chatView.left_side.height_auto.Recipient_Chosen {
	   display: block;
	   height: calc(100vh - -26px)!important;
	   width: 100% !important;
   }

   .your_chat.chat-text-break {
	   display: block;
	   max-width: 100%;
	   width: 100%;
   }

   .chat_time {
	   display: block;
	   float: left;
	   width: 100%;
   }

   .right_agent_text  .agent_details_check {
	   position: absolute;
	   bottom: 20px;
	   left: -10px;
   }

   .chat_message .left_side + .message_right.message_new_width {
	   display: none;
   }

   .chat_message .left_side {
	   padding-bottom: 30px;
   }

   .chat_on_bottom {
	   height: calc(100vh - 178px)!important;
   }

   img.white_leftarrow_image.cursor-pointer_2 {
	   display: inline-block;
	   height: 20px;
	   vertical-align: middle;
	   position: absolute;
	   top: 30px;
	   transform: rotate(90deg);
   }

}


@media only screen and (min-width: 1024px) {

   img.white_leftarrow_image.cursor-pointer_2
   {
	   display: none;
   }
   .col-sm-9.hidden-xs.agent-user-info {
	   display: none;
   }


body {
   /* margin-bottom: 0px !important; */
}

}




@media only screen and (min-width: 1024px) {

	.ng-select.eventChat .ng-control {
		width: auto !important;
	}


}



@media (min-width: 768px) and (max-width: 1023px) {

	.ng-select.eventChat .ng-control {
		width: auto !important;
	}

	/* .check_table1.check-table {
		height: auto !important;
	} */

	img.open_chat {
		display: block;
		opacity: 1;
		position: fixed;
		right: 20px;
		bottom: 30px;
		z-index: 11;
	}


	.message_right_side.send_message_to_group.full-right {
		width: 100% !important;
	}

	input.type_of_person {
		width: auto;
	}

	.chat_message.check_event .left_side.height_auto.eventchatwidth {
		width: 100% !important;
	}

	.chat_message.check_event .left_side.height_auto.Recipient_Chosen {
		width: 100% !important;
	}

	.share-property .modal-dialog.modal-lg{
        width: 80% !important;
	}

	.sp-full-screen .sp-image-container img {
		width: 100% !important;
	}


}



@media only screen and (max-width: 767px) {

	.chat_message.check_event .left_side.height_auto.Recipient_Chosen {
		width: 100% !important;
	}

	.ng-select.eventChat .ng-control {
		width: auto !important;
	}


	.message_right_side.send_message_to_group.full-right {
		width: 100% !important;
	}

	input.type_of_person {
		width: auto;
	}

}

@media only screen and (min-width: 1024px) {
  li.visible-xs.mobile_logo_menu.back_white_menu{
    background: #fff !important;
    position: relative;
    z-index: 11;
  }

  img.img-responsive.mobile_white_logo{
    height: 29px;
    padding-left: 20px;
    margin-bottom: 10px;
    margin-top: 10px;
    display: inline-block;
    position: relative;
    z-index: 11;
  }
  .back_white_menu button.click_mobile_menu.btn.btn-info{
    margin-top: 5px;
    margin-right: 3px;
    display: block !important;
  }
  button.btn.btn-info.menu_button{
    background: transparent;
    border: 0px;
    box-shadow: none;
    float: right;
    margin: 8px 10px 0px 0px;
    outline: 0 !important;
  }
  li.visible-xs.mobile_logo_menu.back_white_menu i.fa.fa-bars{
    padding: 6px 8px;
    color: #00B7A6;
    font-size: 21px;
  }
  .mobile_close{
    /* display: none; */
  }
  .back_white_menu ul.mobile_icon{
    display: inline-block;
    width: 24%;
    top: 16px;
    position: relative;
    float: right;
  }
  .back_white_menu ul.mobile_icon li{
    width: 20%;
    float: left;
  }
  ul.mobile_icon li{
    display: inline-block;
  }
  .mobile_new_menu{
    background: #00B8A7;
    float: left;
    width: 100%;
    top: -50px;
    position: relative;
    padding-top: 50px;
  }
  nav.mobile_new_menu ul{
    float: left;
    width: 104%;
    padding-left: 0px;
    height: 100vh;
  }
  nav.mobile_new_menu li a{
    cursor: pointer;
    font-size: 16px;
    color: white;
    padding: 9px 20px;
    font-weight: normal !important;
    float: left;
    width: 100%;
  }
  .margin-upgrade-button{
    margin-right: 30%;
  }
}
