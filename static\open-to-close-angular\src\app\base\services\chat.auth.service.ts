import { Injectable} from '@angular/core';
import { CanActivate } from '@angular/router';
import { AuthService } from '@app/auth/services/auth.service';
import { BaseComponent} from '@app/base/components/base.component';
import {Params,Router,ActivatedRoute} from '@angular/router';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router/src/router_state';
import { Location } from '@angular/common';

@Injectable()
export class ChatAuthguard implements CanActivate{
    public currentUrl = "";
    
   constructor(private baseComponent:BaseComponent, private authService: AuthService,
            private route:ActivatedRoute,private router:Router,
            private location: Location){
    }

   canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot){
        this.currentUrl = state.url;
        if(BaseComponent.user != undefined){
            return this.verifyUser();
        }
        else{
            return this.authService.getUserDetails().map((res) => {
                BaseComponent.user = res.result;
                return this.verifyUser();
            });
        }
    }

    verifyUser(): boolean{
        if(BaseComponent.user.user_type == 'LA' && BaseComponent.user.is_paid_account == true){
            return true;
        }
        if(BaseComponent.user.user_type == 'BR' && BaseComponent.user.is_paid_account == true){
            return true;
        }
        if(BaseComponent.user.user_type == 'HB'){
            return true;
        }
        else{
            this.location.back();
            return false;
        }
    }
}
