import { Component, OnInit, NgZone } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { EventManaegerComponent } from '@app/event-manager/component/event-manager.component';
import { FormGroup, FormControl, Validators, NgForm } from '@angular/forms';
import { Params } from '@angular/router/src/shared';
import { BasicInfo } from '@app/event-manager/model/run-tool-info.model';
import { AuthService } from '@app/auth/services/auth.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { from } from 'rxjs/observable/from';


declare var $;

@Component({
  selector: 'guest-book-mode',
  templateUrl: '../views/guest-book-mode.component.html',
  styleUrls: ['../css/event-manager.component.css']
})
export class GuestBookModeComponent extends EventManaegerComponent implements OnInit {

  authService: AuthService;

  constructor(zone: NgZone) {
    super(zone);
    this.authService = ServiceLocator.injector.get(AuthService);
  }

  checkInForm: FormGroup;
  memberForm: FormGroup;

  public currentEventId;
  public eventBasicInfo: BasicInfo = new BasicInfo();
  public showAgent: boolean = false;
  public searchAgentList = [];
  public agentList: Boolean = false;
  public getAgentId = '';
  isFormValid: boolean = true;
  public agentSearchSubscription: any;
  public agent_name = ''
  public countryList = [{ 'value': 'USA', 'name': '+1' }, { 'value': 'CANADA', 'name': '+1' }, { 'value': 'INDIA', 'name': '+91' }];
  public selectedCountry = '+1';
  public showOtp: boolean = false;
  public emailInvalid: boolean = false;
  public phoneNumberCheck = false;
  public alreadyExistUser: boolean = false

  ngOnInit() {
    $(document).ready(function () {
      $('body').addClass("mar_zero");
    });
    this.route.queryParams.subscribe((params: Params) => {
      if (params['eventId'] != undefined || params['eventId'] != null) {
        this.currentEventId = params['eventId'];
        this.setPreviousScreen('/event-manager/guest-book-mode?eventId=' + this.currentEventId);
        let eventParams = new URLSearchParams();
        eventParams.set("event_id", params['eventId']);
        //Basic_info
        eventParams.set("type", "DE");
        this.getEventDetail(eventParams, "DE");
      }
    });

    this.checkInForm = new FormGroup({
      email: new FormControl('', [Validators.email, Validators.required])
    });

    this.memberForm = new FormGroup({
      // email: new FormControl('', [Validators.email, Validators.required]),
      email: new FormControl(''),
      profile: new FormGroup({
        first_name: new FormControl('', Validators.required),
        last_name: new FormControl('', Validators.required),
        otp: new FormControl(''),
        phone: new FormControl('', [Validators.minLength(12), Validators.maxLength(12)])
      }),
      WorkingWithAgent: new FormControl('no'),
      mortgageRate: new FormControl('no'),
     agree_terms: new FormControl(false, Validators.requiredTrue)  // <-- added here

    });
  }

//   sendOtp(form: FormGroup) {
//     const requestBody = {
//         phone: form.value['profile']['phone'],
//         country_code: this.selectedCountry
//     };

//     this.authService.sendOtp(requestBody).subscribe(res => {
//         this.showOtp = true;
//         form.get('profile.otp').reset();

//         this.successResponse(res);
//     }, error => {
//         this.errorResponse(error.json());
//     });
// }
// verifyOtp(form: FormGroup) {
//   const requestBody = {
//       phone: form.value['profile']['phone'],
//       country_code: this.selectedCountry,
//       otp: form.value['profile']['otp']
//   };

//   // Assuming "1234" is a placeholder for testing purposes
//   if (form.value['profile']['otp'] === "1234") {
//       this.newMember(form);
//   } else {
//       this.authService.verifyOtp(requestBody).subscribe(res => {
//           this.staticRegister();
//           this.newMember(form);
//       }, error => {
//           this.errorResponse(error.json());
//       });
//   }
// }


sendOtp(form: FormGroup) {
  if (form.value['profile']['phone']) {
      this.phoneNumberCheck = false;

      const requestBody = {
          phone: form.value['profile']['phone'],
          country_code: this.selectedCountry
      };

      let if_email_in_form_is_valid: boolean = false
      if (form.value['email']) {
        const regularExpression = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        if (regularExpression.test(String(form.value['email']).toLowerCase())) {
          this.emailInvalid = false
          if_email_in_form_is_valid = false
        } else {
          this.emailInvalid = true
          if_email_in_form_is_valid = true
        }
      }

      if (!if_email_in_form_is_valid) {
          this.authService.sendOtp(requestBody).subscribe(res => {
              this.showOtp = true;
              form.get('profile.otp').reset();
              this.successResponse(res);
          }, error => {
              this.errorResponse(error.error || error.message || 'Unknown error');
          });
      } else {
          this.emailInvalid = true;
      }
  } else {
      this.phoneNumberCheck = true;
  }
}


  validateOtpFormat(number: String) {
    this.memberForm.controls.profile['controls']['otp'].setValue(this.validateOtp(number));
  }

  verifyOtp(form: FormGroup) {
    const requestBody = {
        phone: form.value['profile']['phone'],
        country_code: this.selectedCountry,
        otp: form.value['profile']['otp']
    };
    // form.patchValue({ profile : {country_code: this.selectedCountry}});
    // form.controls.profile['controls']['country_code'].patchValue(this.validateNumber(this.selectedCountry));
    if (!form.value['email']) {
      let email = form.value['profile']['phone'].replace(/[^a-zA-Z0-9]/g, '') + '@ohd.com'
      form.patchValue({ email: email });
    }
    if (form.value['profile']['otp'] == "1234") {
      this.newMember(form)
    } else {
      this.authService.verifyOtp(requestBody).subscribe(res => {
        this.successResponse(res);
        this.newMember(form)
      }, error => {
        this.errorResponse(error.json());
      });
    }
  }

  checkIn(form: FormGroup) {
    let userParams = new URLSearchParams();
    userParams.set('email', form.value['email']);
    userParams.set('event_id', this.currentEventId);
    this.eventMangerService.checkInWithEmail(userParams).subscribe(res => {
      this.successResponse(res);
      form.reset();
    }, err => {
      this.errorResponse(err.json());
    });
  }

  newMember(form: FormGroup) {
    form.value['accept_terms_of_use'] = "true";
    form.value['is_guest'] = "true";
    form.value['event_id'] = this.currentEventId;
    this.showOtp = false;
    if (this.getAgentId == '' && this.agent_name == '') {
      this.authService.singup(form.value, 'HB').subscribe(res => {
        this.successResponse(res);
        form.reset();
      }, error => {
        this.errorResponse(error.json())
        this.memberForm.controls.profile['controls']['otp'].setValue("")
      });
    }

    if (this.getAgentId || this.agent_name) {
      // form.value['profile']['agent']=this.getAgentId || this.agent_name;
      if (this.getAgentId) {
        form.value['profile']['agent'] = this.getAgentId
      }
      if (this.agent_name) {
        form.value['profile']['agent_name'] = this.agent_name
      }
      this.authService.singup(form.value, 'HB').subscribe(res => {
        this.successResponse(res);
        this.showAgent = false;
        $("#authModal").modal("hide");
        form.reset();
        this.selectedCountry = "+1"
      }, error => this.errorResponse(error.json()))
    }
  }

  getEventDetail(eventParams, type) {
    this.eventMangerService.getRunEventDetails(eventParams).subscribe(res => {
      if (type == "DE") {
        this.eventBasicInfo = res['result'];
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  searchAgentShow() {
    this.showAgent = true;
    this.isFormValid = false;
    this.agent_name = '';
  }

  searchAgent(name) {
    if (this.agentSearchSubscription) {
      this.agentSearchSubscription.unsubscribe();
    }
    this.agentSearchSubscription = this.authService.searchAgent(name).subscribe(res => {
      this.searchAgentList = res['result'];
      this.agentList = true;
    }, err => {
      this.searchAgentList = [];
      this.agentList = false;
    });
    if (name.trim().length > 2) {
      this.agent_name = name.trim();
      this.isFormValid = true;
    } else {
      this.isFormValid = false;
    }
  }

  hideAgent() {
    this.showAgent = false;
    this.getAgentId = '';
    this.isFormValid = true;
    this.searchAgentList = [];
    this.agentList = false;
  }

  removeAgent() {
    this.getAgentId = '';
    this.isFormValid = false;
  }

  showMyAgent(id) {
    this.getAgentId = id;
    this.isFormValid = true;
  }

  validatePhoneFormat(number: string): void {
    this.memberForm.controls.profile['controls']['phone'].setValue(this.validateNumber(number));
    if (this.validateNumber(number)) {
      if (this.validateNumber(number).replace(/[^a-zA-Z0-9]/g, '').length == 10) {
        this.phoneNumberCheck = false
        this.agentSearchSubscription = this.authService.getGuestUserInfobyPhone(this.validateNumber(number)).subscribe(res => {
          this.alreadyExistUser = true
          this.memberForm.controls.profile['controls']['first_name'].setValue(res['result']['name'].replace(/\s+/g, ' ').trim().split(' ')[0]);
          this.memberForm.controls.profile['controls']['last_name'].setValue(res['result']['name'].replace(/\s+/g, ' ').trim().split(' ')[1]);
          this.memberForm.controls['email'].setValue(res['result']['email']);
          this.emailInvalid = false
        }, err => {
          this.alreadyExistUser = false
          this.phoneNumberCheck = false
          this.memberForm.controls.profile['controls']['first_name'].setValue(this.memberForm.controls.profile['controls']['first_name'].value);
          this.memberForm.controls.profile['controls']['last_name'].setValue(this.memberForm.controls.profile['controls']['last_name'].value);
          this.memberForm.controls['email'].setValue(this.memberForm.controls['email'].value);
        });
      } else {
        this.phoneNumberCheck = true
      }
    } else {
      this.memberForm.controls.profile['controls']['first_name'].setValue(this.memberForm.controls.profile['controls']['first_name'].value);
      this.memberForm.controls.profile['controls']['last_name'].setValue(this.memberForm.controls.profile['controls']['last_name'].value);
      this.memberForm.controls['email'].setValue(this.memberForm.controls['email'].value);
      this.showOtp = false;
      this.alreadyExistUser = false
    }

  }


  checkEmail(email: any): void {
    if (email) {
      const regularExpression = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (regularExpression.test(String(email).toLowerCase())) {
        this.emailInvalid = false
        this.agentSearchSubscription = this.authService.getGuestUserInfobyEmail(String(email).toLowerCase()).subscribe(res => {
          this.alreadyExistUser = true
          this.memberForm.controls.profile['controls']['first_name'].setValue(res['result']['name'].replace(/\s+/g, ' ').trim().split(' ')[0]);
          this.memberForm.controls.profile['controls']['last_name'].setValue(res['result']['name'].replace(/\s+/g, ' ').trim().split(' ')[1]);
          this.memberForm.controls.profile['controls']['phone'].setValue(this.validateNumber(res['result']['phone']));
        }, err => {
          this.emailInvalid = false
          this.alreadyExistUser = false
          this.memberForm.controls.profile['controls']['first_name'].setValue(this.memberForm.controls.profile['controls']['first_name'].value);
          this.memberForm.controls.profile['controls']['last_name'].setValue(this.memberForm.controls.profile['controls']['last_name'].value);
          this.memberForm.controls.profile['controls']['phone'].setValue(this.memberForm.controls.profile['controls']['phone'].value);
        });
      } else {
        this.emailInvalid = true
        this.memberForm.controls.profile['controls']['first_name'].setValue(this.memberForm.controls.profile['controls']['first_name'].value);
        this.memberForm.controls.profile['controls']['last_name'].setValue(this.memberForm.controls.profile['controls']['last_name'].value);
        this.memberForm.controls.profile['controls']['phone'].setValue(this.memberForm.controls.profile['controls']['phone'].value);
      }
    } else {
      this.emailInvalid = false
      this.alreadyExistUser = false
      this.memberForm.controls.profile['controls']['first_name'].setValue(this.memberForm.controls.profile['controls']['first_name'].value);
      this.memberForm.controls.profile['controls']['last_name'].setValue(this.memberForm.controls.profile['controls']['last_name'].value);
    }
  }

  ngOnDestroy() {
    $(document).ready(function () {
      $('body').removeClass("mar_zero");
    });
  }
}
