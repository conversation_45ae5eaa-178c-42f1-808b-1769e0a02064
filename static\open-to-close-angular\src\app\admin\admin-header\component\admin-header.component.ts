import { Component, OnInit, Input } from "@angular/core";
import { BaseComponent } from "@app/base/components/base.component";
import { ActivatedRoute, Params } from "@angular/router";
import { ServiceLocator } from "@app/base/components/service-locator";
import { AuthService } from "@app/auth/services/auth.service";

@Component({
  selector: "admin-header",
  templateUrl: "../views/admin-header.component.html",
  styleUrls: ["../css/admin-header.component.css"]
})
export class AdminHeaderComponent extends BaseComponent implements OnInit {
  // @Input()
  showOption: Boolean = false;

  public headerRoute: ActivatedRoute;
  public authService: AuthService;

  public paramsSubscription;
  public mainurl = "";
  public selectedHeader = "";

  constructor() {
    super();
    this.authService = ServiceLocator.injector.get(AuthService);
    this.headerRoute = ServiceLocator.injector.get(ActivatedRoute);
  }

  ngOnInit() {
    this.paramsSubscription = this.headerRoute.queryParams.subscribe(
      (params: Params) => {
        this.mainurl = this.router.routerState.snapshot.url;
        if (this.mainurl.includes("admin-users")) {
          this.selectedHeader = "admin-users";
        } else if (this.mainurl.includes("admin-plans")) {
          this.selectedHeader = "admin-plans";
        } else if (this.mainurl.includes("listing-management")) {
          this.selectedHeader = "listing-management";
        } else if (this.mainurl.includes("home-buyer")) {
          this.selectedHeader = "home-buyer";
        } else if (this.mainurl.includes("listing-agent")) {
          this.selectedHeader = "listing-agent";
        } else if (this.mainurl.includes("brokerage")) {
          this.selectedHeader = "brokerage";
        }
      }
    );

    if (localStorage.getItem("adminToken")) {
      this.showOption = true;
    }
  }

  logOutAdmin() {
    var logoutVar = {
      fcm_token: BaseComponent.fcm_token
    };
    this.authService.logout(logoutVar).subscribe(
      res => {
        this.showOption = false;
        localStorage.removeItem("adminToken");
        this.router.navigate(["admin"]);
      },
      err => {
        this.errorResponse(err.json());
      }
    );
  }
}
