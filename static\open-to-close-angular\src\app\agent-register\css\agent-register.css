body, html {
  margin: auto;
    padding: 0;
  height: 100%;
}

.marg {
  margin: auto;
  display: flex;
  justify-content: center;
  /* Change this from min-height: 100vh to this: */
  overflow: hidden; /* Prevent any overflow scrolling */
  height: 100vh;

}
img.img-responsive.event_agent_img {
  height: 30px !important;
  margin-left: 20px;
  margin-top: 20px;
}
.Event_manager_add {
  display: flex;
  width: 100%;
  background: white;
  overflow: hidden;
}

.small-grid {
  display: grid;
  grid-template-columns: 0.8fr 1.2fr;
}

/* Left Side - Image Area */
.add_event_image_group {
  display: inline-block;
  width: 50% !important;
  height: 100%;
  position: relative;
}

.guest-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.add_event_bg {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.footer_logo_event {
  position: relative;
  z-index: 2;
  max-width: 150px;
  margin: 20px 0 0 20px;
}

.event_address_title {
  position: relative;
  z-index: 2;
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0 0 20px;
  max-width: 90%;
}

.your_open_agent {
  position: relative;
  z-index: 2;
  margin: auto 0 30px 20px;
}

.title2 {
  font-size: 16px;
  margin-bottom: 10px;
}

.open_agent_img {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 15px;
  border-radius: 10px;
  backdrop-filter: blur(5px);
  max-width: 90%;
}

.search-agent-event {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
  object-fit: cover;
  border: 2px solid white;
}

.symbols-property-image {
  display: inline-block;
}

.open_agent_name_group {
  display: flex;
  flex-direction: column;
}

.open_agent_name_group .name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
}

.open_agent_name_group .fname {
  font-size: 14px;
  opacity: 0.9;
}

/* Right Side - Form Area */
.event_agent_group {
  display: inline-block;
  width: 45% !important;
  vertical-align: top;
  margin-left: 20px;
  padding: 20px !important;
}

.event_agent_img {
  max-width: 180px;
  margin: 0 auto 20px;
  display: block;
}

.not_a_member {
  font-size: 25px;
  color: #676767;
  line-height: 30px;
  margin-top: 0px !important;
  font-weight: 600;
  padding-left: 0px !important;
  margin-bottom: 15px;

}

.signup-container {
  max-width: 100%;
}

.scrollable-form {
  max-height: none;
  padding-right: 5px;
}

.new_account .title {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.menu51_text {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

/* Form Elements */
.form_group {
  margin-bottom: 10px;
  position: relative;
}

.form_row {
  display: flex;
  gap: 20px;
  margin-bottom: 0;
}

.form_row .form_group {
  flex: 1;
}

.new_form {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s;
  box-sizing: border-box;
}

.new_form:focus {
  outline: none;
  /* border-color: #0084ff; */
  box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.1);
}

.form-validation {
  color: #ff4d4d;
  font-size: 12px;
  margin-top: 5px;
}

.form_group label {
  font-size: 10px !important;
  color: #666;
  display: block;
  margin-top: 5px;
}

/* Checkbox Styling */
.check_group {
  margin-bottom: 15px;
}

.check_group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin-right: 10px;
  vertical-align: middle;
}

.check_group label {
  font-size: 10px !important;
  color: #333;
  vertical-align: middle;
}

.checkmark {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
}

.width_auto {
  width: auto;
  display: inline-block;
  vertical-align: middle;
}

/* Search Agent Styling */
.search {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
}

.agent_found {
  font-size: 14px;
  color: #666;
  margin: 10px 0;
}

.search-agent-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 5px;
}

.remove_agent {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.search-agent-padding:last-child {
  border-bottom: none;
}

.search-agent-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 15px;
}

.remove-span {
  display: inline-block;
}

.remove_details {
  flex-grow: 1;
}

.remove_details .name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 4px;
  color: #333;
}

.remove_details .sub_name {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.remove_button {
  display: inline-block;
  padding: 8px 12px;
  background-color: #f1f1f1;
  color: #666;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s;
}

.remove_button.select {
  background-color: #0084ff;
  color: white;
}

.remove_button:hover {
  background-color: #0084ff;
  color: white;
}

/* OTP Verification */
[style*="border: 1px solid red"] {
  border: 1px solid #0084ff !important;
  background-color: rgba(0, 132, 255, 0.05);
  padding: 15px;
  border-radius: 8px;
}

.registration-link {
  color: #0084ff;
  text-decoration: none;
  font-size: 14px;
  display: block;
  margin-top: 10px;
  cursor: pointer;
}

/* Submit Button */
input[type="button"] {
  background-color: #10B8A8;
  color: white;
  font-weight: 500;
  cursor: pointer;
  padding: 12px 20px;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  transition: background-color 0.3s;
  display: block;
}
.ml-0{
  margin-left: 0px !important;
}
/*
input[type="button"]:hover:not(.submit-disable) {
  background-color: #0072e5;
} */

.submit-disable {
  opacity: 0.6;
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

/* Form Footer */
.modal_footer {
  text-align: center;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
  font-size: 12px;
  color: #666;
}

.modal_footer a {
  color: #0084ff;
  text-decoration: none;
}

/* Form Success/Error Icons */
.has-success .form-control-feedback {
  color: #4cd964;
  position: absolute;
  right: 15px;
  top: 35%;
}

.has-error .form-control-feedback {
  color: #ff4d4d;
  position: absolute;
  right: 15px;
  top: 35%;
}

.success-border-box {
  border-color: #4cd964 !important;
}

.error-border-box {
  border-color: #ff4d4d !important;
}

/* Responsive Design */

@media screen and  (max-width: 768px) {
  .add_event_image_group {
    display: none !important; /* Hide the image section */
  }

  .event_agent_group {
    width: 100% !important; /* Ensure the form takes full width */
    margin: 0 auto !important; /* Center the form horizontally */
    padding: 20px !important; /* Add some padding for better appearance */
  }

  .small-grid {
    grid-template-columns: 1fr !important; /* Stack columns vertically */
  }

  .form_row {
    flex-direction: column !important; /* Stack form rows vertically */
    gap: 10px !important; /* Add some spacing between rows */
  }
}
/* Responsive Design */
@media screen and (min-width: 1500px) {
  .new_form {
    font-size: 18px; /* Increase font size */
    padding: 14px; /* Increase padding inside input fields */
  }

  .form_row {
    gap: 20px; /* Increase gap between form fields */
  }

  .form_group {
    margin-bottom: 20px; /* Increase margin between form groups */
  }

  .title, .menu51_text  {
    font-size: 22px; /* Increase font size for titles and text */
  }
  .not_a_member{
    font-size: 30px; /* Increase font size for titles and text */



  }
  .event_agent_group{
    margin-top: 55px;
  }

}

@media (max-width: 992px) {
  .small-grid {
    grid-template-columns: 1fr; /* Stack columns vertically on smaller screens */
  }

  .add_event_image_group,
  .event_agent_group {
    width: 100%; /* Ensure both sections take full width */
  }

  .form_row {
    flex-direction: column; /* Stack form rows vertically */
    gap: 10px; /* Add some spacing between rows */
  }
}




  .checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    max-width: 100%;
    margin-bottom: 8px;
  }

  .custom-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  .custom-checkbox input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
  }

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border: 2px solid #ccc;
    border-radius: 3px;
    cursor: pointer;
  }

  .custom-checkbox input:checked + .checkmark {
    background-color: #10B8A8;
    border-color: #10B8A8;
  }
.checkmark::after {
  content: "";
  position: absolute;
  display: none;
}

.custom-checkbox input:checked + .checkmark::after {
  display: block;
left: 4px;
    top: 0px;
    width: 5px;
    height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
