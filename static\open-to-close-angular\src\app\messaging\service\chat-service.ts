import { Injectable,Output,Input,EventEmitter } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { Observable } from 'rxjs/Observable';
import { ApiService } from '@app/base/services/api.service';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';

@Injectable()
export class ChatService {

    @Output()
    public message: EventEmitter<any> = new EventEmitter<any>();

    public baseservice:BaseComponent;
    public apiService:ApiService;
    public chat;
    public chatThread;
    @Input()
    public onFirebase: EventEmitter<any> = new EventEmitter<any>();
    @Input()
    public onRunToolFirebase: EventEmitter<any> = new EventEmitter<any>();
    @Input()
    public updateThread: EventEmitter<any> = new EventEmitter<any>();
    @Input()
    public startNewChat: EventEmitter<any> = new EventEmitter<any>();
    
    constructor() {
        this.baseservice=ServiceLocator.injector.get(BaseComponent);
        this.apiService=ServiceLocator.injector.get(ApiService);
    }

    public setChat(msg){
        this.chat=msg;
    }

    public getChat(){
        return this.chat;
    }

    public setClientChatThread(chatThread){
        this.chatThread = chatThread;
    }

    public getClientChatThread(){
        return this.chatThread;
    }

    getChatThread(){
        let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['chat']['getAllChatThread'], {});
        return this.apiService.apiCall(options);
    }

    getClientChat(clientParams):Observable<any>{
        let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['chat']['getAllChat'], {},clientParams.toString());
        return this.apiService.apiCall(options);
    }

    sendMessage(msgParam){
        let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['chat']['sendMessage'],msgParam.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    markMessageReaded(chatId){
        let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['chat']['markReaded'],chatId.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }
    
    getRecentChat(timeStamp, senderId,status){
        let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['chat']['getRecent']+"?last_timestamp="+timeStamp+"&sender_id="+senderId+"&is_active_thread="+status, {});
        return this.apiService.apiCall(options);
    }

    getAllMyClient():Observable<any>{
        let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['chat']['myClientList'],{});
        return this.apiService.apiCall(options);
    }

    getAllChat(timeStamp, receiverId){
        let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['chat']['getAllChatWithTimestamp']+"?receiver_id="+receiverId+"&last_timestamp="+timeStamp, {});
        return this.apiService.apiCall(options);
    }
}