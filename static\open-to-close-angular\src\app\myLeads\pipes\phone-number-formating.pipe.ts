import {PipeTransform,Pipe} from '@angular/core';

@Pipe({
    name: 'phone'
})
export class PhonePipe implements PipeTransform {
    transform(val,args:string[]) : any {
        let newStr = '';
        let i=0;
        for(i; i < (Math.floor(val.length/3)); i++){
            if(i == 2){
                newStr = newStr+ val.substr(i*3, 4)
            }
            else{
                newStr = newStr+ val.substr(i*3, 3) + '-';     
            }
        }
        return newStr+ val.substr(i*4);
    }
}
