.send-pink{
    background: #AD5FBF !important;
    color: white !important;
    border: 1px solid #AD5FBF !important;
    border-radius: 100px !important;
    font-size: 12px !important;
    padding: 7px 10px 7px 10px !important;
    margin-left: 0px !important;
    width: 100px !important;
    text-align: center !important;
    cursor: pointer !important
}

.send-gray{
    background: #566D77 !important;
    color: white !important;
    border: 1px solid #566D77 !important;
    border-radius: 100px !important;
    font-size: 12px !important;
    padding: 7px 10px 7px 10px !important;
    margin-left: 0px !important;
    width: 100px !important;
    text-align: center !important;
    cursor: pointer !important
}

.send-mountain{
    background: #10B8A9 !important;
    color: white !important;
    border: 1px solid #10B8A9 !important;
    border-radius: 100px !important;
    font-size: 12px !important;
    padding: 7px 10px 7px 10px !important;
    margin-left: 0px !important;
    width: 100px !important;
    text-align: center !important;
    cursor: pointer !important
}
.border-gray{
    color: #566D77 !important;
    border: 1px solid #566D77 !important;
    cursor: pointer !important
}
.border-pink{
    color: #BD3430 !important;
    border: 1px solid #BD3430 !important;
    cursor: pointer !important
}
.ap-c-a{
    margin-left: 15px;
}
.ap-c-a:hover{
    background: #BD3430 !important;
    color: #FFFFFF !important;
}
.ap-c-a-selected{
    background: #AD5FBF !important;
    color: #FFFFFF !important;
}

.border-mountain{
    color: #10B8A9 !important;
    border: 1px solid #10B8A9 !important;
    cursor: pointer !important
}
.rate-label{
    padding-right: 3px !important;
}
.selected-time{
    margin-left: 14px !important;
}
.selected-label{
    text-align: center;
    margin-top: -28px;
    color: #8D8D8D;
    font-size: 16px;
    padding-left: 58px;
}
.check-icon{
    font-size: 21px;
    color: #AD5FBF;
    padding-right: 8px;    
}
.event_save.dis_inline.border-gray.bg_gray:hover{
    background: #566D77;
    color: white !important;
}
.event_save.dis_inline.border-mountain.bg_mountain:hover{
    background: #10B8A9;
    color: white !important;
}
.event_footer .event_save.dis_inline {
    border: 1px solid #FFFFFF;
    border-radius: 100px;
    color: white;
    width: 109px !important;
    padding: 8px 0px 7px 0px !important;
    vertical-align: top !important;
    margin-top: 12px;
    margin-right: 14px !important;
    margin-left: 9px;
}
.modal-icon{
    color: white;
    position: absolute;
    left: 20px;
    opacity: 1;
    font-size: 22px;
    top: 3px;
    outline: 0 !important;
    float: left;
    z-index: 1;
}
.modal-icon .modal-title{
    font-weight: 550;
    padding-left: 10px;
    font-size: 14px;
}
.bg_mountain-selected{
    background: #10B8A9 !important;
    color: white !important;
}
.bg_gray-selected{
    background: #566D77 !important;
    color: white !important;
}
.img-agent{
    border-radius: 36px !important;
    width: 68px !important;
    height: 65px !important; 
}

.agent-non{
    font-size: 16px !important;
    color: #5A5A5A !important;
    margin-left: 20px !important;
    padding-top: 20px !important;
    margin-bottom: 20px !important;
}
.thumns-background{
    background: #10B8A9 !important;
}
.modal-scroll{
    overflow: scroll !important;
}
.event_address-hide{
    position: absolute;
    font-size: 16px;
    color: #FFFFFF;
    top: 39% !important;
    left: 3%;
    width: 95%;
}
.in-go-btn{
    padding-bottom: 20px !important; 
}
.event-addres-line{
    display: inline-block !important;
    width: 81% !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important
}
.event-model-content{
    border-radius: 0px !important;
    border: 0px solid rgba(0,0,0,0.2) !important;
}
.rating-check-img{
    margin-top: -7px;
    margin-left: 3px;
}
.appointment-time-padding{
    padding-bottom: 20px;
}
.add-to-list{
    text-align: left;
    color: #8D8D8D;
    font-size: 16px;
    padding-left: 20px;
}
.add-to-list-s{
    background-color: #F0F2F4;
    border-radius: 15px;
    padding: 7px 12px 7px 7px;
}
.check-icon-oh{
    font-size: 21px;
    color: #10B8A9;
    padding-right: 8px;
}
.check-icon-br{
    font-size: 21px;
    color: #566D77;
    padding-right: 8px;
}
.event_details_modal .heading-agent{
    font-size: 16px;
    color: #5A5A5A;
    padding-top: 20px;
    margin-bottom: 20px;
}
.event-msg-box{
    padding-left: 10px;
    padding-top: 15px;
    border-color: #d2d3d7;
}
.remove_button_event{
    border: 1px solid #F06292;
     border-radius: 100px;
     color: #F06292;
     padding: 4px 24px;
     font-size: 16px;
     margin-top: 10px;
     cursor: pointer;
}
.check_group .form_group input[type="checkbox"]{
    margin-right: 0px !important;
}