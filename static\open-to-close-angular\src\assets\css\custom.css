@import url('slider-pro.css');
@import url('circle.css');
body
{
    background: #F0F2F4;
}
.btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn.active.focus {
    outline: 0;
}
.SumoSelect p {
    margin: 0;
}
 .SelectBox {
    padding: 10px 7px;
}
 .sumoStopScroll{
    overflow:hidden;
}
/* Filtering style */
 .SumoSelect .hidden {
     display:none;
}
 .SumoSelect .search-txt{
    display:none;
    outline:none;
}
 .SumoSelect .no-match{
    display:none;
    padding: 6px;
}
 .SumoSelect.open .search-txt{
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 5px 8px;
    border: none;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 5px;
}
 .SumoSelect.open>.search>span, .SumoSelect.open>.search>label{
    visibility:hidden;
}
/*this is applied on that hidden select. DO NOT USE display:none;
 or visiblity:hidden;
 and Do not override any of these properties. */
 .SelectClass,.SumoUnder {
     position: absolute;
     top: 0;
     left: 0;
     right: 0;
     height: 100%;
     width: 100%;
     border: none;
     -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
     box-sizing: border-box;
     -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
     filter: alpha(opacity=0);
     -moz-opacity: 0;
     -khtml-opacity: 0;
     opacity: 0;
}
 .SelectClass{
    z-index: 1;
}
 .SumoSelect > .optWrapper > .options li.opt label, .SumoSelect > .CaptionCont,.SumoSelect .select-all > label {
     user-select: none;
     -o-user-select: none;
     -moz-user-select: none;
     -khtml-user-select: none;
     -webkit-user-select: none;
}
 .SumoSelect {
     position: relative;
    outline:none;
}
 .SumoSelect > .CaptionCont {
     position: relative;
     border: 1px solid #dbdbdb;
     min-height: 45px;
     background-color: #fff;
    border-radius:2px;
    margin:0;
}
 .SumoSelect > .CaptionCont > span {
     display: block;
     padding-right: 30px;
     text-overflow: ellipsis;
     white-space: nowrap;
     overflow: hidden;
    cursor:default;
}
/*placeholder style*/
 .SumoSelect > .CaptionCont > span.placeholder {
    color: #8D8D8D;
        font-size: 16px;
}
 .SumoSelect > .CaptionCont > label {
     position: absolute;
     top: 0;
     right: 0;
     bottom: 0;
     width: 30px;
}
 .SumoSelect > .CaptionCont > label > i {
     background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3wMdBhAJ/fwnjwAAAGFJREFUKM9jYBh+gBFKuzEwMKQwMDB8xaOWlYGB4T4DA0MrsuapDAwM//HgNwwMDDbYTJuGQ8MHBgYGJ1xOYGNgYJiBpuEpAwODHSF/siDZ+ISBgcGClEDqZ2Bg8B6CkQsAPRga0cpRtDEAAAAASUVORK5CYII=');
     background-position: center center;
     width: 16px;
     height: 16px;
     display: block;
     position: absolute;
     top: 0;
     left: 0;
     right: 0;
     bottom: 0;
     margin: auto;
    background-repeat: no-repeat;
    opacity: 0.8;
}
 .SumoSelect > .optWrapper {
    display:none;
     z-index: 1000;
     top: 30px;
     width: 100%;
     position: absolute;
     left: 0;
     -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
     box-sizing: border-box;
     background: #fff;
     border: 1px solid #ddd;
     box-shadow: 2px 3px 3px rgba(0, 0, 0, 0.11);
     border-radius: 3px;
    overflow: hidden;
}
 .SumoSelect.open > .optWrapper {
    top:35px;
     display:block;
}
 .SumoSelect.open > .optWrapper.up {
    top: auto;
    bottom: 100%;
    margin-bottom: 5px;
}
 .SumoSelect > .optWrapper ul {
    list-style: none;
     display: block;
     padding: 0;
     margin: 0;
     overflow: auto;
}
 .SumoSelect > .optWrapper > .options {
     border-radius: 2px;
    position:relative;
    /*Set the height of pop up here (only for desktop mode)*/
     max-height: 250px;
    /*height*/
}
 .SumoSelect > .optWrapper.okCancelInMulti > .options {
     border-radius: 2px 2px 0 0;
}
 .SumoSelect > .optWrapper.selall > .options {
     border-radius: 0 0 2px 2px;
}
 .SumoSelect > .optWrapper.selall.okCancelInMulti > .options {
     border-radius: 0;
}
 .SumoSelect > .optWrapper > .options li.group.disabled > label{
    opacity:0.5;
}
 .SumoSelect > .optWrapper > .options li ul li.opt{
    padding-left: 22px;
}
 .SumoSelect > .optWrapper.multiple > .options li ul li.opt{
    padding-left: 50px;
}
 .SumoSelect > .optWrapper.isFloating > .options {
    max-height: 100%;
    box-shadow: 0 0 100px #595959;
}
 .SumoSelect > .optWrapper > .options li.opt {
     padding: 6px 6px;
     position: relative;
/*     border-bottom: 1px solid #f5f5f5; */
}
 /* .SumoSelect > .optWrapper > .options > li.opt:first-child {
     border-radius: 2px 2px 0 0;
 } */
 .SumoSelect > .optWrapper.selall > .options > li.opt:first-child {
     border-radius:0;
}
 .SumoSelect > .optWrapper > .options > li.opt:last-child {
    border-radius: 0 0 2px 2px;
     border-bottom: none;
}
 .SumoSelect > .optWrapper.okCancelInMulti > .options > li.opt:last-child {
    border-radius: 0;
}
 .SumoSelect > .optWrapper > .options li.opt:hover {
     background-color: #E4E4E4;
}
 .SumoSelect > .optWrapper > .options li.opt.sel, .SumoSelect .select-all.sel{
    background-color: #00B8A7;
}
 .SumoSelect > .optWrapper > .options li label {
     text-overflow: ellipsis;
     white-space: nowrap;
     overflow: hidden;
     display: block;
    cursor: pointer;
    padding-top: 7px;
}
 .SumoSelect > .optWrapper > .options li span {
     display: none;
}
 .SumoSelect > .optWrapper > .options li.group > label {
    cursor: default;
    padding: 8px 6px;
    font-weight: bold;
}
/*Floating styles*/
 .SumoSelect > .optWrapper.isFloating {
     position: fixed;
     top: 0;
     left: 0;
     right: 0;
     width: 90%;
     bottom: 0;
     margin: auto;
     max-height: 90%;
}
/*disabled state*/
 .SumoSelect > .optWrapper > .options li.opt.disabled {
     background-color: inherit;
    pointer-events: none;
}
 .SumoSelect > .optWrapper > .options li.opt.disabled * {
     -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    /* IE 5-7 */
     filter: alpha(opacity=50);
    /* Netscape */
     -moz-opacity: 0.5;
    /* Safari 1.x */
     -khtml-opacity: 0.5;
    /* Good browsers */
     opacity: 0.5;
}
/*styling for multiple select*/
 .SumoSelect > .optWrapper.multiple > .options li.opt {
     padding-left: 35px;
    cursor: pointer;
}
 .SumoSelect > .optWrapper.multiple > .options li.opt span, .SumoSelect .select-all > span{
    position:absolute;
    display:block;
    width:30px;
    top:0;
    bottom:0;
    margin-left:-35px;
}
 .SumoSelect > .optWrapper.multiple > .options li.opt span i, .SumoSelect .select-all > span i{
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 14px;
    height: 14px;
    border: 1px solid #AEAEAE;
    border-radius: 2px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15);
    background-color: #fff;
}
 .SumoSelect > .optWrapper > .MultiControls {
     display: none;
     border-top: 1px solid #ddd;
     background-color: #fff;
     box-shadow: 0 0 2px rgba(0, 0, 0, 0.13);
     border-radius: 0 0 3px 3px;
}
 .SumoSelect > .optWrapper.multiple.isFloating > .MultiControls {
     display: block;
     margin-top: 5px;
     position: absolute;
     bottom: 0;
     width: 100%;
}
 .SumoSelect > .optWrapper.multiple.okCancelInMulti > .MultiControls {
     display: block;
}
 .SumoSelect > .optWrapper.multiple.okCancelInMulti > .MultiControls > p {
     padding: 6px;
}
 .SumoSelect > .optWrapper.multiple.okCancelInMulti > .MultiControls > p:focus {
    box-shadow: 0 0 2px #a1c0e4;
    border-color: #a1c0e4;
    outline: none;
    background-color: #a1c0e4;
}
 .SumoSelect > .optWrapper.multiple > .MultiControls > p {
     display: inline-block;
     cursor: pointer;
     padding: 12px;
     width: 50%;
     box-sizing: border-box;
     text-align: center;
}
 .SumoSelect > .optWrapper.multiple > .MultiControls > p:hover {
     background-color: #f1f1f1;
}
 .SumoSelect > .optWrapper.multiple > .MultiControls > p.btnOk {
     border-right: 1px solid #DBDBDB;
     border-radius: 0 0 0 3px;
}
 .SumoSelect > .optWrapper.multiple > .MultiControls > p.btnCancel {
     border-radius: 0 0 3px 0;
}
/*styling for select on popup mode*/
 .SumoSelect > .optWrapper.isFloating > .options li.opt {
     padding: 12px 6px;
}
/*styling for only multiple select on popup mode*/
 .SumoSelect > .optWrapper.multiple.isFloating > .options li.opt {
     padding-left: 35px;
}
 .SumoSelect > .optWrapper.multiple.isFloating {
     padding-bottom: 43px;
}
 .SumoSelect > .optWrapper.multiple > .options li.opt.selected span i, .SumoSelect .select-all.selected > span i, .SumoSelect .select-all.partial > span i{
    background-color: #10B8A8;
    box-shadow: none;
    border-color: transparent;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAABx0RVh0U29mdHdhcmUAQWRvYmUgRmlyZXdvcmtzIENTNXG14zYAAABMSURBVAiZfc0xDkAAFIPhd2Kr1WRjcAExuIgzGUTIZ/AkImjSofnbNBAfHvzAHjOKNzhiQ42IDFXCDivaaxAJd0xYshT3QqBxqnxeHvhunpu23xnmAAAAAElFTkSuQmCC');
    background-repeat: no-repeat;
    background-position: center center;
}
/*disabled state*/
 .SumoSelect.disabled {
     opacity: 0.7;
    cursor: not-allowed;
}
 .SumoSelect.disabled > .CaptionCont{
    border-color:#ccc;
    box-shadow:none;
}
/**Select all button**/
 .SumoSelect .select-all{
    border-radius: 3px 3px 0 0;
    position: relative;
    border-bottom: 1px solid #ddd;
    background-color: #fff;
    padding: 8px 0 3px 35px;
    height: 40px;
    cursor: pointer;
}
 .SumoSelect .select-all > label, .SumoSelect .select-all > span i{
    cursor: pointer;
}
 .SumoSelect .select-all.partial > span i{
    background-color:#ccc;
}
/*styling for optgroups*/
 .SumoSelect > .optWrapper > .options li.optGroup {
     padding-left: 5px;
     text-decoration: underline;
}
.SumoSelect > .optWrapper > .options li.opt:hover label {
    color: white;
}
.SumoSelect > .optWrapper > .options li.opt:hover {
    background-color: #00B8A7;
}







/* // */
/* ///  DECORATION CSS ///  */
.cont_select_center {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -30px;
  margin-left: -150px;
}

.cont_heg_50 {
  position: absolute;
  height: 50%;
  left: 0;
  top: 0;
  width: 100%;
  background-color: #fd7b52;
}

/* ///  END DECORATION CSS  ///  */
.icon_select_mate {
  position: absolute;
  top: 12px;
  right: 2%;
  font-size: 16px;
  height: 22px;
  transition: all 275ms;
}

.select_mate {
  position: relative;

    font-family: 'Source Sans Pro', sans-serif;
    color: #8D8D8D;

    background-color: #fff;

    border-radius: 3px;
    transition: all 375ms ease-in-out;
    border: 1px solid #dbdbdb;
  /* Oculto el elemento select */
}
.select_mate select {
  position: absolute;
  overflow: hidden;
  height: 0px;
  opacity: 0;
  z-index: -2;
}

.cont_list_select_mate {
  position: relative;
  float: left;
  width: 100%;
}

.cont_select_int {
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 999;
  overflow: hidden;
  height: 0px;
  width: 100%;
  background-color: #fff;
  padding: 0px;
  margin-bottom: 0px;
  margin-top: 0px;
  border-radius: 0px 0px 3px 3px;
  box-shadow: 1px 4px 10px -2px rgba(0, 0, 0, 0.2);
  transition: all 375ms ease-in-out;
}
.cont_select_int li {
  position: relative;
  float: left;
  width: 100%;
   background-color: #FFF;
  list-style-type: none;
  padding: 10px;
  margin: 0px;
  transition: all 275ms ease-in-out;
  display: block;
  cursor: pointer;
}
.cont_select_int li:last-child {
  border-radius: 3px;
  border-bottom: 0px;
}
.cont_select_int li:hover {
    background-color: #00B8A7;
    color: white;
}
.cont_select_int .active {
      background-color: #00B8A7;
    color: white;
}

/* etiqueta <p> con la opcion selecionada  */
.selecionado_opcion {
      padding: 10px 2%;

    margin: 0px;
    cursor: pointer;
    font-size: 16px;
}
.new_form
{
    margin-top: 0px !important;
}
.drp_soan
{
    position: absolute;
    top: -12px;
    right: 10px;
}
.drop_down_icon
{
    padding: 20px;
}
.drp_span
{
    position: absolute;
    top: 0px;
    right: 10px;
}


/* Property Details */
.green_header
{
    background: #10B8A8;
    min-height: 51px;
}
.property_bar
{
    padding: 17px 0px 17px 23px;
}
.property_bar span
{
   font-size: 16px;
    color: #FFFFFF;
}
.property_bar span.address
{
        margin-left: 18px;
    display: inline-block;
}
.right_property_bar {
    list-style: none;
    color: white;
    padding-left: 0px;
    margin-bottom: 0px;
}
.right_property_bar i
{
    font-size: 20px;
}
.edit_listing:hover {
    color: white;
}

.edit_listing:focus {
    outline: none !important;
    color: white;
}

.right_property_bar li {
    display: initial;
    margin-right: 18px;
}

.edit_listing
{
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    border-radius: 100px;
    background: transparent;
    border: 1px solid white;
    margin-top: -5px;

}
.property_header
{
    margin-top: 51px;
}
.property_header .ptitle
{
    font-size: 50px;
color: #737373;
    margin-top: 0;
}
.property_header small
{
    font-size: 16px;
    color: #8D8D8D;
}

.property_header .status {
    font-size: 12px;
    color: #7A7A7A;
    letter-spacing: 0;
}
.property_header .price{
   font-size: 25px;
    color: #676767;
    line-height: 30px;
    margin-top: 19px;
    margin-bottom:0px;
}
.property_header .aminity{
    font-size: 16px;
    color: #8D8D8D;
    margin-bottom:0px;
}
.property_header .des{
    font-size: 16px;
    color: #8D8D8D;
    margin-bottom:0px;
}
.property_body .tab-content
{
    padding: 0px;
    background: transparent;
}
/* div#Agent_View {
    padding-left: 34px;
} */
div#Agent_View .bg-white{
    padding-left: 34px;
}
#Agent_View .overview-title
{
    font-size: 40px;
    color: #737373;
    letter-spacing: -1px;
     float: left;
}
#Agent_View .exportpdf
{
    font-size: 12px;
    color: #10B8A8;
    background: transparent;
    box-shadow: none;
    border: 1px solid;
    border-radius: 100px;
     margin-top: 33px;
    margin-left: 18px;
}
.overviewBoth
{
    padding-top: 42px;
    padding-bottom: 42px;

}
.overviewLeft p
{
    font-size: 13px;
    color: #8D8D8D;
    margin-bottom: 0px;
    font-weight: bold;
}
.overviewLeft h2
{
    margin: 0px;
    font-size: 30px;
    color: #737373;
    letter-spacing: -1px;
    font-weight: bold;
}
.overviewLeft h2 span
{
    font-size: 20px;
    color: #676767;
    line-height: 30px;
}
.progress
{
    margin-bottom: 13px;
}
.progressbar
{
    margin-top: 20px;
}
.checkins{
    /* background: #566D77; */
    background: #F0F2F4;
    border-radius: 130px;
}
.positives{
    background: #F0F2F4;
border-radius: 130px;

}
.negatives{
    background: #F0F2F4;
border-radius: 130px;
}
.progress-bar-checkins{
    /* background: #006bc7; */
    background: #566D77;
}
.progress-bar-positives{
    background: #10B8A8;
}
.progress-bar-negatives{
    background: #FF8F00;
}
.progressbar_text
{
    position: relative;

}
.progressbar p:after {
    content: '';
    position: absolute;
    width: 73%;
    height: 1px;
    right: -10px;
    display: block;
    clear: both;
    background-color:  #C2C2C2;
    top: 10px;
}
.progressbar_text p
{
    font-size: 16px;
    color: #5A5A5A;
    margin-bottom: 10px;
}
.progressbar span
{
    font-size: 16px;
    color: #8D8D8D;
}
.events .overview-title
{
    padding-bottom: 17px;
}
.myclient_navbar li.active {
    background: transparent !important;
}
#Agent_View img.symbols-glyph-checkin-thumbsup.dis_inline {
    height: 21px;
}
#Property_View .bg-white
{
    float: left;
    width:  100%;
}
#Property_View .property_des
{
    font-size: 16px;
color: #8D8D8D;
}

.property_des .description
{
    padding: 22px 0px 32px 20px;
}
.features h2
{
    font-size: 25px;
    color: #676767;
    line-height: 30px;
}
.fhead
{
    font-size: 13px;
    font-weight: bold;
    color: #8D8D8D;
    padding-bottom: 7px;
    padding-left: 4px;
    padding-top: 7px;
}
.fdetail
{
    font-size: 16px;
    color: #8D8D8D;
    padding-bottom: 4px;
    padding-right: 4px;
    padding-top: 6px;
    padding-left: 0;
}
.prtitle
{
    font-size: 40px;
color: #737373;
letter-spacing: -1px;
}

.add_listing .sub_title {
    font-size: 16px;
    color: #5A5A5A;
}
.width_50{
    width: 50%;
    float: left;
}
input.reset_button
{
    font-size: 12px;
    color: #8D8D8D;
    background: transparent;
    border: 0px;
    margin-right: 23px;
}
.flex_container
{
   display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.flex_container.check_group.profile_checkbox .form_group *
{
    display: inline;
}
.dis_none,.display
{
    display: none;
}
.mb_10{
    margin-bottom: 10px;
}
.mtb{
    margin-top: 20px;
    margin-bottom: 10px;
}
.pdlr_5
{
    padding: 0px 5px;
}
.clrboth
{
    clear: both;
}
h2.bg_title {
    background: #F0F2F4;
    font-size: 16px;
    color: #5A5A5A;
    padding: 8px;
}
.property_des .sub_title
{
        font-size: 16px;
    color: #5A5A5A;
}
.property_detail_list
{
    list-style: none;
    padding: 0px;
}
.property_detail_list li:before {
    content: "• ";
    color: #10B8A8; /* or whatever color you prefer */
    vertical-align: middle;

    padding-top: 0;
    margin-bottom: .5em;

}
.property_detail_list li
{
    font-size: 16px;
    color: #8D8D8D;
        padding-left: 1em;
    text-indent: -0.7em;
}
.provided_by h3{
    font-size: 25px;
color: #676767;
line-height: 30px;
margin-top: 56px;
}
.provided_by p{
    font-size: 16px;
color: #8D8D8D;
}
.provided_by{
  margin-right: 20%;
}
.provided_by span{
font-size: 16px;
color: #00B8A7;
line-height: 18px;
}
.width_85{
    width: 85%;
}
.width_15{
    width: 15%;
}
.width_100{
    width: 100%;
}
.contact_listing
{
    font-size: 25px;
    color: #676767;
    /* text-align: center; */
    line-height: 30px;
    padding-top: 32px;
    padding-bottom: 10px;
}
.property_body .border
{
    border: 1px solid #D8D8D8;
    border-radius: 4px;
    margin: 30px 20px 42px 0px;
    padding: 30px 10px 15px;
}
.property_body .border img{
    width: 100px;
    height: 100px;

}
.participating_leader .title3
{
    font-size: 16px;
    color: #5A5A5A;
}
.participating_leader p
{
    font-size: 12px;
    color: #8D8D8D;
}
.participating_leader .title3
{
    font-size: 16px;
    color: #5A5A5A;
}
.participating_leader img
{
    vertical-align: top;
}
.participating_leader .leadercontact
{
    margin: auto;
}
.properties_gallery
{
    margin-top: 64px;
    margin-bottom: 46px;
}

/*Property Gallery*/
.slider-pro {
    font-family: 'Source Sans Pro', sans-serif;
}
/* Example 3 */
#example3 .sp-thumbnail-container {
    opacity: 0.5;
}
#example3 .sp-selected-thumbnail {
    border: 2px solid #FFF;
    opacity: 1;
}
.box {
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0 rgba(0,0,0,0.30);
    border-radius: 5px;
}
.color_1
{
    background: #566D77;
}
.box-header {
    font-size: 12px;
    padding: 9px;
    text-align: center;
}
.box-body {
    text-align: center;
    padding: 10px 0px;
}
.box span.day {
    width: 65px;
    margin: auto;
    font-weight: bold;
}
.box span.date.title {
    font-size: 30px;
    color: #737373;
    letter-spacing: -1px;
    width: 65px;
    margin: auto;
    margin-bottom: 8px;
    height: 45px;
    font-weight: bold;
    border: 1px #ccc solid;
    border-top: 0px;
}
.box span.time {
    font-size: 12px;
    color: #8D8D8D;
    font-weight: bold;
}
.bgcolor1{
    color: #566D77;
    background: #F0F2F4;
}
.bgcolor2{
    background: #F8EBEA;
    color: #BD3430;
}
.bgcolor3{
    background: #E4F8F8;
    color: #10B8A8;
}
.date_boxes.row {
    margin-top: 25px;
    margin-bottom: 25px;
}
.c100.floorplan:after
{
    background: white url('../images/icon1.png') center no-repeat;
}
.c100.sizeofbedrooms:after
{
    background: white url('../images/icon2.png') center no-repeat;
}
.c100.sizeofbathrooms:after
{
    background: white url('../images/icon3.png') center no-repeat;
}
.c100.kitchen:after
{
    background: white url('../images/icon4.png') center no-repeat;
}
.c100.finishes:after
{
    background: white url('../images/icon5.png') center no-repeat;
}
.c100.landscaping:after
{
    background: white url('../images/icon6.png') center no-repeat;
}
.c100.neighborhood:after
{
    background: white url('../images/icon7.png') center no-repeat;
}
.c100.price:after
{
    background: white url('../images/icon8.png') center no-repeat;
}
.pmain
{
    font-size: 16px;
    color: #8D8D8D;
    /* text-align: center; */
    text-align: left;
}
.overviewRight .pmain
{
    min-height: 40px;
}
.mb-24
{
    margin-bottom: 24px;
}
.relative
{
    position: relative;
}
.cirtext
{
     position: absolute;
    top: 45%;
    left: 100px;
}
.posactive , .negactive{
   font-size: 16px;
    color: #8D8D8D;
}
.posactive span {
    background: #10B8A8;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.50);
    float: left;
    height: 10px;
    width: 10px;
    margin-right: 6px;
    border-radius: 10px;
    margin-top: 5px;

}
.negactive span {
    background: #FF8A2B;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.50);
    float: left;
    height: 10px;
    width: 10px;
    margin-right: 6px;
    border-radius: 10px;
    margin-top: 5px;

}
.property_page .selecionado_opcion
{
    color: #8D8D8D;
}


.new_button_css
{
    letter-spacing: 1px;
    padding-top: 6px !important;

}

.form_group.mt-10-form {
    margin-top: 13px;
}

@media only screen and (max-width: 767px) {

.property_bar {
    padding: 10px 0px 10px 23px;
}
span.backbtn
{
       margin-top: 6px;
    float: left;
}
.property_header .ptitle {
    font-size: 25px;
    margin-bottom: 0px;
}

.property_header .price {
    font-size: 16px;
}

.property_header small {
    font-size: 12px;
}

.property_header .aminity {
    font-size: 12px;
}

.property_header .des {
    font-size: 12px;
    margin-bottom: 25px;
}
div#example3 {
    max-width: 100% !important;
}
.new_profile_group.dis_inline.col-sm-16 {
    min-width: 100%;
    width: 100%;
}
#Property_View .bg-white {
    padding-left: 10px;
}
.row.properties_gallery {
    margin-top: 0px;
    margin-bottom: 10px;
}

.row.properties_gallery .col-sm-12.col-sm-offset-2
{
        padding: 0px 25px 0px 10px;
}

.date_boxes .col-sm-4 {
    display: inline-block;
}


.property_header {
    margin-top: 16px;
}

.pdetails.col-sm-16
{
    float: left;
}


h2.bg_title {
      background-color: transparent;
      background-position: 96%;
      border-top: 1px solid #C2C2C2;
      /* border-bottom: 1px solid #C2C2C2; */
      margin-right: 13px;
      margin-bottom: 0px;
      padding-right: 33px;
      padding-top: 10px;
      padding-bottom: 10px;
      margin-top: 0px;
}

.ls_group .title {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
}
.ls_group {
    margin-bottom: 10px;
}

.myclient_page.My_Listings {
    padding-top: 15px;
}

.myclient_nav_mobile {
    background: #516C76;
}

.myclient_nav_mobile .select_mate {
    background: #516C76;
    color: white;
}

.myclient_nav_mobile .select_mate ul.cont_select_int li {
    font-size: 20px;
    margin: 0px;
    padding: 3px 3px 0px 3px;
}

.myclient_nav_mobile ul.cont_select_int li.active{
        background-color: #00B8A7 !important;
    color: white !important;
}

.myclient_nav_mobile ul.cont_select_int {
    padding: 0px;
}

#Agent_View .overview-title {
    font-size: 25px;
}

#Agent_View .exportpdf {
    margin-top: 20px;
    float: right;
    margin-right: 12px;
}
div#Agent_View .bg-white {
    padding-left: 11px;
}

div#Agent_View .bg-white {
    padding-left: 11px;
}

.col-sm-4.events {
    display: inline-block;
    padding-right: 0;
}

.col-sm-4.guests {
    display: inline-block;
    padding-right: 0px;
}

.col-sm-4.represented {
    display: inline-block;
    padding-right: 0px;
}

.col-sm-4.unrepresented {
    display: inline-block;
    padding-right: 0px;
}

.progressbar p:after {
     content: '';
    position: absolute;
    width: 55%;
    height: 1px;
    right: 4px;
    display: block;
    clear: both;
    background-color: #C2C2C2;
    top: 10px;
}

.row.progressbar {
    margin: 0px;
    margin-top: 20px;
}

.col-xs-8.col-sm-4.relative {
    margin-top: 40px;
}

.overviewRight .pmain {
    min-height: 22px;
}

.my_client_table_group {
    margin-bottom: 10px;
}

.rate_modal .modal-dialog.modal-lg {
    width: 100%;
    margin: auto;
}

.chat_message.check_event .left_side.height_auto {
    display: none;
}

.right_side_event_agent {
    width: 100%;
}

.col-sm-11.visible-xs.mobile_color .name, .col-sm-11.visible-xs.mobile_color .fname {
    color: #8D8D8D;
}
.mobile_color .white_button.dis_inline {
    border: 1px solid #10B8A8;
    border-radius: 100px;
    font-size: 12px;
    color: #10B8A8;
}

.check_table1 {
       width: 96%;
    margin: 0px;
    margin-left: 10px;
    margin-top: 13px;
    margin-bottom: 11px;
}

.check_table1 .title2 {
    display: block;
}

.right_agent_text {
    width: 94%;
    position: absolute;
    top: 30px;
    left: 27px;
    height: 300px;
}

.filter_page .search_location .form_group.col-sm-3 {
    width: 100%;
}

.filter_page .search_location {
    padding-top: 10px;
}


.mobile_page_title .cancle2 {
    font-size: 16px;
    color: #8D8D8D;
    text-align: left;
    align-items: center;
    display: flex;
}

.mobile_page_title .page_title {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
}

.mobile_page_title .search {
    font-size: 16px;
    color: #10B8A8;
    text-align: right;
    vertical-align: middle;
    align-items: center;
    display: flex;
    justify-content: flex-end;
}

.mobile_page_title {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    text-align: center;
    margin: 0px 0px 5px 0px;
    background: #FFFFFF;
    box-shadow: 0 1px 5px 0 rgba(153,153,153,0.21);
    padding: 10px;
}

.mo_fi_name {
    font-size: 16px;
    color: #8D8D8D;

    margin-top: 20px;
}

.mo_fi_price .form_group {
    margin-bottom: 1px;
    /* margin-top: 6px; */
}

hr.fi_dash
{
        border: 2px solid #DBDBDB;
}

.filter_page .checkmark {
    position: absolute;
    top: 6px;
    left: 0px;
    height: 14px;
    width: 12px;
    border: 1px solid #eee;
    background: white;
    top: 4px;
}

.mo_fi_save .title2 {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
}

.mo_fi_save {
    margin-top: 20px;
    float: left;
}

.mo_fi_save.col-xs-16 input.submit_button.with_bg.dis_inline {
    margin-top: 9px;
}

.mo_fi_save.col-xs-16 input.cancle_button.dis_inline {
    margin-left: 10px;
}
.text-left
{
    text-align: left !important;
}

.bg-white {
    background: transparent;
}

span.pull-right.more_button {
    margin-right: 17px;
    margin-top: 4px;
}

.col-sm-11.visible-xs.mobile_color {
    margin-top: 15px;
}

.col-sm-11.visible-xs.mobile_color {
    padding-top: 15px;
    background: #F0F2F4;
    padding-bottom: 15px;
    margin-top: 0px;
}

.agent_details_check button.btn.add_new_list.dis_inline {
    margin-left: 0px;
}



}

.chat_message .left_side.height_auto
{
    height: auto;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
}
