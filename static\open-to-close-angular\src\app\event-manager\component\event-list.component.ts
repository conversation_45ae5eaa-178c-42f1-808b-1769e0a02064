import { Component, OnInit,<PERSON><PERSON>one,ViewChild } from '@angular/core';
import { EventManaegerComponent } from '@app/event-manager/component/event-manager.component';
import { EventManagerService } from '@app/event-manager/services/event-manager.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { URLSearchParams } from '@angular/http';
import { BaseComponent } from '@app/base/components/base.component';
import { Event, PropertyEvent, EventError } from '@app/property-detail/models/event.model';
import { Agent } from '@app/profile/models/profile';
import { AuthService } from '@app/auth/services/auth.service';
import * as moment from 'moment';
import { SearchService } from '@app/search/service/search.service';
import { ChatService } from '@app/messaging/service/chat-service';
import { AddEventComponent } from '@app/add-event/components/add-event.component';

declare var $;

@Component({
  selector: 'event-list',
  templateUrl: '../views/event-list.component.html',
  styleUrls: ['../css/event-manager.component.css']
})
export class EventListComponent extends EventManaegerComponent implements OnInit {

  @ViewChild(AddEventComponent) addEventModal: AddEventComponent;

  eventMangerService: EventManagerService;
  searchService : SearchService;

  // Lists as per event
  upcomingEventList: any[] = [];
  availableEventList: any[] = [];
  requestsEventList: any[] = [];
  pastEventList: any[] = [];

  currentId: any;
  public currentIndex: number;
  selectedEventObj: PropertyEvent = new PropertyEvent();
  // eventTypeParams = new URLSearchParams();
  meAgentType: boolean = false;
  brAgentType: boolean = false;
  saAgentType: boolean = false;
  searchAgentList: Agent[] = [];
  closeBtn: boolean = false;
  disabledEvent: boolean = false;
  upComing : Boolean = false;
  public startTime:any;
  public endTime:any;
  public timeError: String;
  // index variables
  upcIndex: any = 2;
  upcTotalCount: any = 0;
  upcItemPerPage:any;
  availIndex: any = 2;
  availTotalCount: any = 0;
  availItemPerPage:any;
  reqIndex: any = 2;
  reqTotalCount: any = 0;
  reqItemPerPage:any;
  pastIndex: any = 2;
  pastTotalCount: any = 0;
  pastItemPerPage:any;
  public showEventReqDot : Boolean = false;
  public currentUserId = '';
  public todayDate:any;
  event: Event = new Event();
  eventError: EventError = new EventError();
  public selectedEventIndex :any;

  disableLoadMore : Boolean = false;
  showUPLoader : Boolean = false;
  showAVLoader : Boolean = false;
  showRELoader : Boolean = false;
  showPALoader : Boolean = false;
  isBrokerage : Boolean = false;

  public authService: AuthService;
  public chatService : ChatService;

  //Upcoming Sorting
  public upSortObject : any = {};
  public upSortList :any[] = [];

  //Available Sorting
  public avSortObject : any = {};
  public avSortList :any[] = [];

  //Requests Sorting
  public reSortObject : any = {};
  public reSortList :any[] = [];

  //PAST Sorting
  public paSortObject : any = {};
  public paSortList :any[] = [];

  public userRole = "";

  constructor(zone: NgZone) {
    super(zone);
    this.eventMangerService = ServiceLocator.injector.get(EventManagerService);
    this.authService = ServiceLocator.injector.get(AuthService);
    this.searchService = ServiceLocator.injector.get(SearchService);
    this.chatService = ServiceLocator.injector.get(ChatService);
  }

  ngOnInit() {
    if(BaseComponent.user != undefined){
      if((BaseComponent.user.user_type == "LA" && BaseComponent.user.is_paid_account) && BaseComponent.user.is_connected_with_broker && BaseComponent.user.is_broker_paid_account){
        this.userRole = "menu-unlock";
      }
      else if(BaseComponent.user.user_type == "LA" && (BaseComponent.user.is_connected_with_broker == false || BaseComponent.user.is_broker_paid_account == false)){
        this.userRole = "menu-lock";
      }else if(BaseComponent.user.user_type == "LA" && BaseComponent.user.is_paid_account == false){
        this.userRole = "menu-lock";
      }
      else if(BaseComponent.user.user_type == "BR"){
        this.userRole = "menu-unlock";
      }
    }
    this.searchBarComponent.showopenHouseFilter = false;
    this.searchBarComponent.searchFrom = "eventManager";
    if(this.getPreviousScreen() != '/event-manager/event-list'){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/event-manager/event-list');

    if(this.searchBarComponent.selectedOHAId == ''){
      this.searchBarComponent.selectedOHAId = null;
    }

    var date = new Date();
    this.todayDate = moment(date).utc().format('YYYY-MM-DD');
    if(localStorage.getItem('recentSearches') == null){
      this.setEventManagerType();
      this.showUPLoader = true;
      this.showAVLoader = true;
      this.showRELoader = true;
      this.showPALoader = true;
    }else{
      this.searchBarComponent.allowCallGetMapPolygons = false;
      this.searchBarComponent.allowLocalStorageSearch();
      this.showUPLoader = true;
      this.showAVLoader = true;
      this.showRELoader = true;
      this.showPALoader = true;
    }

    let self = this;
    $(document).ready(function () {
      $(document).on("click",function() {
        if(self.currentUserId == ''){
          self.currentUserId = BaseComponent.user.id;
        }
        if(self.currentId !=undefined){
          // $(document).not(".click_menu_open").hide();
        }
      });
    });

    $(document).ready(function()
    {
      $(document).mouseup(function(e)
      {
        if(self.currentId != undefined && self.currentIndex != undefined){
          $("#em_"+self.currentIndex+"_"+self.currentId).hide();
          self.currentId = undefined;
          self.currentIndex = undefined;
        }
      });
    });

    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      if(BaseComponent.user.user_type == 'BR'){
        this.isBrokerage  = true;
      }
      else{
        this.isBrokerage = false;
      }
    }
  }

  openMenu(index,id){
    this.currentIndex = index;
    this.currentId = id;
    $("#em_"+index+"_"+id).toggle();
  }

  loadMoreEventManagerList(pageNo: any, eventType: string){
    let sortList = [];
    this.eventTypeParams.delete("sort_list");

    this.eventTypeParams.set("page_no", pageNo);
    this.eventTypeParams.set("type", eventType);
    this.eventTypeParams.set("event_view","DV");
    this.eventTypeParams.set("is_map_list",'false');
    this.eventTypeParams.set("request_type",'WEB');
    this.eventTypeParams.set("geo_bounding_box",'{}');

    if(eventType == "UP"){
      if(this.upSortList.length !=0){
        sortList = this.upSortList;
        this.eventTypeParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(eventType == "AV"){
      if(this.avSortList.length !=0){
        sortList = this.avSortList;
        this.eventTypeParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(eventType == "RE"){
      if(this.reSortList.length !=0){
        sortList = this.reSortList;
        this.eventTypeParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(eventType == "PA"){
      if(this.paSortList.length !=0){
        sortList = this.paSortList;
        this.eventTypeParams.set('sort_list', JSON.stringify(sortList));
      }
    }

    this.disableLoadMore = true;
    this.searchService.getEventSearch(this.eventTypeParams).subscribe(res => {
      this.disableLoadMore = false;
      if(eventType == "UP"){
        if(res['result']['records'].length != 0){
          res['result']['records'].forEach(record => {
            this.upcomingEventList.push(record);
          });
        }
        this.upcIndex += 1;
      }else if(eventType == "AV"){
        if(res['result']['records'].length != 0){
          res['result']['records'].forEach(record => {
            this.availableEventList.push(record);
          });
        }
        this.availIndex += 1;
      }else if(eventType == "RE"){
        if(res['result']['records'].length != 0){
          res['result']['records'].forEach(record => {
            this.requestsEventList.push(record);
          });
        }
        this.reqIndex += 1;
      }else if(eventType == "PA"){
        if(res['result']['records'].length != 0){
          res['result']['records'].forEach(record => {
            this.pastEventList.push(record);
          });
        }
        this.pastIndex += 1;
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  setEventManagerType(){
    let eventTypeParams = new URLSearchParams();
    eventTypeParams.set("page_no", "1");
    eventTypeParams.set("event_view","DV");
    eventTypeParams.set("today_date",this.todayDate);
    eventTypeParams.set("is_map_list",'false');
    eventTypeParams.set("request_type",'WEB');
    eventTypeParams.set("geo_bounding_box",'{}');

    eventTypeParams.set("type", "UP");
    this.getEventManagerList(eventTypeParams, "UP");


    eventTypeParams.set("type", "PA");
    this.getEventManagerList(eventTypeParams, "PA");

    if(BaseComponent.user != undefined){
      if(BaseComponent.user.user_type == "LA"){
        if(BaseComponent.user.is_paid_account && BaseComponent.user.is_connected_with_broker ){
          eventTypeParams.set("type", "RE");
          this.getEventManagerList(eventTypeParams, "RE");

          eventTypeParams.set("type", "AV");
          this.getEventManagerList(eventTypeParams, "AV");
        }
      }
      else if(BaseComponent.user.user_type == "BR" && BaseComponent.user.is_paid_account){
        eventTypeParams.set("type", "RE");
        this.getEventManagerList(eventTypeParams, "RE");

        eventTypeParams.set("type", "AV");
        this.getEventManagerList(eventTypeParams, "AV");
      }
    }
  }

  getEventManagerList(eventTypeParams, type){
    this.searchService.getEventSearch(eventTypeParams).subscribe(res => {
      if(BaseComponent.user != undefined){
        this.currentUserId = BaseComponent.user.id;
      }

      if(type == "UP"){
        this.showUPLoader = false;
        this.upcomingEventList = res['result']['records'];
        this.upcTotalCount = res['result']['total_records_count'];
        this.upcItemPerPage = res['result']['items_per_page'];
      }else if(type == "AV"){
        this.showAVLoader = false;
        this.availableEventList = res['result']['records'];
        this.availTotalCount = res['result']['total_records_count'];
        this.availItemPerPage = res['result']['items_per_page'];
      }else if(type == "RE"){
        this.showRELoader = false;
        this.requestsEventList = res['result']['records'];
        this.reqTotalCount = res['result']['total_records_count'];
        this.reqItemPerPage = res['result']['items_per_page'];
      }else if(type == "PA"){
        this.showPALoader = false;
        this.pastEventList = res['result']['records'];
        this.pastTotalCount = res['result']['total_records_count'];
        this.pastItemPerPage = res['result']['items_per_page'];
      }
    }, err => {
      console.log(err.json());
    });
  }

  eventDetailView(event,eventType){
    this.selectedEventIndex = this.upcomingEventList.indexOf(event);
    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      if(BaseComponent.user.user_type == 'BR'){
        this.addEventModal.manageEventDetailView(event,"past");
      }
      else{
        this.addEventModal.manageEventDetailView(event,eventType);
      }
    }
  }

  updateUpcomingEvent(eventResponse){
    // if(eventResponse.event_agent_type == 'SA' && eventResponse.agent_id != BaseComponent.user.id){
    //   this.upcomingEventList.splice(this.selectedEventIndex, 1);
    // }
    // else{

      console.log(eventResponse);
      this.upcomingEventList[this.selectedEventIndex]['date'] = eventResponse.event_date;
      this.upcomingEventList[this.selectedEventIndex]['description'] = eventResponse.description;
      this.upcomingEventList[this.selectedEventIndex]['end_time'] = eventResponse.end_time;
      this.upcomingEventList[this.selectedEventIndex]['start_time'] = eventResponse.start_time;
      this.upcomingEventList[this.selectedEventIndex]['event_type'] = eventResponse.event_type;
      this.upcomingEventList[this.selectedEventIndex]['event_type_msg'] = eventResponse.event_type_msg;
      this.upcomingEventList[this.selectedEventIndex]['event_agent_type'] = eventResponse.event_agent_type;
      this.upcomingEventList[this.selectedEventIndex]['open_house_agent_id'] = eventResponse.open_house_agent_id;
      this.upcomingEventList[this.selectedEventIndex]['open_house_agent_image'] = eventResponse.open_house_agent_image;
      this.upcomingEventList[this.selectedEventIndex]['open_house_agent_name'] = eventResponse.open_house_agent_name;
    // }
  }

  cancelEvent(eventId: any, eventType: string, index: any){
    let canEventUrlParams = new URLSearchParams();
    canEventUrlParams.set('event_id', eventId);

    this.eventMangerService.cancelEvent(canEventUrlParams).subscribe(res => {
      this.successResponse(res);
      if(eventType == "UP"){
        this.upcomingEventList.splice(index, 1);
      }
      else if(eventType == "RE"){
        this.requestsEventList.splice(index, 1);
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  acceptRequest(eventId: any, status: string, index: any, pickupEvent: string = "", event:any){
    let accEventUrlParams = new URLSearchParams();
    accEventUrlParams.set('event_id', eventId);
    accEventUrlParams.set('status', status);

    this.eventMangerService.acceptEvent(accEventUrlParams).subscribe(res => {
      this.successResponse(res);
      let eventTypeParams = new URLSearchParams();
      eventTypeParams.set("is_map_list",'false');
      eventTypeParams.set("request_type",'WEB');
      eventTypeParams.set("geo_bounding_box",'{}')
      if(pickupEvent == 'PICK'){
        this.availableEventList.splice(index, 1);
        eventTypeParams.set("page_no", "1");
        eventTypeParams.set("event_view","DV");
        eventTypeParams.set("type", "UP");
        this.getEventManagerList(eventTypeParams, "UP")
      }
      else if(pickupEvent == ''){
        this.requestsEventList.splice(index, 1);
        if(status == 'AC'){
        eventTypeParams.set("page_no", "1");
        eventTypeParams.set("event_view","DV");
        eventTypeParams.set("type", "UP");
        this.getEventManagerList(eventTypeParams, "UP")
        }
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  getSearchObj(eventParams){
    this.zone.run(
      () => {
        this.upcIndex = 2;
        this.availIndex = 2;
        this.reqIndex = 2;
        this.pastIndex = 2;

        this.eventTypeParams = eventParams;
        this.eventTypeParams.set("is_map_list",'false');
        this.eventTypeParams.set("request_type",'WEB');
        this.eventTypeParams.set("geo_bounding_box",'{}');
        this.eventTypeParams.set('today_date',this.todayDate);
        eventParams.set('type', 'UP');
        this.getEventManagerList(eventParams,'UP');
        eventParams.set('type', 'PA');
        this.getEventManagerList(eventParams,'PA');


        if(BaseComponent.user != undefined){
          if(BaseComponent.user.user_type == "LA"){
            // if(BaseComponent.user.is_paid_account && BaseComponent.user.is_connected_with_broker && BaseComponent.user.is_broker_paid_account){
            if(BaseComponent.user.is_paid_account && BaseComponent.user.is_connected_with_broker){ 
              eventParams.set('type', 'RE');
              this.getEventManagerList(eventParams,'RE');

              eventParams.set('type', 'AV');
              this.getEventManagerList(eventParams,'AV');
            }
          }
          else if(BaseComponent.user.user_type == "BR" && BaseComponent.user.is_paid_account){
            eventParams.set('type', 'RE');
            this.getEventManagerList(eventParams,'RE');

            eventParams.set('type', 'AV');
            this.getEventManagerList(eventParams,'AV');
          }
        }

        this.eventTypeParams.delete("sort_list");
        this.removeSorting();
    });
  }

  removeSorting(){
    var sort = 'UP';

    for(let i=0; i<4; i++){
      if(i == 0){
        sort = 'UP';
        this.upSortList = [];
        this.upSortObject = {};
      }
      else if(i == 1){
        sort = 'AV';
        this.avSortList = [];
        this.avSortObject = {};
      }
      else if(i == 2){
        sort = 'RE';
        this.reSortList = [];
        this.reSortObject = {};
      }
      else if(i == 3){
        sort = 'PA';
        this.paSortList = [];
        this.paSortObject = {};
      }
      $('#'+sort+'_PR').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_EV').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_DT').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_OA').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_GO').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_LA').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_HP').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_RE').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_RA').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      // $('#'+sort+'_OAR').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
    }
  }

  isIncomingEventReq(){
    let eventList = this.requestsEventList.filter((eventType) => eventType.request_type == 'I');
    if(eventList.length != 0){
      return true;
    }
    else{
      return false;
    }
  }

  contactListingAgent(selectedClient){
    if(selectedClient.listing_agent_id != this.currentUserId){
      var client = {};
      client['user_name'] = selectedClient['listing_agent_name'];
      client['profile_image'] = selectedClient['listing_agent_image'];
      client['chat_thread_id'] = selectedClient['listing_agent_id'];
      client['receiver_id'] = selectedClient['listing_agent_id'];
      client['last_message_time'] = "";
      client['last_message'] = '';
      this.chatService.setClientChatThread(client);
      this.router.navigate(['messaging']);
    }
  }

  runEvent(event_id,type){
    if(type == 'newEvent'){
      let runEventParams = new URLSearchParams();
      runEventParams.set('event_status','RU');
      runEventParams.set('event_id',event_id);
      this.eventMangerService.runEvent(runEventParams).subscribe(res =>{
        // this.successResponse(res);
        this.router.navigate(['event-manager/run-event-manager'],{queryParams:{eventId:event_id}});
      },err=>{
        this.errorResponse(err.json());
      });
    }
    if(type == 'runningEvent'){
      this.router.navigate(['event-manager/run-event-manager'],{queryParams:{eventId:event_id}});
    }
  }


  eventSortting(listingType,filedName){
    let sortList = [];
    if(listingType == 'UP'){
      this.upcIndex = 2;
      if(this.upSortObject[filedName] == undefined){
        this.upSortObject[filedName] = true;
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.upSortObject[filedName] = this.upSortObject[filedName] === true ? false : true;
       if(this.upSortObject[filedName]){
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
       }else{
        $('#UP_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.upSortList[0] = this.upSortObject;
      sortList = this.upSortList;
    }

    if(listingType == 'AV'){
      this.availIndex = 2;
      if(this.avSortObject[filedName] == undefined){
        this.avSortObject[filedName] = true;
        $('#AV_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.avSortObject[filedName] = this.avSortObject[filedName] === true ? false : true;
       if(this.avSortObject[filedName]){
        $('#AV_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
       }else{
        $('#AV_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.avSortList[0] = this.avSortObject;
      sortList = this.avSortList;
    }

    if(listingType == 'RE'){
      this.reqIndex = 2;
      if(this.reSortObject[filedName] == undefined){
        this.reSortObject[filedName] = true;
        $('#RE_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.reSortObject[filedName] = this.reSortObject[filedName] === true ? false : true;
       if(this.reSortObject[filedName]){
        $('#RE_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
       }else{
        $('#RE_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.reSortList[0] = this.reSortObject;
      sortList = this.reSortList;
    }

    if(listingType == 'PA'){
      this.pastIndex = 2;
      if(this.paSortObject[filedName] == undefined){
        this.paSortObject[filedName] = true;
        $('#PA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
       this.paSortObject[filedName] = this.paSortObject[filedName] === true ? false : true;
       if(this.paSortObject[filedName]){
        $('#PA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
       }else{
        $('#PA_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
       }
      }
      this.paSortList[0] = this.paSortObject;
      sortList = this.paSortList;
    }

    this.eventTypeParams.set("page_no", "0");
    this.eventTypeParams.set("event_view", "DV");
    this.eventTypeParams.set("type", listingType);
    this.eventTypeParams.set("today_date",this.todayDate);
    this.eventTypeParams.set("is_map_list",'false');
    this.eventTypeParams.set("request_type",'WEB');
    this.eventTypeParams.set("geo_bounding_box",'{}');
    this.eventTypeParams.set('sort_list', JSON.stringify(sortList));
    this.eventTypeParams.set('geo_bounding_box', "{}");

    this.getEventManagerList(this.eventTypeParams,listingType);
  }

  checkUserRole(){
    if(BaseComponent.user.user_type == "LA"){
      // $('#lockedFeatureModal').modal('show');
      }
    }
}
