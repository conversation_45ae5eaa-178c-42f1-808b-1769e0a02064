import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRoute } from '@angular/router';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router/src/router_state';

import { AuthService } from '@app/auth/services/auth.service';

import { BaseComponent } from '@app/base/components/base.component';

@Injectable()
export class LPauthguard implements CanActivate {

    public currentUrl = "";

    constructor(private baseComponent: BaseComponent, private authService: AuthService,
        private route: ActivatedRoute, private router: Router) {
    }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
        this.currentUrl = state.url;
        if (BaseComponent.user != undefined) {
            return this.verifyUser();
        }
        else {
            if (this.baseComponent.getUserToken() != null && this.baseComponent.getUserType() != null) {
                return this.authService.getUserDetails().map((res) => {
                    BaseComponent.user = res.result;
                    return this.verifyUser();
                });
            }
            else {
                if (this.currentUrl.endsWith('agent') || this.currentUrl.endsWith('broker')) {
                    return true;
                }
                else if (this.currentUrl.includes('agentupgrade')){
                    this.router.navigateByUrl('/agent');
                }
                else if (this.currentUrl.includes('brokerupgrade')){
                    this.router.navigateByUrl('/broker');
                }
                else {
                    this.router.navigateByUrl('/');
                }
            }
        }
    }

    // after uncommenting - change return type to -> boolean
    verifyUser(): boolean {
        if(BaseComponent.user.user_type == 'LA' || BaseComponent.user.user_type == 'BR'){
            if (this.currentUrl.endsWith('agent')) {
                if (BaseComponent.user.user_type == 'LA') {
                    this.router.navigateByUrl('/agentupgrade');
                }
                if (BaseComponent.user.user_type == 'BR') {
                    this.router.navigateByUrl('/brokerupgrade');
                }
                this.router.navigateByUrl('/');
                return false;
            }
            if (this.currentUrl.endsWith('broker')) {
                if (BaseComponent.user.user_type == 'BR') {
                    this.router.navigateByUrl('/brokerupgrade');
                }
                if (BaseComponent.user.user_type == 'LA') {
                    this.router.navigateByUrl('/agentupgrade');
                }
            }
            if (this.currentUrl.includes('agentupgrade')) {
                if (BaseComponent.user.user_type == 'LA') {
                    return true;
                }
                if (BaseComponent.user.user_type == 'BR') {
                    this.router.navigateByUrl('/brokerupgrade');
                }
            }
            if (this.currentUrl.includes('brokerupgrade')) {
                if (BaseComponent.user.user_type == 'BR') {
                    return true;
                }
                if (BaseComponent.user.user_type == 'LA') {
                    this.router.navigateByUrl('/agentupgrade');
                }
            }
        }
        else {
            this.router.navigateByUrl('/');
        }
    }
}
