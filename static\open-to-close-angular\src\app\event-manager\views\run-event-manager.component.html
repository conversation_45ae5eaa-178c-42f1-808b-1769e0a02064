<div>
    <header></header>
</div>

<div>
    <div class="homepage header_fix">
        <div class="chat_message check_event">
            <div class="left_side height_auto eventchatwidth" *ngIf="showEventChat == true">
                <div *ngIf="isPaidAccount">
                <div class="check_in_title">Event Chat</div>
                <a class="check_in_link" (click)="newChat()">
                    <button type="button" class="btn btn1 add_new_list dis_inline" data-toggle="modal" data-target="#saveSearch">New Message</button>
                </a>
                <input type="text" #text (keyup)="filterUser(text.value)" class="meassage_search checkin" placeholder="Search">

                <div id="scroll" class="message_list_with_name">
                    <div *ngFor="let chatThread of eventChatThreadList" (click)="showSingelChat(chatThread)">
                        <div id={{chatThread.chat_thread_id}} class="message_list cursor-pointer" [ngClass]="{'selected-client':selectedClientId == chatThread.chat_thread_id}">
                            <div *ngIf="chatThread.profile_image =='' " class="ls dis_inline po_rel_1">{{chatThread.user_initial}}</div>
                            <span *ngIf="chatThread.profile_image !='' ">
                                <img class="chat-profile-img" src="{{chatThread.profile_image}}">
                            </span>
                            <div class="meaasge_details chat-thread-img">
                                <a>
                                    <div class="chat-msg message_name">{{chatThread.user_name}}
                                        <i *ngIf="chatThread.is_read == false" class="fa fa-circle unread-msg-dot"></i>
                                        <span *ngIf="chatThread.last_message_time != ''" class="message_time">{{getLastTime(chatThread.last_message_time)}}</span>
                                    </div>
                                    <div class="message_text">
                                        <p class="chat-thread-last">{{chatThread.last_message}}</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
                <div *ngIf="isPaidAccount == false">
                    <div class="check_in_title">Unlock Chat</div>
                    <div>
                        <span>
                            <p class="unlock-text">Upgrade your account to chat with home buyers attending this open house.</p>
                        </span>
                    </div>
                    <div class="unlock-btn-div">
                        <a class="check_in_link" (click)="openPlansModal()">
                            <button type="button" class="btn btn1 add_new_list dis_inline_disabled">Upgrade Account</button>
                        </a>
                    </div>
                </div>
            </div>


            <div class="chatView left_side height_auto Recipient_Chosen" *ngIf="showSingeEventChat == true">
                <div class="recipient_group">
                    <img (click)="backToChat()" src="{{imagePrefix}}symbols-glyph-arrow-line-down.png" class="white_leftarrow_image cursor-pointer"
                        alt="">
                    <div *ngIf="selectedClient.profile_image == '' " class="message_short_name">{{selectedClient.user_initial}}</div>
                    <span *ngIf="selectedClient.profile_image !='' ">
                        <img class="chat-profile-img chat-profile-img-center profile-center" src="{{selectedClient.profile_image}}">
                    </span>
                    <div></div>
                    <div class="title2">{{selectedClient.user_name}}</div>
                </div>
                <div class="chat_session Recipient_Chosen_chat message_list_with_name chat_over_flow">
                    <div *ngFor="let message of chatList">
                        <div class="chat_time">{{getMessageTime(message.date_time)}}</div>
                        <div id={{message.chat_id}} *ngIf="message.sender_id == senderId" class="col-sm-16">
                            <div *ngIf="(message.property_detail | json) != '{}' && message.is_link == true" class="col-sm-16 chat-image">
                                <div (click)="propertyDetailInNewTab(message.property_detail.property_id)" class="box_on_map position_reletive pull-right cursor-pointer">
                                    <div class="map_group">
                                        <img src="{{message?.property_detail?.property_file}}" class="box_on_image_image" alt="">
                                        <div class="on_map_details">
                                            <div class="on_map_price title">{{message?.property_detail?.home_price | currency:"":symbol:"1.0"}}</div>
                                            <div class="on_map_detail">{{message?.property_detail?.bedroom}}bds {{message?.property_detail?.full_bath}}bths
                                                {{message?.property_detail?.lot_size}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="on_map_address">{{message?.property_detail?.street}}</div>
                                </div>
                            </div>
                            <div *ngIf="(message.property_detail | json) == '{}' && message.is_link == true" class="your_chat chat-text-break" [innerHTML]="checkMessageAsLink(message.message)"></div>
                            <div *ngIf="message.is_link == false" class="your_chat chat-text-break" [innerHTML]="checkMessageAsLink(message.message)"></div>
                        </div>

                        <div id={{message.chat_id}} *ngIf="message.sender_id !== senderId">
                            <span class="other_chat container msg-left-padding msg-left-width">
                                <div *ngIf="(message.property_detail | json) != '{}' && message.is_link == true" class="col-sm-4 chat-image chat-co-sm-4">
                                    <div (click)="propertyDetailInNewTab(message.property_detail.property_id)" class="box_on_map position_reletive cursor-pointer">
                                        <div class="map_group">
                                            <img src="{{message?.property_detail?.property_file}}" class="box_on_image_image" alt="">
                                            <div class="on_map_details">
                                                <div class="on_map_price title">{{message?.property_detail?.home_price | currency:"":symbol:"1.0"}}</div>
                                                <div class="on_map_detail">{{message?.property_detail?.bedroom}}bds {{message?.property_detail?.full_bath}}bths
                                                    {{message?.property_detail?.lot_size}}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="on_map_address">{{message?.property_detail?.street}}</div>
                                    </div>
                                </div>
                                <span *ngIf="(message.property_detail | json) == '{}' && message.is_link == true" class="chat-text-break link-message-right"
                                    [innerHTML]="checkMessageAsLink(message.message)"></span>
                                <span class="chat-text-break link-message-right" *ngIf="message.is_link == false" [innerHTML]="checkMessageAsLink(message.message)"></span>
                            </span>
                        </div>

                        <div *ngFor="let sendMsg of messageList" class="col-sm-16">
                            <div class="your_chat">{{sendMsg.message}}</div>
                        </div>
                    </div>
                </div>
                <div>
                    <input type="text" id="msgtxtbox" #message (keyup.enter)="sendMessage(message.value)" class="meassage_search meassage_event checkin"
                        placeholder="Start typing here...">
                </div>
            </div>

            <div class="left_side height_auto Recipient_Chosen" *ngIf="showRecipientChat == true">
                <div class="recipient_group">
                    <img (click)="backToChat()" src="{{imagePrefix}}symbols-glyph-arrow-line-down.png" class="white_leftarrow_image cursor-pointer"
                        alt="">
                    <div class="title2">New Message</div>

                    <div class="form_group col-sm-16 drop-down-chat">
                        <ng-select class="custom date-drop-down eventChat" placeholder="Choose a recipient" [items]="recipientUserList" bindValue="user_name"
                            bindLabel="user_name" [clearable]=false [searchable]=false (change)="onRecipientSelect($event)">
                        </ng-select>
                    </div>
                </div>
            </div>


            <div class="right_side_event_agent right_side_overflow">
                <div class="right_agent_event">
                    <div class="image event-card-image-gradient run-tool-image-gradient property-image-width">
                        <span *ngIf="eventBasicInfo?.property_file == ''">
                            <img class="img-cover" src="{{imagePrefix}}symbols-map-hover.png" alt="">
                        </span>
                        <span *ngIf="eventBasicInfo?.property_file != ''">
                            <img class="img-cover" src="{{eventBasicInfo.property_file}}" alt="">
                        </span>
                    </div>
                    <div class="right_agent_text">
                        <div class="col-sm-4 agent-user-info-in">
                            <div class="open_agent_img">
                                <span>
                                    <div *ngIf="eventBasicInfo?.event_type == 'BO'" class="event-info-month">
                                        <span class="bgcolor1 font_semibold">{{getDayName(eventBasicInfo?.date,eventBasicInfo?.start_time,eventBasicInfo?.is_listhub_event)}}</span>
                                        <div class="font_semibold">{{getDay(eventBasicInfo?.date,eventBasicInfo?.start_time,eventBasicInfo?.is_listhub_event)}}</div>
                                    </div>
                                </span>
                                <span *ngIf="eventBasicInfo?.event_type == 'AO'">
                                    <div class="event-info-month">
                                        <span class="month_2_color font_semibold">{{getDayName(eventBasicInfo?.date,eventBasicInfo?.start_time,eventBasicInfo?.is_listhub_event)}}</span>
                                        <div class="font_semibold">{{getDay(eventBasicInfo?.date,eventBasicInfo?.start_time,eventBasicInfo?.is_listhub_event)}}</div>
                                    </div>
                                </span>
                                <span *ngIf="eventBasicInfo?.event_type == 'OH'">
                                    <div class="event-info-month">
                                        <span class="month_3_color font_semibold">{{getDayName(eventBasicInfo?.date,eventBasicInfo?.start_time,eventBasicInfo?.is_listhub_event)}}</span>
                                        <div class="font_semibold">{{getDay(eventBasicInfo?.date,eventBasicInfo?.start_time,eventBasicInfo?.is_listhub_event)}}</div>
                                    </div>
                                </span>
                                <div class="open_agent_name_group">
                                    <div *ngIf="eventBasicInfo?.event_type == 'OH'" class="name">Open House</div>
                                    <div *ngIf="eventBasicInfo?.event_type == 'BO'" class="name">Broker Open</div>
                                    <!-- <div *ngIf="eventBasicInfo?.event_type == 'AO'" class="name">72 Hour Home Sale</div> -->
                                    <div class="name">{{setEventDateFormat(eventBasicInfo?.date,eventBasicInfo?.start_time,eventBasicInfo?.is_listhub_event)}}</div>
                                    <div *ngIf="eventBasicInfo.date != undefined" class="fname">{{getTimeTypes(eventBasicInfo?.start_time,'',eventBasicInfo?.date,eventBasicInfo?.is_listhub_event)}} - {{getTimeTypes(eventBasicInfo?.end_time,'',eventBasicInfo?.date,eventBasicInfo?.is_listhub_event)}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 hidden-xs agent-user-info-in">
                            <div class="open_agent_img">
                                <span *ngIf="eventBasicInfo?.listing_agent_image == ''">
                                    <img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt="">
                                </span>
                                <span *ngIf="eventBasicInfo?.listing_agent_image != ''">
                                    <img src="{{eventBasicInfo?.listing_agent_image}}" class="search-agent-event symbols-property-image dis_inline" alt="">
                                </span>
                                <div class="open_agent_name_group">
                                    <div class="name">Open House Agent</div>
                                    <div class="name">{{eventBasicInfo?.listing_agent_name}}</div>
                                    <div class="fname">{{eventBasicInfo?.brokerage_name}}</div>
                                </div>
                                <span [hidden]="true">{{currentLoginUserId}}</span>
                                <div *ngIf="currentLoginUserId != undefined && currentLoginUserId != eventBasicInfo?.listing_agent_id " class="white_button dis_inline" (click)="contactListingAgent(eventBasicInfo)">Contact Agent</div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="button_group">
                                <!-- <button type="button" class="btn exportpdf">Export to PDF</button> -->
                                <a *ngIf="disableEventEdit != true" data-toggle="modal" data-target="#endOpenHouseModel" class="cursor-pointer">
                                    <div class="without_white_button">End Open House</div>
                                </a>
                                <a *ngIf="disableEventEdit != true" (click)="openGuestBookMode()" class="cursor-pointer">
                                    <div class="with_white_button guest-btn">Guest Book Mode</div>
                                </a>
                            </div>
                        </div>
                        <div class="row">
                            <div class="agent_details_check">
                                <div class="col-sm-9">
                                    <div class="agent_title2 long-text-dot">{{eventBasicInfo?.address}}</div>
                                    <div class="agent_sub_title2">{{eventBasicInfo?.location}}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-9 hidden-xs agent-user-info">
                        <div class="open_agent_img">
                            <span *ngIf="eventBasicInfo?.listing_agent_image == ''">
                                <img src="{{imagePrefix}}default-placeholder.png" class="search-agent-event symbols-property-image dis_inline" alt="">
                            </span>
                            <span *ngIf="eventBasicInfo?.listing_agent_image != ''">
                                <img src="{{eventBasicInfo?.listing_agent_image}}" class="search-agent-event symbols-property-image dis_inline" alt="">
                            </span>
                            <div class="open_agent_name_group">
                                <div class="name">{{eventBasicInfo?.listing_agent_name}}</div>
                                <div class="fname">{{eventBasicInfo?.brokerage_name}}</div>
                            </div>
                            <span [hidden]="true">{{currentLoginUserId}}</span>
                            <div *ngIf="currentLoginUserId != undefined && currentLoginUserId != eventBasicInfo?.listing_agent_id " class="white_button dis_inline" (click)="contactListingAgent(eventBasicInfo)">Contact Agent</div>
                        </div>
                    </div>


                    <!-- <div class="col-sm-11 visible-xs mobile_color">
                     <div class="open_agent_img">
                        <img src="{{imagePrefix}}symbols-avatar.png" class="agent_symbols-avatar" alt="">
                        <div class="open_agent_name_group">
                           <div class="name">Elnora Casey</div>
                           <div class="fname">North &amp; Co</div>
                        </div>
                        <div class="white_button dis_inline">Contact Agent</div>
                     </div>
                  </div> -->



                    <div id="Agent_View" class="tab-pane fade active in Event_manager_blog check_table1">
                        <div class="bg-white">
                            <div class="row">
                                <div class="col-sm-16">
                                    <h1 class="overview-title">Overview</h1>
                                    <div class="pr-detail-btn">
                                        <a (click)="gotToPropertyDetail('event-manager/property-detail',eventBasicInfo?.property_id)">
                                            <button type="button" class="btn btn1 add_new_list dis_inline">Property Details</button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="row overviewBoth">
                                <div class="col-sm-7 overviewLeft">
                                    <div class="row">
                                        <div class="col-sm-3 events">
                                            <p>Interested</p>
                                            <h2>{{openHouseOverview?.total_going}}</h2>
                                        </div>
                                        <div class="col-sm-5 represented">
                                            <p>
                                                <i class="fa fa-circle dot1"></i>Represented</p>
                                            <h2>{{openHouseOverview?.total_represented}}/
                                                <span>{{openHouseOverview?.total_going}}</span>
                                            </h2>
                                        </div>
                                        <div class="col-sm-5 unrepresented">
                                            <p>
                                                <i class="fa fa-circle dot"></i>Unrepresented</p>
                                            <h2>{{openHouseOverview?.total_unrepresented}}/
                                                <span>{{openHouseOverview?.total_going}}</span>
                                            </h2>
                                        </div>
                                    </div>

                                    <div class="row progressbar">
                                        <div class="col-sm-16">
                                            <div class="row">
                                                <div class="col-sm-14 progressbar_text">
                                                    <p>
                                                        <img src="{{imagePrefix}}symbols-glyph-checkin.png" alt="symbols-glyph-checkin" style="width: 6%"> Check Ins
                                                    </p>
                                                </div>
                                                <div class="col-sm-2 text-right">
                                                    <span>{{ratingOverview?.ratings}}</span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-16">
                                                    <div class="progress checkins">
                                                        <div class="progress-bar progress-bar-checkins" role="progressbar" [attr.aria-valuenow]=ratingOverview?.ratings aria-valuemin="0"
                                                            aria-valuemax="100" [ngStyle]="{'width':ratingOverview?.ratings + '%'}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-16">
                                            <div class="row">
                                                <div class="col-sm-14 progressbar_text">
                                                    <p>
                                                        <img src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png" alt="symbols-glyph-checkin-thumbsup" style="width: 6%"> Positives
                                                    </p>
                                                </div>
                                                <div class="col-sm-2 text-right">
                                                    <span>{{ratingOverview?.positive}}</span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-16">
                                                    <div class="progress positives">
                                                        <div class="progress-bar progress-bar-positives" role="progressbar" [attr.aria-valuenow]=ratingOverview?.positive aria-valuemin="0"
                                                            aria-valuemax="100" [ngStyle]="{'width':ratingOverview?.positive + '%'}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-16">
                                            <div class="row">
                                                <div class="col-sm-14 progressbar_text">
                                                    <p>
                                                        <img src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown.png" alt="symbols-glyph-checkin-thumbsdown" style="width: 6%"> Negatives
                                                    </p>
                                                </div>
                                                <div class="col-sm-2 text-right">
                                                    <span>{{ratingOverview?.negative}}</span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-16">
                                                    <div class="progress negatives">
                                                        <div class="progress-bar progress-bar-negatives" role="progressbar" [attr.aria-valuenow]=ratingOverview?.negative aria-valuemin="0"
                                                            aria-valuemax="100" [ngStyle]="{'width':ratingOverview?.negative + '%'}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-9 overviewRight run-tool-over">
                                    <div class="row mb-24">
                                        <div class="col-xs-8 col-sm-4 relative">
                                            <p class="pmain">Floorplan</p>
                                            <div class="{{getCountProgress(ratingOverview?.floorplan_positive,ratingOverview?.floorplan_negative, 'floorplan')}}">
                                                <div class="slice">
                                                    <div class="bar"></div>
                                                    <div class="fill"></div>
                                                </div>
                                            </div>
                                            <div class="cirtext">
                                                <p class="posactive">
                                                    <span></span> {{ratingOverview?.floorplan_positive}}
                                                </p>
                                                <p class="negactive">
                                                    <span></span> {{ratingOverview?.floorplan_negative}}</p>
                                            </div>

                                        </div>
                                        <div class="col-xs-8 col-sm-4 relative">
                                            <p class="pmain">Size of Bedrooms</p>
                                            <div class="{{getCountProgress(ratingOverview?.bedroom_positive,ratingOverview?.bedroom_negative, 'sizeofbedrooms')}}">
                                                <div class="slice">
                                                    <div class="bar"></div>
                                                    <div class="fill"></div>
                                                </div>
                                            </div>
                                            <div class="cirtext">
                                                <p class="posactive">
                                                    <span></span> {{ratingOverview?.bedroom_positive}}
                                                </p>
                                                <p class="negactive">
                                                    <span></span> {{ratingOverview?.bedroom_negative}}</p>
                                            </div>
                                        </div>
                                        <div class="col-xs-8 col-sm-4 relative">
                                            <p class="pmain">Size of Bathrooms</p>
                                            <div class="{{getCountProgress(ratingOverview?.bathroom_positive,ratingOverview?.bathroom_negative, 'sizeofbathrooms')}}">
                                                <div class="slice">
                                                    <div class="bar"></div>
                                                    <div class="fill"></div>
                                                </div>
                                            </div>
                                            <div class="cirtext">
                                                <p class="posactive">
                                                    <span></span> {{ratingOverview?.bathroom_positive}}
                                                </p>
                                                <p class="negactive">
                                                    <span></span> {{ratingOverview?.bathroom_negative}}</p>
                                            </div>
                                        </div>
                                        <div class="col-xs-8 col-sm-4 relative">
                                            <p class="pmain">Kitchen</p>
                                            <div class="{{getCountProgress(ratingOverview?.kitchen_positive,ratingOverview?.kitchen_negative, 'kitchen')}}">
                                                <div class="slice">
                                                    <div class="bar"></div>
                                                    <div class="fill"></div>
                                                </div>
                                            </div>
                                            <div class="cirtext">
                                                <p class="posactive">
                                                    <span></span> {{ratingOverview?.kitchen_positive}}
                                                </p>
                                                <p class="negactive">
                                                    <span></span> {{ratingOverview?.kitchen_negative}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-24">
                                        <div class="col-xs-8 col-sm-4 relative">
                                            <p class="pmain">Finishes</p>
                                            <div class="{{getCountProgress(ratingOverview?.finishes_positive,ratingOverview?.finishes_negative, 'finishes')}}">
                                                <div class="slice">
                                                    <div class="bar"></div>
                                                    <div class="fill"></div>
                                                </div>
                                            </div>
                                            <div class="cirtext">
                                                <p class="posactive">
                                                    <span></span> {{ratingOverview?.finishes_positive}}
                                                </p>
                                                <p class="negactive">
                                                    <span></span>{{ratingOverview?.finishes_negative}}</p>
                                            </div>
                                        </div>
                                        <div class="col-xs-8 col-sm-4 relative">
                                            <p class="pmain">Landscaping</p>
                                            <div class="{{getCountProgress(ratingOverview?.landscaping_positive,ratingOverview?.landscaping_negative, 'landscaping')}}">
                                                <div class="slice">
                                                    <div class="bar"></div>
                                                    <div class="fill"></div>
                                                </div>
                                            </div>
                                            <div class="cirtext">
                                                <p class="posactive">
                                                    <span></span> {{ratingOverview?.landscaping_positive}}
                                                </p>
                                                <p class="negactive">
                                                    <span></span> {{ratingOverview?.landscaping_negative}}</p>
                                            </div>
                                        </div>
                                        <div class="col-xs-8 col-sm-4 relative">
                                            <p class="pmain">Neighborhood</p>
                                            <div class="{{getCountProgress(ratingOverview?.neighbourhood_positive,ratingOverview?.neighbourhood_negative, 'neighborhood')}}">
                                                <div class="slice">
                                                    <div class="bar"></div>
                                                    <div class="fill"></div>
                                                </div>
                                            </div>
                                            <div class="cirtext">
                                                <p class="posactive">
                                                    <span></span> {{ratingOverview?.neighbourhood_positive}}
                                                </p>
                                                <p class="negactive">
                                                    <span></span> {{ratingOverview?.neighbourhood_negative}}</p>
                                            </div>
                                        </div>
                                        <div class="col-xs-8 col-sm-4 relative">
                                            <p class="pmain">Price</p>
                                            <div class="{{getCountProgress(ratingOverview?.price_positive,ratingOverview?.price_negative, 'price')}}">
                                                <div class="slice">
                                                    <div class="bar"></div>
                                                    <div class="fill"></div>
                                                </div>
                                            </div>
                                            <div class="cirtext">
                                                <p class="posactive">
                                                    <span></span> {{ratingOverview?.price_positive}}
                                                </p>
                                                <p class="negactive">
                                                    <span></span> {{ratingOverview?.price_negative}}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="check_shown_tables">
                        <div class="check_table1 check-table auto-height">
                            <div class="col-sm-16">
                                <div class="title2" data-toggle="collapse" data-parent="#accordion" href="#collapse1">Guest List</div>
                            </div>
                            <div class="group_acc panel-collapse collapse guest-list-table-col" id="collapse1">
                                <div class="list-filter simple-filter-drop-down pull-right">
                                    <select multiple="multiple" class="new_form drop_down_icon filter filter-panel sumoGuestEvent" placeholder="Filter By">
                                        <option class="guest-filter" value="Represented">Represented</option>
                                        <option class="guest-filter" value="Unrepresented">Unrepresented</option>
                                    </select>
                                </div>
                                <div class="row row-zero">
                                    <div class="col-xs-16" *ngIf="disableEventEdit != true">
                                        <div class="pull-right margin-bttom">
                                            <div class="blue_border_button" (click)="checkIn()">Check In</div>
                                        </div>
                                    </div>
                                </div>

                                <table class="table checkins_table">
                                    <thead>
                                        <tr>
                                            <th>
                                                <div class="check_group profile_checkbox guest_check_box guest-check">
                                                    <div class="form_group ">
                                                        <input type="checkbox" (change)="onAllGuestSelect($event)" [(ngModel)]="selectAllGuest">
                                                        <span class="checkmark"></span>
                                                    </div>
                                                </div>
                                            </th>
                                            <th>Name
                                                <img src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                            <th colspan="3"> Date Added
                                                <img src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        </tr>
                                    </thead>
                                    <tbody class="table-position" [ngClass]="{'display-inline': isTabletScreen == true }">
                                        <ng-container *ngFor="let user of guestList | checkedInList : false">
                                            <tr *ngIf="user.is_checkin == false" [ngClass]="{'display-inline': isTabletScreen == true }" class="guest-border">
                                                <td>
                                                    <div class="check_group profile_checkbox guest_check_box ">
                                                        <div class="form_group ">
                                                            <input type="checkbox" (change)="onSingleGuestSelect($event,user)" [(ngModel)]="selectAllGuest">
                                                            <span class="checkmark"></span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="td-show">
                                                    <div class="bname guest-name">{{user.user_name}}</div>
                                                    <div *ngIf="user.user_status == 'U'" class="unre "> Unrepresented </div>
                                                    <div *ngIf="user.user_status == 'R'" class="unre "> Represented </div>
                                                </td>
                                                <td class="guest-time-reper">
                                                    <div>
                                                        <span class="check_time check_time_mr">{{user.date_added}}</span>
                                                        <input id="guest_{{user.user_id}}" type="button" class="btn btn1 msg-in-table" [ngClass]="{'submit-disable': disableCheckInMsgBtn == true}"
                                                            value="Message" (click)="guestMsg(user)">
                                                    </div>
                                                </td>
                                            </tr>
                                        </ng-container>
                                        <ng-container *ngFor="let user of guestList | checkedInList : true">
                                            <tr *ngIf="user.is_checkin == true && hideChecked == true" class="guest-border" [ngClass]="{'display-inline': isTabletScreen == true }" >
                                                <td >
                                                    <div class="check_group profile_checkbox guest_check_box margin-left">
                                                        <div class="form_group ">

                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="td-show">
                                                    <div class="bname disable-text guest-name">{{user.user_name}}</div>
                                                    <div *ngIf="user.user_status == 'U'" class="unre disable-text guest-name"> Unrepresented </div>
                                                    <div *ngIf="user.user_status == 'R'" class="unre disable-text guest-name"> Represented </div>
                                                </td>
                                                <td class="td-appointment">
                                                    <div class="check_time disable-text">
                                                        {{user.date_added}}
                                                    </div>
                                                </td>
                                            </tr>
                                        </ng-container>
                                    </tbody>
                                </table>
                                <div *ngIf="hideChecked == false" class="checked-text" (click)="showCheckedList()">{{getCheckedInCount()}} checked in</div>
                                <div *ngIf="hideChecked == true" class="checked-text" (click)="hideCheckedList()">Hide checked in</div>
                            </div>
                        </div>

                        <div class="check_table1 check_table2 checkins_table">
                            <div class="col-sm-16">
                                <div class="title2" data-toggle="collapse" data-parent="#accordion" href="#collapse2">Check Ins</div>
                            </div>

                            <div class="group_acc panel-collapse collapse" id="collapse2">
                                <div class="list-filter simple-filter-drop-down pull-right">
                                    <select multiple="multiple" class="new_form drop_down_icon ratings filter-panel sumoEvent" placeholder="Filter By">
                                        <option class="rating-filter" value="Represented">Represented</option>
                                        <option class="rating-filter" value="Unrepresented">Unrepresented</option>
                                    </select>
                                </div>
                                <div class="row row-zero">
                                    <div class="col-xs-16">
                                        <div class="pull-right margin-bttom">
                                            <div class="export-lead-ch" (click)="exportMyLeads()">Export Leads</div>
                                        </div>
                                    </div>
                                </div>

                                <table class="table ">
                                    <thead>
                                        <tr>
                                            <th>
                                                <div class="check_group profile_checkbox guest_check_box ">
                                                    <div class="form_group ">
                                                        <input type="checkbox" (change)="onAllRatingSelect($event)" [(ngModel)]="selectAllRatingList">
                                                        <span class="checkmark"></span>
                                                    </div>
                                                </div>
                                            </th>
                                            <th>Name
                                                <img src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                            <th>Check In
                                                <img src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                            <th colspan="8">Rating
                                                <img src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let rating of ratingList">
                                            <td class="checkin-checkbox">
                                                <div class="check_group profile_checkbox guest_check_box ">
                                                    <div class="form_group ">
                                                        <input type="checkbox" [(ngModel)]="selectAllRatingList" (change)="onSingleRatingSelect($event,rating)">
                                                        <span class="checkmark"></span>
                                                    </div>
                                                </div>
                                            </td>

                                            <td>
                                                <div *ngIf="rating.user_status !== 'U'" class="bname ">{{rating.user_name}}</div>
                                                <a *ngIf="rating.user_status === 'U'" class="cursor-pointer"  (click)="openViewLeadModal(rating)">{{rating.user_name}}</a>

                                                <div *ngIf="rating.user_status == 'U'" class="unre "> Unrepresented </div>
                                                <div *ngIf="rating.user_status == 'R'" class="unre "> Represented </div>
                                            </td>
                                            <td>
                                                <div class="check_time">{{getTime(rating.check_in)}}</div>
                                            </td>
                                            <td class="check_rating">
                                                <span (click)="showUserPropertyRating(rating)" class="rating-span cursor-pointer" *ngIf="rating.rating == '0'">
                                                    <img src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown.png" class="check_icon rating-img cursor-pointer" alt="">
                                                </span>
                                                <span (click)="showUserPropertyRating(rating)" class="rating-span cursor-pointer" *ngIf="rating.rating == '1'">
                                                    <img src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png" class="check_icon rating-img cursor-pointer" alt="">
                                                </span>
                                            </td>
                                            <td>



                                                <!-- <div *ngIf="rating.user_status == 'U'" class="export-lead-ch" (click)="openViewLeadModal(rating)">View Lead</div> -->
                                                <input type="button" id="checkIn_{{rating.user_id}}" class="btn btn1 msg-in-table check-msg"
                                                    [ngClass]="{'submit-disable': disableCheckInMsgBtn == true}" value="Message"
                                                    (click)="checkInsMsg(rating)">

                                                    <div class="export-lead-ch" (click)="startNote(rating)" style="margin-right: 5px !important;">Notes
                                                    </div>
                                                    <span class="badge badge-notify-2"   *ngIf="rating.count">{{rating.count}}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <img src="{{imagePrefix}}emailq.png" class="open_chat" />
    </div>


    <div>
        <div id="endOpenHouseModel" class="modal fade" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">End Open House</h4>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to end this event? All guests that have not checked in will be notified.</p>
                    </div>
                    <div class="modal-footer">
                        <input type="submit" value="Confirm" class="submit_button with_bg model-confi" (click)="endOpenHouse()">
                        <input type="submit" class="submit_button with_bg model-cancel" data-dismiss="modal" value="Cancel">
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="modal fade sign_modal" id="viewLeadModal" role="dialog">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="lilly_m_group">
                        <div class="lilly_title">{{leadDialog.user_name}}</div>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>

                    <div class="modal_content width_383">
                        <div class="lilly_modal">
                            <div class="lilly_stat_us">
                                <div class="li_status">Status</div>
                                <div *ngIf="leadDialog.user_status == 'U'" class="li_status_2">Unrepresented</div>
                                <div *ngIf="leadDialog.user_status == 'R'" class="li_status_2">Represented</div>
                            </div>
                            <div *ngIf="leadDialog?.user_phone != null" class="lilly_stat_us">
                                <div class="li_status">Phone</div>
                                <div class="li_status_2 color_green">{{leadDialog?.user_phone}}</div>
                            </div>

                            <div *ngIf="leadDialog?.user_phone != null" class="lilly_stat_us">
                                <div class="li_status">Email</div>
                                <div class="li_status_2 color_green">{{leadDialog?.user_email}}</div>
                            </div>

                            <div class="lilly_stat_us">
                                <div class="li_status">Origin Point</div>
                                <div *ngIf="leadDialog.origin_point == 'EV'" class="li_status_2 color_green">Event</div>
                                <div *ngIf="leadDialog.origin_point == 'LI'" class="li_status_2 color_green">Listing</div>
                            </div>
                            <div *ngIf="leadDialog.origin_point == 'EV'" class="lilly_stat_us">
                                <div class="li_status">Date</div>
                                <div class="li_status_2">{{leadDateFormat(leadDialog.origin_date)}}</div>
                            </div>
                            <div class="lilly_stat_us">
                                <div class="li_status">Property</div>
                                <div class="li_status_2">
                                    <img *ngIf="leadDialog.property_file != ''" src="{{leadDialog.property_file}}" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top"
                                        alt="">
                                    <img *ngIf="leadDialog.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top"
                                        alt="">
                                    <div class="dis_inline po_rel property-text-lead">{{leadDialog.address}}
                                        <br>
                                        <span>{{leadDialog.location}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="lilly_stat_us">
                                <div class="li_status">Listing Agent</div>
                                <div class="li_status_2">
                                    <img *ngIf="leadDialog.listing_agent_image != ''" src="{{leadDialog.listing_agent_image}}" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top"
                                        alt="">
                                    <img *ngIf="leadDialog.listing_agent_image == ''" src="{{imagePrefix}}default-placeholder.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top"
                                        alt="">
                                    <div class="dis_inline po_rel property-text-lead">{{leadDialog.listing_agent_name}}
                                        <br>
                                        <span>{{leadDialog.brokerage_name}}</span>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="leadDialog.origin_point == 'EV'" class="lilly_stat_us">
                                <div class="li_status">Rating</div>
                                <div class="li_status_2">
                                    <span>
                                        <img *ngIf="leadDialog.rating == '0'" src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown.png" class="check_icon back_check_2 thum-size"
                                            height="46px" width="46px" alt="">
                                        <img *ngIf="leadDialog.rating == '1'" class="check_icon back_check_2 thum-size" height="46px" width="46px" src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png">
                                    </span>
                                    <span class="icon-space" *ngIf="leadDialog.floorplan_rating == true">
                                        <img height="20" width="20" src="{{imagePrefix}}icon1.png" alt="">
                                    </span>
                                    <span class="icon-space" *ngIf="leadDialog.bedroom_rating == true">
                                        <img height="20" width="20" src="{{imagePrefix}}icon2.png" alt="">
                                    </span>
                                    <span class="icon-space" *ngIf="leadDialog.bathroom_rating == true">
                                        <img height="20" width="20" src="{{imagePrefix}}icon3.png" alt="">
                                    </span>
                                    <span class="icon-space" *ngIf="leadDialog.kitchen_rating == true">
                                        <img height="20" width="20" src="{{imagePrefix}}icon4.png" alt="">
                                    </span>
                                    <span class="icon-space" *ngIf="leadDialog.finishes_rating == true">
                                        <img height="20" width="20" src="{{imagePrefix}}icon5.png" alt="">
                                    </span>
                                    <span class="icon-space" *ngIf="leadDialog.landscaping_rating == true">
                                        <img height="20" width="20" src="{{imagePrefix}}icon6.png" alt="">
                                    </span>
                                    <span class="icon-space" *ngIf="leadDialog.neighbourhood_rating == true">
                                        <img height="20" width="20" src="{{imagePrefix}}icon7.png" alt="">
                                    </span>
                                </div>
                                <div class="li_status">

                                </div>
                                <div class="li_status_2">
                                    <span>{{leadDialog.notes}}</span>
                                </div>

                            </div>

                            <div class="lilly_stat_us">
                                <div class="li_status">
                                    <input  data-dismiss="modal" (click)="startNote(leadDialog)" type="submit" class="green_button green-archive" value="Notes">
                                    <span class="badge badge-notify" *ngIf="leadDialog.count">{{leadDialog.count}}</span>
                                </div>
                             </div>

                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>


<div class="modal fade note_modal " id="noteEvent" role="dialog">
    <div class="modal-dialog modal-lg modal-md">
       <div class="modal-content">
            <div class="">
                <div class="lilly_m_group">
                    <div class="lilly_title">Notes - {{leadDialog.user_name}}</div>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class=" modal-body modal_content ">
                    <div class="my_client_table" style="padding: 10px !important;">
                        <textarea name="" id="" cols="30" rows="10" (keyup)="isValidNote()" [(ngModel)]="newNote" class="my_client" placeholder="Type here to add a new note…."></textarea>
                        <div *ngIf="disableNotebtn == true" class="save_notes submit-disable">Save Note</div>
                        <div *ngIf="disableNotebtn == false" class="save_notes" (click)="SaveNote()">Save Note</div>
                        <div class="row"></div>
                        <div class="my_client_lists">
                            <div class="No_matches my_client_load" *ngIf="showMNLoader == true">
                                <div class="loader">
                                <div class="message">Loading...</div>
                                <div class="dots"><div class="center"></div></div>
                                </div>
                            </div>
                            <div class="my_client_list" *ngFor="let note of myNoteList; let i = index">
                                <div class="row">
                                <div class="col-sm-4">
                                    <div class="date">{{utcDateFormat(note.date)}}</div>
                                </div>
                                <div class="col-sm-9">
                                    <div *ngIf="updateNoteIndex != i" style="text-align: justify;" >{{note.note}}</div>
                                    <!-- class="text saved-note" -->
                                    <div *ngIf="updateNoteIndex == i">
                                            <textarea class="note-textarea" [value]="note.note" #noteValue id="note.id" cols="80" rows="3"></textarea>
                                            <div class="save_notes save-note" (click)="manageNote(note, 'UPDATE', noteValue.value, i)">Save Note</div>
                                            <a (click)="manageNote(note, 'CANCEL', '', i)"class="Cancel client cancel-note">Cancel</a>
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="edit">
                                            <i (click)="showNote(note, 'UPDATE', i)" class="fa fa-pencil edit_img"></i>
                                            <i (click)="showNote(note, 'DELETE', i)" class="fa fa-trash-o edit_img"></i>
                                    </div>
                                </div>
                                </div>
                            </div>
                            <div *ngIf="myNoteTotalCount > myNoteItemPerPage && myNoteTotalCount != myNoteList.length" class="new_form_group load_more_btn">
                                <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoretList('MN', myNoteIndex)" value="Load More">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


    <event-modal></event-modal>
    <div>
        <footer></footer>
    </div>
