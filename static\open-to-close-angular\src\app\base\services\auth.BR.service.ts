import { Injectable }     from '@angular/core';
import { CanActivate }    from '@angular/router';
import { AuthService } from '@app/auth/services/auth.service';
import { BaseComponent } from '@app/base/components/base.component';
import {Params,Router, ActivatedRoute } from '@angular/router';

@Injectable()
export class BRAuthGuard implements CanActivate {
    constructor(private authService: AuthService,private route:ActivatedRoute,private router:Router){
    }

    canActivate() {
       this.verifyUser(localStorage.getItem('userType'));
       return true;
    }

    verifyUser(user_type){
        if(user_type != "brokerage"){
            this.router.navigateByUrl('/');
        }
    }
}