import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BaseModule } from '@app/base/modules/base.module';
import { AuthComponent } from '@app/auth/components/auth.component';
import { AuthService} from '@app/auth/services/auth.service';
import { VerificationComponent } from '@app/auth/components/verification.component';
import { ResetPasswordComponent } from '@app/auth/components/reset-password.component';

@NgModule({
  imports: [
    CommonModule,
    BaseModule,
  ],
  declarations: [AuthComponent,VerificationComponent, ResetPasswordComponent],
  providers: [AuthService]
})

export class AuthModule { }
