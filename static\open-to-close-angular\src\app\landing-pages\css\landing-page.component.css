body
{
     margin: 0px;
     padding:0px;
    font-family: 'Source Sans Pro', sans-serif;

}
 #banner_page_1
{
    background:linear-gradient(to right, #9F9F9F 0%, #EBEBEB 100%);
    padding-bottom: 50px;
}
#banner_landing_page
{
    background: #ffffff;
    /*padding-bottom: 50px;*/
}
.star-icon {
    border-radius: 50%;
    background: #0fb8a8;
    color: #FFF;
    text-align: center;
    height: 30px;
    width: 30px;
    display: inline-block;
    margin-right: 10px;
}
#banner_landing_page .main-title .star-icon {
    height: 79px;
    width: 79px;
    box-shadow: rgb(0 112 102 / 65%) 0 12px 64px;
    line-height: 0;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: -20px;
}
.star-icon i {
    font-size: 23px !important;
    position: relative;
    top: 3px;
}
#banner_landing_page .main-title .star-icon i {
    font-size: 75px!important;
    top: 2px;
}
.space_1
{
    text-align: center;
    padding-top: 40px;
}
.space_1_h1
{
    font-size: 70px;
    line-height: 70px;
    color: #7a7a7a;

}

.heading-text {
    font-family: "Open Sans", sans-serif;
    font-weight: 800;
    font-size: 54px;
    line-height: 1;
    letter-spacing: -3px;
    color: #232335;
    padding: 50px;
}
.space-1-content,
.space-2-content,
.space-3-content,
.space-4-content {
    padding-top: 100px;
}
.space-3-content {
    padding-top: 70px;
}
.space-1-content,
.space-3-content {
    padding-left: 140px;
}
.space-2-content,
.space-4-content {
    padding-right: 140px;
}
.space-5-content {
    padding: 40px 60px 0px 60px;
}
.sub-heading {
    font-family: "Open Sans", sans-serif;
    font-weight: 800;
    font-size: 17px;
    line-height: 1.5;
    text-transform: none;
    padding: 0 !important;
    margin: 0 !important;
    color: #0FB8A8;
    text-align: left;
}
.sub-big-title {
    color: #ffffff;
    font-family: "Open Sans", sans-serif;
    font-weight: 800;
    font-size: 54px;
    line-height: 1.0;
    text-transform: none;
    padding: 0 !important;
    margin: 0 !important;
}
.sub-title {
    font-family: "Open Sans", sans-serif;
    font-weight: 800;
    font-size: 34px;
    line-height: 1.0;
    text-transform: none;
    color: #333;
    text-align: left;
    margin-top: 10px;
    margin-right: 10px;
    margin-bottom: 23px;
    margin-left: 0px;
}
.sub-content {
    color: #808080;
    font-family: "Montserrat",sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.45;
    padding: 0;
    word-wrap: break-word;
    text-align: left;
}
.space-5-content .sub-content {
    color: #FFF;
    margin-top: 20px;
    font-size: 18px;
}
.play-store-btn {
    margin: 50px 0px;
}
.play-store-btn img:first-child {
    margin-right: 50px;
}
.space_1_p {
    font-size: 22px;
    color: #808080;
    margin-top: 50px;
    margin-bottom: 35px;
    max-width: 50%;
    margin: 50px auto 35px auto;
}


#space_2
{
    background-color: #F4F8F8;
    padding:20px 0px 0px 0px;
}
.max_width {
    max-width: 60%;
}
.space_2_b
{
    font-weight:800;
    color: #5A5A5A;
}
.circle_img
{
    /* background-color: #d8d8d8; */
    /* border-radius: 50%; */
    /* border: 1px solid #979797; */
    /* width: 406px; */
    /*height: 406px;*/
}
#space_3
{
    background-color: #fff;
    padding: 70px 0px 0px 0px;
}

#space_4
{
    background-color: #F4F8F8;
    padding:50px 0px 0px 0px;
}

#space_5
{
    /*background-color: #eceff1;*/
    background-image: linear-gradient(213deg, #03E3CD 0%, #00B3A2 100%);
    padding: 50px 0px 50px 0px;
    margin-bottom: -65px;
}

#space_6
{
    background-color: #fff;
    padding:50px 0px 50px 0px;
}

.space_pricing
{
    background-color: #fff;
    padding:44px 0px 100px 0px;
    background-image: linear-gradient(213deg, #03E3CD 0%, #00B3A2 100%);
    /*padding-bottom: 50px;*/
}
.pricing-price {
    padding: 0 25px;
}
.specing_h2
{
    color: #FFFFFF;
    margin-bottom: 15px;
    font-family: "Open Sans", sans-serif;
    font-weight: 800;
    font-size: 54px;
    line-height: 1.5;
    letter-spacing: -3px;
}
.space_4_p
{
    font-size: 25px;
    color: #FFFFFF;
    margin-bottom: 60px;
    font-family: Montserrat, sans-serif;
    font-weight: 700;
    /*font-size: 18px;*/
    line-height: 1.45;
}
.Pricing_heading
{
    background:#f0f2f4;
    padding: 10px;
    font-size: 30px;
    color: #676767;
}


.btn-pricing_1 {
     width: 128px;
    border-radius: 4px;
    border: 1px solid #00c2b7;
    /* background: #ffffff; */
    color: #4cb8a7 !important;
    font-size: 16px;
    /* outline: 0; */
    margin: 10px 20px 48px 20px;
    display: inline-block;
    padding: 10px;
    text-decoration: none !important;
    cursor: pointer !important;
}

.premium-price-text {
    color: #10b8a8;
    font-size: 18px
}


.btn-pricing_2, .join-now-btn {
    background-color: #0FB8A8;
    width: 200px;
    display: inline-block;
    padding: 20px 0px;
    color: #fff !important;
    text-decoration: none !important;
    margin: 10px 0px;
    text-align: center;
    cursor: pointer !important;
    text-transform: uppercase;
    font-weight: 700;
    border: 1px solid #03ac9c;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    border-bottom-left-radius: 33px;
    border-bottom-right-radius: 30px;
    box-shadow: 0px 26px 55px 0px rgb(0 107 97 / 41%);
    font-size: 16px;
}
.join-now-btn {
    width: auto;
    padding: 20px 90px;
    margin-top: 30px;
}
.btn-pricing_2:hover,
.join-now-btn:hover {
    border: 1px solid #525252 !important;
    background: #5e5e5e !important;
}

.space-4-pricing-h2 {
    font-size: 54px !important;
    font-weight: 900 !important;
    line-height: 1.47 !important;
    color: #0FB8A8;
    margin-top: 0px !important;
}

.space-4-pricing-p
{
    font-size: 16px;
    color: #232335;
    /*padding-top: 10px;*/
    line-height: 27px;
    font-style: italic;
    font-family: Montserrat, sans-serif !important;
    font-weight: 400;
    line-height: 1.57;
    opacity: 0.9;
}
.space-4-pricing-p span,
.space-4-pricing-p sup {
    font-weight: 600 !important;
}
.space-4-pricing-p span.annual-price {
    font-size: 25px !important;
    line-height: 0 !important;
    font-style: normal;
}

.font-check.text-right
{
    padding:0px;
    position: relative;
    top: 4px;
}
.advice-p
{
    padding:0px;
}
.font-check
{
    color: #4cb8a7;

}
.advice
{
    padding-top: 20px;
    /*padding-bottom: 20px;*/
}
.advice ul
{
    padding: 0px
}
.advice-p
{
    font-size: 14px;
    padding-bottom: 18px;
    font-weight: 500;
    color: #dbdbdb;
}
.advice-b
{
    color:#7a7a7a;
}
.advice-c
{
    color:#10b8a8;
}
.advice-d
{
    color: #8d8d8d;
    font-size: 12px;
    color: #8d8d8d;
}


.Pricing_heading_premium {
    /*background: #10b8a8;
    padding: 10px;
    font-size: 30px;
    color: #ffffff;*/
    font-family: "Open Sans", sans-serif !important;
    font-weight: 800;
    font-size: 12px;
    line-height: 3;
    letter-spacing: 3.88px;
    text-align: center;
    margin-top: 20px;
}

.primunm_btn
{
    color: #ffff !important;
    background-color: #10b8a8 !important;
    border-radius: 4px;
    width: 145px;
    /*height: 83px;*/
}


a.sign_up_button.space_1_btn {
    background-color: #10b8a8;
    border-radius: 23.5px;
    width: 122px;
    padding: 10px;
    display: inline-block;
    color: #fff;
    text-decoration: none;
    cursor: pointer !important;
    font-weight: 600;
}


.show_border {
    /*border: 1px solid #c2c2c2;*/
    border-radius: 4px;
    float: left;
    width: 100%;
    margin-bottom: 20px;
    background-color: #ffffff;
    border-top-left-radius: 17px;
    border-top-right-radius: 17px;
    border-bottom-left-radius: 17px;
    border-bottom-right-radius: 17px;
    box-shadow: 0px 62px 62px 0px #00776b;
}

.advice {
    width: 100%;
    margin: auto;
}

.advice li {
    color: #232335;
    list-style-type: none;
    margin-bottom: 7px;
    font-weight: 600;
    margin-left: 5px;
    padding-left: 40px;
    position: relative;
    font-family: Montserrat, sans-serif;
    font-weight: 400;
    font-size: 15px;
    line-height: 2.28;
}

.advice li i {
    margin-right: 10px;
    color: #00c2b7;
    width: 20px;
    height: 20px;
    font-size: 25px !important;
    position: absolute;
    left: 0px;
    top: 2px;
}


.advice li.theme_color {
    color: #10b8a8;
}

.advice li.theme_color_disable , .advice li.theme_color_disable i {
    color: #dbdbdb;
}


.pricing-advise {
    border-top: .5px solid #c2c2c2;
    margin-top: 20px;
    padding-top: 20px;
    margin: 40px 40px 15px 40px;
}

h2.space-4-pricing-h2 sup {
    color: #808080;
    font-size: 22px !important;
    font-weight: 700 !important;
    top: 0px !important;
}


body {
    background: #fff !important;
}

.fl-row-content-wrap {
    padding-bottom: 120px !important;
}


.section5_landing_page .sec_5_text {
    color: #9c9c9c;
    font-size: 23px;
    font-weight: 400;
    text-align: center;
    margin-bottom: 60px;
    font-style: oblique;
}




/*.space_wrapper
{
    display: flex;
    vertical-align: middle;
    height: 100%;
    flex-direction: column;
    justify-content: center;
}

.circle_img {
    display: flex;
    height: 100%;
    vertical-align: middle;
    align-items: center;
    justify-content: center;
}*/

.circle_img img {
    height: 100%;
    width: 100%;
    object-fit: fill;
    background-color: #d8d8d8;
    border-radius: 50%;
    border: 1px solid #979797;
    width: 406px;
    height: 406px;
}

.full_img img{
  height: auto !important;
  width: 100% !important;
  -o-object-fit: fill;
  object-fit: fill;
}

.pricing_max_width {
    max-width: 85%;
    margin: auto;
}
.pricing_max_width .col-md-8 {
    padding-left: 30px;
    padding-right: 30px;
}
.duration-m-d{
  top: 0px !important;
  font-size: medium;
  text-align: start;
  vertical-align: text-top;
  margin: 15px 0px 0px 15px;
  display: inline-block;
  font-weight: 600;
}
.duration-m-s{
  /*vertical-align: sub;*/
  font-weight: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 3;
    text-align: center;
    color: #808080;
    font-family: "Montserrat",sans-serif;
}

.selected-plan-p {
  color: #ffffff !important;
}

.selected-plan-price sup {
  font-size: 28px;
  color: #737373;
}


.banner_p1 {
    font-size: 22px;
    font-weight: 700;
    color: #777777;
}

.banner_p2 {
    font-size: 22px;
    color: #808080;
}

.banner_p3 {
    font-size: 22px;
    color: #808080;
}

.line{
    width: 85.5em;
    border-width: 0.17em;
    border-color: #d3d3d3;
    margin-top: 5%;
    margin-bottom: 5%;
}

.pricing-introduction-title {
    font-size: 50px;
    padding-top: 1.5%;
    font-weight: 600;
    color: #7d7d7d;
    margin-bottom: 15px;
}

.pricing-introduction-description {
    font-size: 25px;
    color: #6d6d6d;
    padding-top: 15px;
}

.copyright_text {
    font-size: 1.5em;
    color: #676767;
    padding-top: 3.1em;
    margin-bottom: -0.2em;
    padding-left: 11%;
    padding-right: 11%;
}


@media only screen and (max-width: 1023px) {


    .pricing_max_width {
        max-width: 100%;
        margin: auto;
        float: left;
        width: 100%;
    }

    .pricing_max_width .col-sm-8 {
        float: left;
        width: 100%;
        margin-bottom: 20px;
    }


    p.space_1_p {
        max-width: 90%;
    }

    /*section#space_2 .row {
        display: block;
    }
    */

    .space_2_h1 {
        font-size: 40px;
        line-height: 40px;
    }

    .circle_img img {
        width: 306px;
        margin: auto;
        height: 306px;
        display: table;
    }

    .space_2_p {
      font-size: 16px;
      line-height: 25px !important;
      margin-top: 20px;
      margin-bottom: 20px;
    }

    .dis_flex_mobile
    {
        display: flex;
        flex-direction: column
    }

    .roder_1
    {
        order: 1
    }


    .roder_2
    {
        order: 2
    }


    .advice li {
        display: flex;
    }

    .advice li i {
        position: relative;
        top: 5px;
        right: 5px;
    }

    .max_width{
        max-width: 100%;
        text-align: center;
    }

    .line{
        width: 90%;
    }

    .pricing-introduction-title {
        font-size: 31px !important;
    }

    .pricing-introduction-description {
        font-size: 23px;
    }

}



@media only screen and (min-width: 1024px) {
    section#space_2 .row {
        display: flex;
    }
}

.banner_title_agent{
    color: #FFFFFF;
    font-family: "Open Sans", sans-serif !important;
    font-weight: 800;
    font-size: 54px;
    line-height: 0.9;
    letter-spacing: -3px;
    text-align: center;
    margin: 0 265px;
}
.banner_subcontent_agent {
    font-weight: 400;
    font-size: 18px;
    line-height: 1.45;
    text-align: center;
    font-family: "Open Sans", sans-serif !important;
    color: #FFFFFF;
    margin: 20px 265px;
}

#space_5
{
    background-color: #fff;
    padding: 50px 0px 50px 0px;
}

#space_6
{
    background-color: #eceff1;
    padding:50px 0px 50px 0px;
}

@media only screen and (max-width: 767px){
  .open_landing_page {
    padding-top: 30px;
  }

  .banner_p1{
    font-size: 18px;
  }

  .banner_p2{
    font-size: 18px;
  }

  .banner_p2_1{
    margin-left: -1%;
  }

  .banner_p3{
    font-size: 18px;
  }

  #space_3{
    padding: 50px 2% 50px 2%;
  }

  #space_2{
    padding: 50px 2% 50px 2%;
  }
  #space_3{
    padding: 50px 2% 50px 2%;
  }
  #space_5{
    padding: 50px 2% 50px 2%;
  }
  #space_6{
    padding: 50px 2% 50px 2%;
  }

  .space_2_p{
    font-size: 18px;
  }

  .specing_h2{
    font-size: 35px;
  }
  .space_4_p{
    margin-left: 5%;
    margin-right: 5%;
    font-size: 18px;
  }

  /* .pricing-mo{ */
    /* padding-left: 0px !important; */
    /* padding-right: 0px !important; */
  /* } */

  .show_border{
    border: 0px solid #c2c2c2;
  }

  .advice{
    width: 85%;
  }
  .banner_title_agent{
    color: #ffffff;
    font-size: 40px;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    position: relative;
    top: 15px;
  }

  .duration-m-s{
    font-size: 20px;
  }
  .duration-m-d{
    margin: 5px 0px 0px 0px;
    font-weight: 200;
  }
  .price-text-title{
    font-size: 18px;
    margin-left: 10px;
    font-weight: 500;
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
  }
  .space-4-pricing-p{
    /* line-height: 15px; */
  }
  .btn-pricing_2{
    margin: 20px 20px 10px 20px;
  }
  .pricing-mo{
    border-bottom: .5px solid #c1c2c1 !important;
  }
}

.price-col{
  color: #8d8d8d;
}


.duration-m-s{
    font-size: 20px;
  }
  .duration-m-d{
    margin: 5px 0px 0px 0px;
    font-weight: 200;
  }
.price-text-title{
    font-size: 18px;
    margin-left: 10px;
    font-weight: 500;
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
}
.free-pricing_1{
    padding-bottom: 14%;
}
.free-pricing_2{
  padding-bottom: 17%;
}
.main-title{
    display: inline-block;
    float: none;
}
