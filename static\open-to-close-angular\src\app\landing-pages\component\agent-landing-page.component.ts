import { Component, OnInit, ViewChild } from '@angular/core';

import { Subscription } from 'rxjs';

import { BaseComponent } from '@app/base/components/base.component';
import { HeaderComponent } from '@app/root/components/header.component';

import { GetPlansService } from '@app/base/services/get.plans.service';
import { ServiceLocator } from '@app/base/components/service-locator';

declare var $;

@Component({
  selector: 'agent-landing-page',
  templateUrl: '../views/agent-landing-page.component.html',
  styleUrls: ['../css/landing-page.component.css']
})
export class AgentLandingPageComponent extends BaseComponent implements OnInit {

  @ViewChild(HeaderComponent) headerComponent: HeaderComponent;

  public getPlansService: GetPlansService;

  public plansAPISubscription: Subscription;

  public plansList = [];

  constructor() {
    super();
    this.getPlansService = ServiceLocator.injector.get(GetPlansService);
  }

  ngOnInit() {

    $(document).ready(function ($) {
      $(this).scrollTop(0);
    });

    this.getPlans();
  }

  public openSignUpModal(): void {
    this.headerComponent.agentSignup = true;
    this.headerComponent.agentList = false;
    this.headerComponent.estateAgent = true;
    this.headerComponent.showAgent = false;
    this.headerComponent.showEstateAgent = true;
    this.headerComponent.isTitleSelected = true;
    this.headerComponent.estateProfessionalRegistration = true;
    this.headerComponent.homeBuyerRegistration = false;
    this.headerComponent.agentType = 'Real Estate Agent';
    $("#authModal").modal("show");
    $('#authModal .modal-body .nav-pills a:eq(' + 1 + ')').tab('show');
  }

  getPlans(): any {
    if (this.plansList.length <= 0) {
      if (this.plansAPISubscription) {
        this.plansAPISubscription.unsubscribe();
      }
      let urlParams: URLSearchParams = new URLSearchParams();
      urlParams.set("user_type", "LA");
      this.plansAPISubscription = this.getPlansService.getPlansByUserType(urlParams).subscribe(res => {
        let tempList = [];
        this.plansList = res.result.filter(plan => {
          if (plan.billing_frequency === 1) {
            plan['annualPrice'] = this.Dec2(Number(12) * Number(plan.price))
          }
          if (plan.billing_frequency === 12) {
            const planPrice = Number(plan.price) - Number(0.01)
            plan['monthlyPrice'] = this.Dec2(Number(planPrice) / Number(12))
          }
          console.log(plan)
          tempList.push(plan);
          return tempList;
        });
      });
    }
  }

  Dec2(num) {
    num = String(num);
    if(num.indexOf('.') !== -1) {
      var numarr = num.split(".");
      if (numarr.length == 1) {
        return Number(num);
      }
      else {
        return Number(numarr[0]+"."+numarr[1].charAt(0)+numarr[1].charAt(1));
      }
    }
    else {
      return Number(num);
    }  
  }

  redirectToApp(url){
    window.open(url);
  }


}
