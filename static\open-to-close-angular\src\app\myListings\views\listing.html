<div class="listing-header">
  <header></header>
</div>

<div class="listing-body">
  <search-bar (googleMapPosition)="setMapPosition($event)" (polygonListEvent)="drawPolygons($event)" (searchObjEvent)="getSearchObj($event)" [listType]='searchListType' [pageNo]='searchPageNo' [currentPage]="'listingAgent'" (polygonErrorHandle)="polygonErrorHandling()"></search-bar>
    <div class="google_map">
      <div class="display_none_map">
      <div class="map_icons">
        <span><div class="map_icon1" id="draw"><a href="#"><img *ngIf="!showCancelDraw" (click)="removeSearchValue()" src="{{imagePrefix}}Icon_Copy_3.png" class="map_icon cursor-pointer" alt=""></a></div></span>
        <span><div class="map_icon1 mobile_cancelDraw" id="cancelDraw"><a><img *ngIf="showCancelDraw" src="{{imagePrefix}}Delete-xhdpi.png" class="cancel-icon map_icon cursor-pointer" alt=""></a></div></span>
      </div>
      <div class="map_icons_location">
        <div class="map_icon1"><img (click)="getCurrentLocation()" src="{{imagePrefix}}locate.png" class="map_icon cursor-pointer" alt=""></div>
      </div>

        <div id="map"></div>
        <div class="show_map_button visible-xs show_list">Show List</div>
        <div *ngIf="showMapLoading" class="map-result-loader">
          <div class="loading-bg-img">
            <img src="{{imagePrefix}}loading.gif" class="loading-map-icon cursor-pointer" alt=""> Loading...
          </div>
        </div>
      </div>

      <div [ngClass]="{'map_right_rail': addLoaderClass}">
        <span *ngIf="showMapLoading">
          <div class="right_bar">
            <div class="right-loading-bg"></div>
          </div>
        </span>

      <div class="map_side_bar">
        <div class="map_icons">
        <div class="map_icon1"><img (click)="showListingDetail()" src="{{imagePrefix}}Icon.png" class="map_icon cursor-pointer" alt=""></div>
        <div class="map_icon1"><img (click)="mapZoomIn()" src="{{imagePrefix}}Icon_Copy.png" class="map_icon cursor-pointer" alt=""></div>
        <div class="map_icon1"><img (click)="mapZoomOut()" src="{{imagePrefix}}Icon_Copy_2.png" class="map_icon cursor-pointer" alt=""></div>
        <span><div class="map_icon1" id="draw"><a href="#"><img *ngIf="!showCancelDraw" (click)="removeSearchValue()" src="{{imagePrefix}}Icon_Copy_3.png" class="map_icon cursor-pointer" alt=""></a></div></span>
        <span><div class="map_icon1" id="cancelDraw"><a><img *ngIf="showCancelDraw" src="{{imagePrefix}}Delete-xhdpi.png" class="map_icon cancel-icon cursor-pointer" alt=""></a></div></span>
      </div>
      <div class="map_icons_location">
        <div class="map_icon1"><img (click)="getCurrentLocation()" src="{{imagePrefix}}locate.png" class="map_icon cursor-pointer" alt=""></div>
      </div>

      <div>
        <div class="col-sm-16 mb-5 padding-0 my-listing-header">
          <div class="title dis_inline ">My Listings</div>
          <div *ngIf="paidAccount" class="add_new_list dis_inline cursor-pointer" (click)="addNewProperty()">Add New</div>
        </div>
      </div>

        <div id="scroll"class="map_listing">
          <div class="home_group">
            <div *ngIf="propertysList.length == 0">
              <div class="no_match_found">
                  <div class="title2">No matches</div>
                  <div class="note_no_match">You don’t have any listings that meet your search criteria. Try running a new search or add a new listing. </div>
              </div>
            </div>

            <div *ngIf="propertysList.length != 0">
              <div class="home" *ngFor="let item of propertysList | paginate: {itemsPerPage:itemsPerPage, currentPage: pageCount,totalItems :totalPorpertyCount}">
                <a class="cursor-pointer" (mouseenter)="showPropertyMarker(item)" (mouseleave)="closeAllPorpertyMarkers()" (click)="gotToPropertyDetail('my-listing/property-detail',item.id)">
                  <div class="home_image cursor-pointer image-gradient">
                    <img *ngIf="item.property_file !=''"  src="{{item.property_file}}" class="symbols-map-hover" alt="">
                    <img *ngIf="item.property_file ==''"  src="{{imagePrefix}}symbols-map-hover.png" class="symbols-map-hover" alt="">
                  </div>

                  <div class="hom_details">
                    <div class="status">
                      <span *ngIf="item.property_status == 'Active'">
                        <img class="property-status-icon" src="{{imagePrefix}}active.png">
                      </span>

                      <span *ngIf="item.property_status == 'Pending'">
                        <img class="property-status-icon" src="{{imagePrefix}}pending.png">
                      </span>

                      <span *ngIf="item.property_status == 'PRE-MLS/Coming Soon'">
                        <img class="property-status-icon" src="{{imagePrefix}}preMLS.png">
                      </span>

                      <span *ngIf="item.property_status == 'Off Market'">
                        <img class="property-status-icon" src="{{imagePrefix}}off-market.png">
                      </span>

                      {{item.property_status}}
                    </div>

                    <div class="amount">{{item.home_price | currency:"":symbol:"1.0"}}</div>
                    <div class="address map-property-address">{{item.street}} {{item.location}}</div>
                    <div class="sub_details">{{item.bedroom}} bds  | {{item.full_bath}} ba  |  {{item.living_area}} sq ft</div>
                  </div>
                </a>

                <div class="map_left_icon">
                  <span *ngIf="item.is_favourite == false">
                      <img  src="{{imagePrefix}}symbols-glyph-nav-favorite.png" class="symbols-glyph-nav-favorite cursor-pointer" (click)="addToFavorite(item.id,item)" alt="">
                  </span>

                  <span *ngIf="item.is_favourite == true">
                    <img  src="{{imagePrefix}}favorited.png" class="symbols-glyph-nav-favorite cursor-pointer" (click)="addToFavorite(item.id,item)" alt="">
                  </span>

                  <div *ngFor="let event of item.event_list">
                    <div *ngIf="event.event_type == 'BO'" class="home_date" (click)="selectedEvent('brokerOpen',event)">
                      <span class="day color_1">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span>
                      <span class="date title">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</span>
                    </div>

                    <div *ngIf="event.event_type == 'AO'" class="home_date" (click)="selectedEvent('appointmentOnly',event)">
                      <span class="day color_2">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span>
                      <span class="date title">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</span>
                    </div>

                    <div *ngIf="event.event_type == 'OH'" class="home_date" (click)="selectedEvent('openHouse',event)">
                        <span class="day color_3">{{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span>
                        <span class="date title">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="totalPorpertyCount > itemsPerPage">
                <span *ngIf="pageCount !=1">
                    <pagination-controls  (pageChange)="getpage($event)"
                      previousLabel="PREVIOUS"
                      maxSize="6"
                    nextLabel="NEXT">
                  </pagination-controls>
                </span>

                <span *ngIf="pageCount ==1">
                  <pagination-controls  (pageChange)="getpage($event)"
                    previousLabel=""
                    maxSize="6"
                    nextLabel="NEXT">
                  </pagination-controls>
                </span>
              </div>
            </div>
            <div (click)="showMap()" class="show_map_button visible-xs show_map">Show Map</div>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>

<event-modal (setPropertyLatestInfo)="UpdatePropertyInfo($event)"></event-modal>

<div class="listing-footer">
    <footer [ngClass]="{'footer-class': propertysList.length == 0}"></footer>
</div>
