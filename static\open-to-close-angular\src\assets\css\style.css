/*semi bold font-family: 'Source Sans Pro', sans-serif;
/*@import url('https://www.fontify.me/wf/c61fba03e48c28001e90eba4b87ca238'); */

/*normal font font-family: 'Source Sans Pro', sans-serif;
@import url('https://www.fontify.me/wf/4dae5b9279578bbce15473cde2ae897f');  */

/*title font-family: 'Source Sans Pro', sans-serif;
@import url('https://www.fontify.me/wf/7116669ff62ea6ce905b50766eaca6b8'); */

/* font-family: 'Source Sans Pro', sans-serif;
 @import url('https://www.fontify.me/wf/be3b96092eb68c3b54ca6718c1777f8c');   */
 @media only screen and (min-width:1200px) {
    .container
    {
             width: 1194px !important;
    }
}
 @font-face {
   font-family: 'Source Sans Pro', sans-serif;
  src: url(../fonts/SF-Compact-Text-Semibold.woff);
}

  @font-face {
  font-family: 'Source Sans Pro', sans-serif;
  src: url(../fonts/SF-UI-Text-Regular.woff);
}

  @font-face {
  font-family: 'Source Sans Pro', sans-serif;
  src: url(../fonts/SF-UI-Display-Light.woff);
}

   @font-face {
  font-family: 'Source Sans Pro', sans-serif;
  src: url(../fonts/SF-UI-Display-Regular.woff);
}


.semi_bold {
    font-family: 'Source Sans Pro', sans-serif;
}
body {
    font-family: 'Source Sans Pro', sans-serif;
    background-color: #F0F2F4 !important;
}
.select_mate {
    font-family: 'Source Sans Pro', sans-serif;
}
ul.nav.navbar-nav li a , ul.nav.navbar-nav li a:hover , ul.nav.navbar-nav li a:focus{
   font-size: 16px;
   color: #4e4e4e;
   font-weight: 600 !important;
   padding: 15px 10px;
   font-family: 'Source Sans Pro', sans-serif;
}
a.navbar-brand img {
    height: 25px;
    width: 172px;
    object-fit: contain;
}
nav.navbar.navbar-inverse {
    background: #FFFFFF;
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.14);
    border: 0px;
    margin: 0px;
    border-radius: 0px;
}
.homepage_slider {
    position: relative;
}
.homepage_text {
    position: absolute;
    top:0%;
    left: 10%;
}
.homepage_text .sub_title {
    font-size: 25px;
    color: #FFFFFF;
    font-family: 'Source Sans Pro', sans-serif;
}
.homepage_text .text {
    font-size: 16px;
    font-family: 'Source Sans Pro', sans-serif;
    color: #FFFFFF;
    margin-bottom: 23px;
}
.homepage_slider img.img-responsive {
    height: 482px;
    width: 100%;
}
.homepage_text .title {
    font-size: 70px;
    color: #FFFFFF;
    font-family: 'Source Sans Pro', sans-serif;
    line-height: 70px;
    margin-bottom: 25px;
    margin-top: 60px;
}
.input-group.stylish-input-group input.form-control , .input-group.stylish-input-group input.form-control:focus {
    width: 373px;
    outline: 0 !important;
    margin-bottom: 30px !important;
    border: 0px !important;
    box-shadow: none !important;
    border-radius: 3px;
}
.input-group.stylish-input-group span.glyphicon.glyphicon-search {
    color: #10B8A8;
    padding: 9px;
    right: 3rem;
    z-index: 11;
    cursor: pointer;
    position: absolute;
    font-size: 24px;
    top: 1.5rem;
}
.search_submit input {
    background: #10B8A8;
    color: white;
    border: 0px;
    width: 99px;
    height: 30px;
    border-radius: 13px;
    padding: 0px;
}
.homepage_content {
    margin-top: 60px;
    margin-bottom: 113px;
}
.homepage_content .title {
    font-size: 26px;
    text-align: center;
    margin-bottom: 16px;
}
.homepage_content .text {
    text-align: center;
    margin-bottom: 16px;
    font-size: 12px;
}
.footer {
    background: #10B8A8;
}
.footer ul {
    padding: 0px;
    margin: 0px;
    text-align: center;
}
.footer ul li a {
    font-size: 16px;
    color: #FFFFFF;
    text-decoration: none;
}
.footer ul li {
    display: inline-block;
    padding: 21px 10px 21px 7px;
}
a.footer_logo img.img-responsive {
    width: 163px;
}
a.footer_logo {
    display: inline-block;
    position: relative;
    top: 8px;
}
img.img-responsive.header_icon {
    height: 20px;
    /* width: 20px; */
}
ul.nav.navbar-nav.navbar-right a , ul.nav.navbar-nav.navbar-right a:hover , ul.nav.navbar-nav.navbar-right a:focus {
    padding: 15px;
    outline: 0;
}
a {
    outline: 0 !important;
}
.sign_modal .modal-content {
    width: 365px;
    display: table;
    margin: auto;
}
.sign_modal .modal-body {
    background: #DCDCDC;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.17);
    padding: 1px;
    border-radius: 0px;
    float: left;
}
.sign_modal .modal_content{
    background: #FFFFFF;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
    padding: 10px 20px;
    border-radius: 0px;
}
.modal_content ul.nav.nav-pills li a {
    font-size: 16px;
    color: #7A7A7A;
    letter-spacing: 0;
    border-radius: 0px;
    padding-bottom: 5px;
}
.nav-pills>li.active>a, .nav-pills>li.active>a:hover, .nav-pills>li.active>a:focus {
    color: #fff;
    background-color: transparent !important;
    border-bottom: 4px solid #10B8A8;
}
.nav-pills>li>a, .nav-pills>li>a:hover, .nav-pills>li>a:focus {
    background-color: transparent !important;
}
ul.nav.nav-pills {
    border-bottom: 2px solid #C2C2C2;
    position: relative;
}
.new_account .title {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
    margin-bottom: 15px;
    margin-top: 15px;
}
.font-weight-title{
    font-weight: 610;
}
input::-webkit-input-placeholder {
    font-size: 16px;
    font-family: 'Source Sans Pro', sans-serif;
    color: #8D8D8D;
}
input::-moz-placeholder {
    font-size: 16px;
    font-family: 'Source Sans Pro', sans-serif;
    color: #8D8D8D;
}
input:-ms-input-placeholder {
    font-size: 16px;
    font-family: 'Source Sans Pro', sans-serif;
    color: #8D8D8D;
}
input:-moz-placeholder {
    font-size: 16px;
    color: #8D8D8D;
    font-family: 'Source Sans Pro', sans-serif;
}
input.new_form {
    background-color: #FFFFFF;
    border: 1px solid #DBDBDB;
    border-radius: 2px;
    width: 100%;
    padding: 10px 7px;
    margin-top: 14px;
    outline: 0;
    margin-bottom: 2px;
    font-size: 16px;
    color: #5A5A5A;
}
.form_group label{
    font-size: 16px;
    color: #8D8D8D;
    box-shadow: none;
    color: #8D8D8D;
    font-weight: normal;
}
.check_group .form_group {
    margin-bottom: 16px;
    margin-top: 0px;
}
.check_group .form_group input[type="checkbox"] {
    position: relative;
    top: 2px;
}
.form_group select {
    background-color: #FFFFFF;
    font-size: 16px;
    color: #8D8D8D;
    border: 1px solid #DBDBDB;
    border-radius: 2px;
    width: 100%;
    padding: 10px 7px;
    margin-top: 14px;
    outline: 0;
    margin-bottom: 2px;
    height: 100% !important;
}
.form_group input[type="submit"] {
    background: #10B8A8;
    color: white;
    border-radius: 20px;
    width: 100px;
    padding: 8px 2px;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
}
.or_line hr {
    border: 1px solid #DBDBDB;
    margin: 14px 0px 29px 0px;
}
.or_line {
    position: relative;
}
.or {
    position: absolute;
    top: -21px;
    left: 45%;
    background: white;
    padding: 10px;
    border-radius: 40px;
    font-size: 16px;
    color: #8D8D8D;
}
.Connect {
    background: #3B5998;
    padding: 8px;
    text-align: center;
    font-family: 'Source Sans Pro', sans-serif;
    border-radius: 40px;
    width: 188px;
    margin: auto auto 21px auto;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
}
.modal_footer {
    background: #E0F2F1;
    font-size: 12px;
    padding: 22px;
    color: #8D8D8D;
}
.modal_footer p {
    margin: 0px;
}
.modal_footer a {
    text-decoration: none;
    font-size: 12px;
    color: #10B8A8;
    line-height: 15px;
}
input.new_form.disable {
    opacity: 0.4;
}
.remove_details .name{
    font-size: 16px;
    color: #8D8D8D;
}
.remove_details .sub_name{
    font-size: 12px;
    color: #8D8D8D;
}
.remove_details .remove_button {
    border: 1px solid #F06292;
    border-radius: 100px;
    color: #F06292;
    display: table;
    padding: 4px 10px;
    font-size: 14px;
    margin-top: 10px;
    cursor: pointer;
}
.remove_agent {
    display: flex;
}
.remove_agent img {
    margin-right: 27px;
}
.remove_details {
    vertical-align: middle;
    align-items: center;
    justify-content: center;
    margin-top: 27px;
}
.remove_button.select {
    border: 1px solid #10B8A8;
    color: #10B8A8;
}
img.img-responsive.search {
    position: absolute;
    height: 20px;
    width: 20px;
    right: 8px;
    top: 41%;
    cursor: pointer;
}
.form_group {
    position: relative;
}
.agent_found {
    font-size: 16px;
    color: #5A5A5A;
    margin-bottom: 15px;
}
img.img-responsive.drop_down {
    height: 20px;
    width: 20px;
    position: absolute;
    right: 8px;
    top: 26px;
}
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    text-indent: 1px;
    text-overflow: '';
}
.check_group .form_group input[type="checkbox"] {
    opacity: 0;
    cursor: pointer;
    z-index: 111;
    margin-right: 8px;
}
.check_group .form_group input[type="checkbox"]:checked ~ .checkmark {
    background-image: url("../images/symbols-glyph-form-check.png");
    background-size: 100% 100%;
}
.checkmark {
    position: absolute;
    top: 6px;
    left: 0px;
    height: 14px;
    width: 12px;
    border: 1px solid #eee;
}
.splash_screen {
    background: url(../images/Rectangle_spl.jpg);
    height: calc(100vh);
    /* width: 100vw; */
    background-size: cover;
}
.splash_logo img , .splash_logo div {
    display: inline-block;
    font-size: 12px;
    color: #7A7A7A;
    letter-spacing: 0;
}
.splash_logo {width: 90%;margin: auto;padding-top: 40px;padding-bottom: 45px;}
.splash_text{
    opacity: 0.7;
    background: #000000;
}
.splash_text .title{
    font-size: 70px;
    color: #FFFFFF;
    line-height: 70px;
    margin-bottom: 27px;
}
.splash_text .text{
    font-size: 25px;
    color: #FFFFFF;
}
.splash_text p{
    font-size: 16px;
    color: #FFFFFF;
    margin-bottom: 0px;
}
.splash_text {
    opacity: 0.7;
    background: #000000;
    padding: 40px;
    position: relative;
    max-width: 50%;
    border-left: 7px solid #10B8A8;
    left: 8%;
}
/* Profile Page Css */
.profile_modal_title {
    background: #10B8A8;
    padding: 30px 36px;
    font-size: 25px;
    color: #FFFFFF;
}
div#profile_modal_1 .modal-body {
    padding: 0px;
}
div#profile_modal_1 .modal-content {
    width: 430px;
}
.individual_detail {
    background: #F0F2F4;
    font-size: 16px;
    color: #8D8D8D;
    padding: 15px 23px;
    margin-top: 15px;
    margin-bottom: 21px;
}
.individual_plans {
    border-bottom: 1px solid #C2C2C2;
   /* padding-bottom: 10px;
    */
}
.individual_plans .individual_plan {
    border-top: 1px solid #C2C2C2;
    padding: 17px 0px;
}
.individual_plans .individual_plan .time {
    font-size: 16px;
    color: #5A5A5A;
    float: left;
    margin: 18px 0px;
}
.individual_plans .individual_plan .price span {
    font-size: 40px;
    color: #10B8A8;
    letter-spacing: -1px;
}
.individual_plans .individual_plan .price{
    font-size: 16px;
    color: #8D8D8D;
   /* margin: 0px 53px;
    */
}
.individual_plans .individual_plan .Upgrade{
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    background: #10B8A8;
    padding:7px 23px 6px 23px;
    float: right;
    text-align: center;
    border-radius: 40px;
    margin: 18px 0px;
    cursor: pointer;
    width: auto;
}
.individual_plan div {
    display: inline-block;
    width: 32%;
}
.cancle a , .cancle a:hover {
    font-size: 12px;
    color: #10B8A8;
    text-decoration: none;
}
.cancle {
    margin: 18px 0px;
    text-align: center;
}
/* Endof Profile Page Css */
.profile_page_1.artboard2_page{
    background: #EFF2F4;
}
.artboard2 {
    background: #FFFFFF;
    border-radius: 4px;
}
.artboard2 {
    background: #FFFFFF;
    border-radius: 4px;
    width: 700px;
    margin-top: 33px !important;
    margin: auto;
    padding-top: 70px;
    padding-bottom: 50px;
    text-align: center;
}
.artboard2 .title {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
    margin-bottom: 5px;
}
.artboard2 .sub_title {
    font-size: 16px;
    color: #8D8D8D;
    margin-bottom: 23px;
}
.artboard2 hr {
    width: 79%;
    border-top: 1px solid #C2C2C2;
}
.atr_title2 {
    font-size: 16px;
    color: #5A5A5A;
}
.art_sub_title {
    font-size: 16px;
    color: #8D8D8D;
    margin-bottom: 35px;
    margin-top: 5px;
}
.profile {
    background: #10B8A8;
    font-size: 12px;
    color: #FFFFFF;
    padding: 9px 25px 8px 25px;
    letter-spacing: 0;
    display: inline-block;
    border-radius: 43px;
    cursor: pointer;
}
.artboard3{
    background: #FFFFFF;
    border-radius: 4px;
    width: 700px;
    margin-top: 33px !important;
    margin: auto;
    padding: 40px 80px;
    margin-bottom: 64px;
}
.artboard3 .Your_Order {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
}
.border {
    border:1px solid #c2c2c269;
    margin-top: 8px;
}
.bill_details .name {
    font-size: 16px;
    color: #5A5A5A;
}
.bill_details .Agents {
    font-size: 16px;
    color: #8D8D8D;
}
.bill_details span {
    font-size: 40px;
    color: #10B8A8;
    letter-spacing: -1px;
}
.bill_details .price {
    font-size: 16px;
    color: #8D8D8D;
    position: relative;
    top: -10px;
}
.bill_detail {
    margin-top: 17px;
}
.dis_inline {
    display: inline-block !important;
}
.bill_detail {
    margin-top: 15px;
    margin-bottom: 15px;
}
.total_price {
    float: right;
    display: flex;
    margin-top: 11px;
    font-size: 16px;
    color: #5A5A5A;
}
.total_price_ {
    font-size: 16px;
    color: #5A5A5A;
}
.mt-70 {
    margin-top: 70px;
}
i.glyphicon.glyphicon-question-sign {
    font-size: 13px;
    color: #676767;
    border: 1px solid #676767;
    padding: 2px;
}
.check_group.payment_pathod label {
    margin-left:12px;
}
form.pay_ment_form label {
    margin: 0px !important;
}
form.pay_ment_form input.new_form {
    margin-top: 5px;
}
form.pay_ment_form .form_group {
    margin: 10px 0px;
}
form.pay_ment_form input.new_form {
    margin-top: 5px;
    padding: 8px 7px;
}
.mt-10 {
    margin-top: 20px;
}
.form_group.c_order input.new_form {
    display: table;
    margin: auto;
    padding: 9px 11px !important;
    width: auto;
}
.form_group.c_order {
    margin-top: 30px !important;
}
.line.dis_inline.text-center {
    float: left;
    margin-top: 40px;
}
.profile_page h1.dis_inline {
    font-size: 40px;
    color: #737373;
    letter-spacing: -1px;
    margin: 0px;
}
img.img-responsive.user_profile {
    height: 52px;
    width: 52px;
}
.profile_page {
    display: flex;
    justify-content: space-between;
}
.profile_page_1.artboard2_page.profile_page_2 input.new_form{
    background: #E4F8F8;
}
.profile_page_2 label {
    width: 40%;
    display: flex;
    vertical-align: middle;
    align-items: center;
    margin-top: 14px;
}
.profile_page_2 .form_group {
    display: flex;
    justify-content: space-between;
    margin-top: 21px;
    margin-bottom: 10px;
}
.col-3 input.new_form {
    width: auto;
}
.col-3 label {
    width: auto;
}
.check_group.profile_checkbox .form_group {
    display: block;
}
.check_group.profile_checkbox .form_group * {
    display: inline-block;
    margin: 0px;
}
.dis_flex {
    display: flex;
    justify-content: space-between;
}
.flex_none {
    display: block !important;
    width:auto !important;
}
.width_auto{
    width: auto !important;
}
.edit_profile span{
    font-size: 12px;
    color: #10B8A8;
    border: 1px solid #10B8A8;
    border-radius: 100px;
    padding: 7px 10px 4px 10px;
    margin-top: 5px;
    float: right;
}
.width_68{
    width: 68%;
}
.width_10{
    width:10%;
}
.width_100 {
    width: 100% !important;
}
.no_margin {
    margin:0px;
}
.display_flex {
    display: flex;
}
.artboard6_sidebar {
    background: #FFFFFF;
    border-radius: 4px;
    width: 345px;
    padding: 10px 30px;
    margin-left: 20px;
    margin-top: 33px;
}
.profile_page_1.artboard2_page.profile_page_2 {
    padding-bottom: 30px;
    height: 100%;
    padding-bottom: 30px;
}
.display_flex_new{
    display: flex;
    justify-content: center;
    vertical-align: middle;
}
.Account {
    font-size: 17px;
    color: #8D8D8D;
    line-height: 30px;
    margin-top: 29px;
}
.account_mtp{
   margin-top: 8px !important;
}
.Account span {
    font-size: 22px;
    color: #10B8A8;
    line-height: 30px;
}
.Account_text {
    font-size: 16px;
    color: #8D8D8D;
    margin-top: 7px;
    margin-bottom: 8px;
}
.a_10 span {
    font-size: 16px;
    color: #10B8A8;
}
.a_10 {
    margin-top: 13px;
    font-size: 16px;
    color: #10B8A8;
}
.Manage {
    background: #10B8A8;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    display: inline-block;
    padding: 7px 12px 6px 12px;
    border-radius: 20px;
    margin-top: 18px;
}
.Refer {
    font-size: 16px;
    color: #5A5A5A;
    margin-top: 7px;
    margin-bottom: 10px;
}
.artboard7_side p.Account_text {
    margin-bottom: 10px;
    margin-top: 2px;
}
.artboard7_side .Manage {
    margin-top: 0px;
    margin-bottom: 12px;
    min-width: 100px;
    text-align: center;
}
.artboard7_side .Refer{
    margin-top: 18px;
    margin-bottom: 0px;
}
.artboard6_sidebar.artboard7_side .form_group {
    margin: 0px;
}
.artboard6_sidebar.artboard7_side input.new_form {
    margin: 0px;
}
.artboard6_sidebar.artboard7_side{
    padding-bottom: 30px;
}
.preferred {
    margin-top: 17px;
    margin-bottom: 25px;
}
.preferred .name {
    font-size: 16px;
    color: #8D8D8D;
}
.preferred .sub_name {
    font-size: 12px;
    color: #8D8D8D;
}
.user_profile_2{
    display: inline-block !important;
    height: 107px;
    width: 107px;
}
.perferred_details {
    margin-top: 30px;
}
.artboard6_sidebar.artboard7_side .form_group {
    display: block;
    margin-bottom: 25px;
}
.artboard6_sidebar.artboard7_side input.new_form {
    background: transparent !important;
}
.Manage_group {
    background: #FFFFFF;
    border: 1px solid #10B8A8;
    padding: 13px;
    margin-top: 18px;
}
.Manage_group .Invite {
    font-size: 16px;
    color: #5A5A5A;
}
.Invite_agents {
    font-size: 16px;
    color: #8D8D8D;
}
.Manage_group {
    background: #FFFFFF;
    border: 1px solid #10B8A8;
    padding: 13px;
    margin-top: 18px;
}
.Manage_group .Invite {
    font-size: 16px;
    color: #5A5A5A;
}
.Your_Order.mt-10 {
    margin-bottom: 10px;
}
.Invite_agents {
    font-size: 16px;
    color: #8D8D8D;
}
.Manage_group .form_group {
    margin: 0px;
}
.Manage_group .form_group input.new_form {
    margin-top: 6px;
}
.search_submit.dis_inline {
    float: right;
    margin-top:1px;
}
.input-group.stylish-input-group.dis_inline input.form-control {
    width: 400px;
}
table.table.table-condensed.table-responsive.artboard3_tabel thead {
    background: #F0F2F4;
    width: 100%;
}
table.table.table-condensed.table-responsive.artboard3_tabel th , table.table.table-condensed.table-responsive.artboard3_tabel td {
    vertical-align: middle;
    font-size: 16px;
    color: #5A5A5A;
    font-weight: normal;
}
.old_form {
    display: block !important;
}
.form_group.old_form input.new_form {
    background: transparent !important;
    margin:0px;
}
.form_group.old_form {
    margin: 0px;
}
.dis_flex_group {
    display: flex;
}
.dis_flex_group label {
    width: 189px;
    display: inline-block;
}
.dis_flex_group input.new_form {
    width: 164px;
}
.dis_flex_group .form_group.old_form .background_color_form{
    background: #E4F8F8 !important;
}
.width_400{
    width: 400px !important;
}
.dis_flex_group.abr8 .form_group.old_form {
    width: 187px;
    margin-top: 21px;
}
.width_300 {
    width: 250px !important
}
.profile_page_1.artboard2_page.profile_page_2 .artboard6_sidebar.artboard7_side .back_color{
    background: #E4F8F8 !important;
}
.width_110 {
    width: 132px !important;
}
.edit_profile.width_110 a.Cancel {
    font-size: 12px;
    color: #10B8A8;
    margin-top: 12px;
    float: left;
    text-decoration: none;
}
.no_background {
    background: transparent !important;
}
.side_bar_height {
    height: 100%;
    padding-bottom: 20px;
}
.save_change {
    background: #10B8A8;
    color: white !important;
}
/* Start Of My Client Pages Css */
.myclient_page {
    background: #F0F2F4;
    padding: 30px 0px;
}
.ls{
    font-size: 40px;
    color: #FFFFFF;
    position: relative;
    letter-spacing: -1px;
    line-height: 40px;
    background: #10B8A8;
    display: inline-block;
    padding: 10px 8px;
    border-radius: 40px;
    top: -15px;
    height: 60px;
    width: 60px;
}
.ls_group .title{
    font-size: 40px;
    color: #737373;
    display: inline-block;
    letter-spacing: -1px;
    font-family: 'Source Sans Pro', sans-serif;
}
.ls_group .sub_title {
    font-size: 16px;
    color: #8D8D8D;
}
.ls_group .title_group {
    display: inline-block;
    margin-left: 25px;
    position: relative;
    top: -4px;
}
.ls_group .title_group span{
    font-size: 16px;
    color: #5A5A5A;
}
.Message_Lilly.pull-right {
    border: 1px solid #10B8A8;
    border-radius: 100px;
    font-size: 12px;
    color: #10B8A8;
    padding: 7px 10px 5px 10px;
    margin-top: 6px;
    cursor: pointer;
}
.myclient_navbar {
    background: #566D77;
}
.myclient_navbar ul {
    padding: 0px;
    margin-bottom: 0px;
    padding-top: 13px;
    padding-bottom: 10px;
}
.myclient_navbar ul li {
    list-style-type: none;
    display: inline;
    font-family: 'Source Sans Pro', sans-serif;
    cursor: pointer;
    margin-right: 5px;
    padding-left: 5px;
    padding-right: 8px;
    margin-left: 25px;
    font-size: 25px;
    color:#acbabd;
}
.myclient_navbar ul li.active {
    color:white;
    border-bottom: 4px solid #10B8A8;
    padding-bottom: 9px;
}
.my_client_table {
    background: #FFFFFF;
    border-radius: 0 0 4px 4px;
    padding: 40px;
}
textarea.my_client {
    background: #F0F2F4;
    font-size: 16px;
    color: #8D8D8D;
    width: 100%;
    height: 108px;
}
.save_notes {
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    cursor: pointer;
    background: #10B8A8;
    padding:8px 18px 7px 18px;
    display: inline-block;
    border-radius: 22px;
    float: right;
    margin-top: 7px;
}
.cancel_notes {
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    cursor: pointer;
    background: red;
    padding:8px 18px 7px 18px;
    display: inline-block;
    border-radius: 22px;
    float: right;
    margin-top: 7px;
}
.my_client_border {
    margin-top:30px;
    margin-bottom: 20px;
}
.my_client_list .date {
    font-size: 16px;
    color: #5A5A5A;
    font-weight: bold;
}
.text {
    font-size: 16px;
    color: #8D8D8D;
}
.my_client_list img.edit_img {
    height: 16px;
    margin-left: 20px;
}
.my_client_list .edit {
    text-align: right;
}
.my_client_list {
    border-top: 1px solid #C2C2C2;
    padding: 20px 0px 20px 0px;
}
.my_client_lists {
    margin-top: 23px;
}
.text.edit_text {
    background: #FFFFFF;
    border: 1px solid #DBDBDB;
    border-radius: 2px;
    padding: 10px;
    width: 100%;
}
.Cancel.client{
    font-size: 12px;
    color: #10B8A8;
    margin-top: 11px;
    float: left;
    text-decoration: underline;
    margin-left: 10px;
}
ul.nav.navbar-nav li a {
    text-shadow: none;
}
/* End of MY Client Pages Css */
.selected_saved {
    background: #FFFFFF;
}
thead {
   background: #627B82;
   color: white;
   font-weight: normal;
}
thead th {
    font-weight: normal;
}
.margin_zero{
    margin: 0px !important;
}
.table-responsive.selected_saved tbody td {
    padding-top: 17px;
    padding-bottom: 20px;
    font-size: 16px;
    color: #8D8D8D;
    vertical-align: middle;
    z-index: 1;
}
img.symbols-glyph-arrow-line {
    height: 10px;
    position: relative;
    top: 1px;
    left: 3px;
}
img.symbols-property-image {
    height: 72px;
}
img.symbols-property-image {
    object-fit: cover;
}
.table-responsive.selected_saved tbody td span {
    font-size: 16px;
    color: #5A5A5A;
    font-family: 'Source Sans Pro', sans-serif;
}
.po_rel {
    position: relative;
    top: 10px;
    left: 5px;
}
span.the_colr {
    color: #10B8A8 !important;
}
img.symbols-glyph-more {
    height: 21px;
    cursor: pointer;
}
.open_h {
    background: #10B8A8;
    display: inline-block;
    color: white;
    padding: 1px 4px;
    font-size: 12px;
    border-radius: 5px;
}
img.symbols-glyph-checkin-thumbsup.dis_inline {
    height: 40px;
}
.po_rel_1 {
    position: relative;
    top: 10px;
}
.mt_10 {
    margin-top: 7px !important;
}
.ml_10 {
    margin-left: 10px;
}
.ml_zero {
    margin:0px !important;
}
ul.nav.navbar-nav li.active a , ul.nav.navbar-nav li.active:hover a , ul.nav.navbar-nav li.active:focus a {
    background: white;
    box-shadow: none;
    color: #5A5A5A;
}
.search_location {
    background: #F0F2F4;
    padding: 30px 10px 11px 10px;
}
.search_location input.new_form {
    margin: 0px;
}
.search_location .form_group {
    padding: 0px 6px;
    max-width: 136px;
}
.form_group.col-sm-3 {
    max-width: 200px;
}
.myclient_page.My_Listings {
    padding-top: 50px;
}
.month span {
   background: #566D77;
   color: white !important;
   font-size: 12px !important;
   padding: 1px 4px 0px 4px;
   display: inline-block;
   line-height: 19px;
   width: 100%;
   border-top-left-radius: 4px;
   border-top-right-radius: 4px;
}
.month {
   font-size: 22px;
   color: #676767;
   border: 1px solid lightgray;
   text-align: center;
   line-height: 8px;
   padding-bottom: 50px;
   width: 50px;
   display: inline-block;
   height: 40px;
   padding-top: 0px;
   margin-left: 12px;
   border-radius: 4px;
   cursor: pointer;
}
.new_shadow {
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.14);
    padding-bottom: 14px;
    padding-left: 15px;
    margin: -10px !important;
}
.save_notes.pull-left.mt_10 {
    margin-top: 15px !important;
    margin-left: 10px;
}
.No_matches .title {
    font-size: 40px;
    color: #10B8A8;
    letter-spacing: -1px;
}
.No_matches {
    text-align: center;
    padding-bottom: 120px;
    padding-top: 120px;
}
.my_client_load {
    text-align: center;
    padding-bottom: 3px !important;
    padding-top: 2px !important;
}

.No_matches .text {
    font-size: 16px;
    color: #8D8D8D;
    margin: 10px 5px 20px 5px;
}
.Add_a_New_Listing {
    background: #10B8A8;
    display: inline-block;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    padding: 7px 10px 7px 10px;
    border-radius: 20px;
}
.notification {
    background: #FFFFFF;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
    border-radius: 8px;
}
.notification .title {
    font-size: 16px;
    color: #5A5A5A;
    margin-bottom: 13px;
    margin-left: 25px;
    line-height: 10px;
    font-family: 'Source Sans Pro', sans-serif;
}
.notification {
    background: #FFFFFF;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
    border-radius: 8px;
    width: 280px;
    display: table;
    margin: auto;
    padding: 15px 0px 11px 0px;
    margin-top: 30px;
    margin-bottom: 30px;
}
.notification span.dark {
    font-size: 16px;
    color: #5A5A5A;
}
.noti_text{
    font-size: 12px;
    color: #8D8D8D;
    margin-left: 10px;
}
img.noti_image.symbols-property-image.dis_inline {
    position: relative;
    top: -20px;
    margin-left: 11px;
}
.notification_list{
   border-top: 1px solid #c2c2c28a !important;
   padding-top: 0px !important;
   cursor: pointer;
   padding-left: 16px;
   padding-right: 16px;
   padding-bottom: 16px !important;
}
.notification_list:hover{
    background: #E4F8F8
}
.see_all_noti{
   font-size: 14px !important;
   color: #10B8A8;
   text-align: center;
   border-top: 1px solid #c2c2c28a !important;
   padding-top: 12px;
   text-decoration: underline;
   cursor: pointer;
   font-weight: bold !important;
}
.back {
   position: absolute;
   background: url(../images/image_bg.png);
   height: 100%;
   top: 0px;
   width: 100%;
   background-repeat: no-repeat;
}
a.footer_logo {
    margin-right: 47px;
}
.glyphicon {
}
a.navbar-brand {
    padding-top: 15px;
}
.notification_page .title {
    font-size: 40px;
    color: #737373;
    letter-spacing: -1px;
}
.notification_page {
    padding-top: 30px;
    padding-bottom: 30px;
}
.ls_group.my_client_new {
   margin-bottom: 10px;
}
.ls_group.my_client_new .save_notes.margin_zero {
    margin-top: 15px;
}
.ls_group.my_client_new input.form-control.margin_zero {
    width: 200px;
}
/* .search_icon {
    background-image: url(../images/symbols-glyph-openhouse.png);
    background-repeat: no-repeat;
    background-size: 26px;
    background-position: 95% 8px;
} */

.search-icon{
    position: absolute;
    margin-left: -4rem;
    margin-top: .5rem;
    font-size: 23px;
    cursor: pointer;
  }
  .search-icon img{
    width: 25px;
  }

  .location-icon{
    position: absolute;
    margin-top: 0.65rem;
    margin-left: -5rem;
    z-index: 9999;
    font-size: 23px;
    cursor: pointer;
  }

  .location-icon img{
      width: 35px;
  }

  .tooltip{
    font-size: 1.4rem !important;
    width: 160px !important;
    margin-bottom: 1rem !important;
 }

.drop_down_icon {
   background-image: url(../images/symbols-glyph-arrow-line.png) !important;
   background-repeat: no-repeat !important;
   background-size: 18px !important;
   background-position: 97% 14px;
}
.search_location select.new_form.drop_down_icon {
    margin: 0px;
}
.google_map {
    position: relative;
}
.map_icon1 img {
    height: 61px;
    width: auto;
    margin: 7px 0px;
}
.map_icons {
    left: -20%;
    position: absolute;
}
.map_side_bar {
    position: absolute;
    top: 0px;
    right: 0;
    width: 350px;
    background: white;
    box-shadow: -2px 2px 4px 0 rgba(0,0,0,0.11);
    height: 100%;
    padding: 10px 4px 30px 4px;
}
.add_new_list.dis_inline {
    background: #10B8A8;
    font-size: 12px;
    color: #FFFFFF;
    vertical-align: bottom;
    border-radius: 21px;
    letter-spacing: 0;
    padding:8px 10px 6px 12px;
    margin-left: 5px;
    width: 110px;
    text-align: center;
}
.title {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
}
.map_listing .home_group {
    float: left;
    width: 100%;
    margin-top: 16px;
}
img.symbols-map-hover {
    position: relative;
   /* top: -16px;
    */
   /* left: -6px;
    */
    margin-top: 0 !important;
    width: 100%;
    margin-top: 4px;
    height: 100%;
    object-fit: cover;
}
.home_image {
    height: 215px;
    overflow: hidden;
    width: 100%;
    position: relative;
}
.home {
    float: left;
    width: 100%;
    position: relative;
}
.hom_details {
    position: absolute;
    bottom: 31px;
    left: 16px;
    color: white;
    width: 100%;
}
.status {
    font-size: 12px;
    color: #FFFFFF;
}
.amount {
    font-size: 25px;
    color: #FFFFFF;
}
.address {
    font-size: 12px;
    color: #FFFFFF;
}
.sub_details {
    font-size: 12px;
    color: #FFFFFF;
}
.map_left_icon {
   position: absolute;
   top: 15px;
   right: 10px;
   height: 90%;
}
.map_left_icon img {
   height: 30px;
   width: 30px;
   margin-bottom: 16px;
   margin-left: 5px;
}
span.day {
    font-size: 11px;
    color: #FFFFFF;
    display: block;
    text-align: center;
    padding: 1px;
    width: 40px;
    border-radius: 5px 5px 0px 0px;
}
.home_date {
    margin-top: 8px;
    cursor: pointer;
}
span.date.title {
    background: white;
    padding: 1px;
   /* margin-top: 10px;
    */
   /* border: 1px solid lightgray;
    */
   /* border-radius: 4px;
    */
    width: 40px;
    display: block;
    text-align: center;
    border-radius: 0px 0px 5px 5px;
    font-size: 18px;
    line-height: normal;
}
.day.color_2 {
    background: #BD3430;
}
span.active {
    background: #10B8A8;
    border: 2px solid #FFFFFF;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.50);
    float: left;
    height: 10px;
    width: 10px;
    position: relative;
    top: 3px;
    margin-right: 6px;
    border-radius: 10px;
}
.map_listing {
    float: left;
    height: 100%;
    overflow: scroll;
    width: 100%;
}
.map_listing::-webkit-scrollbar {
    width: 0px;
   /* remove scrollbar space */
    background: transparent;
   /* optional: just make scrollbar invisible */
}
.cancel_notes.pull-left {
    margin-top: 3px;
    margin-left: 0px;
}
.save_notes.pull-left {
    margin-top: 3px;
    margin-left: 0px;
}
.ls_group {
    margin-bottom: 30px;
}
.check_group.profile_checkbox .form_group label.width_auto {
    margin-left: 13px;
}
div#myModal2 .check_group input[type="checkbox"] {
    margin-right: 23px;
}
.nav.navbar-nav li {
    border-bottom: 3px solid transparent;
}
ul.nav.navbar-nav li.active2 {
    border-bottom: 3px solid #10B8A8;
}
table tr th:nth-child(1) , table tr td:nth-child(1) {
    padding-left: 25px !important;
}
img.symbols-glyph-arrow-line {
    height: 10px;
    position: relative;
    top: 1px;
    left: 10px;
}
table.table {
    margin: 0px;
}
.color1 {
    background: #10B8A8;
}
.color2 {
    background: #566D77;
}
.color3 {
    background: #AD5FBF;
}
.map_ele_1 {
    font-size: 16px;
    color: #FFFFFF;
    padding: 4px 12px;
    font-weight: normal;
    border-radius: 30px;
}
.map_elements {
    margin-top: 30px;
    margin-bottom: 30px;
    margin-left: 30px;
}
.box_on_map {
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
    transition: 0.3s;
    width: 100%;
    border-radius: 5px;
     height: auto;
     min-width: 195px;
     min-height: 230px;
    /* height: 188px; */
    /* width: 203px; */
    /* overflow: hidden; */
    background: #FFFFFF;
    /* box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30); */
    /* position: absolute; */
    /* top: 20%; */
    /* left: 10%; */
}
.event-bds{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.on_map_label_3 {
    position: absolute;
    top: 20%;
    left: 30%;
}
.on_map_label_2 {
    position: absolute;
    top: 50%;
    left: 50%;
}
.on_map_label_1 {
    position: absolute;
    top: 60%;
    left: 60%;
}
.map_group{
    padding: 0px 0px 0px 0px;
    /* border-top: 4px solid #10B8A8; */
}
img.box_on_image_image {
  width: 100%;
  height: 130px;
    /* width: 156px; */
    /* height: 95px; */
}
.on_map_price.title {
    /* margin-top: 5px; */
    /* margin-bottom: 5px; */
    font-weight: 600;
}
.on_map_detail {
    font-size: 12px;
    color: #8D8D8D;
    margin-bottom: 5px;
    font-weight: 600;
}
.on_map_address {
    /* background: #E4F8F8;
    font-size: 12px;
    color: #10B8A8;
    padding-left: 22px;
    padding-top: 3px; */
    font-size: 13px;
    color: #1fb8a8;
    padding-left: 10%;
    padding-top: 3px;
    font-weight: 600;
}
.navbar-inverse .navbar-toggle .icon-bar {
    background: #10B8A8 !important;
}
button.navbar-toggle.collapsed , button.navbar-toggle.collapsed:hover , button.navbar-toggle.collapsed:focus {
    border-color: #10B8A8 !important;
}
.navbar-inverse .navbar-toggle {
    border-color: #10B8A8 !important;
}
.navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus {
    background-color: transparent !important;
}
.my_client_table_group {
    margin-bottom: 70px;
}
html {
    position: relative;
    min-height: 100%;
}
body {
     margin: 0 0 64px ;
}
footer {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 64px;
    width: 100%;
    overflow:hidden;
}
@media only screen and (max-width: 767px) {
    div#bs-example-navbar-collapse-1 {
        background: #10B8A8;
        color: white;
   }
    ul.nav.navbar-nav li a, ul.nav.navbar-nav li a:hover, ul.nav.navbar-nav li a:focus {
        font-size: 16px;
        color: white;
        padding: 15px 20px;
        font-weight: normal !important;
   }
    .homepage_text .title {
        font-size: 40px;
        line-height: 45px;
        margin-bottom: 17px;
        margin-top: 35px;
   }
    .homepage_text {
        position: absolute;
        top: 0%;
        left: 5%;
        width: 92%;
   }
    .input-group {
        position: relative;
        display: table;
        border-collapse: separate;
        width: 100%;
   }
    .input-group.stylish-input-group input.form-control, .input-group.stylish-input-group input.form-control:focus {
        width: 100%;
   }
    .input-group.stylish-input-group span.glyphicon.glyphicon-search {
        color: #10B8A8;
        padding: 9px;
        right: 2px;
        z-index: 11;
        cursor: pointer;
        position: absolute;
        top: 2px;
   }
    a.footer_logo {
        margin-right: 0px;
        display: table;
        margin: auto;
        margin-bottom: 30px;
   }
    .footer ul li {
        display: inline-block;
        padding: 9px 5px 11px 7px;
   }
    .ls_group.my_client_new .pull-right {
        float: left !important;
        width: 100%;
        margin-top: 20px;
   }
    .my_client_table_group {
        float: left;
        margin-top: 10px;
        width: 100%;
   }
    .myclient_navbar ul li {
        display: -webkit-inline-box;
        margin-top: 12px;
   }
    .new_shadow {
        padding-left: 0px;
   }
    .search_location .form_group {
        padding: 0px 6px;
        width: 50%;
        float: left;
        margin-top: 10px;
        max-width: initial;
   }
}
.myclient_navbar {
    background:#516C76;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}
.new_profile_title {
    width: 1060px;
    margin: auto;
    padding-top: 40px;
    padding-bottom: 40px;
}
.new_profile_title img.new_symbols-avatar {
    height: 52px;
    width: 52px;
}
.new_title {
    font-size: 40px;
    margin-left: 22px;
    color: #737373;
    letter-spacing: -1px;
}
.new_profile_title img.new_symbols-avatar {
    height: 52px;
    width: 52px;
    vertical-align: top;
    margin-top: 3px;
}
.new_profile_group {
    min-width: 700px;
}
.new_profile_group ul li a , .new_profile_group ul li a:hover {
    font-size: 16px;
    color: #5A5A5A !important;
}
.new_profile_group ul li.active a , .new_profile_group ul li.focus a{
    font-size: 16px;
    color: #5A5A5A;
    border:0px !important;
}
.new_profile_group ul li.active {
    background: white;
}
.new_profiles {
    background: #F0F2F4;
    padding-bottom: 40px;
}
.new_profile_group .tab-content {
    background: #FFFFFF;
    border-radius: 4px;
    padding: 20px;
}
.new_profile_group ul.nav.nav-pills {
    border: 0px;
}
.new_content_title.dis_inline {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
    max-width: 30%;
}
.new_profile_details img.new_symbols-avatar.dis_inline {
    height: 72px;
    width: 72px;
    vertical-align: top;
}
.new_details.dis_inline .title2 {
    font-size: 16px;
    color: #8D8D8D;
}
.sub_title {
    font-size: 12px;
    color: #8D8D8D;
    width: 164px;
}
.new_details.dis_inline {
    margin-left: 20px;
    margin-top: 15px;
}
.new_form_group.dis_inline input[type="text"] {
    outline: 0 !important;
    border: 0px;
    border-bottom: 1px solid #C2C2C2;
    padding-bottom: 7px;
    font-size: 16px;
    color: #8D8D8D;
    width: 166px;
}
.width_350{
    width: 350px;
}
.ml-30 {
    margin-left: 40px;
}
.pl_detail .title2 {
    font-size: 16px;
    color: #5A5A5A;
}
.pl_detail .sub_title {
    font-size: 16px;
    color: #8D8D8D;
}
.pl_detail {
    margin-left: 33%;
    margin-top: 30px;
    max-width: 45%;
}
.input_new {
    background: #FFFFFF;
    border: 1px solid #5BC1BA !important;
    border-radius: 2px;
    padding-left: 5px;
    margin-top: 10px;
    padding-top: 5px;
}
.new_profile_group_wrap {
    margin: auto;
    display: table;
}
.artboard6_sidebar.artboard7_side {
    padding-bottom: 30px;
    vertical-align: top;
    margin-top: 45px;
}
.artboard6_sidebar {
    background: #FFFFFF;
    border-radius: 4px;
    width: 345px;
    padding: 2px 29px 10px 29px;
    margin-left: 20px;
}
.group_1 .title2{
    font-size: 25px;
    color: #676767;
    vertical-align: top;
    line-height: 30px;
    width: 28%;
    display: inline-block;
    margin-right: 5%;
}
.new_form {
    width: 65%;
    display: inline-block;
}
.invoice_history {
    width: 66%;
    display: inline-block;
}
.new_form label {
    display: block;
    font-weight: normal;
    font-size: 16px;
    color: #8D8D8D;
}
.in_date.dis_inline {
   font-size: 16px;
   color: #5A5A5A;
   min-width: 150px;
   max-width: 150px;
}
.in_rs.dis_inline {
   font-size: 16px;
   min-width: 48px;
   color: #8D8D8D;
}
.question_mark.dis_inline {
    margin-left: 70px;
    cursor: pointer;
}
.ml-10 {
    margin-left: 20px;
}
.invoice_group {
    margin-bottom: 22px;
}
.show_all_invoice {
    font-size: 12px;
    color: #10B8A8;
    text-decoration: underline;
    cursor: pointer;
}
.border_none {
    border-top:0px;
    border-left:0px;
    border-right:0px;
}
.new_form_group input, .new_form_group textarea {
    border: 0px;
    border-bottom: 1px solid #C2C2C2;
    outline: 0 !important;
    padding-bottom: 8px;
    font-size: 16px;
    color: #5A5A5A;
}
.new_form_group {
    margin-bottom: 37px;
}
input.submit_button {
    font-size: 12px;
    color: #10B8A8;
    border: 1px solid #10B8A8;
    border-radius: 100px;
    background: white;
    margin-top: 20px;
    padding: 0px;
    padding: 5px 15px 5px 15px;
}
.mt-20 {
    margin-top: 40px;
}
input.submit_button.with_bg {
    background: #10B8A8;
    color: white;
}
.group.new_form_label {
    position:relative;
}
.new_form_label input, .new_form_label textarea {
    padding: 9px 10px 6px 5px;
    border:none;
    border-bottom: 1px solid #C2C2C2;
}
.new_form_label input:focus, .new_form_label textarea:focus {
    outline:none;
}
/* LABEL ======================================= */
.new_form_label label {
    font-size: 16px;
    color: #8D8D8D;
    font-weight:normal;
    position:absolute;
    pointer-events:none;
    left:0px;
    top:10px;
    transition:0.2s ease all;
    -moz-transition:0.2s ease all;
    -webkit-transition:0.2s ease all;
}
/* active state */
/* .new_form_label input:focus ~ label, .new_form_label input:valid ~ label, .new_form_label textarea:focus ~ label, .new_form_label textarea:valid ~ label{ */
    /* top: -13px; */
    /* font-size:12px; */
   /* color: #10B8A8;
    */
/* } */

.new_form_label input:focus ~ label, .new_form_label input:not(:placeholder-shown) ~ label, .new_form_label textarea:focus ~ label, .new_form_label textarea:not(:placeholder-shown) ~ label{
   top: -13px;
   top: -13px;
   font-size:12px;
  /* color: #10B8A8;
   */
}
/* BOTTOM BARS ================================= */
.new_form_label .bar {
    position:relative;
    display:block;
    width: 350px;
}
.new_form_label .bar:before,.new_form_label .bar:after {
    content:'';
    height:2px;
    width:0;
    bottom: -1px;
    position:absolute;
    transition:0.2s ease all;
    -moz-transition:0.2s ease all;
    -webkit-transition:0.2s ease all;
}
.new_form_label .bar:before {
    left: 50%;
}
.new_form_label .bar:after {
    right: 50%;
}
/* active state */
.new_form_label input:focus ~ .bar:before, .new_form_label input:focus ~ .bar:after, .new_form_label textarea:focus ~ .bar:before, .new_form_label textarea:focus ~ .bar:after {
    width: 50%;
}
/* HIGHLIGHTER ================================== */
.new_form_label .highlight {
    position:absolute;
    height:60%;
    width:100px;
    top:25%;
    left:0;
    pointer-events:none;
    opacity:0.5;
}
/* active state */
input:focus ~ .highlight {
    -webkit-animation:inputHighlighter 0.3s ease;
    -moz-animation:inputHighlighter 0.3s ease;
    animation:inputHighlighter 0.3s ease;
}
.payment_form.group {
    position:relative;
}
.payment_form input{
    padding: 9px 10px 6px 5px;
    width: 100%;
    border:none;
    border-bottom: 1px solid #C2C2C2;
}
.payment_form input:focus{
    outline:none;
}
/* LABEL ======================================= */
.payment_form label{
    font-size: 16px;
    color: #8D8D8D;
    font-weight:normal;
    position:absolute;
    pointer-events:none;
    left:0px;
    top:10px;
    transition:0.2s ease all;
    -moz-transition:0.2s ease all;
    -webkit-transition:0.2s ease all;
}
/* active state */
.payment_form input:focus ~ label,.payment_form input:valid ~ label {
    top: -13px;
    font-size:14px;
    color: #10B8A8;
}
/* BOTTOM BARS ================================= */
.payment_form .bar {
    position:relative;
    display:block;
}
.payment_form .bar:before, .payment_form .bar:after {
    content:'';
    height:2px;
    width:0;
    bottom: -1px;
    position:absolute;
    transition:0.2s ease all;
    -moz-transition:0.2s ease all;
    -webkit-transition:0.2s ease all;
}
.payment_form .bar:before {
    left: 50%;
}
.payment_form .bar:after {
    right: 50%;
}
/* active state */
.payment_form input:focus ~ .bar:before,.payment_form input:focus ~ .bar:after {
    width: 50%;
}
/* HIGHLIGHTER ================================== */
.payment_form .highlight {
    position:absolute;
    height:60%;
    width:100px;
    top:25%;
    left:0;
    pointer-events:none;
    opacity:0.5;
}
/* active state */
.payment_form input:focus ~ .highlight {
    -webkit-animation:inputHighlighter 0.3s ease;
    -moz-animation:inputHighlighter 0.3s ease;
    animation:inputHighlighter 0.3s ease;
}
.artboard6_sidebar.artboard7_side input.new_form.background_color_form{
    background: #E4F8F8 !important;
}
.new_form_group.dis_inline span.bar {
    width: 166px;
}
.title3 {
    font-size: 16px;
    color: #8D8D8D;
}
.agent_plan_upgrade {
    margin-top: 30px;
}
.Upgrade-Agent {
    width: 440px;
}
.Upgrade-Agent .modal-dialog.modal-lg {
    border: 0px;
    border-radius: 0px;
}
.Upgrade-Agent .modal-dialog {
    width: 100%;
}
.upgrade_agent_title.title2 {
    font-size: 25px;
    background: #10B8A8;
    color: #FFFFFF;
    padding: 20px 10px;
}
.Upgrade-Agent .modal-body {
    padding: 0px;
}
.upgrade-account-sub-title{
   font-size: 25px !important;
   color: #676767 !important;
   line-height: 30px;
   padding-left: 20px;
   padding-top: 15px;
   font-weight: 600;
}
.upgrade-text{
    font-size: 16px;
    color: #8D8D8D;
    line-height: 32px;
    margin-left: 21px;
 }
.banifit .title2 {
    font-size: 25px !important;
    color: #676767 !important;
    line-height: 30px;
    padding-left: 20px;
    padding-top: 20px;
    padding-bottom: 15px;
    font-weight: 600;
}
.banifit ul li {
    list-style-type: none;
    font-size: 16px;
    color: #8D8D8D;
    line-height: 32px;
}
.billing_cycle .title2 {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
    padding-left: 20px;
}
.cycle_1 .title2 {
    font-size: 16px;
    color: #8D8D8D;
    padding: 0px;
    background: #F0F2F4;
    box-shadow: 0px 1px 5px -2px #F0F2F4;
}
.cycle_1 {
    width: 44%;
    border: 1px solid #C2C2C2;
    border-radius: 4px;
    margin-left: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center;
    display: inline-block;
}
.cycle_price {
    font-size: 25px;
    color: #10B8A8;
    line-height: 30px;
    margin-top: 15px;
}
.cycle_price span {
    font-size: 12px;
    color: #8D8D8D;
}
.cycle_upgrade {
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    background: #10B8A8;
    display: inline-block;
    padding: 6px 20px 5px 20px;
    border-radius: 20px;
    margin-bottom: 20px;
    margin-top: 11px;
}
.billing_cycle .cycle_1:nth-child(2) {
    margin-left: 20px;
}
.banifit ul li i {
    color: #10B8A8;
    font-weight: 100;
    font-size: 13px;
    position: relative;
    top: -3px;
    left: 3px;
}
.banifit ul li span {
    border: 1px solid #10B8A8;
    border-radius: 12px;
    display: inline-flex;
    height: 13px;
    width: 13px;
    margin-right: 9px;
}
.banifit ul {
    padding-left: 20px;
}
.billing_cycle.team_size .cycle_1 {
    width: 29%;
    margin-left: 5px;
}
.billing_cycle.team_size .cycle_1:nth-child(2) {
    margin-left: 20px;
}
/* .cycle_1:hover * {
    color: white;
    background: #10B8A8;
}
.cycle_1:hover{
    background: #10B8A8;
} */
.artboard2.payment_checkout_group {
    padding: 40px;
    margin-bottom: 40px;
}
.payment_checkout .title2 {
    font-size: 25px;
    font-family: 'Source Sans Pro', sans-serif;
    color: #676767;
    line-height: 30px;
    text-align: left;
    margin-bottom: 10px;
}
.abs_text {
    position: absolute;
    top: -20px;
    right: 0px;
}
.font-40 {
    font-size: 40px;
}
label.pay_end_date {
    font-size: 16px;
    color: #8D8D8D;
}
label.pay_end_date span {
    font-size: 16px;
    color: #10B8A8;
    line-height: 18px;
}
.mt-5 {
    margin-top: 5px;
}
.form_width_350 .group.new_form_label {
    width: 350px
}
input.button_with_bg {
    background: #10B8A8;
    color: white;
}
.new_form_group.dis_inline .group.new_form_label {
    width: 166px;
}
.group.payment_form.mt-20 input[type="text"] {
    font-size: 16px;
    color: #8D8D8D;
}
.artboard6_sidebar.side_bar_height {
    display: inline-block;
    vertical-align: top;
    margin-top: 43px;
    padding-bottom: 30px;
}
i.fa.fa-search.search_button {
    color: #10B8A8;
    position: relative;
    left: -20px;
}
.borerage_box .group.new_form_label {
    width: 276px;
    display: inline-block;
}
.borerage_box .group.new_form_label input.width_350 {
    width: 260px;
}
.borerage_box .new_form_group {
    display: inline-block;
}
.borerage_box .title2 {
    width: 30%;
    margin-right: 3%;
}
.borerage_box .title2 {
    width: 31%;
    margin-right: 3%;
}
.borerage_box input.submit_button.with_bg {
    margin-left: 20px;
    padding: 4px 20px 3px 20px;
}
p.Account_text span {
    font-size: 16px;
    color: #5A5A5A;
}
input.cancle_button.dis_inline {
    background: transparent;
    border: 0;
    margin-left: 20px;
    font-size: 12px;
    color: #8D8D8D;
    margin-left: 20px;
}
.new_form_group.profile_save_button {
    text-align: right;
}
.ml-88 {
    margin-right: 88px;
}
.pt-88 {
    padding-right: 60px;
}
.table-responsive.manage_seats img.symbols-property-image.dis_inline {
    height: 40px;
    width: 40px;
}
td.name {
    font-size: 16px;
    color: #5A5A5A;
}
td.id {
    font-size: 16px;
    color: #8D8D8D;
}
td.trash i {
    font-size: 16px;
    color: #8D8D8D;
}
.manage_seats tr td {
    vertical-align: middle !important;
}
.ml-20 {
    margin-left: 20px;
}
i.fa.fa-trash-o.edit_img {
    color: #F25B8D;
    margin-left: 15px;
    font-size: 16px;
    cursor: pointer;
}
i.fa.fa-pencil.edit_img {
    color: #10B8A8;
    cursor: pointer;
    font-size: 16px;
}
.square_group {
    background: #FFFFFF;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
    padding: 3px;
    padding-bottom: 17px;
    padding-top: 13px;
}
input.mini {
    background: #FFFFFF;
    border: 1px solid #5BC1BA;
    border-radius: 2px;
    display: inline-block;
    width: 89px;
    padding: 6px 6px 6px 6px;
    font-size: 15px;
    color: #8D8D8D;
}
.square_border {
    display: inline-block;
    border: 1px solid #DBDBDB;
    width: 12px;
    margin: 0px 9px;
    vertical-align: middle;
}
.square_footage {
    background: #DCDCDC;
    display: inline-block;
    padding: 0px;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
}
.baths {
    background: #DCDCDC;
    display: inline-block;
    padding: 2px 3px 4px 3px;
}
.baths_group ul {
    margin: 0px;
    padding: 0px;
}
.baths_group ul li {
    list-style-type: none;
    padding: 5px 16px 4px 16px;
    font-size: 16px;
    color: #5A5A5A;
}
.baths_group {
    background: #FFFFFF;
    /* box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30); */
    padding: 0px;
}
.baths_group ul li
{
    cursor: pointer;
}
.baths_group ul li:hover {
    background: #10B8A8;
    color: #FFFFFF;
    transition: all 275ms ease-in-out;
}
.square_group input.new_form {
    margin-top: 0px;
}
.square_group label {
    font-size: 16px;
    color: #5A5A5A;
}
.square_group .title2 {
    background: #10B8A8;
    font-size: 25px;
    color: #FFFFFF;
    text-align: center;
    padding: 12px 0px;
    margin-bottom: 19px;
}
.padding_0 {
    padding: 0px
}
.form_group_square {
    padding: 0px 20px;
    width: 350px;
}
.form_group_square .form_group {
    padding-bottom: 20px;
}
.baths_group.search_option li {
    border-bottom: 1px solid #C2C2C2;
    padding: 10px 16px 10px 16px;
}
.rate_modal img.search_Rectangle.img-responsive {
    height: 192px;
    width: 100%;
    margin-bottom: 100px;
    object-fit: cover;
}
.rate_modal .modal-dialog.modal-lg {
    width: 438px;
    margin: auto;
}
.rate_modal .modal-dialog.modal-lg .modal-body {
    padding: 0px;
}
.rate {
    font-size: 25px;
    color: #FFFFFF;
    position: absolute;
    top: 10px;
    left: 10px;
}
.rate_modal .modal-dialog.modal-lg .modal-body {
    padding: 0px;
}
.rate_modal .modal-dialog.modal-lg .modal-body button.close {
    color: white;
    position: absolute;
    right: 10px;
    opacity: 1;
    font-size: 22px;
    top: 10px;
    outline: 0 !important;
    z-index: 100;
}
.theme_img_modal img {
    width: 130px;
    padding: 30px;
    box-shadow: 0 2px 3px 0 rgba(0,0,0,0.14);
    background: white;
}
.theme_img_modal {
    text-align: center;
    position: absolute;
    top: 18%;
    left: 20%;
}
.rate_title {
    font-size: 16px;
    color: #5A5A5A;
    text-align: center;
}
textarea.the_kitchen {
    font-size: 16px;
    color: #8D8D8D;
    outline: 0 !important;
    width: 93%;
    padding: 3px;
    margin-right: 20px;
    margin-left: 20px;
    margin-top: 14px;
    background: #FFFFFF;
    border: 1px solid #DBDBDB;
    border-radius: 2px;
    height: 70px;
}
.theme_img_modal img:hover {
    /* background: #10B8A8; */
}
.rate {
    font-size: 25px;
    color: #FFFFFF;
    position: absolute;
    top: 60px;
    left: 144px;
}
.mb-20 {
    margin-bottom: 20px;
}
.note {
    background:#FF5289;
    font-size: 12px;
    padding: 3px 4px 3px 4px;
    color: #FFFFFF;
    text-align: center;
}
.mr-30 {
    margin-right: 30px;
}
.save_search_box.pull-right .title2 {
    font-size: 16px;
    color: #5A5A5A;
    vertical-align: super;
}
.save_search_box.pull-right .add_new_list.dis_inline {
    padding: 8px 10px 7px 10px;
}
.no_match_found .title2 {
    font-size: 40px;
    color: #10B8A8;
    letter-spacing: -1px;
}
.no_match_found {
    float: left;
    padding: 0px 17px;
    display: block;
    vertical-align: middle;
    align-items: center;
    margin-top: 40%;
}
.note_no_match {
    font-size: 16px;
    color: #8D8D8D;
}
.save_search_box.pull-right {
    margin-top: 0px;
}
span.color_2 {
    background: #AD5FBF;
}
span.color_3 {
    background: #10B8A8;
}
.event_address {
   position: fixed;
   font-size: 16px;
   color: #FFFFFF;
   /* top: 17%; */
   left: 3%;
   width: 95%;
   margin-top: -79px;
}
.event_date {
    font-size: 25px;
    color: #10B8A8;
    line-height: 30px;
    margin-left: 20px;
}
.search_event img.search_Rectangle.img-responsive {
    margin-bottom: 20px;
    object-fit: cover;
}
.event_date2 {
    font-size: 16px;
    color: #5A5A5A;
    margin-left: 20px;
}
.event_text {
    font-size: 16px;
    color: #8D8D8D;
    padding: 6px 20px 20px 20px;
    white-space: pre-wrap;
    overflow-wrap: anywhere;
}
.event_save.dis_inline {
    border: 1px solid #10B8A8;
    border-radius: 100px;
    font-size: 12px;
    color: #10B8A8;
    padding: 7px 10px 7px 10px;
    margin-left: 20px;
    width: 100px;
    text-align: center;
}
.event_save.with_bg.dis_inline {
    background: #10B8A8;
    color: white;
}
.event_details_modal .title2 {
    font-size: 16px;
    color: #5A5A5A;
    margin-left: 20px;
    padding-top: 20px;
    margin-bottom: 20px;
}
.event_details_modal {
    background: #F0F2F4;
    margin-top: 20px;
    padding-left: 20px;
    padding-bottom: 20px;
}
.event_details_modal {
}
.event_details_modal span.dark {
    font-size: 16px;
    color: #8D8D8D;
}
.event_details_modal .dis_inline.po_rel {
    font-size: 12px;
    color: #8D8D8D;
}
textarea.event_modal_lorem {
    margin-top: 20px;
    width: 97%;
    outline: 0;
    height: 100px;
    margin-bottom: 12px;
}
.event_details_modal {
}
.event_details_modal .event_save.with_bg.dis_inline {
    margin-left: 0px;
}
.event_footer {
   background: #10B8A8;
   margin-top: 0px;
   float: left;
   width: 100%;
   margin-bottom: 25px;
   padding-bottom: 9px;
   padding-top: 4px;
}

.event_footer .title2 {
    font-size: 25px;
    color: #FFFFFF;
    margin-bottom: 5px;
    padding-top: 10px;
    padding-left: 20px;
}
.footer_text {
    font-size: 16px;
    color: #FFFFFF;
    padding-bottom: 10px;
    padding-left: 20px;
}
.event_footer {
}
.event_footer .event_save.dis_inline {
    border: 1px solid #FFFFFF;
    border-radius: 100px;
    color: white;
    width: 89px;
    padding: 4px 0px 4px 0px;
    vertical-align: top;
    margin-top: 32px;
    margin-right: 14px;
    margin-left: 9px;
}

.bg_color_2 {
    background: #566D77 !important;
}
.border_color {
    border-color: #566D77 !important;
}
.font_color_2 {
    color:#566D77 !important;
}
.bg_color_3 {
    background: #10B8A8 !important;
}
.border_color_3 {
    border-color: #10B8A8 !important;
}
.font_color_3 {
    color:#10B8A8 !important;
}
.bg_color_4 {
    background: #AD5FBF !important;
}
.border_color_4 {
    border-color: #AD5FBF !important;
}
.font_color_4 {
    color:#BD3430 !important;
}
.event_save.dis_inline.color_4_modal {
    margin-left: 13px;
    width: 89px;
    margin-bottom: 9px;
}
.header {
    position: fixed;
    z-index: 21;
    width: 100%;
}
.header_fix {
    padding-top: 53px;
}
/*Blog page css*/
@media (min-width: 1200px){
    .blog-container {
        width: 950px;
   }
}
.blog_page {
    background: #F0F2F4;
    padding: 53px 0px;
}
.category_group {
    background: #10B8A8;
    min-height: 51px;
}
.category_group ul {
    width: 100%;
    list-style: none;
    padding-left: 0;
    text-align: center;
    margin-bottom: 0;
}
.category_group ul li {
    display: inline-block;
    padding: 12.4px 8px;
}
li.active {
    border-bottom: 4px solid white;
}
.category_group ul li a {
    font-size: 16px;
    color: #FFFFFF;
    text-decoration: none;
}
.blog-bg {
    background: url('../../assets/images/Rectangle.jpg') no-repeat;
    height: 435px;
    position: relative;
}
.bg-white {
    background: white;
    margin-bottom: 32px;
}
.blog-content {
    margin-top: 112px;
}
.blog-caption {
    position: absolute;
    bottom: 0;
    padding-left: 91px;
    color: white;
}
.blog-caption span {
    font-size: 12px;
}
.blog-caption span:nth-child(1) {
    color: #10B8A8;
}
.blog-caption h1 {
    font-size: 40px;
    color: #FFFFFF;
    letter-spacing: -1px;
    line-height: 40px;
    max-width: 623px;
}
.blog-content .content {
    padding: 0px 90px 30px;
    font-size: 16px;
    color: #8D8D8D;
}
.blog-content .author {
    margin-top: 44px;
    font-size: 12px;
    color: #8D8D8D;
}
h2.blog-title {
    font-size: 25px;
    color: #10B8A8;
    line-height: 30px;
    margin-bottom: 30px;
}
.blog-txt {
    margin-bottom: 40px;
}
.btn-post {
    background: #10B8A8 !important;
    font-size: 12px !important;
    color: #FFFFFF !important;
    letter-spacing: 0 !important;
    border-radius: 50px !important;
    border: 0px !important;
    padding: 6.5px 14px !important;
}
.related-post {
    padding: 0px 90px 30px;
}
.related-content {
    font-size: 16px;
    color: #8D8D8D;
    margin-top: 27px;
}
.related-title {
    font-size: 12px;
    color: #8D8D8D;
}
.search_location.header_fix {
    padding-top: 79px;
    padding-bottom: 10px;
}
.notification {
    margin-top: 0px;
    position: absolute;
    display: none;
    right: 7px;
    top: 54px;
    width: 352px;
}
.notification_div {
    padding: 12px 0px;
}
.notification_div img.noti_image.symbols-property-image.dis_inline {
    height: 65px;
    vertical-align: -webkit-baseline-middle;
    margin-right: 10px;
    margin-left: 0px;
}
.notification_list.mt-20 {
    padding: 0px;
}
.notification_div span.dark {
    font-size: 25px;
    color: #676767;
    line-height: 30px;
}
.notification_div .dis_inline.noti_text {
    font-size: 16px;
    color: #8D8D8D;
   /* margin-top: 10px;
    */
}
.notification_div {
    padding-left: 20px;
    padding-right: 20px;
    height: 100px;
    overflow: hidden;
}
.notification_button.dis_inline.pull-right {
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    background: #10B8A8;
    padding: 4px 10px 3px 10px;
    border-radius: 12px;
    margin-top: 24px;
}
.notification_div {
    border-top: 1px solid #C2C2C2;
}
/* .notification_div:hover {
    background: #E4F8F8;
} */
.notification a {
    padding: 0 !important;
    text-decoration: none !important;
}
.map_icon1 img {
    margin: 3px 0px 3px 0px;
}
img.Bedroom {
    margin-right: 5px;
}
.ls_group.Favorites_page .title_group.ml_zero {
    width: 100%;
}
img.Icon_png {
    float: right;
    display: inline-block;
    position: relative;
    top: -16px;
}
.ls_group.Favorites_page {
    margin: 0px;
}
.mb-5 {
    margin-bottom: 5px;
}
.search_location.no_match_found_fix {
    padding-top: 110px;
}
.search_location.header_fix.search_heder {
    padding-top: 100px;
}
.searchModel .modal-body {
    padding: 0;
}
.searchModel .form_group {
    max-width: 100% !important;
}
.add_new_list.dis_inline:focus {
    outline: none !important;
}
.daterangepicker thead {
    background: transparent !important;
    color: #333333 !important;
    font-weight: normal;
}
.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: #00B8A7 !important;
    border-radius: 100% !important;
}
.daterangepicker td.in-range {
    background-color: #00b8a740 !important;
}
.ranges {
    float: none !important;
}
button.applyBtn {
    float: right !important;
    font-size: 16px !important;
    color: #7A7A7A !important;
    letter-spacing: 0 !important;
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}
button.cancelBtn.btn.btn-sm.btn-default {
    font-size: 16px !important;
    color: #8D8D8D !important;
    letter-spacing: 0 !important;
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
}
button.applyBtn:focus , button.cancelBtn:focus {
    outline: none !important;
}
@media (min-width: 768px) {
    .searchModel .modal-dialog {
        width: 356px;
        margin: 160px auto;
   }
}
.daterangepicker_input {
    display: none;
}
.square_footage.price_group_box {
    position: absolute;
    z-index: 1;
    width: 225px;
    display: none;
    top: 43px;
}
.form_group.col-sm-2.price_group {
    position: relative;
}
.form_group.col-sm-2.price_group option {
    display: none;
}
.form_group.title2.dis_inline.ml-20.mr-30.My_Saved_Searches {
    background: transparent;
    margin-left: 8px;
    width: 55%;
    max-width : 100% !important
}
.form_group.title2.dis_inline.ml-20.mr-30.My_Saved_Searches .select_mate {
    background: transparent;
    border: 0px;
    width: 185px;
}
.save_search_box.pull-right .add_new_list.dis_inline {
    vertical-align: super;
}
.mt-15 {
    margin-top: 20px !important;
}
.myclient_page {
    height: calc(100% - 64px);
}
select.new_form.drop_down_icon {
    max-width: 350px;
}
.new_form_group.new_form_select_css .select_mate {
    width: 350px;
    border: 0px;
    border-bottom: 1px solid #C2C2C2;
    font-size: 16px;
    color: #5A5A5A;
    font-family: 'Source Sans Pro', sans-serif;
}
.cont_select_int .active {
    background-color: #00B8A7 !important;
}
.modal_content ul.nav.nav-pills li.active {
    border: 0px;
}
div#menu1 .form_group {
    margin-bottom: 15px;
}
.map_empty .note_no_match {
    margin-bottom: 17px;
}
textarea{
    resize: none;
}
.load_more {
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
    background: #10B8A8;
    display: table;
    padding: 6px 20px 6px 20px !important;
    margin: 50px auto 0px auto;
    border-radius: 17px;
}
.chat_message .left_side {
    width: 439px;
    background: #37474F;
    display: inline-block;
    padding: 20px 0px 20px 0px;
}
.left_side button.btn.add_new_list.dis_inline {
    display: block !important;
}
.left_side input.meassage_search
{
       margin: 26px auto 19px 20px;
}
input.meassage_search {
    background: #8484846b;
    border: 1px solid #636363;
    border-radius: 2px;
    font-size: 16px;
    color: #8D8D8D;
    height: 40px;
    margin: 20px auto 20px 20px;
    width: 90%;
    padding-left: 10px;
    outline: 0 !important;
}
.message_list.mess_active {
    background: #8484846b;
}
.message_short_name {
    background: #10B8A8;
    border: 1px solid #DBDBDB;
    font-size: 25px;
    color: #FFFFFF;
    display: inline-block;
    padding:6px 8px;
    border-radius: 30px;
    margin-top: 22px;
    vertical-align: top;
}
.meaasge_details {
    display: inline-block;
    width: 80%;
    vertical-align: bottom;
    margin-left: 13px;
    margin-top: 11px;
}
.message_name {
    font-size: 16px;
    color: #FFFFFF;
}
span.message_time {
    font-size: 12px;
    background: #10B8A8;
    color: #FFFFFF;
    padding: 3px 6px 1px 5px;
    border-radius: 11px;
}
.message_text {
    font-size: 12px;
    color: #FFFFFF;
}
.message_list {
    margin-top: 0px;
    padding-bottom: 14px;
    padding-left: 20px;
    padding-right: 20px;
}
.message_right_side {
   /* width: 70.4%;
    */
   /* background: #F0F2F4;
    */
   /* display: inline-block;
    */
   /* vertical-align: top;
    */
   /* padding: 20px;
    */
}
.chat_message {
    background: white;
}
.message_right_side label {
    font-size: 16px;
    color: #5A5A5A;
}
input.type_of_person {
    width: 291px;
    height: 40px;
    font-size: 16px;
    color: #8D8D8D;
    padding-left: 10px;
    outline: 0 !important;
}
.message_right_side.chat {
    background: #566D77;
    font-size: 25px;
    color: #FFFFFF;
}
.chat_name {
    padding-left: 70px;
}
.live_chat {
    padding: 20px 20px;
}
.message_right {
    width: 71.4%;
    background: white;
    display: inline-block;
    vertical-align: top;
    position: relative;
}
.chat_time {
    font-size: 16px;
    color: #5A5A5A;
    text-align: center;
    margin: 20px 0px;
}
.other_chat span {
   background: #F0F2F4;
   width: 40%;
   margin-left: 40px;
   /* border-radius: 20px; */
   padding: 17px 21px;
   border-top-left-radius: 18px;
   border-top-right-radius: 18px;
   border-bottom-right-radius: 18px;
}
.other_chat {
    display: flex;
}
.other_chat .message_short_name {
    margin-top: 18px;
    height: 50px;
}
.your_chat {
   background: #10B8A8;
   font-size: 16px;
   color: #FFFFFF;
   max-width: 40%;
   padding: 17px 21px;
   margin: 10px 0px;
   float: right;
   border-top-left-radius: 18px;
   border-top-right-radius: 18px;
   border-bottom-left-radius: 18px;
}
.position_reletive {
    position: relative;
    top:0;
    left: 0;
}
.type_a_message textarea {
    background: #F0F2F4;
    font-size: 16px;
    color: #8D8D8D;
    border: 0px;
    width: 100%;
    height: 60px;
    margin-top: 20px;
    padding-top: 10px;
    padding-left: 10px;
    outline: 0 !important;
}
.chat_message {
    display: flex;
}
.chat_session {
    margin-left: 60px;
    margin-right: 20px;
}
.message_right_side.send_message_to_group {
   background: #F0F2F4;
   width: 71.4%;
   height: 70px;
   box-shadow: 0 2px 3px 0 rgba(0,0,0,0.14);
   padding-top: 16px;
   padding-left: 20px;
}

.width_250
{
    width: 250px;
}

.Contact_Us_page {
   background: #FFFFFF;
   width: 1076px;
   margin: 30px auto 110px auto;
   padding: 20px;
   box-shadow: 0 2px 3px 0 rgba(0,0,0,0.14);
}
.Contact_Us_page .title2 {
   font-size: 50px;
   color: #737373;
}
.Contact_Us_page .sub_text {
   font-size: 16px;
   color: #8D8D8D;
   width: 49%;
}

.contact_address_form {
   margin-top: 30px;
   display: flex;
}

.contact_del_group label {
   font-size: 16px;
   color: #8D8D8D;
   font-weight: normal;
   margin: 0px;
}

.contact_del_group {
   margin-bottom: 22px;
}
.contact_details {
   width: 36%;
}
.Contact_Us_page .value {
   font-size: 25px;
   color: #10B8A8;
   line-height: 30px;
}
.mb-30
{
    margin-bottom: 20px;
}

.group.new_form_label.mb-30.dis_inline
{
        width: 250px;
   margin-right: 40px;
}

.width_94{
    width: 94% !important;
}

textarea.address_message {
   height: 217px;
   width: 94%;
   background: #FFFFFF;
   border: 1px solid #DBDBDB;
   border-radius: 2px;
}

.contact_form .new_form_group {
   margin-bottom: 10px;
}

.terms_text p {
   font-size: 16px;
   color: #5A5A5A;
}

.about_sub {
   font-size: 25px;
   color: #676767;
   line-height: 30px;
   margin: 20px 0px;
}

img.add_event_bg {
   height: 910px;
    width: 100%;
   object-fit: cover;
}

img.img-responsive.footer_logo_event {
   position: absolute;
   z-index: 111;
   top: 30px;
   left: 30px;
   width: 150px;
}

.add_event_image_group {
   display: inline-block;
   position: relative;
}

.event_address_title {
   font-size: 50px;
   color: #FFFFFF;
   position: absolute;
   top: 20%;
   left: 30px;
}

.event_question {
   font-size: 25px;
   color: #FFFFFF;
   position: absolute;
   top: 31%;
   left: 30px;
}
.event_address_note {
   font-size: 16px;
   color: #FFFFFF;
   position: absolute;
   top: 36%;
   left: 30px;
}
.event_search .input-group.stylish-input-group input.form-control {
   margin-bottom: 20px !important;
}
.event_search {
   position: absolute;
   top: 40%;
   left: 30px;
}
.add_event_image_group {
   border-right: 5px solid #10B8A8;
}

img.img-responsive.event_agent_img {
   height: 31px;
   margin-left: 20px;
   margin-top: 20px;
}
.Event_manager_add {
   display: grid;
   grid-template-columns: 67.8% 32.2%;
}
.event_agent_title {
   font-size: 40px;
   color: #737373;
   letter-spacing: -1px;
   line-height: 43px;
   padding-left: 20px;
   margin-top: 30px;
}

.event_agent_title span{
    color:#10B8A8;
}
.not_a_member {
   font-size: 25px;
   color: #676767;
   line-height: 30px;
   padding-left: 20px;
   margin-top: 70px;
   font-weight: 600;
}
img.agent_symbols-avatar {
   height: 86px;
   width: 86px;
}

.your_open_agent .title2 {
   font-size: 16px;
   color: #FFFFFF;
   border-bottom: 1px solid white;
   width: 60%;
   padding-bottom: 10px;
   margin-bottom: 40px;
}

.your_open_agent {
   position: absolute;
   top: 78%;
   left: 30px;
   width: 100%;
}

.open_agent_name_group .name {
   font-size: 16px;
   color: #FFFFFF;
}

.open_agent_name_group .fname {
   font-size: 12px;
   color: #FFFFFF;
}

.open_agent_img {
   display: flex;
}

.open_agent_name_group {margin-left: 10px;margin-top: 6px;}



.chat_message.check_event .left_side {
   width: 350px;
}


.chat_message.check_event .meaasge_details {
   width: 72%;
}


.right_side_event_agent {
   width: 77%;
}

.right_side_event_agent .image img {
   width: 100%;
   height: 330px;
}

.right_agent_event {
   position: relative;
}

.right_agent_text {
    width: 94%;
   position: absolute;
   top: 30px;
   left: 40px;
}

.white_button {
   font-size: 12px;
   color: #FFFFFF;
   letter-spacing: 0;
   border: 1px solid white;
   border-radius: 20px;
   height: 28px;
   padding: 5px 14px 3px 14px;
   margin-left: 30px;
   margin-top: 33px;
   cursor: pointer;
}
.button_group {
   display: flex;
   margin-top: 34px;
   float: right;
}

.with_white_button {
   border: 1px solid #FFFFFF;
   border-radius: 100px;
   font-size: 12px;
   color: #FFFFFF;
   letter-spacing: 0;
   padding: 4px 11px 3px 11px;
   width: 140px;
   text-align: center;

}

.without_white_button {
   background: #FF8F00;
   font-size: 12px;
   color: #FFFFFF;
   letter-spacing: 0;
   padding: 5px 11px 3px 11px;
   border-radius: 100px;
   margin-left: 19px;
   width: 130px;
   text-align: center;
}

.agent_title2 {
   font-size: 50px;
   color: #FFFFFF;
   line-height: 54px;
}

.agent_sub_title2 {
   font-size: 16px;
   color: #FFFFFF;
}

.agent_details_check {
   margin-top: 50px;
   float: left;
   width: 100%;
}

.agent_details_check button.btn.add_new_list.dis_inline {
   margin-top: 11px;
}
.check_table1 thead {
   background: #F0F2F4;
   font-size: 16px;
   color: #5A5A5A;
}

.check_table1 {
   width: 34%;
   display: inline-block;
   margin-left: 28px;
   background: #FFFFFF;
   box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
   border-radius: 4px;
   padding: 20px 10px;
   margin-top: 20px;
}
.check_table1 td
{
    vertical-align: middle !important;
}
.bname {
   font-size: 16px;
   color: #5A5A5A;
}

.unre {
   font-size: 12px;
   color: #8D8D8D;
}

.check_time {
   font-size: 12px;
   color: #8D8D8D;
}


.check_table1 .title2 {
   font-size: 25px;
   color: #676767;
   line-height: 30px;
   display: inline-block;
}

.check_table1 input.new_form.pull-right {
   display: inline-block;
   width: 130px;
   height: 38px;
   margin-bottom: 15px;
}

img.check_icon {
   height: 16px;
}

.check_table2
{
    width: 59%;
}


.blue_border_button {
   border: 1px solid #10B8A8;
   border-radius: 100px;
   padding: 5px 12px 2px 12px;
   font-size: 12px;
   color: #10B8A8;
   display: inline-block;
   cursor: pointer;
   margin-right: 15px;
   width: 120px;
   text-align: center;
}

.check_in_title {
   font-size: 25px;
   color: #FFFFFF;
   display: inline-table;
   margin-left: 20px;
   position: relative;
   top: 4px;
}

a.check_in_link {
   display: inline-block;
}


.blue_bg_button {
   background: #10B8A8;
   cursor: pointer;
   font-size: 12px;
   color: #FFFFFF;
   letter-spacing: 0;
   border-radius: 100px;
   padding: 7px 20px 5px 22px;
   text-align: center;
   display: inline-block;
   margin-bottom: 11px;
   width: 120px;
}


img.rela_blog_img {
   width: 352px;
   height: 221px;
   object-fit: cover;
}

.cate {
   font-size: 12px;
   color: #10B8A8;
   padding-top: 14px;
}

.cate span {
   font-size: 12px;
   color: #8D8D8D;
}

.rela_title {
   font-size: 25px;
   color: #676767;
   line-height: 30px;
}
.rela_text {
   font-size: 16px;
   color: #8D8D8D;
}

.auther_name {
   font-size: 12px;
   padding-top: 10px;
   color: #8D8D8D;
   border-top: 1px solid #C2C2C2;
   margin-top: 20px;
   width: 90%;
}

.relate_blog_text {
   background: #F0F2F4;
   padding-left: 19px;
   padding-bottom: 20px;
}


.blog_page_header {
   background: url(../images/Rectangle.jpg);
   height: 410px;
}


.blog_title {
   font-size: 40px;
   color: #FFFFFF;
   letter-spacing: -1px;
   line-height: 40px;
   text-align: center;
   padding-top: 40px;
}

.blog_text {
   font-size: 16px;
   color: #FFFFFF;
   max-width: 35%;
   margin: auto;
}

hr.blog_header {
   border-top: 4px solid #10B8A8;
   width: 4%;
   margin: 20px auto;
}

.blog_detail_rela .Related_Posts_group {
   margin-top: 30px;
}

.blog_detail_rela {
   margin-top: -100px;
}

.blog_details_page {
   background: white;
}


.modal_content .form_group {
   margin-top: 20px;
}

.modal-open {
   overflow-x:visible;
   padding: 0 !important;
}

.nav.navbar-nav li.active_menu {
   border-bottom: 3px solid #4cb8a8;
}
.cancel_notes.pull-left.ml-10
{
    margin-left: 10px;
}

.save_notes.pull-left.ml-10
{
    margin-left: 10px;
}

body a , body a:hover
{
    text-decoration: none;
}

.rate_modal .modal-dialog.modal-lg {
   top: 10%;
}


.new_profile_width .tab-content
{
    width: 700px;
}

a.a_tag_color  ,a.a_tag_color:hover {
   color: #8D8D8D;
}


.Event_manager_blog {
   width: 95%;
   z-index: 1;
   margin: auto;
   margin-top: -60px;
   display: block;
   border: white;
   position: relative;
}

.ls_group.mt-20 {
   margin-left: 4px;
}

.save_notes.export_csv {
   margin-right: 60px;
   margin-top: 0px;
}


.new_text_css{
    letter-spacing: 1px !important;
    font-weight: 400;
}

.font_new_class table.table tr td {
   color: #5A5A5A;
}

.change_font_color {
   color: #8D8D8D;
}
img.bath_size {
   height: auto;
   width: 16px;
}

thead th {
   font-size: 16px;
   color: #FFFFFF;
}


.font_color{
    color: #5A5A5A;
}


.new_form_group .new_label_text
{
    font-size: 16px;
   color: #8D8D8D;
}

.pull-right.mt-5.m-top {
   position: relative;
   top: -10px;
}

label.for_get_pass {
   margin-top: 16px;
   margin-left: 2px;
}

.cursor_poiner{
    cursor: pointer;
}

/*.chat_message .left_side {
   height: calc(100vh - 118px);
}
*/

.chat_session.chat_over_flow {
   height: calc(100vh - 276px);
   overflow-y: scroll;
}

.right_side_event_agent.right_side_overflow::-webkit-scrollbar , .message_list_with_name::-webkit-scrollbar , .chat_session.chat_over_flow::-webkit-scrollbar {
   width: 0px;  /* remove scrollbar space */
   background: transparent;  /* optional: just make scrollbar invisible */
}

.chat_on_bottom {
   position: absolute;
   bottom: 0;
   height: calc(100vh - 193px);
   width: 100%;
}

.message_list_with_name {
   overflow-y: scroll;
   height: calc(100vh - 275px);
}

.blueBorder {
    border-bottom: 4px solid #10b8a8;
}
.paddingTop {
    padding-top: 17px;
}
.text-color{
    color: #8D8D8D;
    font-size: 1.2em;
}
.pre-color{
    color: #10b8a8;
}

@media screen and (max-width: 1300px) and (min-width: 1200px) {
       .search_location.header_fix .form_group.col-sm-2 {
        width: 9.4%;
    }

    #map {
   height: 600px !important;
   width: 100% !important;
}

}



.chat_on_bottom::-webkit-scrollbar ,  .chat_message .left_side::-webkit-scrollbar{
    width: 0px;
    background: transparent;
}


.Upgrade-Agent {
   width: 440px !important;
}


.month div {
   padding-top: 10px;
}
.month_2_color
{
    background: #BD3430 !important;
}

.month_3_color
{
    background: #10B8A8 !important;
}


.button_center
{
        float: left;
   margin-left: 50px;
}

.disable_form label {
   top: -13px;
   font-size: 12px;
}

.disable_form input {
   background: transparent;
}


.menu51_text {
   font-size: 16px;
   color: #8D8D8D;
}


.lilly_m_group {
   background: #F0F2F4;
   font-size: 25px;
   color: #676767;
   line-height: 30px;
   padding: 10px;
   padding-left: 20px;
}

.li_status {
   font-size: 16px;
   color: #8D8D8D;
}

.li_status_2 {
   font-size: 16px;
   color: #5A5A5A;
}

.lilly_stat_us {
   display: grid;
   grid-template-columns: 36% 64%;
   padding-top: 15px;
   padding-bottom: 15px;
   border-bottom: 1px solid #C2C2C2;
}


.width_383 {
   width: 438px;
}

.color_green
{
    color: #10B8A8;
}

.red_button {
   border: 1px solid #F06292;
   border-radius: 100px;
   background: transparent;
   font-size: 16px;
   color: #F06292;
   padding: 3px 15px 2px 15px;
   outline: 0 !important;
}

input.red_button {
   margin-top: 20px;
   margin-bottom: 13px;
   margin-right: 20px;
   margin-left: 10px;
}

.green_button {
    border: 1px solid #10B8A8;
    border-radius: 100px;
    background: transparent;
    font-size: 16px;
    background-color: #10B8A8;
    color: white;
    padding: 3px 30px 3px 30px;
    outline: 0 !important;
 }

 input.green_button {
    margin-top: 20px;
    margin-bottom: 13px;
 }

.lilly_title
{
    display: inline-block;
}


.li_status_2 span {
   color: #8D8D8D;
}


.share_property label {
   font-size: 16px;
   color: #7A7A7A;
   letter-spacing: 0;
   font-weight: normal;
   margin: 0px;
   padding-left: 0px;
}

.border
{
        border: 1px solid #C2C2C2 !important;
}

textarea.message_share_property {
   font-size: 16px;
   color: #8D8D8D;
   width: 100%;
   height: 142px;
   /* margin-top: 7px; */
   padding: 6px;
   outline: 0 !important;
}

.share_property_icons
{
        text-align: center;
   border-top: 1px solid #C2C2C2;
   margin-top: 17px;
   padding-top: 20px;
   padding-bottom: 10px;
}

.share_property i.fa.fa-facebook-square {
   color: #3B5998;
   font-size: 35px;
}

.share_property i.fa.fa-twitter {
   color: #42B6E7;
   font-size: 35px;
   margin-left: 30px;
}

.share_property .select_mate.border{
        font-size: 16px;
   color: #8D8D8D;
}

.know_u_pass {
   font-size: 12px;
   color: #8D8D8D;
   margin-top: 4px;
   margin-bottom: 27px;
}

.know_u_pass span a {
   font-size: 12px;
   color: #10B8A8;
   text-decoration: underline;
}

ul.click_menu_open {
   background: #FFFFFF;
   box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
   border-radius: 4px;
   padding: 10px 10px;
   position: absolute;
   left: -200px;
   width: 200px;
   display: none;
   z-index: 2;
}

ul.click_menu_open li {
   list-style-type: none;
}

.pos_rel {
   position: relative;
}

.open_click_menu {
   position: relative;
}




@media only screen and (max-width: 767px) {


ul.mobile_slider_menu  {
   font-size: 16px;
   color: #FFFFFF;
   padding: 0;
   text-align: center;
   margin: 20px 0px;
}

ul.mobile_slider_menu a {
   font-size: 16px;
   color: #FFFFFF;
}


ul.mobile_slider_menu li {
   display: inline;
   margin-right: 20px;
   padding: 5px 5px 3px 5px;
}

li.mobile_active {
   border-bottom: 3px solid #10B8A8;
}

.homepage_slider img.img-responsive {
   height: 482px;
   width: 100%;
   object-fit: cover;
}

div#bs-example-navbar-collapse-1 {
   border-color: transparent;
       height: calc(100vh - 50px);
}

.mobile_search_menu .form_group.col-sm-3 {
   width: 66%;
}

.mobile_search_menu .form_group.text-center {
   width: 34%;
   float: right;
   margin-top: 15px;
}

.display_none_map
{
    display: none;
}
.map_side_bar {
   position: relative;
   width: 100%;
   float: left;
   background: transparent;
   height: 100%;
   padding:10px 15px 0px 15px;
}

img.symbols-map-hover {
   object-fit: cover;
}

footer {
   position: relative;
   left: 0;
   bottom: 0;
   height: auto;
   width: 100%;
   overflow: hidden;
}
body {
   margin-bottom: 0px !important;
}

.show_map_button {
   font-size: 12px;
   color: #FFFFFF;
   letter-spacing: 0;
   background: #10B8A8;
   display: table;
   margin: auto;
   padding: 6px 17px 4px 17px;
   border-radius: 22px;
   position: absolute;
   bottom: 40px;
      left: 36%;
   width: 93px;
}

.show_map_mobile
{
    display: block;
}

.hide_map_mobile
{
    display: none;
}

.show_sidebar_m {
   font-size: 12px;
   color: #FFFFFF;
   letter-spacing: 0;
   background: #10B8A8;
   display: table;
   margin: 10px 0px;
   padding: 6px 24px 5px 24px;
   border-radius: 20px;
   position: absolute;
   bottom: 0px;
   left: 36%;
}

.date_boxes {
   float: left;
   width: 100%;
   overflow: scroll;
   padding-bottom: 20px;
}
.date_boxes .col-xs-16.over_scroll {
   width: 200%;
}

.row.participating_leader {
   padding-bottom: 10px;
}

a.navbar-brand img
{
    width: auto;
}


ul.nav.navbar-nav.navbar-right.visible-xs li {
   display: inline-block;
   float: left;
}

ul.nav.navbar-nav.navbar-right.visible-xs {
   display: inline-block !important;
   width: 55%;
   margin-top: 0px;
   margin-bottom: 0px;
   float: right;
   height: 50px;
   margin-right: 0px;
}

.search_location.header_fix {
   padding-top: 62px;
   padding-bottom: 18px;
}

.map_listing .home_group {
   margin-top: 0px;
}


.col-sm-6.visible-xs {
   float: left;
   width: 100%;
}

.agent_details_check {
   margin-top: 20px;
   float: left;
   width: 100%;
}

.agent_title2 {
   font-size: 25px;
}

.Event_manager_blog {
   margin-top: 30px;
}

.right_agent_text .agent_details_check {
   margin-top: 100px;
}
div#Agent_View {
   box-shadow: none;
}

div#Agent_View {
   box-shadow: none;
}

.check_shown_tables .check_table1 {
   padding-top: 10px;
   padding-bottom: 10px;
   font-size: 16px;
   margin: 0px;
   box-shadow: none;
   width: 100%;
   border-top: 1px solid lightgray;
}

.check_shown_tables .title2.drop_down_icon {
   padding: 3px 0px 3px 15px;
}

.check_shown_tables .check_table1 {
   cursor: pointer;
}

.check_shown_tables .check_table1 .title2 {
   font-size: 16px;
}

div#accordion .panel.panel-default {
   background: transparent;
   border: 0px;
   box-shadow: none;
}

.open_new_page .section4 {
    padding-top: 0% !important;
}

}

@media (min-width: 768px) and (max-width: 1023px) {

    .open_new_page .section4 {
        padding-top: 0%;
    }

}

.property_header h1.prtitle {
   margin-left: 20%;
}
.border_4
{

}

.sign_modal .select_mate p.selecionado_opcion {
   border: 1px solid #DBDBDB;
   border-bottom: 0px;
   box-shadow: none;
}

.save_notes.export_csv {
   margin-right: 13px;
   margin-top: 0px;
   position: relative;
   left: -18px;
}


.add_event_mt
{
    margin-top: 30px;
}

.ml-70
{
        margin-left: 70px;
}

.mls_status {
   font-size: 12px;
   color: #8D8D8D;
}
.mls_detail {
   font-size: 17px;
   color: #5A5A5A;
   font-weight: 600;
   margin-top: 20px;
}
.mls_input{
   margin-bottom: 20px;
   outline: none;
}
.upgrade_input{
   margin-top: 10px !important;
   outline: none;
}

.calendar-table table tr th:nth-child(1), .calendar-table table tr td:nth-child(1) {
   padding-left: 5px !important;
}

.ev_form label {
   font-size: 12px;
   color: #8D8D8D;
   display: block;
   margin: 0px;
   font-weight: normal;
}
input.ev_form_group {
   background: #FFFFFF;
   border: 1px solid #DBDBDB;
   border-radius: 2px;
   padding: 5px 4px;
}
.ev_note {
   font-size: 16px;
   color: #5A5A5A;
}

.ev_form_check span.checkmark {
   background: white;
   border-color: white;
}

.ev_form_check label.width_auto {
   margin-left: 2px !important;
   margin-top: 2px !important;
}

.ev_form_check .form_group {
   margin-bottom: 2px;
   display: inline-block !important;
   margin-right: 29px;
   margin-top: 3px;
}

.ev_form input {
   width: 100%;
   outline: 0 !important;
   font-size: 16px;
    color: #8D8D8D;
}
.ev_form {
   margin-top: 20px;
}

.ev_form_check label {
   font-size: 16px;
   color: #5A5A5A;
   display: block;
   font-weight: normal;
   margin-bottom: 0px;
}

.ev_form_check {
   margin-top: 20px;
}

.event_form {
   float: left;
   width: 90%;
   margin-left: 7px;
}

.check_table1.check_table2 .blue_border_button {
   position: relative;
   top: -3px;
   margin-left: 12px;
}

table.table.checkins_table th {
   font-size: 16px;
   color: #5A5A5A;
}
.check_table1.check_table2.checkins_table th {
   font-size: 16px;
   color: #5A5A5A;
}

input.meassage_search.checkin {
   background: #FFFFFF;
   border: 1px solid #DBDBDB;
   border-radius: 2px;
}

.fa_fa_right i.fa.fa-chevron-right {
   color: white;
   font-weight: normal;
   font-size: 27px;
   position: absolute;
   top: 40px;
   right: 10px;
}

.message_list {
   position: relative;
}


.map_ele_group
{
   position: absolute;
   top: 20%;
   left: 60%;
}

.map_house_abo.hidden-xs {
   display: none;
   position: absolute;
   right: -100%;
}

.map_ele_group2
{
    left: 40%;
   top: 40%
}
.map_ele_group3
{
    top: 20%;
   left: 30%;
}

.check_group.profile_checkbox.guest_check_box span.checkmark {
   background: #FFFFFF;
   border: 1px solid #DBDBDB;
   border-radius: 2px;
}


.checkins_table thead th {
   font-weight: normal;
   vertical-align: middle !important;
}

.check_group.profile_checkbox.guest_check_box .form_group {
   margin-bottom: 1px;
}

img.white_leftarrow {
   height: 16px;
   width: 16px;
   position: absolute;
   top: 43px;
   right: 10px;
   transform: rotate(-89deg);
}
.check_shown_tables
{
    display: flex;
   margin-bottom: 20px;
}
.left_side.height_auto.Recipient_Chosen {
   background: #FFFFFF;
}

.recipient_group {
   margin: 15px;
   padding-bottom: 20px;
}
.recipient_group .title2 {
   font-size: 16px;
   color: #5A5A5A;
   text-align: center;
   margin-bottom: 10px;
}
.recipient_group img.white_leftarrow_image {
   height: 16px;
   transform: rotate(-270deg);
}
.recipient_group .message_short_name {
   margin: auto;
   display: table;
   margin-bottom: 7px;
   margin-top: 8px;
}

.recipient_group {
   border-bottom: 1px solid #C2C2C2;
   float: left;
   width: 91%;
}



.chat_session.Recipient_Chosen_chat {
   margin-left: 0px;
}

.chat_session.Recipient_Chosen_chat .your_chat {
   max-width: 80%;
   margin-bottom: 15px;
}
.chat_session.Recipient_Chosen_chat .other_chat span {
   margin-left: 3px;
   width: 80%;
}

.chat_session.Recipient_Chosen_chat .chat_time {
   margin-top: 5px;
   margin-bottom: 0px;
}


.save_notes.menu6_add {
   display: table;
   margin: auto;
   float: none;
}
.deny_css
{
        border: 1px solid #F06292;
   background: transparent;
   color: #F06292;
}

img.payicon_1 {
   height: 46px;
   position: relative;
}

.form_group.text-left span.checkmark {
   top: 11px;
}


.Icon_button{
    float: right;
   position: absolute;
   right: 0px;
   top: -9px;
}

img.check_icon.back_check_1 {
   background: #FFF9E7;
   height: 31px;
   padding: 7px;
   border-radius: 30px;
}

img.check_icon.back_check_2 {
   background: #E4F8F8;
   height: 31px;
   padding: 7px;
   border-radius: 30px;
}

.add_new_list.dis_inline.ml-o
{
    margin-left: 0px;
}

.bold_font
{
        color: #5A5A5A;
        font-family: 'Source Sans Pro', sans-serif;
}

img.title_button {
   margin-left: 10px;
}

img.img-responsive.title_pro_logo {
   display: inline-block;
   height: 25px;
}

img.OpenHouse_book {
   float: left;
}

.bitmap_img {
   position: absolute;
   right: -80px;
   top: 16%;
}
.width_85.pull-left
{
    position: relative;
}
.table-responsive.selected_saved tbody td span.bold_font {
   color: #5A5A5A;
   font-family: 'Source Sans Pro', sans-serif;
}

.border.row.mt-0 .form_group {
   margin-bottom: 20px;
}

.border.row.mt-0 .form_group input.new_form.message_contact {
   height: 150px;
}


.more_3
{
    font-size: 12px;
    color: #10B8A8;
    text-decoration: underline;
    cursor: pointer;
}
.border.row.mt-0
{
    margin-top: 0px;
}
@media only screen and (min-width:1000px) {
    div#accordion .panel.panel-default {
        border: 0px;
    }
    .panel.panel-default h2.bg_title.drop_down_icon {
         background-image: none !important;
    }
}

input.select_box_price {
   width: 100%;
   height: 43px;
   border: 0px;
   padding-left: 8px;
   outline: 0 !important;
   color: #333;
}

.square_footage {
   position: absolute;
   z-index: 11;
   width: 223px;
   display: none;
   top: 57px;
}

/*.price_select p.selecionado_opcion {
   padding: 0px;
}*/


.dis_inline.po_rel.colr_ {
   color: #8D8D8D;
}



img.noti_image.symbols-property-image.dis_inline {
   margin-top: 0px;
}

.dis_inline.notification_text {
   font-size: 16px;
   color: #5A5A5A;
   font-family: 'Source Sans Pro', sans-serif;
   width: 100%;
}

.dis_inline.notification_text span {
   font-size: 12px;
   color: #8D8D8D;
   margin-right: 17px;
   float: right;
}

.notification_text {
   font-size: 16px;
       font-family: 'Source Sans Pro', sans-serif;
   color: #8D8D8D;
}

.noti_width{
        width: 70%;
   margin-left: 8px;
}

ul.nav.navbar-nav.navbar-right .notification .see_all_noti a {
   font-size: 12px;
   color: #10B8A8;
}

body.mar_zero {
   margin-bottom: 0px !important;
}

.modal-dialog.modal-md.add_property_empty {
   width: 712px;
}

.modal-dialog.modal-md.add_property_empty .modal-content {
   width: 100%;
}

.dis_inline.po_rel.add_event_em_gr_ti span {
   font-size: 25px;
   color: #10B8A8;
   line-height: 30px;
    font-family: 'Source Sans Pro', sans-serif;
}


.dis_inline.po_rel.add_event_em_gr_ti {
   font-size: 16px;
   color: #8D8D8D;
}

.title_ad_e {
   font-size: 25px;
   color: #676767;
   line-height: 30px;
   margin-bottom: 10px;
   padding-bottom: 10px;
}

.event_gr_de {
   margin-top: 40px;
}

label.ad_e {
   font-size: 12px;
   color: #8D8D8D;
   font-weight: normal;
}

.title_ad_e.mt-20 {
   float: left;
   display: block;
   width: 100%;
}

.event_gr_de label.ad_e {
   margin-top:15px;
}


.event_gr_de .form_group.ml-20 {
   margin-top: 3px;
   margin-bottom: 4px;
}

.new_form_group.profile_save_button.ad_e_pro.col-sm-16 {
   text-align: left;
}

.new_form_group.profile_save_button.ad_e_pro.col-sm-16 input.cancle_button.dis_inline {
   margin-left: 5px;
   color: #10B8A8;
   text-decoration: underline;
}

.new_form_group.profile_save_button.ad_e_pro.col-sm-16 input.submit_button.with_bg.dis_inline.disable {
   opacity: 0.6;
}

.add_eve_sear.col-sm-16 .title2 {
   font-size: 16px;
   color: #5A5A5A;
}

.add_eve_sear.col-sm-16 {
   margin-top: 30px;
   padding: 0px;
}

.match_found_ad_e {
   font-size: 12px;
   color: #8D8D8D;
   width: 100%;
   float: left;
   margin-top: 10px;
   padding-left: 15px;
}

.agent_found_css span {
   font-size: 16px;
   color: #8D8D8D;
}

.agent_found_css div {
   font-size: 16px;
   color: #8D8D8D;
   float: right;
   padding-top: 15px;
}

.agent_found_css {
   font-size: 12px;
   color: #8D8D8D;
   float: left;
   width: 100%;
   margin-top: 0px;
   margin-left:10px;
}

.found_agent_add_p
{
   float: left;
   width: 100%;
   margin: 0px;
   padding-left: 15px;
}

.check_group.mt-10.found_agent_add_p span.checkmark {
   top: 31px !important;
}

.check_group.mt-10.found_agent_add_p input[type="checkbox"] {
   top: -32px;
}

span.checkmark
{
        background: #FFFFFF;
   border: 1px solid #DBDBDB;
   border-radius: 2px;
}

.modal-body.bor_top_bg {
   border-top: 5px solid #10B8A8;
   padding: 0px;
}


@media only screen and (max-width:767px) {
    .mobile_search_menu {
        /* padding-left: 19px; */
        background: transparent;
        border-bottom: 1px solid white;
        padding-bottom: 13px;
    }

    .mobile_search_menu_2 input {
        background: transparent;
        border: 0px;
        font-size: 16px;
        color: #FFFFFF;
        margin-top: 10px;
        outline: 0 !important;
        background-image: url(../images/symbols-glyph-openhouse.png);
        background-repeat: no-repeat;
        background-size: contain;
        padding-left: 30px;
    }

    img.img-responsive.mobile_white_logo {
       height: 29px;
       padding-left: 20px;
       margin-bottom: 10px;
       margin-top: 10px;
       display: inline-block;
    }

    li.visible-xs.mobile_logo_menu span {
   float: right;
   display: inline-block;
   margin-top: 9px;
   margin-right: 20px;
   font-size: 21px;
}


ul.nav.navbar-nav.navbar-right.visible-xs.mobile_bottom_menu {
   width: 100%;
   bottom: 0px;
   position: absolute;
}

ul.nav.navbar-nav.navbar-right.visible-xs.mobile_bottom_menu li {
   width: 20%;
}

.sign_modal .modal-content {
   width: 100%;
}

.modal-content {
   position: relative;
   background-color: transparent;
   border: none;
   border: none;
   border-radius: 0px;
   -webkit-box-shadow: none;
   box-shadow: none;
   -webkit-background-clip: unset;
   background-clip: padding-box;
   outline: 0;
}


}

.note span {
   float: right;
   margin-right: 26px;
}


.note a {
   color: #FFFFFF;
   text-decoration: underline;
}


#map {
   height: calc(100vh - 192px) !important;
   width: 100%;
}


.right_side_event_agent.right_side_overflow {
   height: calc(100vh - 117px);
   overflow-y: scroll;
}


.new_form_group.new_form_select_css.width_50
{
        width: 39%;
   float: left;
   margin-right: 17px;
}

.new_profile_group_wrap.add_listing {
   margin-bottom: 100px;
}

p.sp-layer.sp-text {
   background: #566D77;
   width: 100%;
   font-size: 16px;
   color: #FFFFFF;
   padding: 15px 30px 17px 30px;
   display: none;
}

p.sp-layer.Manage.manage1 {width: 100px;left: 78% !important;text-align: center;margin-top: 9px;display: none;}

p.sp-layer.Manage.manage2 {
   width: 100px;
   left: 87% !important;
   text-align: center;
   margin-top: 9px;
   display: none;
}

.sp-full-screen .sp-image-container img {
    width: auto !important;
    margin: auto !important;
    object-fit: cover;
}

.sp-full-screen {
   margin: 0 !important;
   background: #37474F !important;
}

.sp-full-screen p.sp-layer.sp-text ,.sp-full-screen p.sp-layer.Manage.manage1 ,.sp-full-screen p.sp-layer.Manage.manage2
{
    display: block;
}

.sp-full-screen .sp-bottom-thumbnails {
   padding: 10px;
   background: transparent;
}


.sp-full-screen  .sp-full-screen-button:before {
   content: url(../images/icons8-delete-32.png);
   color: white;
   height: 10px;
   width: 10px;
   top: 4px;
   position: relative;
   background: none;
   left: -10px;
}
.sp-arrow.sp-next-arrow {
   content: url(../images/white_leftarrow.png);
   height: 30px;
   width: auto;
   transform: rotate(-90deg);
   right: 10px !important;
}

.sp-arrow.sp-previous-arrow {
   content: url(../images/white_leftarrow.png);
   height: 30px;
   width: auto;
   transform: rotate(90deg);
   left: 10px !important;
}

.sign_modal .select_mate p.selecionado_opcion {
   border: 1px solid #DBDBDB;
   border-bottom: 0px;
   box-shadow: none;
   font-size: 16px;
   color: #8D8D8D;
}


.check_group .form_group input[type="radio"] {
   opacity: 0;
   cursor: pointer;
   z-index: 111;
}
.check_group .form_group input[type="radio"] {
   position: relative;
   top: 2px;
   margin-right: 8px;
}

.check_group .form_group input[type="radio"]:checked ~ .checkmark {
   background-image: url(../images/symbols-glyph-form-check.png);
   background-size: 100% 100%;
}



.sp-thumbnail-arrow.sp-previous-thumbnail-arrow {
   content: url(../images/white_leftarrow.png);
   height: 30px;
   width: auto;
   transform: rotate(90deg);
   left: 10px !important;
}

.sp-thumbnail-arrow.sp-next-thumbnail-arrow {
   content: url(../images/white_leftarrow.png);
   height: 30px;
   width: auto;
   transform: rotate(-90deg);
   right: 10px !important;
}

.chat_session.Recipient_Chosen_chat {
   float: left;
   width: 100%;
}


.chat_session.Recipient_Chosen_chat.message_list_with_name {
   height: 337px;
   overflow-y: scroll;
}

.chat_session.Recipient_Chosen_chat.message_list_with_name {
   height: calc(100vh - 324px);
   overflow-y: scroll;
}


.message_new_width
{
        width: calc(100vw - 439px);
}

input.select_box_price.drop_down_icon {
   background-position-x: 96%;
}

input.select_box_price2.drop_down_icon {
   background-position-x: 91%;
}

p.sp-layer.Manage.manage3
{
    display: none;
}

.sp-full-screen p.sp-layer.Manage.manage3 {
   background: transparent;
   position: absolute;
   display: block;
   right: 10px !important;
   top: 60px !important;
   font-size: 15px;
   left: 94% !important;
}

/* New Page Csssssss */



.open_new_page {
   background: #fff;
   /* padding-top: 54px; */
}

.open_new_page .section_1 {
   background-image: url(../images/OHD_BG.png);
   padding: 150px 0px 0px 100px;
   background-position: bottom center;
   background-size: cover;
   display: flex;
}

.open_new_page .section_1_agent {
  background-image: url(../images/hero-background.png);
  padding: 20% 0% 8% 0%;
  background-position: bottom center;
  background-size: cover;
}

.open_new_page .section_1_broker{
  background-image: url(../images/agent_top.png);
  padding: 16% 0% 8% 0%;
  background-position: bottom center;
  background-size: cover;
}


.open_new_page .section_1 .banner_title {
    color: #ffffff;
    font-size: 34px;
    font-weight: 700;
    text-align: left;
    line-height: 40px;
 }

 .open_new_page .section_1 .banner_text {
    color: #ffffff;
    font-size: 16px;
    font-style: normal;
    font-stretch: normal;
    font-weight: 400;
    text-align: left;
    margin-top: 20px;
    word-spacing: 1px;
 }
 .open_new_page .section_1 .banner_text sup{
    font-size: 67%;
 }

 .open_new_page .section_1 .banner_group {
    max-width: 44%;
 }

 .open_new_page .section_1_agent .banner_group {
   max-width: 44%;
 }

 .open_new_page .section_1 .search_group{
     width: 100%;
     margin-right: 16rem;
     margin-top: 2.5rem;
 }

 .open_new_page .section_1 .search_group .search_text{
     text-align: center;
     font-size: 24px;
     margin-top: 8px;
 }

 .open_new_page .section_1 .search_group .search_text_below{
    text-align: center;
    font-size: 20px;
    margin-top: 48px;
    padding-left: 0;
 }

 .search_button{
     margin-top: 20px !important;
 }

 .search_button .input-group{
     width: 90%;
     margin: 0 auto;
 }

 .search_button .input-group .search-input-layout{
     width: 100% !important;
     height: 50px;
     box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px !important;
     /* border-radius: 50px !important; */
     padding-left: 3rem;
     font-size: 20px;
 }

 /* .search_button .input-group .search-input-layout:not(:placeholder-shown) {
    border-bottom-left-radius: 10px !important;
    border-bottom-right-radius: 10px !important;
  } */

 .search_button .input-group .search-input-layout::-webkit-input-placeholder{
     font-size: 20px;
     line-height: 24;
 }
 .search-span{
     position: relative !important;
 }
 .search-span,.search-result{
    width: 89.9% !important;
    z-index: 9999;
    margin: -.3rem auto 0 auto;
 }

 .search-result-map{
     width: 100% !important;
     left: 0 !important;
 }

 .caption[_ngcontent-c1]{
     width: 82% !important;
 }

.open_new_page .sec_2_title {
   color: #676767;
   font-size: 60px;
   font-weight: 400;
   text-align: center;
   line-height: 64px;
   max-width: 92%;
   margin-left: 3%;
}

.open_new_page .sec_2_title span {
   color: #00B7A6;
}

.open_new_page .sec_2_text {
   color: #676767;
   font-size: 24px;
   margin-top: 20px;
   font-weight: 400;
   text-align: center;
}

.open_new_page .section2 {
   padding: 120px 0px 100px 0px;
   background: #fff;
}

.open_new_page .section2_group {
   max-width: 50em;
   margin: auto;
}

.open_new_page .section3 .sec_3_title {
   color: #676767;
   font-size: 40px;
   font-weight: 400;
   text-align: center;
}

.open_new_page .section3 .sec_3_video {
    margin: 50px 0px;
    height: 536px;
    color: #676767;
    font-size: 40px;
    font-weight: 400;
    text-align: center;
    background-position: bottom center;
    background-size: cover;
    background-image: url(../images/Rectangle.jpg);
}

.open_new_page .section3 .video_icon {
   display: flex;
   vertical-align: middle;
   align-items: center;
   justify-content: center;
   height: 100%;
   opacity: 1;
   background-color: #43454573;
}

.open_new_page .section3 .sec_3_group {
    width: 96%;
    margin-left: 1.8%;
    margin-bottom: 2.8%;
}

.sec_4_left_gorup{
    margin-left: 4%;
}

.sec_4_right_group{
    margin-right: 4%;
}

.sec_3_bottom_text_sub{
  padding-top: 20px;
}

.open_new_page .section3 .sec_3_bottom {
   border-radius: 0;
   position: absolute;
   background: #00c2b7;
   color: #ffffff;
   font-size: 18px;
   font-weight: 400;
   text-align: center;
   margin: auto;
   padding: 80px 120px 60px 120px;
   max-width: 62.5em;
   left: 0;
   right: 0;
}

.open_new_page .section3 .sec_3_bottom_title {
   color: #ffffff;
   font-size: 40px;
   font-weight: 400;
   text-align: center;
   margin-bottom: 20px;
}
.open_new_page .section3 .sec_3_bottom_title span{
    font-weight: 700;
}

.open_new_page .section3 .sec_3_bottom_text {
   color: #ffffff;
   font-size: 18px;
   font-weight: 400;
   text-align: center;
   width: 97%;
   margin: auto;
   letter-spacing: 1px;
   line-height: 1.7;
}

.open_new_page .section3 {
   background: #eceff1;
   padding-top: 70px;
   position: relative;
   margin-bottom: 180px;
   padding-bottom: 260px;
   text-align: center;
}

.open_new_page .section5 .sec_5_text {
   color: #9c9c9c;
   font-size: 23px;
   font-weight: 400;
   text-align: center;
   margin-bottom: 8px;
   font-style:oblique;
}

.open_new_page .section4 {
   padding: 70px 0px;
   background: #fff;
   padding-top: 17em;
}

.open_new_page .sec_4_title {
   color: #676767;
   font-size: 55px;
   font-weight: 400;
   line-height: 54px;
   text-align: left;
   width: 85%;
}

.open_new_page .sec_5_title {
  color: #676767;
  font-size: 40px;
  font-weight: 400;
  line-height: 49px;
  text-align: left;
  width: 100%;
}

.open_new_page .sec_footer_title{
  font-size: 24px;
  font-weight: 400;
  color: #9c9c9c;
}

.open_new_page .sec_5_sub_title {
  font-size: 23px;
  color: #676767;
  font-weight: 400;
  width: 71%;
}

.social{
  float: right;
}
.social .fb{
  padding-right: 15px;
}

.open_new_page .sec_4_text {
   color: #676767;
   font-size: 18px;
   font-weight: 400;
   text-align: left;
   margin: 20px 0px 30px 0px;
   width: 85%
}

.open_new_page .sec_4_download_app {
   color: #8d8d8d;
   font-size: 16px;
   font-weight: 400;
   text-align: left;
}

.open_new_page .sec_4_title span,
.sec_4_download_app span,
.open_new_page .section3 .sec_3_title span {
   color: #00B7A6;
}

.open_new_page img.android.img-responsive,
.open_new_page img.ios.img-responsive {
   display: inline-block;
   cursor: pointer;
}

.section5 {
   background: #fff;
}

@media only screen and (max-width: 767px) {
   nav.visible-xs.mobile_new_menu ul li {
        list-style-type: none;
   }
   .mobile_new_menu {
       background: #00B8A7;
       float: left;
       width: 100%;
       top: -50px;
       position: relative;
       padding-top: 50px;
   }
   span.close_menu {
        color: white;
   }
   ul.nav.navbar-nav.menu_new li a {
        display: inline-block;
   }
   .open_new_page {
        /* padding-top: 52px; */
   }
   ul.nav.navbar-nav.navbar-right li {
        display: inline-block;
        width: 32%;
   }
   button.btn.btn-info.menu_button {
       background: transparent;
       border: 0px;
       box-shadow: none;
       float: right;
       margin: 8px 10px 0px 0px;
       outline: 0 !important;
   }
   ul.nav.navbar-nav li a,
   ul.nav.navbar-nav li a:hover,
   ul.nav.navbar-nav li a:focus {
        /* color: #676767; */
        padding: 6px 15px;
   }
   ul.nav.navbar-nav.navbar-right a,
   ul.nav.navbar-nav.navbar-right a:hover,
   ul.nav.navbar-nav.navbar-right a:focus {
        padding: 6px 15px;
   }
   .open_new_page .section_1 {
        padding-left: 30px;
        padding-right: 30px;
   }
   .open_new_page .section_1 .banner_title {
        font-size: 38px;
        line-height: 48px;
   }
   .open_new_page .section_1 .banner_text {
        max-width: 350px;
   }
   .open_new_page .section_1 .banner_group {
        max-width: 100%;
   }
   .open_new_page .section_1_agent .banner_group {
    max-width: 100%;
  }
   .open_new_page .section2 {
        padding: 10px 0px 20px 0px;
   }
   .open_new_page .section2_group {
        max-width: 100%;
   }
   .open_new_page .sec_2_title {
        font-size: 30px;
        margin-bottom: 20px;
        line-height: 38px;
   }
   .open_new_page .sec_2_text {
        margin-top: 20px;
        font-size: 18px;
   }
   .open_new_page .section3 {
        padding-top: 30px;
   }
   .open_new_page .section3 .sec_3_title {
        font-size: 30px;
        line-height: 44px;
   }
   .open_new_page .sec_4_title {
        line-height: 40px;
   }
   .open_new_page .section5 .sec_5_text {
        margin-bottom: 30px;
   }
   .open_new_page .section3 .sec_3_video {
        margin: 20px 0px;
        height: 226px;
   }
   .built-text{
        width: 100% !important;
        color: white !important;
        line-height: 33px !important;
        font-size: 30px !important;
   }
   .built-text-read-btn{
        width: 43% !important;
   }
   .open_new_page .section3 .sec_3_bottom {
        padding: 30px 20px;
        position: relative;
        width: 97%;
        bottom: 0px;
   }
   .open_new_page .section3 .sec_3_bottom_title {
        font-size: 30px;
   }

   .open_new_page .section3 .sec_3_bottom_text {
        width: 100%;
        margin: auto;
        letter-spacing: 0px;
    }
   .open_new_page .section3 {
        margin-bottom: 10px;
        padding-bottom: 30px;
   }
   .open_new_page .sec_4_title {
        font-size: 30px;
   }
   .open_new_page .sec_4_text{
       width: 100%
   }
   .open_new_page .sec_4_title {
        color: #676767;
        font-size: 40px;
        font-weight: 400;
        text-align: left;
   }

   .open_new_page .sec_5_title {
    color: #676767;
    font-size: 30px;
    font-weight: 400;
    text-align: left;
    line-height: 32px;
}

.open_new_page .sec_5_sub_title {
    font-size: 16px;
    color: #676767;
    font-weight: 400;
    width: 100%;
  }
   .sec_4_right_group.pull-right {
        float: none !important;
        margin: 10px auto;
        display: table;
   }
   .open_new_page .section5 .sec_5_text {
        font-size: 18px;
        font-style: oblique;
    }
   nav.mobile_new_menu li a {
       font-size: 16px;
       color: white;
       padding: 9px 20px;
       font-weight: normal !important;
       float: left;
       width: 100%;
   }
   nav.mobile_new_menu ul {
       float: left;
       width: 104%;
       padding-left: 0px;
       height: 85vh;
   }
   .mobile_new_menu ul li {
       list-style-type: none;
   }
   ul.mobile_icon li {
       display: inline-block;
       width: calc(73vw / 10);
   }
   ul.mobile_icon {
       padding: 0px !important;
   }
   ul.mobile_icon li a {
       padding: 0px !important;
       text-align: center !important;
   }
   ul.mobile_icon li a img {
       margin: auto;
   }
   li.visible-xs.mobile_logo_menu {
       background: transparent;
       z-index: 11111;
       position: relative;
   }
   li.mobile_new_search_menu {
       border-bottom: 1px solid white;
       padding-left: 10px;
       margin-bottom: 10px;
   }

   .property-view-padding
   {
           padding-left:15px;
           padding-right:15px;
   }

   body .bg-white .property-view-padding
   {
           padding-left:15px;
           padding-right:15px;
   }

   .date_boxes .event-box {
       width: 55% !important;
   }

   .date_boxes.event-box {
       width: 100%;
   }

   .col-xs-16.scrollmenu::-webkit-scrollbar {
       display: none;
   }


   .property_des.row div#accordion {
       width: 100%;
   }
   li.visible-xs.mobile_logo_menu.back_white_menu {
       background: #fff;
   }

   li.visible-xs.mobile_logo_menu.back_white_menu i.fa.fa-bars {
       /* border: 1px solid #00B7A6; */
       padding: 6px 8px;
       color: #00B7A6;
       font-size: 21px;
   }

   .back_white_menu button.click_mobile_menu.btn.btn-info {
       margin-top: 5px;
       margin-right: 3px;
   }

   .back_white_menu ul.mobile_icon li {
       width: 20%;
       float: left;
   }

   .back_white_menu ul.mobile_icon {
       display: inline-block;
       width: 40%;
       top: 16px;
       position: relative;
       float: right;
   }

   .back_white_menu + nav#menu_group_mobile ul {
       height: 91vh;
   }
   .change_bg_color
   {
       background: #00B7A6 !important;
   }
}

@media (max-width: 1023px) and (min-width: 768px){
    .open_new_page .sec_2_text {
        font-size: 16px;
    }
    .content-pending{
        padding-top: 50px !important;
    }
    .home_raw_btn.dis_inline{
        width: 40% !important;
    }
}

/* @media only screen and (min-width: 767px) {
   #menu_group_mobile {
       display: none;
   }
} */

.dis_mo_none
{
   display: none !important;
}


.back_white_menu .mobile_close{
color: #00B8A6;
}
.mobile_close {
   color: #fff;
   float: right;
   margin-top: 7px;
   padding-right: 6px;
   margin-right: 19px;
   font-size: 22px;
   font-weight: 100;
   cursor: pointer;
   padding-left: 6px;
   margin-left: 14px;
}

.mobile_close .fa-remove:before,.mobile_close .fa-close:before,.mobile_close .fa-times:before {
   content: "\f00d";
   font-size: 12px;
}


.loading-bg-img{
   background: url(../images/trans.png) repeat scroll 0 0 rgba(0, 0, 0, 0);
   color: #FFFFFF;
   padding: 5px 0;
   text-align: center;
   width: 225px;
   border-radius: 10px;
   margin: auto;
}

.map-result-loader{
   /* left: 644px; */
   position: absolute !important;
   bottom: 5%;
   width: 100%;
}

.loading-map-icon{
   height: 15px;
}

.right-loading-bg{
   background: url(../images/transparent.png);
   color: #FFFFFF;
   height: 100%;
   z-index: 1000;
}

.right_bar{
   position: absolute;
   width: 100%;
   z-index: 100;
   height: 100%;
}

.map_right_rail{
   position: absolute;
   top: 0px;
   right: 0;
   height: 100%;
   width: 350px;
}


/*
Agent/Broker Upgrade and Landing Page CSS
*/

/* ------------------Start---------------- */



/* -------------------end------------------ */
/* .cke_dialog
{
    z-index: 10055 !important;
} */

.ck-rounded-corners .ck.ck-balloon-panel, .ck.ck-balloon-panel.ck-rounded-corners {
    z-index: 10055 !important;
}
.markerLabel  {
    margin-left: 18px;
}
