<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <title>OpenToCloseAngular</title>
  <base href="/">

    <link rel="stylesheet" href="./assets/styles.css">
    <link rel="stylesheet" href="./assets/css/style.css">
    <link rel="stylesheet" href="./assets/css/bootstrap.css">
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/bootstrap-theme.css">
    <link rel="stylesheet" href="./assets/css/bootstrap-theme.min.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/custom.css">
    <link rel="stylesheet" type="text/css" media="all" href="../assets/css/daterangepicker.css"/>
    <link rel="stylesheet" type="text/css" media="all" href="../assets/css/datepicker.css"/>



    <script type="text/javascript" src="./assets/js/jquery-3.3.1.js"></script>
    <script type="text/javascript" src="./assets/js/jquery.sumoselect.js"></script>
    <script type="text/javascript" src="./assets/js/select.js"></script>
    <script type="text/javascript" src="./assets/js/moment.js"></script>
    <script type="text/javascript" src="./assets/js/daterangepicker.js"></script>
    <script type="text/javascript" src="./assets/js/infobubble.js"></script>
    <script type="text/javascript" src="./assets/js/require.js"></script>
    <script type="text/javascript" src="./assets/js/datepicker.js"></script>

    <link rel="icon"  href="./assets/images/favicon.png">
    <link href="./assets/ngToaster/ng2-toastr.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="./assets/ngToaster/ng-toast-style.css">
    <link rel="stylesheet" href="./assets/css/font-awesome.min.css">
    <link href="./assets/ng-select/default.theme.css" rel="stylesheet">
    <link rel="manifest" href="./manifest.json">
    <link rel="stylesheet" href="./assets/ng2dnd/style.css">
    <link rel="stylesheet" href="./assets/css/tablet.css">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <!-- <link rel="stylesheet" href="assets/home/<USER>/style.css"> -->
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,600,600i,700,700i" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,600;0,700;0,800;1,300;1,400;1,600;1,700;1,800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

</head>
<body>
  <div id="fb-root"></div>
  <script>
    window.fbAsyncInit = function() {
    FB.init({appId: '854805355265204', status: true, cookie: true,
    xfbml: true});
    };
    (function() {
    var e = document.createElement('script'); e.async = true;
    e.src = document.location.protocol +
    '//connect.facebook.net/en_US/all.js';
    document.getElementById('fb-root').appendChild(e);
    }());
  </script>

  <!-- Facebook Pixel Code -->
<script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '373562147204484');
  fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none"
  src="https://www.facebook.com/tr?id=373562147204484&ev=PageView&noscript=1"
/></noscript>
<!-- End Facebook Pixel Code -->

  <script>window.twttr = (function(d, s, id) {
    var js, fjs = d.getElementsByTagName(s)[0],
      t = window.twttr || {};
    if (d.getElementById(id)) return t;
    js = d.createElement(s);
    js.id = id;
    js.src = "https://platform.twitter.com/widgets.js";
    fjs.parentNode.insertBefore(js, fjs);

    t._e = [];
    t.ready = function(f) {
      t._e.push(f);
    };

    return t;
  }(document, "script", "twitter-wjs"));</script>

  <app-root></app-root>
  <script type="text/javascript" src="./assets/js/jquery.sliderPro.js"></script>
  <script src="./assets/js/bootstrap.min.js"></script>
  <script src="./assets/js/script.js"></script>
  <script type="text/javascript" src="./assets/js/listing_key.js"></script>


  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBhuVc1V9fAtU7KftUoHrsVuIZH0Y7E6ow"></script>
</body>
</html>
