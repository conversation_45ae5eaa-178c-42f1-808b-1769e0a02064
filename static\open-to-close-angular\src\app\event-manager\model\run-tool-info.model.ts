export class BasicInfo{
    address : String;
    brokerage_name : String;
    listing_agent_id : any;
    listing_agent_image : String;
    listing_agent_name : String;
    location : String;
    property_file : String;
    property_id : any;
    event_type : String;
    date : String;
    start_time : String;
    end_time : String;
    is_listhub_event : Boolean;
}

export class OpenHouseOverview{
    total_going : any;
    total_interested : any;
    total_represented : any;
    total_unrepresented : any;
}

export class RatingOverview{
    bathroom_negative : any;
    bathroom_positive : any;
    bedroom_negative : any;
    bedroom_positive : any;
    finishes_negative : any;
    finishes_positive : any;
    floorplan_negative : any;
    floorplan_positive : any;
    kitchen_negative : any;
    kitchen_positive : any;
    landscaping_negative : any;
    landscaping_positive : any;
    negative : any;
    neighbourhood_negative : any;
    neighbourhood_positive : any;
    positive : any;
    price_negative : any;
    price_positive : any;
    ratings : any;
}

export class PropertyOverview{
    total_events : any;
    total_going : any;
    total_represented : any;
    total_unrepresented : any;
}