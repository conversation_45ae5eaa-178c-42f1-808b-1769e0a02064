import { Component, OnInit,ViewChild } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { FormGroup, FormControl,Validators } from '@angular/forms';
import { AuthService } from '@app/auth/services/auth.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { AdminHeaderComponent } from '@app/admin/admin-header/component/admin-header.component';

@Component({
  selector: 'admin-login',
  templateUrl: '../views/admin-login.component.html',
  styleUrls: ['../css/admin-login.component.css']
})
export class AdminLoginComponent extends BaseComponent implements OnInit {

  public isOptionShow : Boolean = false;

  adminLoginForm : FormGroup;
  public authService:AuthService;

  constructor() {
    super();
    this.authService=ServiceLocator.injector.get(AuthService);
   }

  ngOnInit() {
    this.initData();

    if(localStorage.getItem('adminToken')){
      this.router.navigate(['admin/home-buyer']);
    }
  }

  initData(){
    this.adminLoginForm = new FormGroup({
      email: new FormControl('',[Validators.email, Validators.required]),
      password :new FormControl('',Validators.required)
    });
  }

  loginAdmin(form:FormGroup){
    this.authService.singin(form.value).subscribe(res=>{
      form.reset();
      this.setAdminToken(res.result.token);
      if(res.result['is_admin'] != undefined && res.result['is_admin'] == true){
        this.router.navigate(['admin/home-buyer']);
      }
    }, error => this.errorResponse(error.json()));
  }
}
