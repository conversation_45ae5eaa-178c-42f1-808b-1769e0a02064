.mls-input{
    width: 100%;
    height: 36px;
    outline: none;
    padding: 10px 7px;
    font-size: 16px;
    color: #5A5A5A;
}
.city-input{
    width: 100%;
    height: 36px;
    outline: none;
    padding: 10px 7px;
    font-size: 16px;
    color: #5A5A5A;
}
.search-agent-ul{
    list-style-type: none;
    text-align: left;
    color: #5a5a5a;
    padding: 0;
    margin-bottom: 0px;
}
.search-agent-div{
    vertical-align: middle;
    align-items: center;
    justify-content: center;
    padding-top: 10px;
    padding-bottom: 10px;
    cursor: pointer;
    padding-left: 5px;
    padding-right: 4px;
}
.search-agent-div:hover{
    background: #10B8A8;
    color: white;
}
.search-agent-box{
    width: 88%;
    max-height: 300px;
    min-height: 50px;
    overflow-x: auto;
}
.search-agent-BR-box{
    width: 100%;
    max-height: 300px;
    min-height: 50px;
    overflow-x: auto;
}
.search-agent-pos{
    position: absolute;
    background: white;
    width: 88%;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
}
.search-agent-BR-pos{
    position: absolute;
    background: white;
    width: 100%;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.30);
}
.search-input{
    width: 100%;
    height: 36px;
    outline: none;
    padding: 10px 7px;
    font-size: 16px;
    color: #5A5A5A;
}
.status .ng-dropdown-panel{
    width: 129% !important;
}