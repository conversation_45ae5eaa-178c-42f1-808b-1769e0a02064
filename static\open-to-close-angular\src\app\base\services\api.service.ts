import { Injectable } from '@angular/core';
import { Http, Request } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/map'

@Injectable()
export class ApiService{

    constructor(public http:Http){}

    apiCall(options:any):Observable <any>{     
        var request =new Request(options);
        return this.http.request(request)
        .map(res => res.json() , err => err.json());  
    }

    downloadFile(options:any):Observable <any>{     
        var request =new Request(options);
        return this.http.request(request)
        .map(res => res, err => err.json());  
    }
}