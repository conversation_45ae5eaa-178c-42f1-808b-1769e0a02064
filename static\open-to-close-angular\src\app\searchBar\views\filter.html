<div class="filter_page">
    <div class="mobile_page_title">
        <div (click)="cancelFilterSearch()" class="cancle2">Cancel</div>
        <div class="page_title">Filters</div>
        <div class="search" (click)="filterSearch()">Search</div>
    </div>
    <div class="search_location ">
        <div class="row new_shadow visible-xs mobile_search_menu">
            <div class="form_group col-sm-3">
                <input type="text" [(ngModel)]="selectedSuggestions" class="new_form search_icon" #search placeholder="City, State or Zip" [value]="searchedValue" (keyup)="suggestions(search.value)" autocomplete="off">
                <span *ngIf="flag">
                  <div class="baths_group price_ul">
                    <ul class="search-result" >
                      <li class="search-result-li" *ngFor="let value of locationList" (click)="onselectLocation(value)">
                          {{value.suggestion}}
                      </li>
                    </ul>
                  </div>
                </span>
            </div>
        </div>
    </div>
    <div class="filter_group col-xs-16 ">
        <div class="mo_fi_price">
            <div class="mo_fi_name">Price</div>
            <div class="row">
                <div class="col-xs-7">
                    <div class="form_group price_select">
                        <div class="select_mate">
                        <input type="number" id="pricetxt" class="select_box_price cursor_poiner color-black" [(ngModel)]="filterModel.min_price" placeholder="Minimum">
                        </div>
                    </div>
                </div>
                <div class="col-xs-2">
                <hr class="fi_dash" />
                </div>
                <div class="col-xs-7">
                    <div class="form_group price_select">
                        <div class="select_mate">
                        <input type="number" id="pricetxt" class="select_box_price cursor_poiner color-black" [(ngModel)]="filterModel.max_price" placeholder="Maximum">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="filter_group col-xs-16 ">
        <div class="mo_fi_price">
            <div class="row">
                <div *ngIf="filterSearchFrom != 'eventManager'" class="col-xs-9">
                <div class="mo_fi_name">Home Type</div>
                <div class="check_group">
                    <div class="form_group" *ngFor="let homeType of homeTypes">
                        <input class="check-mob" type="checkbox" (change)="setFilterHomeTypes($event)" value="{{homeType.name}}" [(checked)]="homeType.checked">   <span class="checkmark"></span>
                        <!-- <label [ngClass]="{'filter-word-break' : homeType.name == 'Single Family Detached' }" class="width_auto">{{homeType.name}}</label> -->
                        <label [ngClass]="{'filter-word-break' : homeType.name == 'Single Family - Detached' }" class="width_auto">{{homeType.name}}</label>
                    </div>
                </div>
                </div>
                <!-- <div *ngIf="filterSearchFrom == 'eventManager'" class="col-xs-16">
                <div class="mo_fi_name">Event Type</div>
                <div class="check_group">
                    <div class="form_group" *ngFor="let eventType of eventTypes">
                        <input class="check-mob" type="checkbox" (change)="setFilterEventTypes($event)" value="{{eventType.value}}" [(checked)]="eventType.checked">   <span class="checkmark"></span>
                        <label class="width_auto">{{eventType.name}}</label>
                    </div>
                </div>
                </div> -->
                <!-- <div *ngIf="isFavSearch != true" class="col-xs-7"> -->
                    <div *ngIf="showStatusFilter" class="col-xs-7">
                    <div class="mo_fi_name">Status</div>
                    <div class="check_group">
                        <div class="form_group" *ngFor="let statusType of statusTypes">
                            <input class="check-mob" type="checkbox" (change)="setFilterStatus($event)" value="{{statusType.name}}" [(checked)]="statusType.checked">   <span class="checkmark"></span>
                            <!-- <img class="property-status-icon" src="{{statusType.image}}"/> -->
                            <label [ngClass]="{'filter-status-break' : statusType.name == 'PRE-MLS/Coming Soon' }" class="width_auto">{{statusType.name}}</label>
                        </div>
                    </div>
                </div>
                <!-- <div *ngIf="filterSearchFrom == '' || filterSearchFrom == 'eventManager'" class="col-xs-16">
                    <div class="mo_fi_name">Event Type</div>
                    <div class="check_group">
                        <div class="form_group" *ngFor="let eventType of eventTypes">
                            <input class="check-mob" type="checkbox" (change)="setFilterEventTypes($event)" value="{{eventType.value}}"
                                [(checked)]="eventType.checked"> <span class="checkmark"></span>
                            <img class="{{eventType.css}}" src="{{eventType.image}}"/>
                            <label class="width_auto">{{eventType.name}}</label>
                        </div>
                    </div>
                </div> -->

                <div *ngIf="filterSearchFrom == '' && listType == 0" class="col-xs-16">
                    <div class="mo_fi_name">Event Type</div>
                    <div class="check_group">
                        <div class="form_group" *ngFor="let openHouseType of openHouseTypeFilter">
                            <input class="check-mob" type="checkbox" (change)="setFilterOpenHouseTypes($event)" value="{{openHouseType.value}}"
                                [(checked)]="openHouseType.checked"> <span class="checkmark"></span>
                            <!-- <img class="{{eventType.css}}" src="{{eventType.image}}"/> -->
                            <label class="width_auto">{{openHouseType.name}}</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="filter_group col-xs-16 ">
        <div class="mo_fi_price">

            <div class="row">
                <div *ngIf="searchFrom != 'eventManager'" class="col-xs-7">
                    <div class="mo_fi_name">Bedrooms</div>
                    <div class="form_group padding-0 col-sm-2">
                        <ng-select class="custom search-dropdown cursor_poiner"
                            placeholder="Beds"
                            [items]="BedsList"
                            bindValue="value"
                            bindLabel="value"
                            [clearable]=false
                            [searchable]=false
                            [virtualScroll]=true
                            [(ngModel)]='selectedBeds'
                            (change)="setFilterBeds($event)"
                            >
                        </ng-select>
                    </div>
                </div>
                <div class="col-xs-2">

                </div>
                <div *ngIf="searchFrom != 'eventManager'" class="col-xs-7">
                    <div class="mo_fi_name">Bathrooms</div>
                    <div class="form_group padding-0 col-sm-2 drop-down-spc">
                        <ng-select class="custom search-dropdown cursor_poiner"
                            placeholder="Baths"
                            [items]="BathList"
                            bindValue="value"
                            bindLabel="value"
                            [clearable]=false
                            [searchable]=false
                            [(ngModel)]='selectedBaths'
                            (change)="setFilterBaths($event)"
                            >
                        </ng-select>
                    </div>
                </div>

            </div>
        </div>
    </div>



    <div class="filter_group col-xs-16 ">
        <div class="mo_fi_price">
            <div class="mo_fi_name">Sq. ft.</div>
            <div class="row">
                <div class="col-xs-7">
                    <div class="form_group price_select">
                        <div class="select_mate">
                        <input type="number" id="pricetxt" class="select_box_price cursor_poiner color-black" [(ngModel)]="filterModel.min_sqft" placeholder="Minimum">
                        </div>
                    </div>
                </div>
                <div class="col-xs-2">
                <hr class="fi_dash" />
                </div>
                <div class="col-xs-7">
                    <div class="form_group price_select">
                        <div class="select_mate">
                        <input type="number" id="pricetxt" class="select_box_price cursor_poiner color-black" [(ngModel)]="filterModel.max_sqft" placeholder="Maximum">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="mo_fi_save col-xs-16" *ngIf="listType == 0 || listType == 3">
        <div class="title2">Save this search</div>

        <div class="check_group">
            <div class="form_group">
                <input type="checkbox">   <span class="checkmark"></span>
                <label class="width_auto">Send me alerts based on these features</label>
            </div>
        </div>

        <div class="new_form_group profile_save_button  text-left">
            <input type="submit" class="submit_button with_bg dis_inline" (click)="filterSearch()" value="Search">
            <input type="reset" class="cancle_button dis_inline " (click)="cancelFilterSearch()" value="Cancel">
        </div>

    </div>
</div>

<div>
    <footer></footer>
</div>
