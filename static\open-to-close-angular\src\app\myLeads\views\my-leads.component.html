<div>
  <header></header>
</div>

<div>
    <div class="myclient_page header_fix">
        <div class="container">
            <div class="ls_group my_client_new mt-20">
                <div class="title_group ml_zero">
                    <div class="title">My Leads</div>
                </div>
                <div class="pull-right  m-top">
                    <div class="search_button dis_inline" style="margin-right:25px ; margin-top: 0px !important;">
                        <div class="input-group stylish-input-group">
                            <input type="text" class="form-control margin_zero" id="searchUserInput" placeholder="Search by name" #search (keyup)="searchUser(search.value)">
                            <span class="glyphicon glyphicon-search" style="top: 0px !important;"></span>
                        </div>
                    </div>
                    <div (click)="exportCSV()" class="save_notes export_csv">Export CSV</div>
                </div>
            </div>
            <div class="my_client_table_group">
                <div class="myclient_navbar">
                 <ul>
                    <li (click)="onTabChange('leads')" class="active" data-toggle="pill" href="#Leads" >Leads</li>
                    <li (click)="onTabChange('archive')" data-toggle="pill" href="#Archive" >Archive</li>
                 </ul>
                </div>

                <div class="tab-content">
                    <div id="Leads"  class="tab-pane fade in active table-responsive selected_saved">
                        <div class="No_matches" *ngIf="showLELoader == true">
                            <div class="loader">
                            <div class="message">Loading...</div>
                            <div class="dots"><div class="center"></div></div>
                            </div>
                        </div>
                        <div *ngIf="myLeadsList.length == 0 && showLELoader == false" class="No_matches">
                            <div class="title">No Leads</div>
                            <div class="text">You have no leads in your list. To add leads,<br> run an open house event and all unrepresented buyers attending the event will be added automatically.</div>
                        </div>
                        <div *ngIf="myLeadsList.length != 0" class="property-list-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th (click)="leadSorting('LE','NA')">Name <img id="LE_NA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="leadSorting('LE','PH')">Phone <img id="LE_PH" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="leadSorting('LE','DT')">Date <img id="LE_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="leadSorting('LE','ST')">Buyer Status <img id="LE_ST" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="leadSorting('LE','PR')" colspan="4" >Property<img id="LE_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let lead of myLeadsList">
                                    <td (click)="openArchivedialog(lead,'LE')" class="cursor-pointer">
                                        <span *ngIf="lead.user_profile_image == ''">
                                            <div class="ls dis_inline po_rel_1 font_semibold">{{lead.user_initial}}</div>
                                        </span>
                                        <span *ngIf="lead.user_profile_image != ''">
                                            <img [src]="lead.user_profile_image" class="client-profile-pic search-agent-event symbols-property-image dis_inline" alt="">
                                        </span>
                                        <div class="dis_inline po_rel colr_ lead-icon">
                                            <span class="dark font_semibold lead-name">{{lead.user_name}}<br></span>
                                            {{lead.user_email}}
                                        </div>
                                    </td>
                                    <td><div class="bold_font font_semibold">{{lead.user_phone}}</div></td>
                                    <td><div class="bold_font font_semibold">{{getLeadDate(lead.lead_date)}}</div></td>
                                    <td>
                                        <div *ngIf="lead.user_status == 'U'" class="bold_font font_semibold">Unrepresented</div>
                                        <div *ngIf="lead.user_status == 'R'" class="bold_font font_semibold">Represented</div>
                                        <div *ngIf="lead.represented_agent">{{lead.represented_agent}}</div>
                                    </td>
                                    <td (click)="gotToPropertyDetail('my-leads/property-detail',lead.property_id)" class="cursor-pointer">
                                        <span class="vertical-align-top" *ngIf="lead.property_file != ''">
                                            <img src="{{lead.property_file}}" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt="">
                                        </span>
                                        <span class="vertical-align-top" *ngIf="lead.property_file == ''">
                                            <img src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt="">
                                        </span>
                                        <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{lead.address}}<br></span>{{lead.location}}</div>
                                    </td>
                                    <td class="action-view action-lead-view">
                                      <div (click)="startChat(lead)" class="save_notes lead-chat">Message</div>
                                    </td>
                                    <!-- <td class="action-view action-lead-view">
                                        <div (click)="startNote(lead)" class="save_notes lead-chat">Notes</div>
                                      </td> -->
                                    <td class="action-option">
                                        <a>
                                        <div (click)="startNote(lead)" class="archive pull-right action-btn mt_10">Notes </div>
                                        <span class="badge badge-notify"  *ngIf="lead.count">{{lead.count}}</span>
                                        <!-- <div (click)="startChat(lead)" class="save_notes lead-chat">Notes</div> -->
                                        </a>
                                    </td>
                                    <td>
                                        <!-- <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more" alt=""> -->
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                            <div *ngIf="myLeadsTotalCount > leadsItemsPerPage && myLeadsTotalCount != myLeadsList.length" class="new_form_group load_more_btn">
                            <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreLeads('LE',myLeadsIndex)" value="Load More">
                            </div>
                        </div>
                    </div>

                    <div id="Archive"  class="tab-pane fade table-responsive selected_saved">
                        <div class="No_matches" *ngIf="showARLoader == true">
                            <div class="loader">
                            <div class="message">Loading...</div>
                            <div class="dots"><div class="center"></div></div>
                            </div>
                        </div>
                        <div *ngIf="myLeadsArchiveList.length == 0 && showARLoader == false" class="No_matches">
                            <div class="title">No Leads</div>
                            <div class="text">You have no leads in your archive list. To add leads to your archive, select the archive button on leads from your active list.</div>
                        </div>
                        <div *ngIf="myLeadsArchiveList.length != 0" class="property-list-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th (click)="leadSorting('AR','NA')">Name <img id="AR_NA" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="leadSorting('AR','PH')">Phone <img id="AR_PH" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="leadSorting('AR','DT')">Date <img id="AR_DT" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="leadSorting('AR','ST')">Buyer Status <img id="AR_ST" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                    <th (click)="leadSorting('AR','PR')" colspan="4" >Property<img id="AR_PR" src="{{imagePrefix}}symbols-glyph-arrow-line.png" class="symbols-glyph-arrow-line" alt=""> </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let archive of myLeadsArchiveList">
                                    <td (click)="openArchivedialog(archive,'AR')" class="cursor-pointer">
                                        <span *ngIf="archive.user_profile_image == ''">
                                            <div class="ls dis_inline po_rel_1 font_semibold">{{archive.user_initial}}</div>
                                        </span>
                                        <span *ngIf="archive.user_profile_image != ''">
                                            <img [src]="archive.user_profile_image" class="client-profile-pic search-agent-event symbols-property-image dis_inline" alt="">
                                        </span>
                                        <div class="dis_inline po_rel colr_ lead-icon">
                                            <span class="dark font_semibold lead-name">{{archive.user_name}}<br></span>
                                            {{archive.user_email}}
                                        </div>
                                    </td>
                                    <td><div class="bold_font font_semibold">{{archive.user_phone}}</div></td>
                                    <td><div class="bold_font font_semibold">{{getLeadDate(archive.lead_date)}}</div></td>
                                    <td>
                                        <div *ngIf="archive.user_status == 'U'" class="bold_font font_semibold">Unrepresented</div>
                                        <div *ngIf="archive.user_status == 'R'" class="bold_font font_semibold">Represented</div>
                                    </td>
                                    <td (click)="gotToPropertyDetail('my-leads/property-detail',archive.property_id)" class="cursor-pointer" >
                                        <span class="vertical-align-top" *ngIf="archive.property_file != ''">
                                            <img src="{{archive.property_file}}" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt="">
                                        </span>
                                        <span class="vertical-align-top" *ngIf="archive.property_file == ''">
                                            <img src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt="">
                                        </span>
                                        <div class="dis_inline po_rel"><span class="dark text-line-clamp font_semibold">{{archive.address}}<br></span>{{archive.location}}</div>
                                    </td>
                                    <td class="action-view">
                                        <a>
                                        <div (click)="addRemoveLead(archive,false,'AR')" class="un-archive-btn action-btn un-archive mt_10">Unarchive</div>
                                        </a>
                                    </td>
                                    <!-- <td><img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more" alt=""></td> -->
                                </tr>
                            </tbody>
                        </table>
                        <div *ngIf="myLeadsArchiveTotalCount > ArchiveItemsPerPage && myLeadsArchiveTotalCount != myLeadsArchiveList.length" class="new_form_group load_more_btn">
                            <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoreLeads('AR',myLeadsArchiveIndex)" value="Load More">
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

 <div class="modal fade sign_modal " id="originEvent" role="dialog">
    <div class="modal-dialog modal-md">
       <div class="modal-content">
            <div class="modal-body">
                <div class="lilly_m_group">
                    <div class="lilly_title">{{leadDialog.user_name}}</div>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal_content width_383">
                    <div class="lilly_modal">
                        <div class="lilly_stat_us">
                            <div class="li_status">Status</div>
                            <div *ngIf="leadDialog.user_status == 'U'" class="li_status_2">Unrepresented</div>
                            <div *ngIf="leadDialog.user_status == 'R'" class="li_status_2">Represented</div>
                        </div>
                        <form [formGroup]="leadProfileForm">
                        <div *ngIf="leadDialog?.user_phone != null" class="lilly_stat_us">
                            <div class="li_status">Phone</div>
                                <input  type="text" maxlength="12" #user_phone (keyup)="validateFormat(user_phone.value)" class="new_form" formControlName="user_phone" placeholder="Phone Number"/>
                                    <div *ngIf="leadProfileForm['controls']['user_phone'].touched">
                                        <p class="form-validation" *ngIf="leadProfileForm.controls.user_phone.errors?.minlength">phone number must be digits and 10 characters</p>
                                        <p class="form-validation" *ngIf="leadProfileForm.controls.user_phone.errors?.maxlength">phone number must be digits and 10 characters</p>
                                    </div>
                            <!-- <div class="li_status_2 color_green">{{leadDialog?.user_phone}}</div> -->
                        </div>

                        <div *ngIf="leadDialog?.user_phone != null" class="lilly_stat_us">

                            <div class="li_status">Email</div>
                            <!-- <div class="li_status_2 color_green">{{leadDialog?.user_email}}</div> -->
                            <input type="text" class="new_form" formControlName="user_email" placeholder="Enter your email address*" required />
                            <div *ngIf="leadProfileForm.controls['user_email'].untouched">
                                <span></span>
                            </div>
                            <div *ngIf="leadProfileForm.controls['user_email'].touched && leadProfileForm.controls.user_email.errors?.user_email">
                                <span class="form-validation">Enter valid email address</span>
                            </div>

                        </div>
                    </form>

                        <div class="lilly_stat_us">
                            <div class="li_status">Origin Point</div>
                            <div *ngIf="leadDialog.origin_point == 'EV'" class="li_status_2 color_green">Event</div>
                            <div *ngIf="leadDialog.origin_point == 'LI'" class="li_status_2 color_green">Listing</div>
                        </div>
                        <div *ngIf="leadDialog.origin_point == 'EV'" class="lilly_stat_us">
                            <div class="li_status">Date</div>
                            <div class="li_status_2">{{leadDateFormat(leadDialog.lead_date)}}</div>
                        </div>
                        <div class="lilly_stat_us">
                            <div class="li_status">Property</div>
                            <div class="li_status_2">
                                <img *ngIf="leadDialog.property_file != ''" src="{{leadDialog.property_file}}" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt="">
                                <img *ngIf="leadDialog.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt="">
                                <div class="dis_inline po_rel property-text-lead">{{leadDialog.address}}<br> <span>{{leadDialog.location}}</span> </div>
                            </div>
                        </div>
                        <div class="lilly_stat_us">
                            <div class="li_status">Listing Agent</div>
                            <div class="li_status_2">
                                <img *ngIf="leadDialog.lead_owner_profile != ''" src="{{leadDialog.lead_owner_profile}}" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt="">
                                <img *ngIf="leadDialog.lead_owner_profile == ''" src="{{imagePrefix}}default-placeholder.png" class="symbols-property-image rounded-img dis_inline list-pro-img vertical-align-top" alt="">
                                <div class="dis_inline po_rel property-text-lead">{{leadDialog.lead_owner_name}}<br> <span>{{leadDialog.lead_owner_company}}</span> </div>
                            </div>
                        </div>
                        <div *ngIf="leadDialog.origin_point == 'EV'" class="lilly_stat_us">
                            <div class="li_status">Rating</div>
                            <div class="li_status_2">
                                <span>
                                    <img *ngIf="leadDialog.property_rate.rating == '0'" src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown.png" class="check_icon back_check_2 thum-size" height="46px" width="46px" alt="">
                                    <img *ngIf="leadDialog.property_rate.rating == '1'" class="check_icon back_check_2 thum-size" height="46px" width="46px" src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png">
                                </span>
                                <span class="icon-space" *ngIf="leadDialog.property_rate.floorplan_rating == true"><img height="20" width="20" src="{{imagePrefix}}icon1.png" alt=""></span>
                                <span class="icon-space" *ngIf="leadDialog.property_rate.bedroom_rating == true"><img height="20" width="20" src="{{imagePrefix}}icon2.png" alt=""></span>
                                <span class="icon-space" *ngIf="leadDialog.property_rate.bathroom_rating == true"><img height="20" width="20" src="{{imagePrefix}}icon3.png" alt=""></span>
                                <span class="icon-space" *ngIf="leadDialog.property_rate.kitchen_rating == true"><img height="20" width="20" src="{{imagePrefix}}icon4.png" alt=""></span>
                                <span class="icon-space" *ngIf="leadDialog.property_rate.finishes_rating == true"><img height="20" width="20" src="{{imagePrefix}}icon5.png" alt=""></span>
                                <span class="icon-space" *ngIf="leadDialog.property_rate.landscaping_rating == true"><img height="20" width="20" src="{{imagePrefix}}icon6.png" alt=""></span>
                                <span class="icon-space" *ngIf="leadDialog.property_rate.neighbourhood_rating == true"><img height="20" width="20" src="{{imagePrefix}}icon7.png" alt=""></span>
                            </div>
                            <div class="li_status"></div>
                            <div class="li_status_2"> <span>{{leadDialog.property_rate.notes}}</span></div>
                        </div>
                        <input *ngIf='showUnArchiveBtn == false' data-dismiss="modal" (click)="addRemoveLead(leadDialog,true,'LE')" type="submit" class="red_button red-archive" value="Archive">
                        <input *ngIf='showUnArchiveBtn == true' data-dismiss="modal" (click)="addRemoveLead(leadDialog,false,'AR')" type="submit" class="red_button red-archive" value="Unarchive">
                        <input  data-dismiss="modal" (click)="startNote(leadDialog)" type="submit" class="green_button green-archive" value="Notes">
                        <span class="badge badge-notify-1" *ngIf="leadDialog.count">{{leadDialog.count}}</span>

                            <input type="submit"  [ngClass]="{'submit-disable': leadProfileForm.invalid }" class="green_button green-archive" [disabled]="leadProfileForm.invalid" value="Submit" (click)="saveLead(leadProfileForm)" />


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<div class="modal fade note_modal " id="noteEvent" role="dialog">
    <div class="modal-dialog modal-lg modal-md">
       <div class="modal-content">
            <div class="">
                <div class="lilly_m_group">
                    <div class="lilly_title">Notes - {{leadDialog.user_name}}</div>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class=" modal-body modal_content ">
                    <div class="my_client_table" style="padding: 10px !important;">
                        <textarea name="" id="" cols="30" rows="10" (keyup)="isValidNote()" [(ngModel)]="newNote" class="my_client" placeholder="Type here to add a new note…."></textarea>
                        <div *ngIf="disableNotebtn == true" class="save_notes submit-disable">Save Note</div>
                        <div *ngIf="disableNotebtn == false" class="save_notes" (click)="SaveNote()">Save Note</div>
                        <div class="row"></div>
                        <div class="my_client_lists">
                            <div class="No_matches my_client_load" *ngIf="showMNLoader == true">
                                <div class="loader">
                                <div class="message">Loading...</div>
                                <div class="dots"><div class="center"></div></div>
                                </div>
                            </div>
                            <div class="my_client_list" *ngFor="let note of myNoteList; let i = index">
                                <div class="row">
                                <div class="col-sm-4">
                                    <div class="date">{{utcDateFormat(note.date)}}</div>
                                </div>
                                <div class="col-sm-9">
                                    <div *ngIf="updateNoteIndex != i" style="text-align: justify;" >{{note.note}}</div>
                                    <!-- class="text saved-note" -->
                                    <div *ngIf="updateNoteIndex == i">
                                            <textarea class="note-textarea" [value]="note.note" #noteValue id="note.id" cols="80" rows="3"></textarea>
                                            <div class="save_notes save-note" (click)="manageNote(note, 'UPDATE', noteValue.value, i)">Save Note</div>
                                            <a (click)="manageNote(note, 'CANCEL', '', i)"class="Cancel client cancel-note">Cancel</a>
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <div class="edit">
                                            <i (click)="showNote(note, 'UPDATE', i)" class="fa fa-pencil edit_img"></i>
                                            <i (click)="showNote(note, 'DELETE', i)" class="fa fa-trash-o edit_img"></i>
                                    </div>
                                </div>
                                </div>
                            </div>
                            <div *ngIf="myNoteTotalCount > myNoteItemPerPage && myNoteTotalCount != myNoteList.length" class="new_form_group load_more_btn">
                                <input type="submit" class="submit_button with_bg load_more" [ngClass]="{'submit-disable': disableLoadMore == true}" [disabled]="disableLoadMore == true" (click)="loadMoretList('MN', myNoteIndex)" value="Load More">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div>
  <footer></footer>
</div>
