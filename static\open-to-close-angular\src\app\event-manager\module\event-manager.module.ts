import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EventManaegerComponent } from '@app/event-manager/component/event-manager.component';
import { EventListComponent } from '@app/event-manager/component/event-list.component';
import { RunEventManagerComponent } from '@app/event-manager/component/run-event-manager.component';
import { GuestBookModeComponent } from '@app/event-manager/component/guest-book-mode.component';
import { BaseModule } from '@app/base/modules/base.module';
import { EventManagerService } from '@app/event-manager/services/event-manager.service';
import { CheckedInCountPipes } from '@app/event-manager/pipes/checked-in-count-pipe';
import { CheckedInListPipe } from '@app/event-manager/pipes/checkedin-list.pipe';
import { BaseComponent } from '@app/base/components/base.component';

@NgModule({
  imports: [
    CommonModule,
    BaseModule
  ],
  declarations: [EventManaegerComponent,EventListComponent,RunEventManagerComponent,GuestBookModeComponent,CheckedInCountPipes, CheckedInListPipe],
  providers: [EventManagerService]
})
export class EventManagerModule { }
