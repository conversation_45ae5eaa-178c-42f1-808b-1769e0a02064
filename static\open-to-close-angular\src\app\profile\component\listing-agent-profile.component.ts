import { Component, OnInit, ViewChild, EventEmitter } from '@angular/core';
import { ProfileComponent } from '@app/profile/component/profile.component';
import { FormGroup, FormControl, Validators, NgForm, FormArray } from '@angular/forms';
import { AuthService } from '@app/auth/services/auth.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { BaseComponent } from '@app/base/components/base.component';
import { State, City, ZipCode } from '@app/profile/models/profile';
import { PurchaseService } from '@app/purchase/service/purchase.service';
import { UpdatePaymentMethod } from '@app/profile/models/update-payment';
import { DISABLED } from '@angular/forms/src/model';
import { Observable } from 'rxjs/Observable';
import { MessagingService } from '@app/messaging.service';
import { HeaderComponent } from '@app/root/components/header.component';
import { AgentDetail } from '@app/profile/models/agent-detail';
import * as FileSaver from 'file-saver';

declare var $;
declare var require;

@Component({
  selector: 'listing-agent-profile',
  templateUrl: '../views/listing-agent-profile.html',
  styleUrls: ['../css/profile.component.css']
})

export class ListingAgentProfileComponent extends ProfileComponent implements OnInit {

  @ViewChild(HeaderComponent) headerComponent: HeaderComponent;

  listingAgentPaymentForm: FormGroup;
  listingAgentProfileForm: FormGroup;
  listingAgentBillingInfo: FormGroup;
  wiseAgentSignupForm: FormGroup;

  githubAccount: any;
  items = [];
  typeahead = new EventEmitter<string>();

  fileUrl: any = this.imagePrefix + "testmonial-default (1).png";


  @ViewChild('creditCardNumber') creditCardNumber;

  public updatePaymentModal: UpdatePaymentMethod = new UpdatePaymentMethod();

  states = [];
  cities = [];
  zipCodes = [];
  homeTypes = [];

  stateList: State[] = [];
  cityList: City[] = [];
  zipCodeList: ZipCode[] = [];

  br_stateList: State[] = [];
  br_cityList: City[] = [];
  br_zipCodeList: ZipCode[] = [];


  br_stateList_new: State[] = [];
  br_cityList_new: City[] = [];
  br_zipCodeList_new: ZipCode[] = [];

  public myLender: any;
  public searchLenderList = [];
  public showLenderList: Boolean = false;

  public agentMLSStatus: String = '';

  // homeTypeList: any[] = ['Apartment','Condominium','Duplex','Manufactured Home','Mobile Home','Quadruplex','Single Family Attached',
  // 'Single Family Detached','Townhouse','Triplex'];
  homeTypeList: any[] = ['Single Family - Detached', 'Loft Style', 'Moduler/Pre-Fab', 'Mfg/Mobile Housing', 'Gemini/Twin Home',
    'Apartment Style/Flat', 'Townhouse', 'Patio Home']
  authService: AuthService;
  agentPlansList = this.agentPlans;
  accountStatus: Boolean = false;
  permanentPremium: Boolean = false;
  profileName: String;
  public cardLast4Digit;
  public inVisibaleNumber = "************";
  public msgCP: boolean = false;
  public w_ohCP: boolean = false;
  public showextrafeild: boolean = false;
  public isBrokerEmailFound: boolean = true;
  public CLIENT_AUTHORIZATION;
  public cardFirstName;
  public cardLastName;
  defaultCardNuber: any;
  public selectedMonth: string;
  public selectedYear: String;

  public stateObj_1: any
  public cityObj_1: any

  public userIsWiseAgentRegistered: Boolean = false;

  cardNuberPattern = "^((\\+91-?)|0)?[0-9]{13,16}$";
  cvvPattern = "^((\\+91-?)|0)?[0-9]{3,4}$";
  EXMonthPattern = "^((\\+91-?)|0)?[0-9]{1,2}$";
  EXYearPattern = "^((\\+91-?)|0)?[0-9]{4}$";

  public showOHPStatesLabel: Boolean = false;
  public showOHPCitysLabel: Boolean = false;
  public showOHPZipCodesLabel: Boolean = false;
  public showOHPTypeLabel: Boolean = false;
  public disablecancelSubsBtn: Boolean = false;

  public purchaseHistory = [];
  public showAllinvoicesLink: Boolean = false;

  //email confirm
  public oldEmailAddress: String = '';
  public NewEmailAddress: String = '';
  public tempUpdatedProfileInfo: FormGroup;

  public invoiceLoadingIndex: string = null;
  public showInvoiceLoading: Boolean = false;

  public message: any;
  public fragmentRedirect: any;

  public selectedCity = '';
  public selectedState = '';
  public selectedZipCode = '';
  public selectedCityId = '';
  public mlsIdText = 'ARMLS ID*';

  public isSeletedCity: Boolean = false;
  public isSeletedZipCode: Boolean = false;
  public showCitySearchResult: Boolean = false;
  public showZipCodeSearchResult: Boolean = false;
  public lastSearchedCity: String = "";
  public lastZipCodeSearch: String = "";

  public citySearchSubscription: any;
  public zipSearchSubscription: any;

  public showStateLabel: Boolean = true;
  public showCityLabel: Boolean = true;
  public showZipCodeLabel: Boolean = true;
  public isBrokerInfoExist: Boolean = true;
  brokerage_info :any;

  constructor(private msgService: MessagingService) {
    super();
    this.authService = ServiceLocator.injector.get(AuthService);
  }

  ngOnInit() {
    if (this.getPreviousScreen() != '/profile') {
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/profile/listingAgent');
    $(document).ready(function () {
    });

    document.getElementById('zip').addEventListener('keydown', function (e) {
      if (e.which === 38 || e.which === 40) {
        e.preventDefault();
      }
    });
    this.initData();
    this.initAPI();
    this.headerComponent.getPlans();
    this.checkBrokerInfo()
  }

  checkBrokerInfo() {
    try {
      if (localStorage.getItem("showBrokerInfo")) {
        this.isBrokerInfoExist = false
        this.isBrokerEmailFound = false
        document.querySelector('#' + 'brockerInfo').scrollIntoView();
        var current = document.getElementById("brokerDiv");
        current.classList.add("broker-info");
        setTimeout(() => {
          current.classList.remove("broker-info");
        }, 3000);
        this.showUpdateBrokerInformation();
      }
    } catch (e) { }
    if(!BaseComponent.user){
      this.router.navigate(['/search']);
    }
    if(BaseComponent && BaseComponent.user && BaseComponent.user.is_open_registration){
      this.listingAgentProfileForm.controls.profile['controls']['mls_agent_id'].clearValidators()
      this.listingAgentProfileForm.controls.profile['controls']['mls_agent_id'].updateValueAndValidity()
      this.mlsIdText = 'ARMLS ID'
    }

  }

  defineForms() {
    this.listingAgentPaymentForm = new FormGroup({
      firstName: new FormControl('', Validators.required),
      lastName: new FormControl('', Validators.required),
      creditCard: new FormGroup({
        number: new FormControl('', [Validators.pattern(this.cardNuberPattern)]),
        cvv: new FormControl('', [Validators.pattern(this.cvvPattern)]),
        expirationMonth: new FormControl('', [Validators.required, Validators.maxLength(2), Validators.minLength(1)]),
        expirationYear: new FormControl('', [Validators.required, Validators.maxLength(4), Validators.minLength(4)]),
        billingAddress: new FormGroup({
          firstName: new FormControl(''),
          lastName: new FormControl(''),
        })
      }),
    });

    this.listingAgentProfileForm = new FormGroup({
      email: new FormControl('', [Validators.required, Validators.email]),
      profile: new FormGroup({
        name: new FormControl('', Validators.required),
        mls_agent_id: new FormControl('', Validators.required),
        cell_phone: new FormControl('', Validators.required),
        office_phone: new FormControl(''),
        mls_status: new FormControl(''),
        brokerage_id: new FormControl(''),
        broker_address: new FormControl(''),
        broker_city: new FormControl(''),
        broker_state: new FormControl(''),
        broker_zipcode: new FormControl(''),
        broker_name: new FormControl(''),
        broker_name_actual: new FormControl(''),
        // street: new FormControl('',Validators.required),
        city_br: new FormControl(''),
        // state_br: new FormControl(null),
        // broker_cell_phone: new FormControl(''),
        broker_office_phone: new FormControl({ value: null, disabled: false }),
        zipcode_br: new FormControl(''),

        ohp_city: new FormControl(''),
        ohp_state: new FormControl(''),
        ohp_zipcode: new FormControl(''),
        ohp_home_type: new FormControl(''),
        // // ohp_cell_phone_number: new FormControl('',Validators.required),
        ohp_price_high: new FormControl(''),
        ohp_price_low: new FormControl(''),
        email_preferences: new FormArray([])
      })
    });

    this.listingAgentBillingInfo = new FormGroup({
      address_1: new FormControl('', Validators.required),
      address_2: new FormControl(''),
      state: new FormControl('', Validators.required),
      city: new FormControl('', Validators.required),
      zipcode: new FormControl('', [Validators.required, Validators.minLength(5)]),
      user: new FormGroup({
        id: new FormControl('')
      })
    });

    this.wiseAgentSignupForm = new FormGroup({
      wiseAgentKey: new FormControl('', Validators.required, )
      // name: new FormControl('', Validators.required, ),
      // email: new FormControl('', [Validators.required, Validators.email]),
      // brokerage_id: new FormControl(''),
      // cell_phone: new FormControl('', Validators.required),
      // passwordForm: new FormGroup({
      //   password: new FormControl('', [Validators.required, Validators.minLength(5), Validators.maxLength(15)]),
      //   confirm_password: new FormControl('', [Validators.required, Validators.minLength(5), Validators.maxLength(15)]),
      // }, passwordMatchValidator),

    })

    // this.listingAgentBillingInfo.controls['user']['controls']['id'].setValue(BaseComponent.user.id);
    function passwordMatchValidator(g: FormGroup) {
      if (g.get('confirm_password').value != null && g.get('confirm_password').value.length >= 5 && g.get('confirm_password').value.length < 15) {
        return g.get('password').value === g.get('confirm_password').value ? null : { 'mismatch': true };
      }
    }
  }

  initAPI() {
    this.purchaseService.subscriptionClientToken().subscribe(res => {
      this.CLIENT_AUTHORIZATION = res['result']['client_token'];
    }, err => console.log(err));
  }


  async initData() {
    console.log("---InitForm");

    console.log("Per ====>",this.permanentPremium);
    console.log("acc stats ===>",this.accountStatus);
    await this.defineForms();
    if (Object.keys(ProfileComponent.listingAgent).length == 0) {
      this.profileService.agentDetail().subscribe(res => {
        this.brokerage_info = res
        this.userIsWiseAgentRegistered = res.result.is_wise_registered;

        if (res.result.profile.profile_photo != "") {
          this.fileUrl = res.result.profile.profile_photo;
        }
        this.listingAgentProfileForm.patchValue(res.result);
        // this.listingAgentProfileForm.controls.profile['controls']['cell_phone'].setValue(this.validateNumber(res.result.profile.cell_phone));
        this.validateFormat(res.result.profile.cell_phone)
        this.listingAgentProfileForm.controls.profile['controls']['office_phone'].setValue(this.validateNumber(res.result.profile.office_phone));
        this.listingAgentBillingInfo.patchValue(res.result.billing_info);

        //WiseAgent Patch Value
        this.wiseAgentSignupForm.patchValue(res.result);

        console.log(res.result)

        //Wise Agent Profile Details
        // this.wiseAgentSignupForm.controls.name.setValue(res.result.profile.name);
        // this.wiseAgentSignupForm.controls.cell_phone.setValue(res.result.profile.cell_phone);

        this.wiseAgentSignupForm.controls.wiseAgentKey.setValue(res.result.wise_api_key)

        this.listingAgentPaymentForm.controls.firstName.setValue(res.result.payment_method.first_name);
        this.listingAgentPaymentForm.controls.lastName.setValue(res.result.payment_method.last_name);
        this.listingAgentPaymentForm.controls.creditCard['controls']['number'].setValue(res.result.payment_method.number);

        ProfileComponent.listingAgent = res.result;
        if (res.result.profile.ohp_price_low == 0) {
          this.listingAgentProfileForm.controls.profile['controls']['ohp_price_low'].setValue('')
          ProfileComponent.listingAgent.profile.ohp_price_low = ''
        }
        if (res.result.profile.ohp_price_high == 0) {
          this.listingAgentProfileForm.controls.profile['controls']['ohp_price_high'].setValue('')
          ProfileComponent.listingAgent.profile.ohp_price_high = ''
        }
        if (res.result.brokerage_info != undefined && Object.keys(res.result.brokerage_info).length != 0) {

          this.listingAgentProfileForm.controls.profile['controls']['brokerage_id'].setValue(res.result.brokerage_info.brokerage_id);
          this.listingAgentProfileForm.controls.profile['controls']['broker_address'].setValue(res.result.brokerage_info.address);
          this.listingAgentProfileForm.controls.profile['controls']['broker_city'].setValue(res.result.brokerage_info.city);
          this.listingAgentProfileForm.controls.profile['controls']['broker_state'].setValue(res.result.brokerage_info.state);
          this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].setValue(res.result.brokerage_info.zipcode);
          this.listingAgentProfileForm.controls.profile['controls']['broker_name'].setValue(res.result.brokerage_info.name);

          //WiseAgent Broker Detail
          // this.wiseAgentSignupForm.controls.brokerage_id.setValue(res.result.brokerage_info.brokerage_id);

        }
        this.headerComponent.selectedPlan = res.result.selected_plan_id;
        this.accountStatus = res.result.paid_account;
        this.permanentPremium = res.result.permanent_premium_account;
        this.agentMLSStatus = res.result.profile.mls_status;

        ProfileComponent.listingAgent.billing_info = res.result.billing_info;
        ProfileComponent.listingAgent.payment_method = res.result.payment_method;
        this.cardFirstName = res.result.payment_method.first_name;
        this.cardLastName = res.result.payment_method.last_name;
        this.defaultCardNuber = res.result.payment_method.number;

        this.homeTypeList = res.result.profile.ohp_home_type;
        this.profileName = res.result.profile.name;
        this.checkCP(res.result.profile.email_preferences);

        this.stateList = res.result.profile.ohp_state_s;

        this.cityList = res.result.profile.ohp_city_s;
        this.setZipCode(res.result.profile.ohp_zipcode_s);

        if (res.result.profile.ohp_state != null) {
          for (let stateId of res.result.profile.ohp_state) {
            this.states.push.apply(this.states, this.stateList.filter(state => state.id == stateId));
          }
          this.OHPListCount('state');
        }
        if (res.result.profile.ohp_city != null) {
          for (let cityId of res.result.profile.ohp_city) {
            this.cities.push.apply(this.cities, this.cityList.filter(city => city.id == cityId));
          }
          this.OHPListCount('city');
        }
        if (res.result.profile.ohp_zipcode != null) {
          for (let zipCodeId of res.result.profile.ohp_zipcode) {
            this.zipCodes.push.apply(this.zipCodes, this.zipCodeList.filter(zipCode => zipCode.id == zipCodeId));
          }
          this.OHPListCount('zipCode');
        }

        if (res.result.profile.ohp_home_type != null) {
          this.homeTypes = res.result.profile.ohp_home_type;
          this.OHPListCount('homeType');
        }
        // this.homeTypeList = ['Apartment','Condominium','Duplex','Manufactured Home','Mobile Home','Quadruplex','Single Family Attached',
        // 'Single Family Detached','Townhouse','Triplex'];
        this.homeTypeList = ['Single Family - Detached', 'Loft Style', 'Moduler/Pre-Fab', 'Mfg/Mobile Housing', 'Gemini/Twin Home',
          'Apartment Style/Flat', 'Townhouse', 'Patio Home']

        this.getStateList();
        this.getBRStateList();


        $("#lenderInviteLink").val(res.result.invite_link);
        this.myLender = res.result.lender_info;
        this.getPurchasehistory(false);
        this.setOldEmailAddress(res.result.email);
      }, err => this.errorResponse(err.json()));
    }
    else {
      if (ProfileComponent.listingAgent.profile.profile_photo != '') {
        this.fileUrl = ProfileComponent.listingAgent.profile.profile_photo;
      }
      this.checkCP(ProfileComponent.listingAgent.profile.email_preferences);
      this.listingAgentProfileForm.patchValue(ProfileComponent.listingAgent);
      this.listingAgentBillingInfo.patchValue(ProfileComponent.listingAgent.billing_info);
      this.brokerage_info = ProfileComponent.listingAgent.brokerage_info

      // console.log("PCOMP====>",ProfileComponent.listingAgent.wise_api_key);
      this.wiseAgentSignupForm.controls.wiseAgentKey.setValue(ProfileComponent.listingAgent.wise_api_key)

      // WiseAgent Profile Details
      // this.wiseAgentSignupForm.controls.name.setValue(ProfileComponent.listingAgent.profile.name);
      // this.wiseAgentSignupForm.controls.cell_phone.setValue(ProfileComponent.listingAgent.profile.cell_phone);

      //WiseAgent Broker Details
      // this.wiseAgentSignupForm.patchValue(ProfileComponent.listingAgent);4
      this.userIsWiseAgentRegistered = ProfileComponent.listingAgent.is_wise_registered;
      this.profileName = ProfileComponent.listingAgent.profile.name;
      this.cardFirstName = ProfileComponent.listingAgent.payment_method.first_name;
      this.cardLastName = ProfileComponent.listingAgent.payment_method.last_name;
      this.headerComponent.selectedPlan = ProfileComponent.listingAgent.selected_plan_id;
      this.states = ProfileComponent.listingAgent.profile.ohp_state_s;
      this.cities = ProfileComponent.listingAgent.profile.ohp_city_s;
      this.zipCodes = ProfileComponent.listingAgent.profile.ohp_zipcode_s;
      this.homeTypes = ProfileComponent.listingAgent.profile.ohp_home_type;
      this.accountStatus = ProfileComponent.listingAgent.paid_account;
      this.permanentPremium = ProfileComponent.listingAgent.permanent_premium_account;
      this.listingAgentProfileForm.controls.profile['controls']['cell_phone'].setValue(this.validateNumber(ProfileComponent.listingAgent.profile.cell_phone));
      this.listingAgentProfileForm.controls.profile['controls']['office_phone'].setValue(this.validateNumber(ProfileComponent.listingAgent.profile.office_phone));
      this.OHPListCount('state');
      this.OHPListCount('city');
      this.OHPListCount('zipCode');
      this.OHPListCount('homeType');
      if (ProfileComponent.listingAgent.brokerage_info != undefined && Object.keys(ProfileComponent.listingAgent.brokerage_info).length != 0) {
        this.listingAgentProfileForm.controls.profile['controls']['brokerage_id'].setValue(ProfileComponent.listingAgent.brokerage_info.brokerage_id);
        this.listingAgentProfileForm.controls.profile['controls']['broker_address'].setValue(ProfileComponent.listingAgent.brokerage_info.address);
        this.listingAgentProfileForm.controls.profile['controls']['broker_city'].setValue(ProfileComponent.listingAgent.brokerage_info.city);
        this.listingAgentProfileForm.controls.profile['controls']['broker_state'].setValue(ProfileComponent.listingAgent.brokerage_info.state);
        this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].setValue(ProfileComponent.listingAgent.brokerage_info.zipcode);
        this.listingAgentProfileForm.controls.profile['controls']['broker_name'].setValue(ProfileComponent.listingAgent.brokerage_info.name);

        //WiseAgent Broker Detail
        // this.wiseAgentSignupForm.controls.brokerage_id.setValue(ProfileComponent.listingAgent.brokerage_info.brokerage_id);
      }

      this.listingAgentPaymentForm.controls.firstName.setValue(ProfileComponent.listingAgent.payment_method.first_name);
      this.listingAgentPaymentForm.controls.lastName.setValue(ProfileComponent.listingAgent.payment_method.last_name);
      this.listingAgentPaymentForm.controls.creditCard['controls']['number'].setValue(ProfileComponent.listingAgent.payment_method.number);
      this.defaultCardNuber = ProfileComponent.listingAgent.payment_method.number;
      this.homeTypeList = ProfileComponent.listingAgent.profile.ohp_home_type;
      this.getStateList();
      // this.homeTypeList = ['Apartment','Condominium','Duplex','Manufactured Home','Mobile Home','Quadruplex','Single Family Attached',
      // 'Single Family Detached','Townhouse','Triplex'];
      this.homeTypeList = ['Single Family - Detached', 'Loft Style', 'Moduler/Pre-Fab', 'Mfg/Mobile Housing', 'Gemini/Twin Home',
        'Apartment Style/Flat', 'Townhouse', 'Patio Home']

      $("#lenderInviteLink").val(ProfileComponent.listingAgent.invite_link);
      this.myLender = ProfileComponent.listingAgent.lender_info;
      this.agentMLSStatus = ProfileComponent.listingAgent.profile.mls_status;
      this.getPurchasehistory(false);
      this.setOldEmailAddress(ProfileComponent.listingAgent.email);
    }
  }


  getPurchasehistory(getType: Boolean) {
    if (getType == true) {
      this.showAllinvoicesLink = false;
    }
    this.profileService.getSubscriptionPurchaseHistory(getType).subscribe(res => {
      this.purchaseHistory = res['result']['purchase_history'];

      if (res['result']['total_count'] > 5) {
        this.showAllinvoicesLink = true;
      }
    });
  }

  updatePassword(form: FormGroup) {
    this.authService.changePassword(form.value).subscribe(res => {
      form.reset();
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    });
  }

  updateBillingInfo(form: FormGroup) {
    form.value['user']['id'] = BaseComponent.user.id;
    this.profileService.updateBillingInfo(form.value).subscribe(res => {
      ProfileComponent.listingAgent.billing_info = res.result;
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    })
  }

  validateFormat(number: String) {
    this.listingAgentProfileForm.controls.profile['controls']['cell_phone'].setValue(this.validateNumber(number));
  }

  validateOfficePhoneFormat(number: String) {
    this.listingAgentProfileForm.controls.profile['controls']['office_phone'].setValue(this.validateNumber(number));
  }

  validateFormatBroker(number: String) {
    this.listingAgentProfileForm.controls.profile['controls']['broker_cell_phone'].setValue(this.validateNumber(number));
  }

  validateOfficePhoneFormatBroker(number: String) {
    this.listingAgentProfileForm.controls.profile['controls']['broker_office_phone'].setValue(this.validateNumber(number));
  }

  checkEmailIsChanged(form: FormGroup) {
    console.log("FIRST TIME FORM ====>", form);
    console.log("OLD EMAIL ADDRESS ===>",this.oldEmailAddress);

    this.NewEmailAddress = form.value['email'];

    console.log("NEW EMAIL ADDRESS ===>", this.NewEmailAddress);

    if (this.oldEmailAddress.toLowerCase() != this.NewEmailAddress.toLowerCase()) {
      this.tempUpdatedProfileInfo = form;
      $("#confirmEmailChangePopup").modal("show");
    }
    else {
      this.updateProfile(form);
    }
  }

  onConfirmEmailClick() {
    this.updateProfile(this.tempUpdatedProfileInfo);
  }

  updateProfile(form: FormGroup) {
    event.preventDefault(); // Prevent default form submission

    console.log("UPDATE FORM =====>",form)

    if (this.states != null) {
      let stateIdList = [];
      this.states.filter(state => {
        stateIdList.push(state.id);
      })
      form.value.profile.ohp_state = stateIdList;
    }

    if (this.cities != null) {
      let cityIdList = [];
      this.cities.filter(city => {
        cityIdList.push(city.id);
      })
      form.value.profile.ohp_city = cityIdList;
    }

    if (this.zipCodes != null) {
      let zipCodeIdList = [];
      this.zipCodes.filter(zipCode => {
        zipCodeIdList.push(zipCode.id);
      })
      form.value.profile.ohp_zipcode = zipCodeIdList;
    }

    let homeTypeNameList = [];
    if (this.homeTypes != null) {
      this.homeTypes.filter(type => {
        if (type.hasOwnProperty('display')) {
          homeTypeNameList.push(type.display);
        }
        else {
          homeTypeNameList.push(type);
        }
      })
      form.value.profile.ohp_home_type = homeTypeNameList;
    }

    var email_preferencesList = [];
    if (this.msgCP == true) {
      email_preferencesList.push('msg');
    }

    if (this.w_ohCP == true) {
      email_preferencesList.push('w_oh');
    }

    form.value.profile.mls_status = 'U';
    form.value['profile']['email_preferences'] = email_preferencesList;
    form.value['profile']['ohp_price_low'] = form.value['profile']['ohp_price_low'] == '' ? 0 : form.value['profile']['ohp_price_low'];
    form.value['profile']['ohp_price_high'] = form.value['profile']['ohp_price_high'] == '' ? 0 : form.value['profile']['ohp_price_high'];

    let brokerId = form.value['profile']['brokerage_id'];

    form.value['profile']['office_phone'] = form.value['profile']['office_phone'] == undefined ? '' : form.value['profile']['office_phone']

    if (localStorage.getItem("showBrokerInfo")) {
      form.value.profile.is_open_registration = true;
      form.value.brokerage = {
        email: form.value['profile']['brokerage_id'],
        firm_name: form.value['profile']['broker_name'],
        address: form.value['profile']['broker_address'],
        state: form.value['profile']['broker_state'],
        city: form.value['profile']['broker_city'],
        zipcode: form.value['profile']['broker_zipcode'],
        is_open_registration: true
      }
      if (form.value['profile']['broker_name_actual']) {
        form.value.brokerage.contact_name = form.value['profile']['broker_name_actual']
      }
      if (form.value['profile']['broker_office_phone']) {
        form.value.brokerage.office_phone = form.value['profile']['broker_office_phone']
      }
    }
    if(BaseComponent && BaseComponent.user && BaseComponent.user.is_open_registration){
      form.value.profile.is_open_registration = true;
    }
    this.profileService.agentUpdate(form.value).subscribe((res:any) => {

      console.log("RESPONSE ====>",res)

      $("#confirmEmailChangePopup").modal("hide");
      this.oldEmailAddress = res.result.email;



      this.isBrokerInfoExist = true
      this.successResponse(res);
      if (BaseComponent.user != undefined && Object.keys(BaseComponent.user).length != 0) {
        if (Object.keys(res.result['brokerage_info']).length == 0) {
          BaseComponent.user.is_broker_paid_account = false
          BaseComponent.user.is_connected_with_broker = false
        }
        else {
          BaseComponent.user.is_connected_with_broker = true;
          BaseComponent.user.is_broker_paid_account = res.result['brokerage_info']['is_paid_account'];
        }
      }
      if (res.result['is_logout'] == true) {
        ProfileComponent.listingAgent = new AgentDetail();
        this.refreshBasePropertys();
        this.router.navigate(['/'], { queryParams: { email_confirm: true } });
      }
      else {
        this.checkCP(res.result.profile.email_preferences);
        ProfileComponent.listingAgent = res.result;
        console.log("FIRST TIME RESULT =====>", res.result);
        this.listingAgentProfileForm.patchValue(res.result);
        this.listingAgentProfileForm.controls.profile['controls']['brokerage_id'].patchValue(brokerId);
        this.listingAgentProfileForm.controls.profile['controls']['broker_address'].setValue(res.result.brokerage_info.address);
        this.listingAgentProfileForm.controls.profile['controls']['broker_city'].setValue(res.result.brokerage_info.city);
        this.listingAgentProfileForm.controls.profile['controls']['broker_state'].setValue(res.result.brokerage_info.state);
        this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].setValue(res.result.brokerage_info.zipcode);
        this.listingAgentProfileForm.controls.profile['controls']['broker_name'].setValue(res.result.brokerage_info.name);
        this.profileName = res.result.profile.name;
        this.agentMLSStatus = res.result.profile.mls_status;
        if (res.result.profile.ohp_price_low == 0) {
          this.listingAgentProfileForm.controls.profile['controls']['ohp_price_low'].setValue('')
        }
        if (res.result.profile.ohp_price_high == 0) {
          this.listingAgentProfileForm.controls.profile['controls']['ohp_price_high'].setValue('')
        }

        console.log("PPA =======>", res.result.permanent_premium_account);
        console.log("PA =======>",res.result.paid_account);

        console.log("PPA RESPONSE ======>",res.result);

        if(res.result.permanent_premium_account !== undefined){
          if (res.result.permanent_premium_account == true) {
            this.accountStatus = res.result.paid_account;
            this.permanentPremium = res.result.permanent_premium_account;
            if (res.result.paid_account) {
              BaseComponent.user.is_paid_account = true;
            }
          }
          else {
            this.accountStatus = res.result.paid_account;
            this.permanentPremium = res.result.permanent_premium_account;
            if (!res.result.paid_account) {
              BaseComponent.user.is_paid_account = false;
            }
          }
        }

      }
      if (localStorage.getItem("showBrokerInfo")) {
        BaseComponent.user.is_agent_have_broker = true
        localStorage.removeItem("showBrokerInfo");
        this.router.navigate(['/search']);
      }
    }, err => {
      this.errorResponse(err.json());
    });

    window.location.reload();
  }

  enableEditPaymentMethod(value) {
    if (value == true) {
      this.listingAgentPaymentForm.controls.creditCard['controls']['number'].setValue('');
      this.listingAgentPaymentForm.controls.creditCard['controls']['cvv'].setValue('');
      this.listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].setValue('');
      this.listingAgentPaymentForm.controls.creditCard['controls']['expirationYear'].setValue('');
      this.listingAgentPaymentForm.controls.creditCard['controls']['number'].reset();
      this.listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].reset();
      this.listingAgentPaymentForm.controls.creditCard['controls']['cvv'].reset();
      this.listingAgentPaymentForm.controls.creditCard['controls']['expirationYear'].reset();
      this.showextrafeild = value;
    }
    else {
      this.showextrafeild = value;
      this.listingAgentPaymentForm.controls.firstName.setValue(ProfileComponent.listingAgent.payment_method.first_name);
      this.listingAgentPaymentForm.controls.lastName.setValue(ProfileComponent.listingAgent.payment_method.last_name);
      this.listingAgentPaymentForm.controls.creditCard['controls']['number'].setValue(ProfileComponent.listingAgent.payment_method.number);
      this.defaultCardNuber = ProfileComponent.listingAgent.payment_method.number;
    }
  }

  updatePayment(form: FormGroup) {
    var self = this;
    form.value['cardHolderName'] = form.value['firstName'] + ' ' + form.value['lastName'];
    form.value['creditCard']['billingAddress']['firstName'] = form.value['firstName'];
    form.value['creditCard']['billingAddress']['lastName'] = form.value['lastName'];
    form.value['options'] = { validate: true }

    var createClient = require('braintree-web/client').create;

    createClient({
      authorization: this.CLIENT_AUTHORIZATION
    }, function (createErr, clientInstance) {
      clientInstance.request({
        endpoint: 'payment_methods/credit_cards',
        method: 'post',
        data: form.value
      }, function (requestErr, response) {
        if (requestErr) {
          self.errMessageResponse('Invalid CreditCard');
          throw new Error(requestErr);
        }
        self.cardFirstName = form.value['firstName'];
        self.cardLastName = form.value['lastName'];
        ProfileComponent.listingAgent.payment_method.first_name = self.cardFirstName;
        ProfileComponent.listingAgent.payment_method.last_name = self.cardLastName;
        self.updatePaymentMethod(response.creditCards[0].nonce);
      });
    });
  }

  updatePaymentMethod(nonce) {
    this.updatePaymentModal.nonce = nonce;
    this.updatePaymentModal.user.id = BaseComponent.user.id;
    this.profileService.updatePaymentMethod(this.updatePaymentModal).subscribe(res => {
      this.successResponse(res);
      ProfileComponent.listingAgent.payment_method.token = res.result.token;
      ProfileComponent.listingAgent.payment_method.last_4 = res.result.last_4;
      this.cardLast4Digit = this.inVisibaleNumber + res.result.last_4;
      ProfileComponent.listingAgent.payment_method.number = this.cardLast4Digit;
      this.listingAgentPaymentForm.controls.creditCard['controls']['number'].setValue(ProfileComponent.listingAgent.payment_method.number);
      this.defaultCardNuber = ProfileComponent.listingAgent.payment_method.number;
      this.showextrafeild = false;
    }, err => {
      this.errorResponse(err.json());
    })
  }

  createWiseAgentAccount = (form: FormGroup) => {
    this.profileService.wiseAgentCreateAccount(form.value).subscribe(res => {
      if(res.status === 1 && res.statusCode === 200){
        this.sucMessageResponse(res.message);
        console.log(res)
        this.userIsWiseAgentRegistered = true;
      }else{
        this.errMessageResponse("Failed to register with WiseAgent. Please try again later")
        this.userIsWiseAgentRegistered = false;
      }
    });
    $("#connectWithWiseAgent").modal("hide");
  }

  openPlansModal() {
    this.headerComponent.listingAgent = true;
    this.headerComponent.openHeaderPlansModal();
  }

  gotoUpgradeView() {
    this.router.navigateByUrl('/agentupgrade');
  }

  upgradeAccount(plan) {
    this.purchaseService.setPlan(plan);
    this.routeOnUrl('/purchase');
    $("#upgradeModal").modal("hide");
  }

  loadCities(stateId: number) {
    this.profileService.getCities(stateId).subscribe(res => {
      this.cityList = res.result;

      if (ProfileComponent.brokerageUser != null) {
        this.loadZipcode(ProfileComponent.brokerageUser.profile.city.id);
      }

    }, err => this.errMessageResponse(err));
  }

  loadZipcode(cityId: number) {
    this.profileService.getZipCodes(cityId).subscribe(res => {
      this.zipCodeList = res.result;

      if (ProfileComponent.brokerageUser != null) {
      }
    }, err => this.errMessageResponse(err));
  }

  checkCP(res) {
    for (let i = 0; i < res.length; i++) {
      if (res[i] == 'msg') {
        this.msgCP = true;
      }
      if (res[i] == 'w_oh') {
        this.w_ohCP = true;
      }
    }
  }

  CPStatusChange(status) {
    if (status == 'msg') {
      if (this.msgCP == false) {
        this.msgCP = true;
      }
      else {
        this.msgCP = false;
      }
    }
    if (status == 'w_oh') {
      if (this.w_ohCP == false) {
        this.w_ohCP = true;
      }
      else {
        this.w_ohCP = false;
      }
    }
  }

  /*
  * @desc: On item selection from tag-input, modify list accordingly
  */
  onItemAdd(item) {
    if (!item.hasOwnProperty('state') && !item.hasOwnProperty('city')) {
      this.getCityList(item.id);
      this.showOHPStatesLabel = true;
    }
    else if (item.hasOwnProperty('state') && !item.hasOwnProperty('city')) {
      this.getZipCode(item.id);
      this.showOHPCitysLabel = true;
    }
    else if (!item.hasOwnProperty('state') && item.hasOwnProperty('city')) {
      // this.getCityList(item.id);
      this.showOHPZipCodesLabel = true;
    }

  }

  /*
  * @desc: On item from tag-input remove, remove its related items
  */
  onItemRemoved(item) {
    if (!item.hasOwnProperty('state') && !item.hasOwnProperty('city')) {
      if (this.states.length == 0) {
        this.showOHPStatesLabel = false;
        this.showOHPCitysLabel = false;
        this.showOHPZipCodesLabel = false;
      }
      for (let city of this.cities) {
        if (item.id == city.state) {
          this.zipCodeList = this.zipCodeList.filter(zipCode => zipCode.city != city.id);
          this.zipCodes = this.zipCodes.filter(zipCode => zipCode.city != city.id);
        }
      }
      this.cityList = this.cityList.filter(city => city.state != item.id);
      this.cities = this.cities.filter(city => city.state != item.id);
    }
    else if (item.hasOwnProperty('state') && !item.hasOwnProperty('city')) {
      if (this.cities.length == 0) {
        this.showOHPCitysLabel = false;
        this.showOHPZipCodesLabel = false;
      }
      this.zipCodeList = this.zipCodeList.filter(zipCode => zipCode.city != item.id);
      this.zipCodes = this.zipCodes.filter(zipCode => zipCode.city != item.id);
    }
    else if (!item.hasOwnProperty('state') && item.hasOwnProperty('city')) {
      // this.getCityList(item.id);
      if (this.zipCodes.length == 0) {
        this.showOHPZipCodesLabel = false;
      }
    }

  }

  // Added by angular Start

  searchCity(cityName) {
    this.isSeletedCity = false;
    if (cityName.trim().length != 0) {
      if (this.lastSearchedCity != cityName.trim()) {
        this.lastSearchedCity = cityName.trim();

        this.showCitySearchResult = true;
        var cityPramas = new URLSearchParams();
        cityPramas.set('q', cityName.trim());
        if (this.citySearchSubscription) {
          this.citySearchSubscription.unsubscribe();
        }
        this.citySearchSubscription = this.profileService.searchCity(cityPramas).subscribe(res => {
          this.br_cityList_new = res;
        }, err => console.log(err))
      }
    } else {
      this.showCitySearchResult = false;
    }

  }

  searchZipCode(zipCode) {
    this.isSeletedZipCode = false;
    if (zipCode.trim().length != 0) {
      if (this.lastZipCodeSearch != zipCode.trim()) {
        this.lastZipCodeSearch = zipCode.trim();
        this.showZipCodeSearchResult = true;
        var zipCodePramas = new URLSearchParams();
        zipCodePramas.set('code', zipCode.trim());
        zipCodePramas.set('city', this.selectedCityId);
        if (this.zipSearchSubscription) {
          this.zipSearchSubscription.unsubscribe();
        }
        this.zipSearchSubscription = this.profileService.searchZipCode(zipCodePramas).subscribe(res => {
          this.br_zipCodeList_new = res;
        }, err => console.log(err))
      }
    } else {
      this.showZipCodeSearchResult = false;
    }
  }

  onCitySelect(value) {
    this.selectedState = value['state_name'];
    this.selectedCityId = value['city_id'];

    var stateObj = [{
      name: value['state_name'],
      id: value['state_name'],
      country: 1
    }];
    this.selectedCity = value['city_name'];
    if (this.selectedCity) {
      let splitCity = this.selectedCity.split(",");
      if (splitCity && Object.keys(splitCity).length && splitCity[0]) {
        this.selectedCity = splitCity[0].trim()
      }
    }
    console.log(this.selectedCity)
    this.listingAgentProfileForm.controls.profile['controls']['broker_city'].patchValue(this.selectedCity);

    // this.addPropertyFormGroup.controls.city.patchValue(value['city_id']);
    // this.addPropertyFormGroup.controls.city_name.setValue(this.selectedCity);

    this.br_stateList_new = stateObj;

    // this.addPropertyFormGroup.controls.state.patchValue(value['state_id']);
    this.listingAgentProfileForm.controls.profile['controls']['broker_state'].patchValue(value['state_name']);
    this.showStateLabel = true;
    this.selectedZipCode = '';
    // this.addPropertyFormGroup.controls.zipcode.setValue('');
    // this.addPropertyFormGroup.controls.zipcode_code.setValue('');
    this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].patchValue('');
    this.showCitySearchResult = false;
    this.br_zipCodeList_new = [];
    this.isSeletedCity = true;
    this.isSeletedZipCode = false;
  }

  onZipCodeSelect(value) {
    // this.addPropertyFormGroup.controls.zipcode.patchValue(value['id']);
    this.selectedZipCode = value['code'];
    this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].setValue(this.selectedZipCode);
    this.showZipCodeSearchResult = false;
    this.isSeletedZipCode = true;
  }


  // getCityList(state_id) {
  //   this.showStateLabel = true;
  // }
  setZipCodeLabel() {
    this.showZipCodeLabel = true;
  }

  focusOutFunction(elementType: string, inputValue: string): void {
    setTimeout(() => {
      console.log("---ElementType", elementType)
      if (elementType == 'city' && inputValue == '') {
        if (this.isSeletedCity == false) {
          this.errMessageResponse("Please select a City from the list.");
        }
      } else if (elementType == 'zipCode' && inputValue == '') {
        if (this.isSeletedZipCode == false) {
          this.errMessageResponse("Please select a ZipCode from the list.");
        }
      }
    }, 200);
  }

  // Added by angular END

  /*
  * @desc: to get list of states
  * @return: list of state Objects
  */
  getStateList() {
    this.profileService.getStates('US').subscribe(res => {
      this.stateList = res.result;
      this.checkAddressList();
    }, err => this.errorResponse(err.json()));
  }

  /*
  * @desc: to get list of cities
  * @return: list of city Objects
  */
  getCityList(state_id) {
    this.showStateLabel = true;
    this.profileService.getCities(state_id).subscribe(res => {
      this.cityList.push.apply(this.cityList, res.result);
    }, err => this.errorResponse(err.json()));
  }

  /*
  * @desc: to get list of zipCodes
  * @return: list of zipCode Objects
  */
  getZipCode(city_id) {
    this.profileService.getZipCodes(city_id).subscribe(res => {
      this.setZipCode(res.result);
    }, err => this.errorResponse(err.json()));
  }

  getBRStateList() {
    this.profileService.getStates('US').subscribe(res => {
      this.br_stateList = res.result;

      if (this.listingAgentProfileForm.controls['profile']['controls']['broker_state'].value) {
        this.stateObj_1 = this.br_stateList.filter(state => state.name == this.listingAgentProfileForm.controls['profile']['controls']['broker_state'].value)
        if (this.stateObj_1 && Object.keys(this.stateObj_1).length) {
          this.stateObj_1 = this.stateObj_1[0]
          const statename = this.listingAgentProfileForm.controls['profile']['controls']['broker_state'].value.toString()
          const stateObj = [{
            name: statename,
            country: 1,
            id: this.stateObj_1.id
          }];
          if (this.listingAgentProfileForm.controls['profile']['controls']['broker_city'].value) {
            this.profileService.getCities(this.stateObj_1.id).subscribe(res => {
              const br_cityList_1 = res.result;
              this.cityObj_1 = br_cityList_1.filter(city => city.name == this.listingAgentProfileForm.controls['profile']['controls']['broker_city'].value)
              if (this.cityObj_1 && Object.keys(this.cityObj_1).length) {
                this.cityObj_1 = this.cityObj_1[0];
                this.selectedCityId = this.cityObj_1.id
              }
            }, err => this.errorResponse(err.json()));
          }
          this.br_stateList_new = stateObj;
          this.isSeletedCity = true;
          this.isSeletedZipCode = true;
        }
      }
    }, err => this.errorResponse(err.json()));
  }

  getBRCityList(state_id) {
    this.listingAgentProfileForm.controls['profile']['controls']['city_br'].setValue(null);
    this.listingAgentProfileForm.controls['profile']['controls']['zipcode_br'].setValue(null);
    this.profileService.getCities(state_id).subscribe(res => {
      this.br_cityList = res.result;
    }, err => this.errorResponse(err.json()));
  }

  getBRZipCode(city_id) {
    this.listingAgentProfileForm.controls['profile']['controls']['zipcode_br'].setValue(null);
    this.profileService.getZipCodes(city_id).subscribe(res => {
      this.br_zipCodeList = res.result;
      this.setBRZipCode(res);
    }, err => this.errorResponse(err.json()));
  }

  setBRZipCode(codelist) {
    let response = [];
    for (let zipCode of codelist) {
      var zipCodeObj = {
        'id': zipCode.id,
        'code': "" + zipCode.code,
        'city': zipCode.city
      }
      response.push(zipCodeObj);
    }
    this.br_zipCodeList.push.apply(this.br_zipCodeList, response);
  }


  setZipCode(codelist) {
    let response = [];
    for (let zipCode of codelist) {
      var zipCodeObj = {
        'id': zipCode.id,
        'code': "" + zipCode.code,
        'city': zipCode.city
      }
      response.push(zipCodeObj);
    }
    this.zipCodeList.push.apply(this.zipCodeList, response);
  }

  /*
  * @desc: add cities/zipcodes list basis on already selected states
  * @return: list of zipCode Objects
  */
  checkAddressList() {
    if (this.states.length != 0) {
      for (let state of this.states) {
        this.getCityList(state.id);
      }
      if (this.cities.length != 0) {
        for (let city of this.cities) {
          this.getZipCode(city.id);
        }
      }
    }
  }

  /*
  * @desc: upload/change profile image
  */
  uploadProfileImage(event) {
    let formData = new FormData();
    formData.append('profile_photo', event.srcElement.files[0]);
    this.profileService.uploadUserImage(formData).subscribe(res => {
      this.fileUrl = res['result'];
      ProfileComponent.listingAgent.profile.profile_photo = res['result'];
      this.successResponse(res);
    }, err => {
      this.errorResponse(err.json());
    })
  }

  cardExDateV(value) {
    if (this.isInteger(value)) {
      if (value.length == 0) {
        this.listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'incorrect': true });
      }
      else {
        if (value > 12 || value.length > 2) {
          this.listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'incorrect': true });
        }
        else {
          if (value.length == 1) {
            this.selectedMonth = '0' + value.toString();
          }
          else {
            this.selectedMonth = value;
          }
        }
        if (this.selectedYear != null) {
          this.cardExYearV(this.selectedYear);
        }
      }
    }
    else {
      this.listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'number': true });
    }

  }

  cardExYearV(year) {
    let currentYear: String = new Date().getFullYear().toString();

    if (year.toString() >= currentYear.toString()) {
      this.selectedYear = year;
      let month: Number = new Date().getMonth() + 1;

      let months: String;

      if (month <= 9) {
        months = "0" + month;
      }
      else {
        months = month.toString();
      }

      if (this.selectedMonth >= months && this.selectedYear == currentYear) {
        if (this.selectedMonth >= months) {
        }
      }
      else {
        if (this.selectedYear > currentYear && this.selectedYear.length == 4) {

          if (this.selectedYear.length == 4 && parseInt(this.selectedMonth) > 12) {
            this.listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'incorrect': true });
          }
        }
        else {
          this.listingAgentPaymentForm.controls.creditCard['controls']['expirationMonth'].setErrors({ 'incorrect': true });
        }
      }
    }
    else {
      this.listingAgentPaymentForm.controls.creditCard['controls']['expirationYear'].setErrors({ 'incorrect': true });
    }
  }

  cancelSubscription() {
    this.disablecancelSubsBtn = true;
    this.profileService.cancelSubscription().subscribe(res => {
      this.successResponse(res);
      this.accountStatus = false;
      this.permanentPremium = false;
      this.headerComponent.selectedPlan = '';
      ProfileComponent.listingAgent.selected_plan_id = '';
      ProfileComponent.listingAgent.paid_account = false;
      BaseComponent.user.is_paid_account = false;
      this.disablecancelSubsBtn = false;
      $("#cancelSubscription").modal("hide");
    }, err => {
      this.errorResponse(err.json());
    });
  }

  OHPListCount(filedName) {
    if (filedName == 'state') {
      if (this.states.length != 0) {
        this.showOHPStatesLabel = true;
      }
    } else if (filedName == 'city') {
      if (this.cities.length != 0) {
        this.showOHPCitysLabel = true;
      }
    } else if (filedName == 'zipCode') {
      if (this.zipCodes.length != 0) {
        this.showOHPZipCodesLabel = true;
      }
    } else if (filedName == 'homeType') {
      if (this.homeTypes.length != 0) {
        this.showOHPTypeLabel = true;
      }
    }
  }

  onHomeTypeAdd(item) {
    this.showOHPTypeLabel = true;
  }

  onHomeTypeRemoved(item) {
    if (this.homeTypes.length == 0) {
      this.showOHPTypeLabel = false;
    }
  }

  dropDownArrowClick(type) {
    $(document).ready(function () {
      if (type == 'state') {
        $("#stateTag").trigger("click");
      } else if (type == 'city') {
        $("#cityTag").trigger("click");
      } else if (type == 'zipCode') {
        $("#zipCodeTag").trigger("click");
      } else if (type == 'homeTypeTag') {
        $("#homeTypeTag").trigger("click");
      }
    });
  }

  searchLenderUser(lenderName) {
    if (lenderName.trim().length != 0) {
      let lenderParam = new URLSearchParams();
      lenderParam.set('name', lenderName);

      this.profileService.lenderSearch(lenderParam).subscribe(res => {
        this.searchLenderList = res['result'];
        this.showLenderList = true;
      }, err => this.errorResponse(err.json()));
    }
  }

  manageLender(lenderId, status) {
    let manageLender = new URLSearchParams();
    manageLender.set('attach_user_id', lenderId);
    this.profileService.addRemoveLender(manageLender).subscribe(res => {

      this.myLender = res['result']['lender_info'];
      ProfileComponent.listingAgent = res['result'];
      if (status == 'add') {
        this.searchLenderList = [];
        this.showLenderList = false;
        $("#searchLenderTxt").val('');
      }
    }, err => {
      this.errorResponse(err.json());
    });
  }

  setOldEmailAddress(email) {
    this.oldEmailAddress = email;
  }

  verifyAgent() {
    this.profileService.verifyAgentSatatus().subscribe(res => {
      this.successResponse(res);
      if (res['result']['is_agent_verify'] == true) {
        this.agentMLSStatus = "V";
        ProfileComponent.listingAgent.profile.mls_status = "V";
      }
      else if (res['result']['is_agent_verify'] == false) {
        this.agentMLSStatus = "U";
        ProfileComponent.listingAgent.profile.mls_status = "U";
      }
    }, err => this.errorResponse(err.json()));
  }

  downLoadInvoiceHistory(transaction) {
    this.invoiceLoadingIndex = transaction['id'];
    this.showInvoiceLoading = true;
    var invoiceName = this.getInvoiceDateFormat(transaction['transaction_date']);
    var transactionId = transaction['id'];
    this.profileService.genratePurchaseHistoryPdf(transactionId).subscribe(res => {
      this.invoiceLoadingIndex = null;
      this.showInvoiceLoading = false;
      let fileBlob = res.blob();
      let blob = new Blob([fileBlob], {
        type: 'application/pdf'
      });
      let filename = 'Invoice_' + invoiceName.toLocaleLowerCase() + '.pdf';
      FileSaver.saveAs(blob, filename);
    }, err => {
      this.invoiceLoadingIndex = null;
      this.showInvoiceLoading = false;
      this.errorResponse(err.json());
    });
  }

  getBrokerEmail(email: string) {
    if (!this.isBrokerInfoExist && email.trim()) {
      this.profileService.getBrokerEmail(email).subscribe(res => {
        this.isBrokerEmailFound = true;

        if (res.result != undefined && Object.keys(res.result).length != 0) {
          this.listingAgentProfileForm.controls.profile['controls']['brokerage_id'].setValue(res.result.email);
          this.listingAgentProfileForm.controls.profile['controls']['broker_address'].setValue(res.result.address);
          this.listingAgentProfileForm.controls.profile['controls']['broker_city'].setValue(res.result.city);
          this.listingAgentProfileForm.controls.profile['controls']['broker_state'].setValue(res.result.state);
          this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].setValue(res.result.zipcode);
          this.listingAgentProfileForm.controls.profile['controls']['broker_name'].setValue(res.result.firm_name);
          this.listingAgentProfileForm.controls.profile['controls']['broker_name_actual'].setValue(' ');
          this.listingAgentProfileForm.controls.profile['controls']['broker_office_phone'].setValue(' ');
        }
      }, err => {
        this.isBrokerEmailFound = false;
        // this.errorResponse(err.json());
        this.listingAgentProfileForm.controls.profile['controls']['broker_address'].setValue('');
        this.listingAgentProfileForm.controls.profile['controls']['broker_city'].setValue('');
        this.listingAgentProfileForm.controls.profile['controls']['broker_state'].setValue('');
        this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].setValue('');
        this.listingAgentProfileForm.controls.profile['controls']['broker_name'].setValue('');
        this.listingAgentProfileForm.controls.profile['controls']['broker_name_actual'].setValue(' ');
        this.listingAgentProfileForm.controls.profile['controls']['broker_office_phone'].setValue(' ');
      });
    }
    else{
      this.profileService.getBrokerEmail(email).subscribe(res => {
        this.isBrokerEmailFound = true;
        if (res.result != undefined && Object.keys(res.result).length != 0) {
          this.listingAgentProfileForm.controls.profile['controls']['brokerage_id'].setValue(res.result.email);
          this.listingAgentProfileForm.controls.profile['controls']['broker_address'].setValue(res.result.address);
          this.listingAgentProfileForm.controls.profile['controls']['broker_city'].setValue(res.result.city);
          this.listingAgentProfileForm.controls.profile['controls']['broker_state'].setValue(res.result.state);
          this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].setValue(res.result.zipcode);
          this.listingAgentProfileForm.controls.profile['controls']['broker_name'].setValue(res.result.firm_name);
          this.listingAgentProfileForm.controls.profile['controls']['broker_name_actual'].setValue(' ');
          this.listingAgentProfileForm.controls.profile['controls']['broker_office_phone'].setValue(' ');
        }
      }, err => {
        if(this.brokerage_info){
          this.listingAgentProfileForm.controls.profile['controls']['broker_address'].setValue(this.brokerage_info.address);
          this.listingAgentProfileForm.controls.profile['controls']['broker_city'].setValue(this.brokerage_info.city);
          this.listingAgentProfileForm.controls.profile['controls']['broker_state'].setValue(this.brokerage_info.state);
          this.listingAgentProfileForm.controls.profile['controls']['broker_zipcode'].setValue(this.brokerage_info.zipcode);
          this.listingAgentProfileForm.controls.profile['controls']['broker_name'].setValue(this.brokerage_info.name);
          this.listingAgentProfileForm.controls.profile['controls']['broker_name_actual'].setValue(' ');
          this.listingAgentProfileForm.controls.profile['controls']['broker_office_phone'].setValue(' ');
        }
      });
    }
  }
}
