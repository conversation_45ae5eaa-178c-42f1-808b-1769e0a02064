<div>
    <div *ngIf="showTabletFilter == false" class="search_location header_fix">
        <div class="row new_shadow visible-xs mobile_search_menu">
            <div class="form_group col-sm-3">
              <input type="text" [(ngModel)]="searchLocation" class="new_form search_icon" #search placeholder="City, State or Zip" [value]="searchedValue" (keyup)="suggestions(search.value)" autocomplete="off">
              <span *ngIf="flag">
                <div class="baths_group price_ul">
                  <ul class="search-result" >
                    <li class="search-result-li" *ngFor="let value of locationList" (click)="onselectSuggestion(value)">
                        {{value.suggestion}}
                    </li>
                  </ul>
                </div>
              </span>
            </div>
             <div class="form_group text-center">
               <a (click)="otherFilters()"> <input type="submit" class="new_form" data-dismiss="modal" value="Other Filters"></a>
             </div>
          </div>

         <div class="row new_shadow hidden-xs">
          <div class="form_group col-sm-3">
            <input type="text" [(ngModel)]="searchLocation" class="new_form search_icon location-box" #search placeholder="City, State or Zip" [value]="searchedValue" (keyup)="suggestions(search.value)" autocomplete="off">
            <span *ngIf="flag">
              <div class="baths_group price_ul">
                <ul class="search-result" >
                  <li class="search-result-li" *ngFor="let value of locationList" (click)="onselectSuggestion(value)">
                      {{value.suggestion}}
                  </li>
                </ul>
              </div>
            </span>
          </div>

          <div class="form_group col-sm-2 price_select">
            <div class="select_mate">
              <input type="text" [(ngModel)]="ngModelfullPrice" readonly="readonly" id="pricetxt" class="select_box_price drop_down_icon cursor_poiner" placeholder="Price">
              <div class="square_footage">
                <div class="square_group">
                  <input type="number" (blur)="onPriceChanged(min.value,max.value)" [(ngModel)]="ngModelMinPrice" #min id="min_price" (keyup.enter)="closeMinMaxPrice()" class="mini minimum" placeholder="Minimum">
                  <div class="square_border"></div>
                  <input type="number" (blur)="onPriceChanged(min.value,max.value)" [(ngModel)]="ngModelMaxPrice" #max id="max_price" (keyup.enter)="closeMinMaxPrice()" class="mini max"  placeholder="Maximum">
                </div>
                <div class="baths_group price_ul price-list-res">
                    <ul id="targetId">
                      <li *ngFor="let price of PriceListForUI">{{price}}</li>
                    </ul>
                  </div>
              </div>
            </div>
          </div>
          <div *ngIf="searchFrom != 'eventManager'" class="form_group col-sm-2 drop-down-spc cursor_poiner homeType-width">
             <!-- <select multiple="multiple" class="new_form drop_down_icon cursor_poiner testSelAll2" placeholder="Home type">
                <option class="homeType"  value="Single Family Detached">Single Family Detached</option>
                <option class="homeType"  value="Townhouse">Townhouse</option>
                <option class="homeType" value="Apartment">Apartment</option>
                <option class="homeType" value="Condominium">Condominium</option>
                <option class="homeType" value="Duplex">Duplex</option>
                <option class="homeType" value="Manufactured Home">Manufactured Home</option>
                <option class="homeType" value="Mobile Home">Mobile Home</option>
                <option class="homeType" value="Quadruplex">Quadruplex</option>
                <option class="homeType" value="Single Family Attached">Single Family Attached</option>
                <option class="homeType" value="Triplex">Triplex</option>
             </select> -->
            <select multiple="multiple" class="new_form drop_down_icon cursor_poiner testSelAll2" placeholder="Home type">
              <option class="homeType"  value="Single Family - Detached">Single Family - Detached</option>
              <option class="homeType"  value="Loft Style">Loft Style</option>
              <option class="homeType" value="Moduler/Pre-Fab">Moduler/Pre-Fab</option>
              <option class="homeType" value="Mfg/Mobile Housing">Mfg/Mobile Housing</option>
              <option class="homeType" value="Gemini/Twin Home">Gemini/Twin Home</option>
              <option class="homeType" value="Apartment Style/Flat">Apartment Style/Flat</option>
              <option class="homeType" value="Townhouse">Townhouse</option>
              <option class="homeType" value="Patio Home">Patio Home</option>
            </select>
          </div>

          <!-- <div *ngIf="searchFrom == 'eventManager' || searchFrom == ''" class="form_group col-sm-2 drop-down-spc cursor_poiner event-type">
            <select multiple="multiple" class="new_form drop_down_icon cursor_poiner  sumoEvent" placeholder="All Event Types">
               <option class="eventType-drop-down"  value="Appointment Only">RSVP Only</option>
               <option class="eventType-drop-down" value="Open House">Open House</option>
               <option class="eventType-drop-down" value="Broker Open">Broker Open</option>
               <option class="eventType-drop-down" value="No Events">No Events</option>
            </select>
         </div> -->

          <!-- <div *ngIf="isFavoriteSearch != true" class="form_group col-sm-2 property-status"> -->
            <div *ngIf="showStatusFilter" class="form_group col-sm-2 property-status">
             <select multiple="multiple" class="new_form drop_down_icon cursor_poiner   testSelAll3" placeholder="Status">
                <option class="anyStatus" value="Active">Active</option>
                <option class="anyStatus" value="Pending">Pending</option>
                <option class="anyStatus" value="PRE-MLS/Coming Soon">PRE-MLS/Coming Soon</option>
             </select>
          </div>
          <div *ngIf="searchFrom != 'eventManager'" class="form_group col-sm-2">
                <ng-select class="custom search-dropdown cursor_poiner"
                    placeholder="Beds"
                    [items]="BedsList"
                    bindValue="value"
                    bindLabel="value"
                    [clearable]=false
                    [searchable]=false
                    [virtualScroll]=true
                    [(ngModel)]="savedBeds"
                    (change)="setAllBeds($event)"
                    >
                  </ng-select>

          </div>
          <div *ngIf="searchFrom != 'eventManager'" class="form_group col-sm-2 drop-down-spc">
              <ng-select class="custom search-dropdown cursor_poiner"
                  placeholder="Baths"
                  [items]="BathList"
                  bindValue="value"
                  bindLabel="value"
                  [clearable]=false
                  [searchable]=false
                  [(ngModel)]="savedBath"
                  (change)="setAllBaths($event)"
                  >
                </ng-select>
          </div>

          <div class="form_group col-sm-2 price_select">
            <div class="select_mate">
              <input id="sqfttxt" [(ngModel)]="ngModelfullSqft" readonly="readonly" type="text" class="select_box_price2 drop_down_icon cursor_poiner " placeholder="Sq. ft.">
              <div class="square_footage2">
                <div class="square_group">
                  <input type="number" [(ngModel)]="ngModelMinSqft" #minsqft id="minSqft" (keyup.enter)="minSqft(minsqft.value,maxsqft.value,true)" class="mini2 minimumSqft" placeholder="Minimum">
                  <div class="square_border"></div>
                  <input type="number" [(ngModel)]="ngModelMaxSqft" #maxsqft id="maxSqft" (keyup.enter)="minMaxSqft(minsqft.value,maxsqft.value,true)" class="mini2 maxSqft"  placeholder="Maximum">
                </div>
              </div>
            </div>
          </div>

          <div *ngIf="searchFrom == 'eventManager'" class="agent-align">
            <div class="agent-drop-down">
            <ng-select class="custom o-h-a"
                placeholder="Open House Agent"
                [items]="openHouseAgentList"
                bindValue="id"
                bindLabel="agent_name"
                [clearable]=false
                [(ngModel)]="selectedOHAId"
                (change)="onOpneHouseSelect($event)"
                notFoundText="No Open House Agent Found"
                [searchable]=false
                >
              </ng-select>
            </div>
         </div>

         <!-- <div *ngIf="searchFrom == 'eventManager'" class="no-of-going">
            <div class="select_mate">
              <input readonly="readonly" type="text" class="number-of-going drop_down_icon cursor_poiner " placeholder="Number Going">
              <div class="square_footage2">
                <div class="square_group">
                  <input type="number" #minNoOFGoing id="minNoOFGoing" (keyup.enter)="minGoing(minNoOFGoing.value,maxNoOFGoing.value)" class="mini2 mintext" placeholder="Minimum">
                  <div class="square_border"></div>
                  <input type="number" #maxNoOFGoing id="maxNoOFGoing" (keyup.enter)="minMaxGoing(minNoOFGoing.value,maxNoOFGoing.value)" class="mini2 maxtext"  placeholder="Maximum">
                </div>
                <div class="goingGroup price_ul">
                  <ul id="sqftul">
                    <li>0</li>
                    <li>50</li>
                    <li>100</li>
                    <li>150</li>
                    <li>200</li>
                    <li>250</li>
                  </ul>
                </div>
              </div>
            </div>
          </div> -->
          <!-- <div id="event_filter" *ngIf="searchFrom == 'eventManager' || searchFrom == ''" class="form_group col-sm-2 drop-down-spc cursor_poiner event-type">
              <select multiple="multiple" class="new_form drop_down_icon cursor_poiner  sumoEvent" placeholder="All Event Types">
                 <option class="eventType-drop-down" value="Open House">Open House</option>
                 <option class="eventType-drop-down"  value="Appointment Only">RSVP Only</option>
                 <option class="eventType-drop-down" value="Broker Open">Broker Open</option>
                 <option class="eventType-drop-down" value="No Events">No Events</option>
              </select>
           </div> -->
           <div *ngIf="searchFrom == '' && showopenHouseFilter == true" class="form_group col-sm-2 drop-down-spc cursor_poiner event-type event_type_filter">
            <select multiple="multiple" class="new_form drop_down_icon cursor_poiner  sumoOpenHouseEvent" placeholder="Open House Type">
              <option class="openHouseEventTypeDropDown" selected value="SHOW ALL Open Houses">SHOW ALL Open Houses</option>
              <option class="openHouseEventTypeDropDown" value="SHOW ONLY PRE-MLS /Coming Soon">SHOW ONLY Coming Soon</option>
              <option *ngIf="searchFrom != 'eventManager'" class="openHouseEventTypeDropDown" value="Active Listings - No Open Houses">Active Listings - No Open Houses</option>
            </select>
           </div>

          <div *ngIf="showSaveSearchbtn == true" class="save_search_box pull-right" [ngClass]="{'save-search-btn-top' : currentPage == 'listingAgent'}">
            <button type="button" class="btn  add_new_list dis_inline cursor_poiner" data-toggle="modal" (click)="openSaveSearchCard()">Save Search</button>
             <div *ngIf="currentPage != 'listingAgent'" class="form_group  title2 dis_inline ml-20 mr-30 My_Saved_Searches">
                <ng-select class="custom save-dropdown"
                  placeholder="My Saved Searches"
                  [items]="savedSearchList"
                  bindLabel="title"
                  bindValue="id"
                  [clearable]=false
                  [searchable]=false
                  notFoundText=" No Saved Searches"
                  (change)="showSavedSearch($event)"
                  >
                </ng-select>
             </div>
          </div>
        </div>
    </div>

    <div *ngIf="showTabletFilter == true" class="search_location tablet_search_filter tablet_search_location">
     <div class="row new_shadow hidden-xs">
        <div class="form_group col-sm-3">
          <input id="tabSearchLocation" type="text" [(ngModel)]="searchLocation" class="new_form search_icon" #search placeholder="City, State or Zip" [value]="searchedValue" (keyup)="suggestions(search.value)" autocomplete="off" autocomplete="off">
          <span class="search-icon" (click)="getLocation()" data-toggle="tooltip" data-placement="top" title="Detect Current Location" onmouseenter="$(this).tooltip('show')"> <img src="{{imagePrefix}}location-icon.png" alt=""> </span>
          <span *ngIf="flag" class="map-search">
              <div class="baths_group price_ul">
                <ul class="search-result search-result-map" >
                  <li class="search-result-li" *ngFor="let value of locationList" (click)="onselectSuggestion(value)">
                      {{value.suggestion}}
                  </li>
                </ul>
              </div>
            </span>
        </div>

          <div class="form_group col-sm-2 price_select">
            <div class="select_mate">
              <input type="text" [(ngModel)]="ngModelfullPrice" readonly="readonly" id="pricetxt" class="select_box_price drop_down_icon cursor_poiner" placeholder="Price">
              <div class="square_footage price-range">
                <div class="square_group">
                  <input type="number" (blur)="onPriceChanged(min.value,max.value)" [(ngModel)]="ngModelMinPrice" #min id="min_price" (keyup.enter)="closeMinMaxPrice()" class="mini minimum" placeholder="Minimum">
                  <div class="square_border"></div>
                  <input type="number" (blur)="onPriceChanged(min.value,max.value)" [(ngModel)]="ngModelMaxPrice" #max id="max_price" (keyup.enter)="closeMinMaxPrice()" class="mini max"  placeholder="Maximum">
                </div>
                <div class="baths_group price_ul price-list-res">
                    <ul id="targetId">
                      <li *ngFor="let price of PriceListForUI">{{price}}</li>
                    </ul>
                  </div>
              </div>
            </div>
          </div>

        <div *ngIf="searchFrom != 'eventManager'" id="tabHomeType" class="form_group col-sm-2 all_home_type">
           <!-- <select multiple="multiple" class="new_form drop_down_icon testSelAll2" placeholder="Home type">
            <option class="homeType"  value="Single Family Detached">Single Family Detached</option>
            <option class="homeType"  value="Townhouse">Townhouse</option>
            <option class="homeType" value="Apartment">Apartment</option>
            <option class="homeType" value="Condominium">Condominium</option>
            <option class="homeType" value="Duplex">Duplex</option>
            <option class="homeType" value="Manufactured Home">Manufactured Home</option>
            <option class="homeType" value="Mobile Home">Mobile Home</option>
            <option class="homeType" value="Quadruplex">Quadruplex</option>
            <option class="homeType" value="Single Family Attached">Single Family Attached</option>
            <option class="homeType" value="Triplex">Triplex</option>
           </select> -->
          <select multiple="multiple" class="new_form drop_down_icon cursor_poiner testSelAll2" placeholder="Home type">
            <option class="homeType"  value="Single Family - Detached">Single Family - Detached</option>
            <option class="homeType"  value="Loft Style">Loft Style</option>
            <option class="homeType" value="Moduler/Pre-Fab">Moduler/Pre-Fab</option>
            <option class="homeType" value="Mfg/Mobile Housing">Mfg/Mobile Housing</option>
            <option class="homeType" value="Gemini/Twin Home">Gemini/Twin Home</option>
            <option class="homeType" value="Apartment Style/Flat">Apartment Style/Flat</option>
            <option class="homeType" value="Townhouse">Townhouse</option>
            <option class="homeType" value="Patio Home">Patio Home</option>
          </select>
        </div>

        <!-- <div id="event_tablet" *ngIf="searchFrom == 'eventManager' || searchFrom == ''" class="form_group col-sm-2 drop-down-spc cursor_poiner event-type">
          <select multiple="multiple" class="new_form drop_down_icon cursor_poiner  sumoEvent" placeholder="All Event Types">
             <option class="eventType-drop-down"  value="Appointment Only">RSVP Only</option>
             <option class="eventType-drop-down" value="Open House">Open House</option>
             <option class="eventType-drop-down" value="Broker Open">Broker Open</option>
             <option class="eventType-drop-down" value="No Events">No Events</option>
          </select>
        </div> -->

        <div class="more_filter all_home_type_2">
          <input type="text" value="More" readonly="readonly" id="moreFilter" (click)="showMoreFilter()" class="drop_down_icon more_show_filter">

        <div class="show_more_search_filter">
            <!-- <div class="form_group col-sm-2" *ngIf="isFavoriteSearch != true"> -->
              <div class="form_group col-sm-2" *ngIf="showStatusFilter">
                <select multiple="multiple" class="new_form drop_down_icon cursor_poiner   testSelAll3" placeholder="Status">
                  <option class="anyStatus" value="Active">Active</option>
                  <option class="anyStatus" value="Pending">Pending</option>
                  <option class="anyStatus" value="PRE-MLS/Coming Soon">PRE-MLS/Coming Soon</option>
                </select>
             </div>

             <div *ngIf="searchFrom != 'eventManager'" class="form_group col-sm-2">
                <ng-select class="custom search-dropdown-tablet cursor_poiner"
                  placeholder="Beds"
                  [items]="BedsList"
                  bindValue="value"
                  bindLabel="value"
                  [clearable]=false
                  [searchable]=false
                  [virtualScroll]=true
                  [(ngModel)]="savedBeds"
                  (change)="setAllBeds($event)"
                  >
                </ng-select>
              </div>


              <div *ngIf="searchFrom != 'eventManager'" class="form_group col-sm-2 drop-down-spc">
                <ng-select class="custom search-dropdown-tablet cursor_poiner"
                    placeholder="Baths"
                    [items]="BathList"
                    bindValue="value"
                    bindLabel="value"
                    [clearable]=false
                    [searchable]=false
                    [(ngModel)]="savedBath"
                    (change)="setAllBaths($event)"
                    >
                  </ng-select>
            </div>

            <div *ngIf="searchFrom == 'eventManager'" class="form_group col-sm-2 drop-down-spc agent-align">
              <ng-select class="custom search-dropdown-tablet"
                  placeholder="Open House Agent"
                  [items]="openHouseAgentList"
                  bindValue="id"
                  bindLabel="agent_name"
                  [clearable]=false
                  [(ngModel)]="selectedOHAId"
                  (change)="onOpneHouseSelect($event)"
                  notFoundText="No Open House Agent Found"
                  [searchable]=false
                  >
                </ng-select>
           </div>

             <div class="form_group col-sm-2 price_select sqft-width">
              <div class="select_mate">
                <input id="sqfttxt" [(ngModel)]="ngModelfullSqft" readonly="readonly" type="text" class="select_box_price2 drop_down_icon cursor_poiner sqft-mt" placeholder="Sq. ft.">
                <div class="square_footage2 sqft-box">
                  <div class="square_group">
                    <input type="number" [(ngModel)]="ngModelMinSqft" #minsqft id="minSqft" (keyup.enter)="minSqft(minsqft.value,maxsqft.value,true)" class="mini2 minimumSqft" placeholder="Minimum">
                    <div class="square_border square_border_tab"></div>
                    <input type="number" [(ngModel)]="ngModelMaxSqft" #maxsqft id="maxSqft" (keyup.enter)="minMaxSqft(minsqft.value,maxsqft.value,true)" class="mini2 maxSqft"  placeholder="Maximum">
                  </div>
                </div>
              </div>
            </div>

            <!-- <div id="event_tablet" *ngIf="searchFrom == 'eventManager' || searchFrom == ''" class="form_group col-sm-2 drop-down-spc cursor_poiner event-type">
                <select multiple="multiple" class="new_form drop_down_icon cursor_poiner  sumoEvent" placeholder="All Event Types">
                   <option class="eventType-drop-down" value="Open House">Open House</option>
                   <option class="eventType-drop-down"  value="Appointment Only">RSVP Only</option>
                   <option class="eventType-drop-down" value="Broker Open">Broker Open</option>
                   <option class="eventType-drop-down" value="No Events">No Events</option>
                </select>
              </div> -->

              <div id="open_house_event_tablet" *ngIf="searchFrom == '' && showopenHouseFilter == true" class="form_group col-sm-2 drop-down-spc cursor_poiner event-type">
                <select multiple="multiple" class="new_form drop_down_icon cursor_poiner  sumoOpenHouseEvent" placeholder="Open House Type">
                  <option class="openHouseEventTypeDropDown" selected value="SHOW ALL Open Houses">SHOW ALL Open Houses</option>
                  <option class="openHouseEventTypeDropDown" value="SHOW ONLY PRE-MLS /Coming Soon">SHOW ONLY Coming Soon</option>
                  <option *ngIf="searchFrom != 'eventManager'" class="openHouseEventTypeDropDown" value="Active Listings - No Open Houses">Active Listings - No Open Houses</option>
                </select>
              </div>


          <div *ngIf="showSaveSearchbtn == true" class="save_search_box pull-right" [ngClass]="{'save-search-btn-top' : currentPage == 'listingAgent'}">
            <button type="button" class="btn  add_new_list dis_inline cursor_poiner" data-toggle="modal" (click)="openSaveSearchCard()">Save Search</button>
              <div *ngIf="currentPage != 'listingAgent'" class="form_group  title2 dis_inline ml-20 mr-30 My_Saved_Searches">
                <ng-select class="custom save-dropdown"
                  placeholder="My Saved Searches"
                  [items]="savedSearchList"
                  bindLabel="title"
                  bindValue="id"
                  [clearable]=false
                  [searchable]=false
                  notFoundText=" No Saved Searches"
                  (change)="showSavedSearch($event)"
                  >
                </ng-select>
              </div>
          </div>
        <!-- <div class="save_search_box pull-right">
            <button type="button" class="btn add_new_list dis_inline" data-toggle="modal" data-target="#saveSearch">Save Search</button>
            <div class="form_group  title2 dis_inline ml-20 mr-30 My_Saved_Searches">
               <div class="select_mate" data-mate-select="active" >
                  <select name="" class="new_form drop_down_icon" onclick="return false;" id="">
                     <option value="">My Saved Searches</option>
                     <option value="">Phoenix, AZ</option>
                     <option value="">My saved search 2</option>
                     <option value="">Under $200k</option>
                  </select>
                  <p class="selecionado_opcion"  onclick="open_select(this)" ></p>
                  <span onclick="open_select(this)" class="icon_select_mate" >
                  <span class="drop_down_icon drp_soan"></span>
                  </span>
                  <div class="cont_list_select_mate">
                     <ul class="cont_select_int">  </ul>
                  </div>
               </div>
            </div>
         </div> -->
        </div>
      </div>
    </div>
  </div>

    <div class="modal fade searchModel" id="saveSearch" role="dialog">
      <div class="modal-dialog">
         <div class="modal-content">
            <div class="modal-body">
                  <div class="square_group padding_0 save-search-border">
                     <div class="title2">
                        Save your search
                     </div>
                     <div class="form_group_square">
                        <form [formGroup]="saveSearchForm">
                        <div class="form_group">
                           <label for="">Name your search</label>
                           <input type="text" formControlName="name" #searchName class="new_form" placeholder="Ex. Phoenix, AZ" required>
                        </div>
                        <div class="form_group">
                           <label for="">Email Settings</label>
                           <div>
                              <ng-select class="custom date-drop-down date-drop-down1"
                                    placeholder="Email Settings"
                                    [items]="emailSettingList"
                                    formControlName="email"
                                    bindValue="name"
                                    [clearable]=false
                                    [searchable]=false
                                    (change)="setEmailSettings($event)">
                            </ng-select>
                          </div>
                        </div>
                        <div class="form_group text-center">
                          <input type="submit" [ngClass]="{'submit-disable':saveSearchForm.invalid || !isEmail}" [disabled]="saveSearchForm.invalid || !isEmail" class="new_form" data-dismiss="modal" value="Save" (click)="saveSearch(searchName.value)">
                        </div>
                        </form>
                     </div>
                  </div>
              </div>
          </div>
        </div>
   </div>
</div>
