import { Injectable } from '@angular/core';
import { ServiceLocator } from '@app/base/components/service-locator';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { Observable } from 'rxjs/Observable';
import { ApiService } from '@app/base/services/api.service';
import { Plans,PlanResponse} from '@app/profile/models/plan'
import { PlansDetails} from '@app/admin/adminPlans//model/plans-details';
import { ApiResponse } from '@app/auth/models/api-response';
import { BaseComponent } from '@app/base/components/base.component';

@Injectable()
export class GetPlansService {

  public baseservice:BaseComponent;
  public apiService:ApiService
  
  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
   }

   public getPlans():Observable<PlanResponse>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['purchase']['getPlans'],{});
    return this.apiService.apiCall(options);
  }

  public getPlansByUserType(urlParams):Observable<PlanResponse>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'], API_REFERENCE['purchase']['getPlans'] + '?' + urlParams.toString(), null, null, null, null, null);
    return this.apiService.apiCall(options);
  }
}