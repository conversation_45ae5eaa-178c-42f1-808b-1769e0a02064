<div class="modal fade rate_modal search_event event-card-responsive" id="eventModal" role="dialog">
  <div class="modal-dialog modal-lg">
      <div class="modal-content event-model-content">
        <div class="modal-body">
            <span class="event-card-image-gradient card-image">

                <img *ngIf="event.property_file == ''" src="{{imagePrefix}}symbols-map-hover.png" class="search_Rectangle img-responsive" alt="">
                <img *ngIf="event.property_file != ''" src="{{event.property_file}}" class="search_Rectangle img-responsive" alt="">

                <span class="modal-icon">
                    <img *ngIf= "eventDetail == 'appointmentOnly'" style="width: 25px;" src="{{imagePrefix}}72_White.png">
                    <img *ngIf= "eventDetail != 'appointmentOnly'" src="{{imagePrefix}}whiteHouse.png">
                    <span class="modal-title" *ngIf="event.event_type_msg =='72 Hour Launch'" style="padding-left: 0px !important">Hour Home Sale</span>
                    <span class="modal-title" *ngIf="event.event_type_msg !='72 Hour Launch'" style="padding-left: 0px !important">{{event.event_type_msg}}</span>
                </span>

                <button type="button" class="close" data-dismiss="modal">&times;</button>

            </span>

            <div class="event_address">
                <span class="event-addres-line">{{event.street_address}}<br>{{event.location}}</span>
            <span class="Icon_button cursor-pointer"><img class="car-icon" (click)="mapDirections(event.latitude,event.longitude)" src="{{imagePrefix}}Car-Icon.png" alt=""></span>
          </div>
            <div *ngIf="event.date != ''" class="event_date" [ngClass]="{'font_color_4':eventDetail == 'appointmentOnly','font_color_2':eventDetail == 'brokerOpen','font_color_3':eventDetail == 'openHouse'}">{{getStringEventDate(event.date,event.start_time,event.is_listhub_event)}}</div>
            <div *ngIf="event.date == ''" class="event_date" [ngClass]="{'font_color_4':eventDetail == 'appointmentOnly','font_color_2':eventDetail == 'brokerOpen','font_color_3':eventDetail == 'openHouse'}">{{getStringEventDate(event.date,event.start_time,event.is_listhub_event)}}</div>
            <div *ngIf="event.start_time != '' && event.end_time != ''" class="event_date2">{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}} - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</div>
            <div *ngIf="event.start_time == '' && event.end_time == ''" class="event_date2">-</div>
            <!-- <div class="event_text" [innerHtml]="event.description | safeHtml: 'html'"></div> -->
            <div class="event_text" [innerHtml]="linkify(event.description) | safeHtml: 'html'"></div>
            <!-- <div *ngIf="eventDetail == 'appointmentOnly'" class="event_text">This is an RSVP only open house, so please contact agent for showing times.</div> -->

            <div class="appointment-time-padding" *ngIf="eventDetail == 'appointmentOnly'">
                <div *ngIf="event.is_going == false && isFreeUser">
                    <div class="selected_go event_save dis_inline ap-c-a" [ngClass]="{'border-pink':eventDetail == 'appointmentOnly', 'ap-c-a-selected': onContactAgentClickAP == true}" (click)="going(event.id)">Contact Agent</div>
                </div>
                <div *ngIf="event.is_going == true" class="add-to-list" [ngClass]="{'in-go-btn':!isFreeUser == true}">
                    <span class="add-to-list-s"><i class="fa fa-check check-icon"></i>This event is in your list</span>
                    <span *ngIf="event.is_checkin == false" class="remove_button_event" (click)="removePropertyFromMyList(event.open_house_id,event)">Remove</span>
                </div>
            </div>

            <!-- <div *ngIf="eventDetail == 'appointmentOnly'" class="event_text">{{event.description}}</div> -->

            <div *ngIf="eventDetail == 'openHouse' || eventDetail == 'brokerOpen' " [ngClass]="{'in-go-btn':isValidForMsg == false}">
                <div *ngIf="event.is_going == false" class="selected_go event_save dis_inline" [ngClass]="{'border-gray bg_gray':eventDetail == 'brokerOpen' ,'border-mountain bg_mountain':eventDetail == 'openHouse','bg_mountain-selected':isGoingOH == true,'bg_gray-selected':isGoingBO == true}" (click)="going(event.id)">Add to my list</div>
                <span *ngIf="showConatctAgentBtn == true">
                    <div *ngIf="event.is_going == false && isFreeUser" class="selected_go event_save dis_inline" [ngClass]="{'border-gray bg_gray':eventDetail == 'brokerOpen' ,'border-mountain bg_mountain':eventDetail == 'openHouse','bg_mountain-selected':onContactAgentClickOH == true,'bg_gray-selected':onContactAgentClickBO == true}" (click)="openMessageBox()">Contact Agent</div>
                </span>
                <div *ngIf="event.is_going == true" class="add-to-list" [ngClass]="{'in-go-btn':!isFreeUser == true}">
                    <span class="add-to-list-s"><i class="fa fa-check" [ngClass]="{'check-icon-br':eventDetail == 'brokerOpen' ,'check-icon-oh':eventDetail == 'openHouse'}"></i>This event is in your list</span>
                    <span *ngIf="event.is_checkin == false" class="remove_button_event" (click)="removePropertyFromMyList(event.open_house_id,event)">Remove</span>
                </div>
            </div>

            <span *ngIf="isValidForMsg == true && isFreeUser">
                <div class="event_details_modal">
                    <div class="heading-agent">Contact Open House Agent</div>
                    <img *ngIf="event.agent_profile_photo != ''" src="{{event.agent_profile_photo}}" class="symbols-property-image dis_inline img-agent" alt="">
                    <img *ngIf="event.agent_profile_photo == ''" src="{{imagePrefix}}default-placeholder.png" class="symbols-property-image dis_inline img-agent" alt="">
                    <div class="dis_inline po_rel"><span class="dark">{{event.agent_name}}<br></span>{{event.agent_brokerage_firm_name}}</div>
                    <div *ngIf="hideMsgBox == true" class="form_group">
                        <textarea class="event_modal_lorem event-msg-box" [(ngModel)]="openHouseAgentMessage" #msg (keyup)="sendMessage(msg.value,'validation')" placeholder="Message" cols="30" rows="10"></textarea>
                        <div *ngIf="disableSendBtn == true" class="send-pink submit-disable" [ngClass]="{'send-pink':eventDetail == 'appointmentOnly','send-gray':eventDetail == 'brokerOpen','send-mountain':eventDetail == 'openHouse'}">Send</div>
                        <div *ngIf="disableSendBtn == false" class="send-pink" [ngClass]="{'send-pink':eventDetail == 'appointmentOnly','send-gray':eventDetail == 'brokerOpen','send-mountain':eventDetail == 'openHouse'}" (click)="sendMessage(msg.value,'send')">Send</div>
                    </div>
                </div>
            </span>
            <div *ngIf="showRating == true" class="event_footer" [ngClass]="{'bg_color_4':eventDetail == 'appointmentOnly','bg_color_2':eventDetail == 'brokerOpen','bg_color_3':eventDetail == 'openHouse'}" >
              <div class="dis_inline">
                  <div class="title2">Did you visit this home?</div>
              </div>
              <div class="event_save  dis_inline cursor-pointer" data-dismiss="modal" data-toggle="modal" (click)="openRatingModal(event,true)">Log your thoughts</div>
            </div>
        </div>
      </div>
  </div>
</div>

<div class="modal fade rate_modal modal-scroll event-card-responsive" id="myModalrate" role="dialog">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
          <div class="modal-body">
            <span class="event-card-image-gradient card-image">
                <span class="modal-icon">
                    <img *ngIf= "eventDetail == 'appointmentOnly'" style="width: 25px;" src="{{imagePrefix}}72_White.png">
                    <img *ngIf= "eventDetail != 'appointmentOnly'" src="{{imagePrefix}}whiteHouse.png">
                    <span class="modal-title">{{eventModal.event_type_msg}}</span>
                </span>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <span *ngIf="eventModal.property_file == ''"><img src="{{imagePrefix}}symbols-map-hover.png" class="search_Rectangle img-responsive" alt=""></span>
                <span *ngIf="eventModal.property_file != ''"><img src="{{eventModal.property_file}}" class="search_Rectangle img-responsive" alt=""></span>
            </span>

            <div *ngIf="!disabledEventModal" class="rate">Log your thoughts</div>
            <div class="theme_img_modal rate-thumb cursor-pointer">
                <img *ngIf="selectedThumb != 1" (click)="setThumb(1)" src="{{imagePrefix}}symbols-glyph-checkin-thumbsup2.png" class="symbols-glyph-checkin-thumbsup2 rate-thumb-img" [ngClass]="{'thumns-background':selectedThumb == 1}" alt="">
                <img *ngIf="selectedThumb == 1" (click)="setThumb(1)" src="{{imagePrefix}}thumbsupic.png" class="symbols-glyph-checkin-thumbsup2 rate-thumb-img white-thumbup-img" [ngClass]="{'thumns-background':selectedThumb == 1}" alt="">
                <img (click)="setThumb(0)" src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown6.png" class="symbols-glyph-checkin-thumbsdown6 rate-thumb-img" [ngClass]="{'thumns-background':selectedThumb == 0}" alt="">
            </div>
            <div class="rate_title">What influenced your opinion?</div>
            <div class="form_group">
                <div class="row rate-modal-row">
                  <div class="col-sm-5 col-sm-offset-3">
                      <div class="check_group mt-10">
                        <div class="form_group">
                            <input type="checkbox" [checked]="eventModal.property_rate.floorplan_rating" [disabled]="disabledEventModal" (change)="propertyRating('floorplan_rating',$event)">  <span class="checkmark"></span>
                            <img height="30px" width="26px" src="{{imagePrefix}}ricon1.png" class="rating-check-img">
                            <label class="rate-label">Floorplan</label>
                        </div>
                        <div class="form_group ">
                            <input type="checkbox" [(ngModel)]="eventModal.property_rate.bathroom_rating" [disabled]="disabledEventModal" (change)="propertyRating('bathroom_rating',$event)">  <span class="checkmark"></span>
                            <img height="30px" width="26px" src="{{imagePrefix}}ricon3.png" class="rating-check-img">
                            <label class="rate-label">Bathroom size</label>
                        </div>
                        <div class="form_group">
                            <input type="checkbox" [(ngModel)]="eventModal.property_rate.finishes_rating" [disabled]="disabledEventModal" (change)="propertyRating('finishes_rating',$event)">  <span class="checkmark"></span>
                            <img  height="30px" width="26px" src="{{imagePrefix}}ricon5.png" class="rating-check-img">
                            <label class="rate-label">Finishes</label>
                        </div>
                        <div class="form_group ">
                            <input type="checkbox" [(ngModel)]="eventModal.property_rate.neighbourhood_rating" [disabled]="disabledEventModal" (change)="propertyRating('neighbourhood_rating',$event)">  <span class="checkmark"></span>
                            <img height="30px" width="26px" src="{{imagePrefix}}ricon7.png" class="rating-check-img">
                            <label class="rate-label">Neighborhood</label>
                        </div>
                      </div>
                  </div>
                  <div class="col-sm-8">
                      <div class="check_group mt-10">
                        <div class="form_group">
                            <input type="checkbox" [(ngModel)]="eventModal.property_rate.bedroom_rating" [disabled]="disabledEventModal" (change)="propertyRating('bedroom_rating',$event)">  <span class="checkmark"></span>
                            <img height="30px" width="26px" src="{{imagePrefix}}ricon2.png" class="rating-check-img">
                            <label class="rate-label">Bedroom size</label>
                        </div>
                        <div class="form_group ">
                            <input type="checkbox" [(ngModel)]="eventModal.property_rate.kitchen_rating" [disabled]="disabledEventModal" (change)="propertyRating('kitchen_rating',$event)">  <span class="checkmark"></span>
                            <img height="30px" width="26px" src="{{imagePrefix}}ricon4.png" class="rating-check-img">
                            <label class="rate-label">Kitchen</label>
                        </div>
                        <div class="form_group">
                            <input type="checkbox" [(ngModel)]="eventModal.property_rate.landscaping_rating" [disabled]="disabledEventModal" (change)="propertyRating('landscaping_rating',$event)">  <span class="checkmark"></span>
                            <img height="30px" width="26px" src="{{imagePrefix}}ricon6.png" class="rating-check-img">
                            <label class="rate-label">Landscaping</label>
                        </div>
                        <div class="form_group ">
                            <input type="checkbox" [(ngModel)]="eventModal.property_rate.price_rating"
                                                    [disabled]="disabledEventModal"
                                                    (change)="propertyRating('price_rating',$event)">
                            <span class="checkmark"></span>
                            <img height="30px" width="26px" src="{{imagePrefix}}ricon8.png" class="rating-check-img">
                            <label class="rate-label">Price</label>
                        </div>
                      </div>
                  </div>
                </div>
                <div class="rate_title">Anything else?</div>
                <textarea name="" class="the_kitchen" id="" [(ngModel)]="ratingDescription" cols="30" rows="10" placeholder="Message" [disabled]="disabledEventModal"></textarea>
                <div class="form_group text-center mb-20">
                    <span *ngIf="!disabledEventModal"><input type="submit" (click)="addRating('ADD')" class="new_form" value="Save"></span>
                    <span *ngIf="disabledEventModal"><input type="submit" (click)="addRating('CLOSE')" class="new_form" value="Close"></span>
                </div>
            </div>
          </div>
      </div>
    </div>
</div>
