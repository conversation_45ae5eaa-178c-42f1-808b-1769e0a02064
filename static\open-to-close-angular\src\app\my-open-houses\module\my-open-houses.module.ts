import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MyOpenHousesComponent } from '@app/my-open-houses/component/my-open-houses.component';
import { MyOpenHousesListComponent } from '@app/my-open-houses/component/my-open-houses-list.component';
import { MyOpenHousesService } from '@app/my-open-houses/services/my-open-houses.service';
import { BaseModule } from '@app/base/modules/base.module';

@NgModule({
  imports: [
    CommonModule,
    BaseModule
  ],
  declarations: [MyOpenHousesComponent,MyOpenHousesListComponent],
  providers : [MyOpenHousesService]
})
export class MyOpenHousesModule { }
