import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PropertyDetailComponent } from '@app/property-detail/components/property-detail.component';
import { BaseModule } from '@app/base/modules/base.module';
import { ChatComponent } from '@app/messaging/components/chat.component';
import { PropertyInfoPipe } from '@app/property-detail/pipes/property-info.pipe';
import { ClipboardModule } from 'ngx-clipboard';

@NgModule({
  imports: [
    CommonModule,
    BaseModule,
    ClipboardModule
  ],
  declarations: [PropertyDetailComponent,PropertyInfoPipe]
})
export class PropertyDetailModule { }
