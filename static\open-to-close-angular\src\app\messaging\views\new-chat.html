
<div class="form_group">
        <label for="" class="pd-r-10">Send message to</label>
        <input type="text" #text (keyup)="filterUser(text.value)" class="type_of_person" placeholder="Type name of a person">
</div>

<div class="new-chat-div">
    <div *ngFor="let client of myClientList" class="new-client-list" (click)="selectedClient(client)">
        <span>
            <div *ngIf="client.profile_image == '' " class="ls dis_inline po_rel_1">{{client.user_initial}}</div>
            <span *ngIf="client.profile_image !='' ">
                <img class="chat-profile-img" src="{{client.profile_image}}">
            </span>
            <span class="new-chat-name-msg new-chatmessage_name">{{client.user_name}}</span>
        </span>
    </div>
</div>
        
