import { Component, OnInit } from "@angular/core";
import { BaseComponent } from "@app/base/components/base.component";
import { UsersService } from "@app/admin/users/service/users-services";
import { ServiceLocator } from "@app/base/components/service-locator";

declare var $;
@Component({
  selector: "app-home-buyer",
  templateUrl: "../view/home-buyer.component.html",
  styleUrls: [
    "../css/home-buyer.component.css",
    "../../users/css/users.component.css"
  ]
})
export class HomeBuyerComponent extends BaseComponent implements OnInit {
  usersService: UsersService;

  public HBuserList = [];
  public totalRecordsCount: number;
  public itemsPerPage: number;
  public pageCount: number = 1;

  public dialogTitile = '';
  public dialogMessage = '';
  public selectedUserType = '';
  public selectedAccountStatus = '';
  public selectedUserEmail = '';
  public selectedUserStatus = '';
  public disableConfirmBtn :  boolean = false;
  public selecteduserIndex;
  public currentMenuId;
  public currentMenuIndex;

  public agentStatusList = ["Represented", "Unrepresented"];
  public selectedAgentStatus = "";

  public searchUserParams = new URLSearchParams;
  public downloadCSVSubscription: any;

  constructor() {
    super();
    this.usersService = ServiceLocator.injector.get(UsersService);
  }
  ngOnInit() {
    this.initHBUserList();
    let self = this;
    $("body").addClass("admin-body");
    $('#deactiveUser').on('hidden.bs.modal', function () {
      self.selectedUserEmail = '';
      self.selectedUserStatus = '';
      self.disableConfirmBtn = false;
      self.selecteduserIndex = undefined;
    });

    $(document).ready(function()
    {
      $(document).mouseup(function(e)
      {
        if(self.currentMenuId != undefined && self.currentMenuIndex != undefined){
          $("#user_"+self.currentMenuIndex+"_"+self.currentMenuId).hide();
          self.currentMenuId = undefined;
          self.currentMenuIndex = undefined;
        }
      });
    });
  }

  public setAgentStatus(agentStatus: string): void {
    this.selectedAgentStatus = agentStatus;
  }

  openMenu(index,id){
    this.currentMenuIndex = index;
    this.currentMenuId = id;
    $("#user_"+index+"_"+id).toggle();
  }

  initHBUserList() {
    this.usersService.getHBUsersList().subscribe(
      res => {
        this.HBuserList = res["result"]["records"];
        this.HBuserList.forEach(user =>{
          user.sign_up_date = this.getAdminDateFormat(user.sign_up_date,'MM/DD/YYYY')
          if(user.last_login_date != ''){
            user.last_login_date = this.getAdminDateFormat(user.last_login_date,'MM/DD/YYYY')
          }else{
            user.last_login_date = "-";
          }
        });
        this.totalRecordsCount = res["result"]["total_records_count"];
        this.itemsPerPage = res["result"]["items_per_page"];
      },
      err => this.errorResponse(err.json())
    );
  }

  public getPageChange(pageNo){
    this.pageCount = pageNo;
    this.searchUserParams.set('page_no',pageNo);
    this.usersService.HBfilterUserSearch(this.searchUserParams).subscribe(res =>{
      this.HBuserList = res['result']['records'];
      this.HBuserList.forEach(user =>{
        user.sign_up_date = this.getAdminDateFormat(user.sign_up_date,'MM/DD/YYYY')
        if(user.last_login_date != ''){
          user.last_login_date = this.getAdminDateFormat(user.last_login_date,'MM/DD/YYYY')
        }else{
          user.last_login_date = "-";
        }
      })
      this.totalRecordsCount = res['result']['total_records_count'];
      this.itemsPerPage = res['result']['items_per_page'];
    },err=>{
      this.errorResponse(err.josn());
    });
  }

  public searchHomeBuyerUser(userName :string,email : string) : void{
    if(userName != ''){
      this.searchUserParams.set('user_name',userName);
    }
    else{
      this.searchUserParams.delete('user_name');
    }
    if(email != ''){
      this.searchUserParams.set('user_email',email.toString());
    }
    else{
      this.searchUserParams.delete('user_email');
    }

    if(this.selectedAgentStatus != ''){
      var agent_status = '';
      if(this.selectedAgentStatus == 'Represented'){
        agent_status = 'represented'
      }
      else if(this.selectedAgentStatus == 'Unrepresented'){
        agent_status = 'unrepresented'
      }
      this.searchUserParams.set('agent_status',agent_status);
    }
    else{
      this.searchUserParams.delete('agent_status');
    }

    this.getPageChange(1);
  }


  public resetUserPassword(userEmail : string) :void{
    var email = {
      "email" : userEmail
    }

    this.usersService.sendforgotPasswordEmail(email).subscribe(res =>{
      this.successResponse(res);
    },err=>{
      this.errorResponse(err.json());
    });
  }

  public impersonateUser(userEmail : string) : void{
    let impersonateParams = new URLSearchParams;
    impersonateParams.set("email",userEmail)
    this.usersService.getImpersonateUserToken(impersonateParams).subscribe(res =>{
      this.setUserToken(res['result']['token']);
      let userType = this.userTypes.filter(item => item.code === res['result']['user_type']);
      localStorage.setItem('userType',userType[0]['userType']);
      window.open(window.location.origin);
    },err=>{
      this.errorResponse(err.json());
    });
  }

  manageUserModal(type,userId,user){
    this.selecteduserIndex = this.HBuserList.indexOf(user);
    this.selectedUserEmail = userId;
    $("#deactiveUser").modal("show");
    if(type == 'active'){
      this.dialogTitile = 'Activate user';
      this.dialogMessage = 'Are you sure you want to Activate this user?';
      this.selectedUserStatus = 'active';
    }
    else if(type == 'deActive'){
      this.dialogTitile = 'Deactivate user';
      this.dialogMessage = 'Are you sure you want to Deactivate this user?';
      this.selectedUserStatus = 'deActive';
    }
  }

  manageUser(){
    if(this.selectedUserEmail != '' && this.selectedUserStatus != ''){
      let manageUserParams = new URLSearchParams;
      if(this.selectedUserStatus == 'deActive'){
        manageUserParams.set('is_active','false');
      }
      else if(this.selectedUserStatus == 'active'){
        manageUserParams.set('is_active','true');
      }
      manageUserParams.set('email',this.selectedUserEmail);
      this.disableConfirmBtn = true;
      this.usersService.manageUserAccount(manageUserParams).subscribe(res =>{
        this.successResponse(res);
        this.disableConfirmBtn = false;
        $("#deactiveUser").modal("hide");
        if(this.selecteduserIndex != undefined){
          if(this.HBuserList[this.selecteduserIndex]['is_active'] == false){
            this.HBuserList[this.selecteduserIndex]['is_active'] = true;
          }
          else if(this.HBuserList[this.selecteduserIndex]['is_active'] == true){
            this.HBuserList[this.selecteduserIndex]['is_active'] = false;
          }
        }
      },err=>{
        this.errorResponse(err.json());
      });
    }
  }

  downloadCSV(userName, email){
    this.searchUserParams.set('user_role','HB');
    if(userName != ''){
      this.searchUserParams.set('user_name',userName);
    }
    else{
      this.searchUserParams.delete('user_name');
    }
    if(email != ''){
      this.searchUserParams.set('user_email',email.toString());
    }
    else{
      this.searchUserParams.delete('user_email');
    }

    if(this.selectedAgentStatus != ''){
      var agent_status = '';
      if(this.selectedAgentStatus == 'Represented'){
        agent_status = 'represented'
      }
      else if(this.selectedAgentStatus == 'Unrepresented'){
        agent_status = 'unrepresented'
      }
      this.searchUserParams.set('home_buyer_agent_status',agent_status);
    }
    else{
      this.searchUserParams.delete('home_buyer_agent_status');
    }

    this.downloadCSVFile();
  }

  downloadCSVFile() {
    if(this.downloadCSVSubscription){
      this.downloadCSVSubscription.unsubscribe();
    }
    this.downloadCSVSubscription = this.usersService.downloadCSV(this.searchUserParams).subscribe(res=>{
      this.downloadFile(res,"Home-Buyers.csv");
    },err => this.errorResponse(err.json()));
  }
}
