import { Component, OnInit,NgZone, ViewChild} from '@angular/core';
import { FavoriteComponent } from '@app/favorite/component/favorite.component';
import { FavoriteService } from '@app/favorite/service/favorite-service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { BaseComponent } from '@app/base/components/base.component';
import { ChatService } from '@app/messaging/service/chat-service';
import { SearchBarComponent } from '@app/searchBar/component/search-bar.component';

declare var $;

@Component({
  selector: 'favorite-list',
  templateUrl: '../views/favorite-list.html',
  styleUrls: ['../css/favorite.component.css']
})
export class FavoriteListComponent extends FavoriteComponent implements OnInit {

  @ViewChild(SearchBarComponent) searchBarComponent: SearchBarComponent;

  favoriteService : FavoriteService
  chatService : ChatService;
  public favoritesPropertyList = [];
  public csFavoritesPropertyList = [];
  public ofmFavoritesPropertyList = [];
  faviouriteTotalCount: any = 0;
  faviouriteItemsPerPage: any = 0;
  faviouriteIndex: any = 2;

  csFaviouriteTotalCount: any = 0;
  csFaviouriteItemsPerPage: any = 0;
  csFaviouriteIndex: any = 2;

  ofmFaviouriteTotalCount: any = 0;
  ofmFaviouriteItemsPerPage: any = 0;
  ofmFaviouriteIndex: any = 2;

  currentId :any;
  currentIndex : number;

  public currentUserId = '';
  public favouritesParams = new URLSearchParams();

  public currentSelectedTab = 'On the market';
  public disableLoadMore : Boolean = false;

  public showOFMLoader : Boolean = false;
  public showCSLoader  : Boolean = false;
  public showONMLoader : Boolean = false;

  //On the market Sorting
  public ONMSortObject : any = {};
  public ONMSortList :any[] = [];

  //Coming soon Sorting
  public CSSortObject : any = {};
  public CSSortList :any[] = [];

  //off market Sorting
  public OFMSortObject : any = {};
  public OFMSortList :any[] = [];

  constructor(zone: NgZone) {
    super(zone);
    this.favoriteService = ServiceLocator.injector.get(FavoriteService);
    this.chatService = ServiceLocator.injector.get(ChatService);
   }

  ngOnInit(){
    this.searchBarComponent.isFavoriteSearch = true;
    this.searchBarComponent.listType = this.searchListType;
    if(this.getPreviousScreen() != '/favorites/favorites-list'){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/favorites/favorites-list');

    if(localStorage.getItem('recentSearches') == null){
      this.initListData();
      this.showOFMLoader = true;
      this.showCSLoader  = true;
      this.showONMLoader = true;
    }else{
      this.searchBarComponent.allowCallGetMapPolygons = false;
      this.searchBarComponent.allowLocalStorageSearch();
      this.showOFMLoader = true;
      this.showCSLoader  = true;
      this.showONMLoader = true;
    }

    let self = this;
    $(document).ready(function()
    {
      $(document).mouseup(function(e)
      {
        if(self.currentId != undefined && self.currentIndex != undefined){
          $("#fav_"+self.currentIndex+"_"+self.currentId).hide();
          self.currentId = undefined;
          self.currentIndex = undefined;
        }
      });
    });
  }

  initListData(){
    this.getFavouritesList("ONM", "1");
    this.getFavouritesList("CS", "1");
    this.getFavouritesList("OFM", "1");
  }

  getFavouritesList(type, pageNumber){
    this.favouritesParams.set("page_no", pageNumber);
    this.favouritesParams.set("listing_status", type);
    this.favouritesParams.set('list_type','2');
    this.favouritesParams.set("is_map_list",'false');
    this.favouritesParams.set("request_type",'WEB');
    this.favouritesParams.set("geo_bounding_box",'{}');

    this.favoriteService.getMyFavorites(this.favouritesParams).subscribe(res => {
      if(BaseComponent.user != undefined){
        this.currentUserId = BaseComponent.user.id;
      }

      if(type == "ONM"){
        this.showONMLoader = false;
        this.favoritesPropertyList = res['result']['property_list'];
        this.faviouriteTotalCount = res['result']['total_proeprty_count'];
        this.faviouriteItemsPerPage = res['result']['items_per_page'];
      }
      else if(type == "CS"){
        this.showCSLoader = false;
        this.csFavoritesPropertyList = res['result']['property_list'];
        this.csFaviouriteTotalCount = res['result']['total_proeprty_count'];
        this.csFaviouriteItemsPerPage = res['result']['items_per_page'];
      }
      else if(type == "OFM"){
        this.showOFMLoader = false;
        this.ofmFavoritesPropertyList = res['result']['property_list'];
        this.ofmFaviouriteTotalCount = res['result']['total_proeprty_count'];
        this.ofmFaviouriteItemsPerPage = res['result']['items_per_page'];
      }
    },err => console.log(err))
  }

  openMenu(index,id){
    if(BaseComponent.user != undefined){
      this.currentUserId = BaseComponent.user.id;
    }
    this.currentIndex = index;
    this.currentId = id;
    $("#fav_"+index+"_"+id).toggle();
  }

  remove(id,item,list_type){
    let index:any;
    if(list_type == 'ONM'){
      index = this.favoritesPropertyList.indexOf(item);
    }else if(list_type == 'CS'){
      index = this.csFavoritesPropertyList.indexOf(item);
    }else if(list_type == 'OFM'){
      index = this.ofmFavoritesPropertyList.indexOf(item);
    }

    let urlParams = new URLSearchParams();
    urlParams.set("is_favourite",'False');
    urlParams.set("property_id",id);
    this.favoriteService.removeFavorite(urlParams).subscribe(res =>{
      this.successResponse(res);

      if(list_type == 'ONM'){
        this.favoritesPropertyList[index]['is_favourite'] = false;
        this.favoritesPropertyList.splice(index,1);

      }else if(list_type == 'CS'){
        index = this.csFavoritesPropertyList.indexOf(item);
        this.csFavoritesPropertyList[index]['is_favourite'] = false;
        this.csFavoritesPropertyList.splice(index,1);

      }else if(list_type == 'OFM'){
        this.ofmFavoritesPropertyList[index]['is_favourite'] = false;
        this.ofmFavoritesPropertyList.splice(index,1);
      }
    },err =>{
      this.errorResponse(err);
    });
  }

  loadMoreFaviouriteList(faviouriteIndex,type){
    let sortList = [];
    this.favouritesParams.set("page_no", faviouriteIndex);
    this.favouritesParams.set('listing_status',type);
    this.disableLoadMore = true;
    this.favouritesParams.delete("sort_list");

    if(type == "ONM"){
      if(this.ONMSortList.length !=0){
        sortList = this.ONMSortList;
        this.favouritesParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(type == "CS"){
      if(this.CSSortList.length !=0){
        sortList = this.CSSortList;
        this.favouritesParams.set('sort_list', JSON.stringify(sortList));
      }
    }else if(type == "OFM"){
      if(this.OFMSortList.length !=0){
        sortList = this.OFMSortList;
        this.favouritesParams.set('sort_list', JSON.stringify(sortList));
      }
    }

    this.favoriteService.getMyFavorites(this.favouritesParams).subscribe(res => {
      this.disableLoadMore = false;
      if(res['result']['property_list'].length != 0){
        if(type == 'ONM'){
          res['result']['property_list'].forEach(record => {
            this.favoritesPropertyList.push(record);
          });
          this.faviouriteIndex += 1;
        }
        if(type == 'CS'){
          res['result']['property_list'].forEach(record => {
            this.csFavoritesPropertyList.push(record);
          });
          this.csFaviouriteIndex += 1;
        }
        if(type == 'OFM'){
          res['result']['property_list'].forEach(record => {
            this.ofmFavoritesPropertyList.push(record);
          });
          this.ofmFaviouriteIndex += 1;
        }
      }
    },err => console.log(err))
  }

  messageListingAgent(selectedClient){
    var client = {};
    client['user_name'] = selectedClient['listing_agent_name'];
    client['profile_image'] = selectedClient['listing_agent_image'];
    client['chat_thread_id'] = selectedClient['user'];
    client['receiver_id'] = selectedClient['user'];
    client['last_message'] = "";
    client['last_message_time'] = "";
    this.chatService.setClientChatThread(client);
    this.router.navigate(['messaging']);
  }

  getSearchObj(searchParams){
    this.zone.run(
      () => {
        this.faviouriteIndex = 2;
        this.csFaviouriteIndex = 2;
        this.ofmFaviouriteIndex = 2;
        this.favouritesParams = searchParams;
        this.getFavouritesList("ONM", "1");
        this.getFavouritesList("CS", "1");
        this.getFavouritesList("OFM", "1");
        this.favouritesParams.delete("sort_list");
        this.removeFAVSorting();
    });
  }

  removeFAVSorting(){
    var sort = 'ONM';
    for(let i=0; i<3; i++){

      if(i == 0){
        sort = 'ONM';
        this.ONMSortList = [];
        this.ONMSortObject = {};
      }
      else if(i == 1){
        sort = 'CS';
        this.CSSortList = [];
        this.CSSortObject = {};
      }
      else if(i == 2){
        sort = 'OFM';
        this.OFMSortList = [];
        this.OFMSortObject = {};
      }

      $('#'+sort+'_PR').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_HP').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_BD').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_BT').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_SF').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      $('#'+sort+'_DT').attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
    }
  }

  setSelectedTab(tabName){
    this.currentSelectedTab = tabName;
  }

  openEventModel(type,event){
    this.eventModal.openEventModal(type,event,false);
  };

  UpdatePropertyInfo(propertyInfo){
    var property:any = '';
    var propertyIndex:any = '';
    var eventIndex:any = '';
    if(this.currentSelectedTab == 'On the market'){
       property = this.favoritesPropertyList.filter((propertyId) => propertyId.id == propertyInfo['property']);
       propertyIndex = this.favoritesPropertyList.indexOf(property[0]);
    }
    if(this.currentSelectedTab == 'Coming soon'){
      property = this.csFavoritesPropertyList.filter((propertyId) => propertyId.id == propertyInfo['property']);
       propertyIndex = this.csFavoritesPropertyList.indexOf(property[0]);
    }
    if(property.length !=0){
      let updatedPropertyParams = new URLSearchParams();
      updatedPropertyParams.set('property_id',propertyInfo['property']);
      updatedPropertyParams.set('list_type',this.searchListType.toString());

      this.favoriteService.getUpdatedPropertyInfo(updatedPropertyParams).subscribe(res =>{
        if(this.currentSelectedTab == 'On the market'){
          this.favoritesPropertyList[propertyIndex]['event_list'] = res['result']['event_list'];
        }
        if(this.currentSelectedTab == 'Coming soon'){
          this.csFavoritesPropertyList[propertyIndex]['event_list'] = res['result']['event_list'];
        }
      },err=>{
        this.errorResponse(err.json());
      });
    }
  }

  favouriteSortting(listingType,filedName){
    let sortList = [];
    if(listingType == 'ONM'){
      this.faviouriteIndex = 2;
      if(this.ONMSortObject[filedName] == undefined){
        this.ONMSortObject[filedName] = true;
        $('#ONM_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
      this.ONMSortObject[filedName] = this.ONMSortObject[filedName] === true ? false : true;
      if(this.ONMSortObject[filedName]){
        $('#ONM_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }else{
        $('#ONM_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      }
      }
      this.ONMSortList[0] = this.ONMSortObject;
      sortList = this.ONMSortList;
    }

    if(listingType == 'CS'){
      this.csFaviouriteIndex = 2;
      if(this.CSSortObject[filedName] == undefined){
        this.CSSortObject[filedName] = true;
        $('#CS_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
      this.CSSortObject[filedName] = this.CSSortObject[filedName] === true ? false : true;
      if(this.CSSortObject[filedName]){
        $('#CS_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }else{
        $('#CS_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      }
      }
      this.CSSortList[0] = this.CSSortObject;
      sortList = this.CSSortList;
    }

    if(listingType == 'OFM'){
      this.ofmFaviouriteIndex = 2;
      if(this.OFMSortObject[filedName] == undefined){
        this.OFMSortObject[filedName] = true;
        $('#OFM_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }
      else{
      this.OFMSortObject[filedName] = this.OFMSortObject[filedName] === true ? false : true;
      if(this.OFMSortObject[filedName]){
        $('#OFM_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line-up.png');
      }else{
        $('#OFM_'+filedName).attr('src',this.imagePrefix+'symbols-glyph-arrow-line.png');
      }
      }
      this.OFMSortList[0] = this.OFMSortObject;
      sortList = this.OFMSortList;
    }

    if(sortList.length != 0){
      this.favouritesParams.set('sort_list', JSON.stringify(sortList));
      this.getFavouritesList(listingType,"1");
    }
  }
}
