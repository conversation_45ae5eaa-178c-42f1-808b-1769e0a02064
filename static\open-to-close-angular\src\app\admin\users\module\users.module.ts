import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { UsersComponent } from "@app/admin/users/component/users.component";
import { BaseModule } from "@app/base/modules/base.module";
import { UsersService } from "@app/admin/users/service/users-services";
import { HomeBuyerComponent } from "@app/admin/home-buyer/component/home-buyer.component";
import { ListingAgentComponent } from "@app/admin/listing-agent/component/listing-agent.component";
import { BrokerUserComponent } from "@app/admin/broker-user/component/broker-user.component";

@NgModule({
  imports: [CommonModule, BaseModule],
  declarations: [UsersComponent, HomeBuyerComponent, ListingAgentComponent, BrokerUserComponent],
  providers: [UsersService]
})
export class UsersModule {}
