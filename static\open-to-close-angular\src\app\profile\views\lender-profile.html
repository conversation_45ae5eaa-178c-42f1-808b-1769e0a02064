<div class="header">
    <header></header>
</div>

<div class="lender-profile">
    <div class="new_profiles">
        <div class="new_profile_title lender-title">
            <img src="{{fileUrl}}" class="new_symbols-avatar dis_inline" alt="">
            <div class="new_title dis_inline file-upload-title">
                {{profileName}}
                <label for="file-upload" class="file-upload-label">
                <span class="custom-file-upload">
                    <span class="file-upload-text">Add Profile Image</span>
                </span>
                </label>
                <input id="file-upload" type="file" (change)="uploadProfileImage($event)"/>
            </div>
        </div>

        <div class="new_profile_group_wrap">
            <div class="new_profile_group dis_inline">
                <ul class="nav nav-pills">
                    <li class="active"><a data-toggle="pill" href="#menu1">Profile</a></li>
                    <li><a data-toggle="pill" href="#menu2">Password</a></li>
                </ul>
                <div class="tab-content">                
                    <div id="menu1" class="tab-pane fade in active new_profile_details">
                        <div class="group_1 mt-20">
                            <div class="title2">Basic Information</div>
                            <div class="new_form">
                                <form [formGroup]="lenderProfileForm">
                                    <div class="new_form_group" formGroupName="profile">
                                        <div class="group new_form_label">      
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="name">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Name*</label>
                                        </div>
                                    </div>
                                    <div class="new_form_group" formGroupName="profile">
                                        <div class="group new_form_label">      
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="lending_company">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Lending Company*</label>
                                        </div>
                                    </div>
                                    <div class="new_form_group ">
                                        <div class="group new_form_label">      
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="email">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Email Address*</label>
                                        </div>
                                    </div>
                                    <div class="new_form_group" formGroupName="profile">
                                        <div class="group new_form_label">      
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="cell_phone">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Cell Phone Number*</label>
                                        </div>
                                    </div>
                                    <div class="new_form_group" formGroupName="profile">
                                        <div class="group new_form_label">      
                                        <input type="text" placeholder=" " required class=" width_350" formControlName="office_phone">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Office Phone Number*</label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="group_1 mt-20">
                            <div class="title2">Contact Preferences</div>
                            <div class="new_form">
                                <div class="check_group profile_checkbox width_350 ">
                                    <div class="form_group flex_none">
                                    <input type="checkbox" (change)="CPStatusChange('msg')" [checked]="msgCP"> <span class="checkmark"></span>
                                    <label class="width_auto">Message Notifications</label>
                                    </div>
                                    <div class="form_group">
                                    <input type="checkbox" (change)="CPStatusChange('w_oh')" [checked]="w_ohCP"> <span class="checkmark"></span>
                                    <label class="width_auto">Weekly Open House Notification</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="new_form_group">
                            <input type="submit" [ngClass]="{'submit-disable':lenderProfileForm.invalid}" [disabled]="lenderProfileForm.invalid" class="submit_button with_bg lender-submit" value="Submit" (click)="updateProfile(lenderProfileForm)" >
                        </div>
                    </div>

                    <div id="menu2" class="tab-pane fade">
                    <div class="group_1 mt-20">
                        <div class="title2">Change your Password</div>
                        <div class="new_form">
                        <form [formGroup]="profilePassword" >
                            <div class="new_form_group ">
                                <div class="group new_form_label">      
                                <input type="password" placeholder=" " required class=" width_350" formControlName="password" >
                                <span class="highlight"></span>
                                <span class="bar"></span>
                                <label>Old Password*</label>
                                </div>

                                <div *ngIf="profilePassword.controls.password.touched">
                                    <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.required">Enter old password</p>
                                    <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.minlength">Password must be 5-15 characters</p>
                                    <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.maxlength">Password must be 5-15 characters</p>
                                </div>
                            </div>
                            <div class="new_form_group ">
                                <div class="group new_form_label">      
                                <input type="password" placeholder=" " required class=" width_350" formControlName="new_password">
                                <span class="highlight"></span>
                                <span class="bar"></span>
                                <label>New Password*</label>
                                </div>
                                <div *ngIf="profilePassword.controls.new_password.touched">
                                    <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.required">Enter new password</p>
                                    <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.minlength">Password must be 5-15 characters</p>
                                    <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.maxlength">Password must be 5-15 characters</p>
                                </div>
                            </div>
                            <div class="new_form_group ">
                                <div class="group new_form_label">      
                                <input type="password" placeholder=" " required class=" width_350" formControlName="confirm_new_password">
                                <span class="highlight"></span>
                                <span class="bar"></span>
                                <label>Confirm New Password*</label>
                                </div>
                                <div *ngIf="profilePassword.controls.confirm_new_password.touched">
                                    <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.required">Enter confirm password</p>
                                    <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.minlength">Password must be 5-15 characters</p>
                                    <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.maxlength">Password must be 5-15 characters</p>
                                    <p class="form-validation" *ngIf="profilePassword.hasError('mismatch')">Confirm password not match</p>
                                </div>
                            </div>
                            <div class="new_form_group ">
                                <input type="submit" [ngClass]="{'submit-disable':profilePassword.invalid}" [disabled]="profilePassword.invalid" class="submit_button with_bg" value="Change" (click)="updatePassword(profilePassword)" >
                            </div>
                            </form>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="footer">
    <footer></footer>
</div>