{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "project": {"name": "open-to-close-angular"}, "apps": [{"root": "src", "outDir": "dist", "assets": ["assets", "favicon.png", "manifest.json", "firebase-messaging-sw.js"], "index": "index.html", "main": "main.ts", "polyfills": "polyfills.ts", "test": "test/test.ts", "tsconfig": "tsconfig.app.json", "testTsconfig": "tsconfig.spec.json", "prefix": "app", "serviceWorker": true, "styles": ["assets/styles.css"], "scripts": [], "environmentSource": "environments/environment.ts", "environments": {"source": "environments/environment.ts", "dev": "environments/environment.ts", "prod": "environments/environment.prod.ts", "staging": "environments/environment.staging.ts"}}], "e2e": {"protractor": {"config": "./protractor.conf.js"}}, "lint": [{"project": "src/tsconfig.app.json", "exclude": "**/node_modules/**"}, {"project": "src/tsconfig.spec.json", "exclude": "**/node_modules/**"}, {"project": "e2e/tsconfig.e2e.json", "exclude": "**/node_modules/**"}], "test": {"karma": {"config": "./karma.conf.js"}}, "defaults": {"styleExt": "css", "component": {}}}