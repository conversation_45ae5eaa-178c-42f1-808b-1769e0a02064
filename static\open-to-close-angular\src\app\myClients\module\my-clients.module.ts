import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MyClientsComponent } from '@app/myClients/components/my-clients.component';
import { CheckInsComponent } from '@app/myClients/components/check-ins.component';
import { MyClientService } from '@app/myClients/services/my-clients.service';
import { BaseModule } from '@app/base/modules/base.module';

@NgModule({
  imports: [
    CommonModule,
    BaseModule
  ],
  declarations: [MyClientsComponent,CheckInsComponent],
  providers: [MyClientService]
})
export class MyClientsModule { }
