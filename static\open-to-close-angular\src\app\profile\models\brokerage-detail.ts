import { Profile } from '@app/profile/models/profile';

export class BrokerageDetail{
    firm_name:String;
    firm_image:String;
    mls_status:String;
    contact_fname:String;
    contact_lname:String;
    email:String;
    password:String;
    availabel_seats:any;
    plan:Plan;
    invite_link:String;
    agents:Agent[];
    profile: Profile;
    billing_info:BillingDetails; 
    payment_method:PaymentsDetails;
    selected_plan_id:String;
    paid_account:Boolean;
    permanent_premium_account:Boolean;
    jwt_token:String; 
    lender_info : BrokerLender;
}
export class BrokerLender{
    lender_id : any;
    cell_phone : any;
    lending_company : string;
    name : string;
    office_phone : any;
    profile_photo : string;
}

export class Plan{
    id:String;
    start_duration:Date;
    end_duration:Date;
    seats:any;
    price:any;
}

export class Agent{
    fname:String;
    lname:String;
    profile_image:String;
}
export class BillingDetails{
    user:BRUser;
    address_1:String;
    address_2:String;
    state: String;
    city: String;
    zipcode: String;    
}
export class PaymentsDetails{
    expiration_month:String;
    last_name: String;
    token:String;
    last_4:String;
    number:String;
    first_name: String;
    expiration_year:String;
}
export class BRUser{
    id:any;
}

export class BrokerageResponse{
    result: BrokerageDetail;
    message: String;
    status: any;
    statusCode: any;
}