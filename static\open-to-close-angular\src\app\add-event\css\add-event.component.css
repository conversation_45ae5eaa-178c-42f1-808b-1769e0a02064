.date-height{
    height: 0px;
}
.oha-label{
    padding-left: 5px;
}
.oha-label-description{
    padding-left: 20px;
    padding-top: 5px;
    font-size: 14px;
}
.locked{
    color: #10b8a8;
    margin-left: 15px;
    text-decoration: underline;
    cursor: pointer;
}
#manageEvent{
    overflow-x: hidden;
    overflow-y: auto;
}

.check_group.mt-10.found_agent_add_agent input[type="checkbox"]{
 top: -32px !important;
}
.agent_found_css_one{
    font-size: 12px;
    color: #8D8D8D;
    float: left;
    width: 100%;
    margin-top: -27px;
    margin-left: 10px;
}
.agent_found_css_one span {
    font-size: 16px;
    color: #8D8D8D;
}
.padding-left-check{
    padding-left: 26px;
}

/* :root {
    --ck-z-default: 100;
    --ck-z-modal: calc( var(--ck-z-default) + 999 );
} */