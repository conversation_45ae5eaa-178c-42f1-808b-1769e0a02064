import { Injectable } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ServiceLocator } from '@app/base/components/service-locator';
import { ApiService } from '@app/base/services/api.service';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiResponse } from '@app/auth/models/api-response';

@Injectable()
export class EventModalService {
  
  public baseservice:BaseComponent;
  public apiService:ApiService;
  
  constructor() {
    this.baseservice=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
  }

   addToMyOpenHouse(event): Observable<any>{
    let options=this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addToMyOpenHouse'],event.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);   
  }

   addPropertyToFavorite(property):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['saveToFavorite'],property.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  addPropertyRating(rating):Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addPropertyRating'],rating.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

  removeListedProperty(openHouseIdParams): Observable<any>{
    let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['property']['removeProperty'], openHouseIdParams.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }
}