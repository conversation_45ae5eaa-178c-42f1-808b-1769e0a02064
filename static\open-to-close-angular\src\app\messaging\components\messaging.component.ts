import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild ,NgZone,EventEmitter,Output} from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ChatService } from '@app/messaging/service/chat-service';
import { ServiceLocator } from '@app/base/components/service-locator';
import * as moment from 'moment';
import { NotificationService } from '@app/notification/service/notification-service';
import { Params } from '@angular/router/src/shared';

declare var $;

@Component({
  selector: 'messaging',
  templateUrl: '../views/messaging.html',
  styleUrls: ['../css/messaging.component.css']
})
export class MessagingComponent extends BaseComponent implements OnInit, OnDestroy {

  public chatService : ChatService;
  public oldChatView : Boolean = true;
  public newChatView : Boolean = false;
  public chatThreadList = [];
  public tempChatThreadList = [];
  public selectedClientId : Number = 0;
  public selectedClient = {};
  public notificationService : NotificationService;
  public receiverSubscription;
  public updateThreadSubscription;
  public getThreadSubscription;
  
  constructor(private zone:NgZone) { 
    super();
    this.chatService = ServiceLocator.injector.get(ChatService);
    this.notificationService = ServiceLocator.injector.get(NotificationService);
  }

  ngOnInit() {

    var windowSize = $(window).width();
    if (windowSize <= 1024) {
      $(document).on( "click", "img.white_leftarrow_image.cursor-pointer_2", function() {
        $('.chat_message .left_side').show();
        $('.chat_message .left_side + .message_right.message_new_width').hide();
      });
      
      $(document).on( "click", ".message_list", function() {
          $('.chat_message .left_side').hide();
          $('.chat_message .left_side + .message_right.message_new_width').show();
      });

      $(document).on( "click", ".chat_message .left_side button.btn.add_new_list.dis_inline.new-left-msg", function() {
        $('.chat_message .left_side').hide();
      });

      $(document).on( "click", ".new-client-list", function() {
        $('.message_right_side.send_message_to_group.full-right').hide();  
        $('.chat_message .left_side + .message_right.message_new_width').show(); 
      });
    }
    
    this.updateThreadSubscription = this.chatService.updateThread.subscribe(res =>{
      let chatThread = this.chatThreadList.filter((chatThread) => chatThread.receiver_id == res['sender_id']);
      if(chatThread.length !=0){
        let chatThreadIndex = this.chatThreadList.indexOf(chatThread[0]);
        this.chatThreadList[chatThreadIndex]['last_message'] = res['message'];
        this.chatThreadList[chatThreadIndex]['is_read'] = res['is_read'];
        this.chatThreadList[chatThreadIndex]['last_message_time'] = res['date_time'];
        chatThread = this.chatThreadList[chatThreadIndex];
        this.chatThreadList.splice(chatThreadIndex,1);
        this.chatThreadList.unshift(chatThread);
        
        if(res['is_read'] == true){
          this.notificationService.setMessageDotIcon.emit('getStatusFormAPI');
        }
        
        $(document).ready(function() {
          $("#scroll").scrollTop(0);
        });
      }
      else{
       this.getThreadSubscription = this.chatService.getChatThread().subscribe(userList =>{
          let threadArray = userList['result'];
          let newUser = threadArray.filter((chatThread) => chatThread.receiver_id == res['sender_id']);
          if(newUser.length !=0){
            let isAlreadyExists = this.chatThreadList.filter((chatThread) => chatThread.receiver_id == res['sender_id']);
            if(isAlreadyExists.length == 0){
              this.chatThreadList.unshift(newUser[0]);
              this.oldChat(newUser[0]);  
            }
          }
        },err=>{
          console.log(err.json());
        });
      }
    },err =>{
      this.errorResponse(err.json())
    });

    this.getThreadSubscription = this.chatService.getChatThread().subscribe(res =>{
      let reverseArray = res['result'];
      this.tempChatThreadList = res['result'];
      this.chatThreadList = reverseArray.reverse();
      if(this.chatThreadList.length != 0){
        this.setDefaultClient(this.chatThreadList[0]);
        this.receiverSubscription = this.route.queryParams.subscribe((params:Params)=>{   
          this.filterchatThreadList(params);
        });
      }
      let chatThreadResponse = this.chatService.getClientChatThread();
      if(chatThreadResponse != undefined){
        this.setChatThread(chatThreadResponse)
      }
    },err => this.errorResponse(err.json()));

    this.chatService.startNewChat.subscribe(res =>{
      this.setChatThread(res);
    },err=>{
      this.errorResponse(err.json());
    });
  }

  setChatThread(selctedChatThread){
    let chatThreadId = this.chatThreadList.filter((chatThread) => chatThread.receiver_id == selctedChatThread['chat_thread_id']);
    if(chatThreadId.length !=0){
      let chatThreadIndex = this.chatThreadList.indexOf(chatThreadId[0]);
      this.chatThreadList.splice(chatThreadIndex,1);
      this.chatThreadList.unshift(chatThreadId[0]);
      this.oldChat(chatThreadId[0]);  
    }
    else{
      this.chatThreadList.unshift(selctedChatThread);
      this.selectedClientId = selctedChatThread['chat_thread_id'];
      this.selectedClient = selctedChatThread;
      this.newChatView = false;
      this.oldChatView = true;
    }
  }
  
  newMessage(){
    this.oldChatView = false;
    this.newChatView = true;
  }

  oldChat(client){
    this.newChatView = false;
    this.oldChatView = true;
    this.selectedClient = client;
    let index = this.chatThreadList.indexOf(client);
    if(this.chatThreadList[index]['is_read'] != undefined){
      this.chatThreadList[index]['is_read'] = true;
    }    
    this.selectedClientId = client.chat_thread_id;
  }
  setDefaultClient(client){
    this.selectedClient = client;
    let index = this.chatThreadList.indexOf(client);
    this.chatThreadList[index]['is_read'] = true;
    this.selectedClientId = client.chat_thread_id;
  }
  
  getLastTime(dateTime){
    return  moment.utc(dateTime).local().format('hh:mm a');
  }

  filterchatThreadList(params){
    if(params['receiver'] != undefined){
      let chatThread = this.chatThreadList.filter(
        (chat_thread) => chat_thread.receiver_id == params['receiver']
      );
      if(chatThread.length != 0){
        if(chatThread[0]['receiver_id'] != this.chatThreadList[0]['receiver_id']){
          let chatThreadIndex = this.chatThreadList.indexOf(chatThread[0]);
          this.chatThreadList.splice(chatThreadIndex,1);
          this.chatThreadList.unshift(chatThread[0]);
          this.oldChat(chatThread[0]);  
          this.routeOnUrl('messaging');    
          if(this.receiverSubscription != undefined){
            this.receiverSubscription.unsubscribe();
          }
        }
      }
    }
  }

  filterUser(userName){
    if(userName.trim().length !=0){
      this.chatThreadList = this.tempChatThreadList.filter(name => {
        return name['user_name'].toLowerCase().includes(userName.toLowerCase());
      });
    }else{
      this.chatThreadList = this.tempChatThreadList;
    }
  }

  ngOnDestroy(){
    this.chatService.setClientChatThread(undefined);
    if(this.receiverSubscription != undefined){
      this.receiverSubscription.unsubscribe();
    }
    if(this.updateThreadSubscription != undefined){
      this.updateThreadSubscription.unsubscribe();
    }
    if(this.getThreadSubscription != undefined){
      this.getThreadSubscription.unsubscribe();
    }
  }
}
