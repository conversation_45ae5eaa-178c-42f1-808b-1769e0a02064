import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON>Change,OnChanges ,Input, NgZone,ApplicationRef, ChangeDetectorRef,<PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';
import { MessagingComponent } from '@app/messaging/components/messaging.component';
import { BaseComponent } from '@app/base/components/base.component';
import { ChatService } from '@app/messaging/service/chat-service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { SimpleChanges} from '@angular/core/src/metadata/lifecycle_hooks';
import { ChatThread } from '@app/messaging/model/chat-thread.model';
import * as moment from 'moment';
import { NotificationService } from '@app/notification/service/notification-service';

declare var $;

@Component({
  selector: 'chat',
  templateUrl: '../views/chat.html',
  styleUrls: ['../css/messaging.component.css']
})

export class ChatComponent extends BaseComponent implements OnInit,OnChanges,<PERSON><PERSON><PERSON><PERSON> {

  @Input() selectedClient : ChatThread =  new ChatThread;
  
  public sharePropertyMessage;
  public messageList = [];
  public chatList = [];
  public senderId = '';
  public firebaseSubscription;
  public getClientChatSubscription;
  public getOldChatSubscription;
  public getRecentChatSubscription;

  public chatService:ChatService;
  public notificationService : NotificationService
  
  constructor(public zone:NgZone) { 
    super();
    this.chatService = ServiceLocator.injector.get(ChatService);
    this.notificationService = ServiceLocator.injector.get(NotificationService);
  }

  ngOnInit() {
    if(this.getPreviousScreen() != '/messaging'){
      this.clearLocalStorageSearch();
    }
    this.setPreviousScreen('/messaging');
    
    let self = this;
    $(document).ready(function() {
      $('.chat_session').scroll(function() {
        var getScroll = $('.chat_session').scrollTop();
        if(getScroll == 0)
        {
          if(self.chatList.length != 0){
            var lastTimestamp = self.chatList[0]['date_time'];
            var receiverId = self.chatList[0]['receiver_id'];
            var chatId = self.chatList[0]['chat_id']
           self.getOldChatSubscription = self.chatService.getAllChat(lastTimestamp,receiverId).subscribe(res =>{
              self.zone.run(
                () => {
                  res['result'].forEach(record => {
                    self.chatList.unshift(record);
                  });                
                })
              var position = ($("#"+chatId).offset())
              if(position !=undefined || position != null){
                $(".chat_session").scrollTop(position['top']-195);
              }
            },err=>console.log(err));
          }
        }
      });
    });

    this.firebaseSubscription = this.chatService.onFirebase.subscribe(res =>{
      let chatParam = new URLSearchParams();
      chatParam.set('last_timestamp',res['data']['last_timestamp']);
      chatParam.set('sender_id',res['data']['sender_id']);
      var  is_active_thread = false;

      if(res['data']['sender_id'] == this.selectedClient['receiver_id']){
        is_active_thread = true;
      }
      this.getRecentChatSubscription = this.chatService.getRecentChat(res['data']['last_timestamp'], res['data']['sender_id'],is_active_thread).subscribe(msgRes => {
        msgRes['result'].forEach(record => {
          if(record['sender_id'] == this.selectedClient['receiver_id']){
            this.zone.run(()=>{
              this.chatList.push(record);
              $(document).ready(function() {
                $(".chat_session").scrollTop($(".chat_session")[0].scrollHeight);
              });
            });
            record['is_read'] = true;
            this.chatService.updateThread.emit(record);
          }
          else{
            this.chatService.updateThread.emit(msgRes['result'][0]);
          }
        });
      },err => {
        console.log(err);
      });
    },err=>{
      console.log(err)
    });
  }

  ngOnChanges(changes :SimpleChanges){
    const clientObj: SimpleChange = changes.selectedClient;
    if(clientObj.previousValue != clientObj.currentValue){
      if(Object.keys(this.selectedClient).length != 0){
        this.initMsg();
        this.chatList = [];
      }
    }
  }

  initMsg(){
    // this.sharePropertyMessage = this.chatService.getChat();
    let clientParams = new URLSearchParams();
    clientParams.set('receiver_id',this.selectedClient['receiver_id']);
    this.getClientChatSubscription = this.chatService.getClientChat(clientParams).subscribe(res =>{
      //Update header message
      this.notificationService.setMessageDotIcon.emit('getStatusFormAPI');

      let array = res['result'];
      this.chatList = array.reverse();
      this.senderId = BaseComponent.user.id;
        if(this.chatList[this.chatList.length -1] != undefined){
          // this.setMessageAsReaded(this.chatList[this.chatList.length -1]);
        }      
      $(document).ready(function() {
        $(".chat_session").scrollTop($(".chat_session")[0].scrollHeight);
      });
    },err => console.log(err));
  }

  SentMessage(msg){
    if(msg.trim().length !=0){
      var isLink = false;
      var messageType = msg.split('?');
      if(messageType.length > 0){
        if(messageType[1] != undefined){
          var property = messageType[1].split('=');
          if(property[0] == 'propertyId' && property[1] != undefined){
            isLink = true;
          }
        }
      }
      let msgParams = new URLSearchParams();
      msgParams.set('message',msg);
      msgParams.set('receiver_id',this.selectedClient['receiver_id']);
      msgParams.set('is_link',isLink.toString());
      var self = this;
      var msgObj={
      "sender_id": BaseComponent.user.id,
      "receiver_id": this.selectedClient['receiver_id'],
      "message": msg,
      "date_time": new Date(),
      "is_link": isLink
      }
      this.chatList.push(msgObj);
      let indexOfLastMsg = this.chatList.indexOf(msgObj);
      this.chatService.sendMessage(msgParams).subscribe(res =>{
        if(res['result']['is_link'] == true){
          this.chatList[indexOfLastMsg]['property_detail'] = res['result']['property_detail'];
        }
        res['result']['sender_id'] = res['result']['receiver_id'];
        this.chatService.updateThread.emit(res['result']);
      },err=>{
        this.chatList.splice(indexOfLastMsg,1);
        this.errorResponse(err.json())
      });

      $(document).ready(function() {
        $("#msgtxtbox").val('');
        $(".chat_session").scrollTop($('.chat_session')[0].scrollHeight)
      });
    }
  }
  
  setMessageAsReaded(lastChatId){
    let readParms = new URLSearchParams();
    readParms.set('chat_id',lastChatId['chat_id']);
    this.chatService.markMessageReaded(readParms).subscribe(res => {
      // console.log(res);
    },err => {
      // console.log(err);
    })
  }
  
  getMessageTime(dateTime){
    return moment.utc(dateTime).local().format('MM/DD/YYYY hh:mm a');
  }

  ngOnDestroy(){
    if(this.firebaseSubscription != undefined){
      this.firebaseSubscription.unsubscribe();
    }
    if(this.getClientChatSubscription != undefined){
      this.getClientChatSubscription.unsubscribe();
    }
    if(this.getOldChatSubscription != undefined){
      this.getOldChatSubscription.unsubscribe();
    }
    if(this.getRecentChatSubscription != undefined){
      this.getRecentChatSubscription.unsubscribe();
    }
  }
}
