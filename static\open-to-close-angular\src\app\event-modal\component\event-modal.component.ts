import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { EventModalService } from '@app/event-modal/service/event-service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { EventModal } from '@app/event-modal/models/event-modal';
import { ChatService } from '@app/messaging/service/chat-service';
import * as moment from 'moment';

declare var $;

@Component({
  selector: 'event-modal',
  templateUrl: '../views/event-modal.component.html',
  styleUrls: ['../css/event-modal.component.css']
})
export class EventModalComponent extends BaseComponent implements OnInit{

  public eventDetail;
  public event:EventModal = new EventModal();
  public eventModal:EventModal = new EventModal();
  eventModalService : EventModalService;
  public isInterestedOH :Boolean = false;
  public isInterestedBO :Boolean = false;
  public isGoingBO :Boolean = false;
  public isGoingOH :Boolean = false;
  public isGoingAO : Boolean = false;
  public onContactAgentClickOH : Boolean = false;
  public onContactAgentClickAP : Boolean = false;
  public onContactAgentClickBO : Boolean = false;
  public showConatctAgentBtn :  Boolean = false;
  public isValidForMsg :Boolean = false;
  public selectedThumb : Number = -1;
  public ratingParams = new URLSearchParams();
  public propertyId;
  public eventId;
  public ratingDescription: String = '';
  public openHouseAgentMessage = '';
  public disableSendBtn : Boolean = true;
  public hideMsgBox : Boolean = false;

  public showRating : Boolean = false;

  public disabledEventModal: boolean = false;
  public currentUserType : any;
  public isFreeUser : Boolean = false;

  public disableAddToMyList: Boolean = false;

  @Output() setPropertyLatestInfo = new EventEmitter<any>();

  chatService : ChatService;

  constructor() {
    super();
    this.eventModalService = ServiceLocator.injector.get(EventModalService);
    this.chatService = ServiceLocator.injector.get(ChatService);
  }

  ngOnInit() {
    let self = this;
    $('#eventModal').on('hidden.bs.modal', function () {
      this.eventDetail = '';
      self.isInterestedBO = false;
      self.isInterestedOH = false;
      self.isGoingBO = false;
      self.isGoingOH = false;
      self.isGoingAO = false;
      self.isValidForMsg = false;
      self.hideMsgBox = false;
      self.openHouseAgentMessage = '';
      self.onContactAgentClickBO = false;
      self.onContactAgentClickAP = false;
      self.onContactAgentClickOH = false;
      self.disableAddToMyList = false;
    });

    $('#myModalrate').on('hidden.bs.modal', function () {
      self.selectedThumb = -1;
      self.ratingDescription = '';
      self.eventModal = new EventModal();
      self.event = new EventModal();
      $(this)
      .find("input[type=checkbox], input[type=radio]")
      .prop("checked", "")
      .end();
    });
  }

  openEventModal(type,event,showRatingOption){
    this.isFreeUser = event['open_house_agent_account_status']

    if(event.event_type_msg == "72 Hour Home Sale" || event.event_type_msg == "RSVP Only") {
      event.event_type_msg = "72 Hour Home Sale"
    }

    if(BaseComponent.user != undefined && Object.keys(BaseComponent.user).length !=0){
      this.currentUserType = BaseComponent.user.user_type;
      if(BaseComponent.user.user_type != 'HB'){
        if(BaseComponent.user.is_paid_account == false){
          this.isFreeUser = false;
        }
      }
    }

    this.showRating = showRatingOption;
    this.eventDetail = type;
    this.event = event;
    this.propertyId = event['property_id'];
    this.eventId = event['id'];

    this.isValidForMessage(event);

    if(type == 'openHouse'){
      this.isGoingOH = event['is_going'];
    }
    else if(type == 'brokerOpen'){
      this.isGoingBO = event['is_going'];
    }
    else if(type == 'appointmentOnly'){
      this.isGoingAO = event['is_going'];
    }

    if(this.eventDetail != undefined){
      this.disabledEventModal = false;
      $(".theme_img_modal img").hover(function(e) {
        $(this).css("background",e.type === "mouseenter"?"#10B8A8":"white")
      });

      $('#eventModal').modal('show');
    }
  }

  openRatingModal(event,Isupdateable){
    this.eventModal.property_rate.bathroom_rating = event.property_rate.bathroom_rating;
    this.eventModal.property_rate.bedroom_rating = event.property_rate.bedroom_rating;
    this.eventModal.property_rate.finishes_rating = event.property_rate.finishes_rating;
    this.eventModal.property_rate.floorplan_rating = event.property_rate.floorplan_rating;
    this.eventModal.property_rate.kitchen_rating = event.property_rate.kitchen_rating;
    this.eventModal.property_rate.landscaping_rating = event.property_rate.landscaping_rating;
    this.eventModal.property_rate.neighbourhood_rating = event.property_rate.neighbourhood_rating;
    this.eventModal.property_rate.notes = event.property_rate.notes;
    this.eventModal.property_rate.price_rating = event.property_rate.price_rating;
    this.eventModal.property_rate.rating = event.property_rate.rating;
    this.eventModal.event_type_msg = event.event_type_msg;
    this.eventModal.property_file = event.property_file;
    this.propertyId = event.property_id;
    this.eventId = event.id;

    if(Isupdateable == false){
      if(this.eventModal != undefined){
        this.disabledEventModal = true;
      }
    }
    this.selectedThumb = this.eventModal.property_rate.rating;
    this.setThumb(this.eventModal.property_rate.rating)
    this.ratingDescription = this.eventModal.property_rate.notes;
    $('#myModalrate').modal('show');
  }

  going(eventId){
    let urlParams = new URLSearchParams();
    urlParams.set('event',eventId);
    urlParams.set('open_house_status','GO');
    if(this.disableAddToMyList == false){
      this.disableAddToMyList = true
      this.eventModalService.addToMyOpenHouse(urlParams).subscribe(res =>{
        this.disableAddToMyList = false;
        this.successResponse(res);
        this.event['is_checkin'] = res['result']['is_checkin'];
        this.event['open_house_id'] = res['result']['open_house_id'];
        this.event['is_going'] = true;
        this.isValidForMsg = true;
        this.isValidForMessage(this.event);
        if(this.eventDetail == 'openHouse'){
          this.isGoingOH = true;
        }
        else if(this.eventDetail == 'brokerOpen'){
          this.isGoingBO = true;
        }else if(this.eventDetail == 'appointmentOnly'){
          this.isGoingAO = true;
        }
        var propertyObj = {
          property : this.event['property_id'],
          openHouseAgent : this.event['open_house_request_id'],
          eventId : this.eventId
        }
        this.setPropertyLatestInfo.emit(propertyObj);
      },err => {
        this.disableAddToMyList = false;
        this.errorResponse(err.json());
      });
    }
  }

  removePropertyFromMyList(open_house_id,eventDetails){
    let urlParams = new URLSearchParams();
    urlParams.set('open_house_id',open_house_id);
    this.eventModalService.removeListedProperty(urlParams).subscribe(res => {
      this.successResponse(res);
      $('#eventModal').modal('hide');
      var propertyObj = {
        property : eventDetails['property_id'],
        openHouseAgent : eventDetails['open_house_request_id'],
        eventId : eventDetails['id'],
        deleteFromList : true
      }
      this.setPropertyLatestInfo.emit(propertyObj);
    },
    err => {
      this.errorResponse(err.json());
    });
  }

  propertyRating(key,value){
    this.eventModal.property_rate[key] = value.target.checked;
  }

  setThumb(type){
    if(this.disabledEventModal == false){
      this.selectedThumb = type;
      this.eventModal.property_rate.rating = type;
      this.ratingParams.set('rating',type);
    }
  }

  addRating(status){
    if(status == 'ADD'){
      for(let key of Object.keys(this.eventModal.property_rate)){
        this.ratingParams.set(key, this.eventModal.property_rate[key]);
      }
      this.ratingParams.set('property_id',this.propertyId.toString());
      this.ratingParams.set('event_id',this.eventId.toString());
      this.ratingParams.set('notes',this.ratingDescription.toString());
      this.eventModal.property_rate.notes = this.ratingDescription.toString();
      let updatedRating = this.eventModal.property_rate;
      this.eventModalService.addPropertyRating(this.ratingParams).subscribe(res=>{
        this.successResponse(res);
        var propertyObj = {
          property : this.propertyId,
          openHouseAgent : this.event['open_house_request_id'],
          eventId : this.eventId,
          rating : updatedRating,
          deleteFromList : false
        }
        this.setPropertyLatestInfo.emit(propertyObj);
        $('#myModalrate').modal('hide');
      },err=> {
        this.errorResponse(err.json());
      });
    }
    else{
      $('#myModalrate').modal('hide');
    }
  }

  isValidForMessage(event){
    if(event['is_going'] == true){
      this.isValidForMsg = true;
      if(BaseComponent.user.id != event['agent_id']){
        this.hideMsgBox = true;
      }
    }
    if(BaseComponent.user.id != this.event['agent_id']){
      this.showConatctAgentBtn = true;
    }
    else{
      this.showConatctAgentBtn = false;
    }
  }

  sendMessage(msg,type){
    if(type == 'validation'){
      if(this.openHouseAgentMessage.trim().length == 0){
        this.disableSendBtn = true;
      }
      else{
        this.disableSendBtn = false;
      }
    }
    if(type == 'send' && this.openHouseAgentMessage.trim().length != 0){
      let msgParams = new URLSearchParams();
      msgParams.set('message',this.openHouseAgentMessage);
      msgParams.set('receiver_id',this.event['agent_id']);
      msgParams.set('event_id',this.eventId);
      msgParams.set('is_with_thumbnail','true');
      msgParams.set('is_link','false');

      this.chatService.sendMessage(msgParams).subscribe(res => {
          this.successResponse(res);
          this.openHouseAgentMessage = '';
          this.disableSendBtn = true;
          $('#eventModal').modal('hide');
          if(this.eventDetail == 'appointmentOnly'){
            this.router.navigate(['messaging']);
          }
        },err=>{
          this.errorResponse(err.json());
      });
    }
  }

  setMyAppointment(selectedTime){
    let urlParams = new URLSearchParams();
    urlParams.set('event',this.eventId);
    urlParams.set('open_house_status','GO');
    urlParams.set('start_time',selectedTime);
    this.eventModalService.addToMyOpenHouse(urlParams).subscribe(res =>{
      this.successResponse(res);
      this.event.appointment_time = selectedTime;
      this.isValidForMessage(this.event);
      var propertyObj = {
        property : this.event['property_id'],
        openHouseAgent : this.event['open_house_request_id'],
        eventId : this.eventId
      }
      this.setPropertyLatestInfo.emit(propertyObj);
    },err => {
      this.errorResponse(err.json());
    });
  }

  openMessageBox(){
    this.isValidForMsg = true;
    if(BaseComponent.user.id != this.event['agent_id']){
      this.hideMsgBox = true;
    }
    if(this.eventDetail == 'openHouse'){
      this.onContactAgentClickOH = true
    }else if(this.eventDetail == 'brokerOpen'){
      this.onContactAgentClickBO = true
    }else if(this.eventDetail == 'appointmentOnly'){
      this.onContactAgentClickAP = true
    }
  }

  linkify(text) {
    var urlRegex =/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
    var emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/gi;
    var phoneRegex = /(?:[-+() ]*\d){10,13}/g;

    //urlRegex = /[-a-zA-Z0-9@:%_\+.~#?&//=]{2,256}\.[a-z]{2,4}\b(\/[-a-zA-Z0-9@:%_\+.~#?&//=]*)?/gi; 

    text = text.replace(urlRegex, function(url) {
      return '<a href="' + url + '" target="_blank">' + url + '</a>';
    });

    text = text.replace(emailRegex, function(url) {
      return '<a href="mailto:' + url + '">' + url + '</a>';
    });

    return text.replace(phoneRegex, function(url) {
        return '<a href="tel:' + url + '">' + url + '</a>';
    });
  }

}
