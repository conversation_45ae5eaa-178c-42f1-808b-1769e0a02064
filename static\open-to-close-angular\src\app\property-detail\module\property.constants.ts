export var appliances = [{'BOR':'Barbeque or Grill'},{'RNG':'Range'},{'COF':'Coffee System'},{'RGB':'Range - Built In'},{'CTP':'Cooktop'},{'RGD':'Range - Dual'},{'CEL':'Cooktop - Electric'},{'RGE':'Range - Electric'},{'CGS':'Cooktop - Gas'},{'RGG':'Range - Gas'},{'CID':'Cooktop - Induction'},{'RGI':'Range - Induction'},{'DWR':'Dishwasher'},{'RGO':'Range - Other'},{'DYR':'Dryer'},{'RPE':'Rangetop - Electric'},{'DDF':'Dryer - Dual Fuel'},{'RPG':'Rangetop - Gas'},{'DEL':'Dryer - Electric'},{'RPI':'Rangetop - Induction'},
  {'DAS':'Dryer - Gas'},{'REF':'Refrigerator'},{'FZR':'Freezer'},{'REB':'Refrigerator - Bar'},{'GDP':'Garbage Disposer'},{'RBI':'Refrigerator - Built-in'},{'ICM':'Ice Maker'},{'RED':'Refrigerator - Drawer'},{'MCW':'Microwave'},{'RSS':'Refrigerator - Side by Side'}, {'OVN':'Oven'},{'RFU':'Refrigerator - Undercounter'},{'OCN':'Oven - Convection'},{'RFW':'Refrigerator - Wine Storage'},{'OVD':'Oven - Double'},{'THC':'Trash Compactor'}, {'OGS':'Oven - Gas'},{'VMS':'Vacuum System'},{'OVC':'Oven - Self Cleaning'},
  {'VTH':'Vent Hood'},{'OVS':'Oven - Steam'},{'NON':'None'}, {'OVT':'Oven - Twin'},{'OTR':'Other'}];

export var basement=[{'FNS':'Finished'},{'NNE':'None'},{'PFN':'Partially finished'},{'UFN':'Unfinished'}];

export var floor_Covering=[{'BMB':'Bamboo'},{'PQW':'Parquet Wood'},{'BCK':'Brick'},{'RHI':'Rough-in'},{'CPT':'Carpet'},{'STE':'Slate'},{'CFL':'Carpet - Full'},{'SWD':'Soft Wood'},{'CPL':'Carpet - Partial'},{'SWD':'Solid Wood'},{'CRT':'Concrete'},{'SPY':'Specialty'},{'CTB':'Concrete - Bare'},{'SCT':'Specialty Concrete'},{'CPI':'Concrete - Painted'},{'TLE':'Tile'},{'CRK':'Cork'},{'TCM':'Tile - Ceramic'},{'DGE':'Drainage'},{'TPN':'Tile - Porcelain'},{'EWD':'Engineered Wood'},{'TSE':'Tile - Stone'},
    {'GSS':'Glass'},{'TOS':'Tile or Stone'},{'GTE':'Granite'},{'VYL':'Vinyl'},{'HWD':'Hardwood'},{'WOD':'Wood'},{'LTE':'Laminate'},{'UKN':'Unknown'},{'LUM':'Linoleum'},{'NON':'None'},{'LRN':'Load Restriction'},{'OTR':'Other'},{'MBL':'Marble'}];

export var rooms=[{'RAM':'Atrium'},{'RLB':'Library'},{'RAF':'Attic - Finished'},{'RLR':'Living Room'},{'RAU':'Attic - Unfinished'},{'RLT':'Loft'},{'RBF':'Basement - Finished'},{'RMB':'Master Bathroom'},{'RBU':'Basement - Unfinished'},{'RMR':'Media Room'},{'RBM':'Bedroom'},{'RMD':'Mudroom'},{'RBM':'Bonus Room'},{'RMR':'Music Room'},{'RBN':'Breakfast Nook'},{'ROF':'Office'},{'RBF':'Breakfast Room'},{'RQB':'One-Quarter Bath'},{'RCR':'Crafts Room'},
    {'RPO':'Patio'},{'RDN':'Den'},{'RPL':'Photo Lab'},{'RDR':'Dining Room'},{'RRR':'Recreational Room'},{'REK':'Eat-In Kitchen'},{'RSA':'Sauna'},{'REY':'Efficiency'},{'RSQ':'Servant Quarters'},{'REP':'Enclosed Patio'},{'RSR':'Sitting Room'},{'RFR':'Family Room'},{'RSM':'Solarium'},{'RFL':'Florida Room'},{'RSE':'Storage'},{'RFD':'Formal Dining Room'},{'RSO':'Studio'},{'RFO':'Foyer'},{'RMS':'Study'},{'RFB':'Full Bath'},{'RSN':'Sunroom'},{'RGR':'Game Room'},
    {'RTR':'Theatre'},{'RGM':'Great Room'},{'RTU':'Three-Quarter Bath'},{'RGH':'Guest House'},{'RUT':'Utility'},{'RHB':'Half Bath'},{'RWC':'Walk-In Closet'},{'RLS':'In-Law Suite'},{'RWP':'Walk-In Pantry'},{'RKN':'Kitchen'},{'RWK':'Wok Kitchen'},{'RMI':'Kitchenette'},{'RWS':'Workshop'},{'RLC':'Laundry Closet'},{'RUK':'Unknown'},{'RLR':'Laundry Room'},{'ROT':'Other'}];

export var indoor_Features=[{'DPW':'Double Pane Windows'},{'WTB':'Wet bar'},{'ETR':'Elevator'},{'ICM':'Intercom'},{'JBT':'Jetted Bathtub'},{'CBR':'Cable Ready'},{'SNA':'Sauna'},{'WED':'Wired'},{'SLT':'Skylight'},{'SSM':'Security system'},{'VCG':'Vaulted Ceiling'},{'SLS':'Skylights'}];

export var building_Amenities=[{'OAC':'Over 55+ active community'},{'ELR':'Elevator'},{'ALC':'Assisted living community'},{'FNC':'Fitness center'},{'BBC':'Basketball court'},
      {'GEY':'Gated entry'},{'SCT':'Sports court'},{'SRG':'Storage'},{'CAS':'Controlled access'},{'NTP':'Near transportation'},{'DRM':'Doorman'},{'TNC':'Tennis court'}];

export var architectural_Style=[{'AFM':'A Frame'},{'MDN':'Modern'},{'ADO':'Art Deco'},{'MTY':'Monterey'},{'BLW':'Bungalow'},{'MTI':'Mountain'},{'COD':'Cape Cod'},{'NOL':'National'},{'CAL':'Colonial'},{'NCL':'Neoclassical'},{'CRY':'Contemporary'},{'NTL':'New Traditional'},{'CTL':'Conventional'},{'OTH':'Other'},{'CTE':'Cottage'},{'PIE':'Prairie'},{'CTN':'Craftsman'},{'PLO':'Pueblo'},{'CLE':'Creole'},{'QNE':'Queen Anne'},{'DME':'Dome'},{'RLR':'Rambler'},{'DCL':'Dutch Colonial'},{'RCH':'Ranch'},
        {'ENG':'English'},{'RGY':'Regency'},{'FDL':'Federal'},{'RTC':'Rustic'},{'FNC':'French'},{'SBX':'Saltbox'},{'FPL':'French Provincial'},{'STF':'Sante Fe'},{'GON':'Georgian'},{'SDE':'Second Empire'},{'GRL':'Gothic Revival'},{'SED':'Shed'},{'GRL':'Greek Revival'},{'SLE':'Shingle'},{'HRE':'High Rise'},{'SGN':'Shotgun'},{'HTC':'Historical'},{'SPH':'Spanish'},{'ITL':'International'},{'SNE':'Spanish Eclectic'},{'ITE':'Italianate'},{'STL':'Split Level'},{'LFT':'Loft'},{'SCK':'Stick'},{'MON':'Mansion'},{'TDR':'Tudor'},{'MTR':'Mediterranean'},{'VTN':'Victorian'}];

export var exterior_Type=[{'ADB':'Adobe'},{'LSG':'Log Siding'},{'AMS':'Aluminum Siding'},{'MTE':'Masonite'},{'ATS':'Asbestos'},{'MRY':'Masonry'},{'ALT':'Asphalt'},{'MTL':'Metal'},{'BLK':'Block'},{'MLS':'Metal Siding'},{'BAB':'Board and Batten'},{'NON':'None'},{'BCK':'Brick'},{'OTH':'Other'},{'BAW':'Brick and Wood'},{'PCE':'Poured Concrete'},{'BKV':'Brick Veneer'},{'SLS':'Shingles (Not Wood)'},{'CSG':'Cedar Siding'},{'SNE':'Stone'},{'CMB':'Comb'},{'SNV':'Stone Veneer'},{'CON':'Composition'},{'SCO':'Stucco'},{'CSS':'Composition Shingles'},{'SOS':'Stucco - Synthetic'}
  ,{'CNT':'Concrete'},{'TLE':'Tile'},{'CTB':'Concrete Block'},{'TTU':'Tilt-up (Pre-Cast Concrete)'},{'EIF':'EIFS'},{'UNK':'Unknown'},{'FRG':'Fiberglass'},{'VLS':'Vinyl Siding'},{'GLS':'Glass'},{'WOD':'Wood'},{'HBD':'Hardboard'},{'WSE':'Wood Shingle'},{'LOG':'Log'},{'WDS':'Wood Siding'}];

export var outdoor_Amenities=[{'PRH':'Porch'},{'GHE':'Greenhouse'},{'BPO':'Balcony / Patio'},{'HTS':'Hot tub / spa'},{'RVP':'RV parking'},{'SUA':'Sauna'},{'BRA':'Barbecue area'},{'LWN':'Lawn'},{'DCK':'Deck'},{'STM':'Sprinkler system'}
      ,{'DOK':'Dock'},{'POD':'Pond'},{'FYD':'Fenced yard'},{'POL':'Pool'},{'GDN':'Garden'}];

// export var parking=[{'ALY':'Alley'},{'OSD':'Oversized'},{'ASD':'Assigned'},{'OWD':'Owned'},{'BOT':'Boat'},{'PLT':'Parking Lot'},{'BIN':'Built-in'},{'PGS':'Parking Structure'},{'CRT':'Carport'},{'POS':'Paved or Surfaced'},{'CML':'Commercial'},{'POE':'Pole'},{'CRD':'Covered'},{'PRC':'Porte-Cochere'},{'DWY':'Driveway'},{'PTH':'Pull-through'},{'FEE':'Fee'},{'RMP':'Ramp'},{'FCD':'Fenced'},{'PRV':'RV'},{'GRG':'Garage'},{'SRD':'Secured'},{'GGA':'Garage - Attached'},{'SAN':'Side Apron'},{'GGD':'Garage - Detached'},
//     {'SBS':'Side by Side'},{'GTD':'Gated'},{'SND':'Special Needs'},{'GCT':'Golf Cart'},{'SKD':'Stacked'},{'GUT':'Guest'},{'TDM':'Tandem'},{'HTD':'Heated'},{'TKU':'Tuck-Under'},{'LSD':'Leased'},{'USG':'Unassigned'},{'MCS':'Mechanics'},{'UGB':'Underground/Basement'},{'MTR':'Meter'},{'UPR':'Unimproved'},{'MIX':'Mixed'},{'VLT':'Valet'},{'OFA':'Off Alley'},{'WKS':'Workshop'},{'OST':'Off Street'},{'ZOP':'Zoned Permit'},{'OFS':'Offsite'},{'UNK':'Unknown'},{'OSR':'On Street'},{'OTH':'Other'},{'OPN':'Open'},{'NON':'None'}];
export var parking = [{"AGC":"Attch'd Gar Cabinets"}, {"ASP":"Assigned Parking"}, {"DTH":"Detached"}, {"EDO": "Electric Door Opener"}, {"ELG":"Extnded Lngth Garage"}, {"GCG":"Golf Cart Garage"}, {"OHG":"Over Height Garage"}, {"RVE":"Rear Vehicle Entry"}, {"SSA":"Separate Strge Area"}, {"SVE":"Side Vehicle Entry"}, {"TDG":"Tandem Garage"}, {"UAP":"Unassigned Parking"}, {"RVG":"RV Gate"}, {"RVP":"RV Parking"}, {"CMS":"Community Structure"}, {"GTP":"Gated Parking"}, {"PDR":"Permit/Decal Req'd"}, {"VEL":"Valet"}, {"RGG":"RV Garage"},
    {"APS":"Addtn'l Purchasable"}, {"TCT":"Temp Controlled"}, {"HNG":"Hangar"}, {"EFG":"Dir Entry frm Garage"}, {"SDW":"Shared Driveway"}];

export var roof_Type=[{'ANM':'Aluminum'},{'SLT':'Slate'},{'ATS':'Asbestos'},{'SPL':'Solar Panel'},{'ALT':'Asphalt'},{'SSM':'Standing Seam'},{'BTP':'Built-up'},{'STL':'Steel'},{'CTL':'Clay Tile'},{'TAG':'Tar and Gravel'},{'CSL':'Composition Shingle'},{'TED':'Thatched'},{'COT':'Concrete Tile'},{'TLE':'Tile'},{'CPT':'Copper'},{'UNE':'Urethane'},{'CML':'Corrugated Metal'},{'WDS':'Wood Shake'},{'GEN':'Green'},{'WDS':'Wood Shingle'},{'MCS':'Masonite or Cement Shake'},{'UNK':'Unknown'},{'MNE':'Membrane'},{'OTH':'Other'},{'MTL':'Metal'}];

export var cooling_type=[{'ATF':'Attic Fan'},{'RGL':'Radiant Floor Ground Loop'},{'CFN':'Celing Fan(s)'},{'RFE':'Refrigerator/Evaporative'},{'CAC':'Central A/C'},{'SAC':'Solar A/C - Active'},{'CLE':'Central Evaporative'},{'SPC':'Solar A/C - Passive'},{'CFN':'Central Fan'},{'UNK':'Unknown'},{'CDW':'Chilled Water'},{'WUC':'Wall Unit(s) A/C'},{'DFS':'Dehumidifiers'},{'WUE':'Wall Unit(s) Evaporative'},{'DLR':'Dessicant Cooler'},{'WOC':'Window Unit(s) A/C'},{'EPE':'Evaporative'},{'WOE':'Window Unit(s) Evaporative'},{'HPS':'Heat Pumps'},{'ZAC':'Zoned A/C'},{'PTl':'Partial'},
  {'NON':'None'},{'RTF':'Radiant Floor'},{'OTR':'Other'}];

export var heating_System=[{'BRD':'Baseboard'},{'RDT':'Radiant'},{'CFE':'Central Furnace'},{'RCG':'Radiant Ceiling'},{'EAF':'Electric Air Filter'},{'RFL':'Radiant Floor'},{'FPE':'Fireplace'},{'RTR':'Radiator'},{'FPI':'Fireplace - Insert'},{'SWC':'S-W Changeover'},{'FFE':'Floor Furnace'},{'SAE':'Solar Active'},{'FRW':'Floor Wall'},{'SAP':'Solar Active and Passive'},{'FAR':'Forced Air'},{'SPE':'Solar Passive'},{'GMl':'Geothermal'},{'SHR':'Space Heater'},{'GAR':'Gravity Air'},{'STM':'Steam'},{'GHW':'Gravity Hot Water'},{'STV':'Stove'},
    {'HPP':'Heat Pump'},{'WUT':'Wall Unit'},{'HWR':'Hot Water'},{'ZND':'Zoned'},{'HWF':'Hot Water Radiant Floor'},{'UNK':'Unknown'},{'HFF':'Humidifier'},{'NON':'None'},{'PSV':'Pellet Stove'},{'OTR':'Other'}];

export var heating_fuel=[{'BGS':'Butane Gas'},{'PLT':'Pellet'},{'COL':'Coal'},{'PGS':'Propane Gas'},{'ELC':'Electric'},{'SOL':'Solar'},{'GTL':'Geothermal'},{'SLP':'Solar Panel'},{'KRO':'Kerosene'},{'WOD':'Wood'},{'NGS':'Natural Gas'},{'UNK':'Unknown'},{'OIL':'Oil'},{'NON':'None'},{'PHP':'Passive Heat Pump'},{'OTR':'Other'},{'PSL':'Passive Solar'}];

// export var water = [{'DCK':'Dock'},{'HTS':'Hot Tub Spa'},{'PND':'Pond'},{'IWF':'Is Waterfront'},{'POL':'Pool'}];
export var water = [{"BPC":"Both Private & Community"}, {"PVO":"Private Only"}, {"COM":"Community Only"}, {"NON":"None"}];

export var landscape =[{'GDN':'Garden'},{'LWN':'Lawn'},{'GHS':'Greenhouse'},{'SRS':'Sprinkler System'}];

// export var security = [{'GTE':'Gated Entry'},{'DMN':'Doorman'},{'STS':'Security System'}];
export var security = [{ "CML":"Community Laundry"}, { "COL":"Coin-Op Laundry"}, { "CMP":"Community Pool"}, { "CPH":"Community Pool Htd"},{ "CMS":"Community Spa" }, { "CSH":"Community Spa Htd" }, { "GDE":"Guarded Entry" }, { "HTD":"Historic District" }, { "LSD":"Lake Subdivision" }, { "GCM":"Gated Community" },
{ "OSG":"On-Site Guard" }, { "NLR":"Near Light Rail Stop" }, { "GCS":"Golf Course" }, { "HFL":"Horse Facility" }, { "CCR":"Concierge" }, { "TPS":"Transportation Svcs" }, { "NBS":"Near Bus Stop" }, { "CTC":"Comm Tennis Court(s)" }, { "HRB":"Handball/Racquetball" }, { "CPG":"Children's Playgrnd" }, { "BWP":"Biking/Walking Path" },
{ "CRR":"Clubhouse/Rec Room" }, { "CMR":"Community Media Room" }, { "WFL":"Workout Facility" }, { "RAC":"Runway Access" }];

// export var accessibility = [{'DAS':'Disabled Access'}];
export var accessibility = [{ "ZGE":"Zero-Grade Entry" }, { "DAW":"Dr/Access 32in+ Wide" }, { "HWW":"Hallways 36in+ Wide" }, { "HNF":"Hard/Low Nap Floors" }, { "BRS":"Bath Roll-In Shower" }, { "BRT":"Bath Raised Toilet" }, { "BGB":"Bath Grab Bars" }, { "BUS":"Bath Roll-Under Sink" }, { "BLF":"Bath Lever Faucets" },
{ "KAL":"Ktch Apps Low/Secure" }, { "KLC":"Ktch Low Counters" }, { "KRS":"Ktch Roll-Under Sink" }, { "KMR":"Ktch Modified Range" }, { "KCB":"Ktch Low Cabinetry" }, { "LHD":"Lever Handles" }, { "TVM":"Tactile/Visual Mrkrs" }, { "CBR":"Closet Bars 15-48in" }, { "RMP":"Ramps" }, { "SLF":"Stair Lifts" },
{ "RMD":"Remote Devices" }, { "ECC":"Exterior Curb Cuts" }, { "BTR":"Bath 60in Trning Rad" }, { "BSC":"Bath Scald Ctrl Fct" }, { "BIP":"Bath Insulated Pipes" }, { "KTR":"Ktch 60in Trning Rad" }, { "KIP":"Ktch Insulated Pipes" }, { "KRD":"Ktch Raised Dishwshr" }, { "KSO":"Ktch Side Open Oven" }, { "PRE":"Pool Ramp Entry" },
{ "PPL":"Pool Power Lift" },{ "MEE":"Mltpl Entries/Exits" }];

export var OtherExternalFeatures = [{'BEA':'Barbecue Area'},{'PCH':'Porch'},{'DCK':'Deck'},{'SPC':'Sports Court'},{'PIO':'Patio'}];

// export var view_Types =[{'APT':'Airport'},{'MTN':'Mountain'},{'ARG':'Average'},{'NON':'None'},{'BUF':'Bluff'},{'OCN':'Ocean'},{'BGE':'Bridge'},{'OTH':'Other'},{'CYN':'Canyon'},{'PRM':'Panorama'},{'CIT':'City'},{'PRK':'Park'},{'DST':'Desert'},{'RNE':'Ravine'},{'FOT':'Forest'},{'RVR':'River'},{'GFC':'Golf Course'},{'TTL':'Territorial'},{'HOR':'Harbor'},
//     {'UNK':'Unknown'},{'HIL':'Hills'},{'VLY':'Valley'},{'LKE':'Lake'},{'VTA':'Vista'},{'MNA':'Marina'},{'WTR':'Water'}]
export var view_Types = [{ "ALY": "Alley" }, { "ATW": "Adjacent to Wash" }, { "BCA": "Borders Common Area" }, { "BPL": "Border Pres/Pub Lnd" }, { "CDS": "Cul-De-Sac Lot" }, { "CLT": "Corner Lot" },
    { "CLV": "City Light View(s)" }, { "GCL": "Golf Course Lot" }, { "HSL": "Hillside Lot" }, { "MTV": "Mountain View(s)" }, { "NSE": "North/South Exposure" }, { "SNP": "Street(s) Not Paved" },
    { "WFL": "Waterfront Lot" }, { "RHH": "Nat Reg Historic Hms" }];

// export var otherBuildingCon = [{'NWC':'New Construction'},{'YRU':'Year Updated'}];
export var otherBuildingCon = [{ "ADB":"Adobe" }, { "BLK":"Block" }, { "BRK":"Brick" }, { "FRM":"Frame - Metal" }, { "FRW":"Frame - Wood" }, { "OSR":"Other (See Remarks)" },
    { "SMB":"Slump Block" }, { "SFI":"Spray Foam Insulatn" }, { "BCL":"Blown Cellulose" }, { "STB":"Straw-bale" }, { "RME":"Rammed Earth" }, { "PNC":"Panelized Constrctn" },
    { "ICF":"Insltd Concrete Form" }, { "LVI":"Low VOC Insulation" }, { "IRL":"ICAT Recessed Lights" }, { "LVW":"Low VOC Wood Products" }, { "DPA":"Ducts Professionally Air-Sealed" }];
