<div>
  <header></header>
</div>

<div>
  <div class="property_page header_fix">
    <div class="property_bar green_header">
      <a *ngIf="getScreenName()?.value != '' && showBackButton == true" (click)="backToScreen()" class="cursor-pointer">
        <span class="backbtn"><i class="fa fa-angle-left" aria-hidden="true"></i> Back to {{getScreenName()?.value}}
        </span></a>
      <span class="address hidden-xs">({{singelProperty?.basic_info?.city_name}}
        {{singelProperty?.basic_info?.state_name}} {{singelProperty?.basic_info?.zipcode_code}}
        {{singelProperty?.basic_info?.street}} {{singelProperty?.basic_info?.unit_number}} )</span>
      <span class="pull-right more_button visible-xs"> <img src="{{imagePrefix}}symbols-glyph-more-white.png"
          class="symbols-glyph-more" alt=""> </span>

      <ul class="pull-right right_property_bar">
        <span *ngIf="userIsLogin()">
          <li (click)="manageToFavorite(false,singelProperty?.basic_info?.id)"
            *ngIf="singelProperty?.is_favourite == true" class="hidden-xs">
            <i class="fa fa-heart cursor-pointer" aria-hidden="true"></i>
          </li>
          <li (click)="manageToFavorite(true,singelProperty?.basic_info?.id)"
            *ngIf="singelProperty?.is_favourite == false" class="hidden-xs">
            <!-- <i class="fa fa-heart-o cursor-pointer" aria-hidden="true"></i> -->
            <img class="property-detail-fav-icon cursor-pointer" src="{{imagePrefix}}add.png">
          </li>
        </span>
        <li *ngIf="userIsLogin()" class="hidden-xs">
          <!-- <i class="fa fa-share-square-o cursor-pointer" aria-hidden="true"></i> -->
          <img class="property-detail-share-icon cursor-pointer" (click)="showSharePropertyCard()"
            src="{{imagePrefix}}share.png">
        </li>
        <li *ngIf="isUserPaid()"><a (click)="editProperty()"><button type="btn" class="btn btn1 edit_listing">Edit
              Listing</button></a></li>
      </ul>
    </div>

    <div id="shareBox" class="shareProperty boxzindex">
      <div class="box">
        <form [formGroup]="sharPropertyFormGroup">
          <div class="sharePropertyTitile">Share this property <i (click)="closeShareProperty()"
              class="fa fa-close"></i></div>
          <label>Email</label>
          <div>
            <input formControlName="email" placeholder="Email" class="email-input" type="text">
            <div
              *ngIf="sharPropertyFormGroup.controls['email'].untouched && sharPropertyFormGroup.controls.email.errors?.email && showEmailMessage == true">
              <span class="form-validation">Enter valid email address</span>
            </div>

            <div
              *ngIf="sharPropertyFormGroup.controls['email'].touched && sharPropertyFormGroup.controls.email.errors?.email">
              <span class="form-validation">Enter valid email address</span>
            </div>

          </div>
          <div *ngIf="isValidForShareProperty()">
            <div>
              <div class="line">
                <div class="or_line">
                  <hr />
                  <div class="or">OR</div>
                </div>
              </div>
            </div>

            <label class="cilent-label">Select your client</label>
            <div form>
              <div class="demo dropdown">
                <ng-select class="custom share-property-email shareProrty" placeholder="Client name"
                  formControlName="client" notFoundText="No Client Name found" [items]="sharePropertyClientList"
                  bindValue="client_id" bindLabel="client_name" [clearable]=false [searchable]=false
                  (change)="onClientChange($event)">
                </ng-select>
              </div>
            </div>

            <label>Message</label>
            <textarea name="" formControlName="message" placeholder="Write a message to your client"
              class="message_share_property" id="" cols="30" rows="10" req></textarea>
          </div>

          <span *ngIf="showShareErrorMsg" class="form-validation">{{sharePropertyErrorMSg}}</span>

          <div class="form_group sendbtn">
            <input type="button" value="Send" class="new_form new_text_css" placeholder="Send"
              (click)="shareProperty(sharPropertyFormGroup)" />
          </div>

          <div class="share_property_icons">
            <div class="sharable-Link cursor-pointer" > <span ngxClipboard (click)="copyToClipboard()">Get Sharable Link </span>
              <i (click)="shareFBProperty($event)" style="font-size:24px !important; margin-left: 25px;" class="fa fa-facebook-square"></i>
              <a class="twitter popup" href="http://twitter.com/share?url=https://share.openhousesdirect.com/share/p/{{propertyId}}"><i  style="font-size:24px !important" class="fa fa-twitter"></i></a>
            </div>
            <div id="link_copy" style="text-align: left;color: #7a7a7a;">Link copied to clipboard</div>
          </div>

        </form>
      </div>
    </div>

    <div class="property_header">
      <div class="container">
        <div class="row">
          <div class="col-sm-10">
            <div class="status">
              <span *ngIf="singelProperty?.basic_info?.property_status == 'Active'">
                <img class="property-status-icon" src="{{imagePrefix}}active.png">
              </span>

              <span *ngIf="singelProperty?.basic_info?.property_status == 'Pending'">
                <img class="property-status-icon" src="{{imagePrefix}}pending.png">
              </span>

              <span *ngIf="singelProperty?.basic_info?.property_status == 'PRE-MLS/Coming Soon'">
                <img class="property-status-icon" src="{{imagePrefix}}preMLS.png">
              </span>

              <span *ngIf="singelProperty?.basic_info?.property_status == 'Off Market'">
                <img class="property-status-icon" src="{{imagePrefix}}off-market.png">
              </span>
              <span *ngIf="singelProperty?.basic_info?.property_status == 'PRE-MLS/Coming Soon'">
                Coming Soon
            </span>
             <span *ngIf="singelProperty?.basic_info?.property_status != 'PRE-MLS/Coming Soon'">
              {{singelProperty?.basic_info?.property_status}}
            </span>
            </div><span class="direction-icon"><img
                src="{{imagePrefix}}Car-Icon.png"
                (click)="mapDirections(singelProperty?.location_info?.latitude,singelProperty?.location_info?.longitude)"
                class="title_button car-icon cursor-pointer" alt=""></span>
            <h1 class="ptitle">{{singelProperty?.basic_info?.street}} {{singelProperty?.basic_info?.unit_number}}</h1>
            <small>{{singelProperty?.basic_info?.location}}</small>
            <!-- <h4>{{singelProperty?.agent_info?.brokerage_firm_name}}</h4>   -->

          </div>
          <div class="col-sm-6">
            <p class="price">{{singelProperty?.basic_info?.home_price | currency:"":'symbol':"1.0"}}</p>
            <p class="aminity">{{singelProperty?.basic_info?.bedroom}} bedrooms
              {{singelProperty?.basic_info?.full_bath}} bathrooms
              <span *ngIf="singelProperty?.basic_info?.living_area != ''">{{singelProperty?.basic_info?.living_area | number:'1.0-0'}}
                square feet</span>
            </p>
            <p class="des">{{singelProperty?.basic_info?.property_type}}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="property_body mt-20">
      <div class="container">
        <div class="row">
          <div class="new_profile_group dis_inline col-sm-16">
            <ul class="nav nav-pills" id="tabMenu">
              <li (click)="reFreshImageView()" class="active"><a data-toggle="pill" href="#Property_View"
                  class="title-bold">Property View</a></li>
              <li *ngIf="isUserPaid()"><a data-toggle="pill" href="#Agent_View" class="title-bold">Agent View</a></li>
            </ul>
            <div class="tab-content">
              <div id="Property_View" class="tab-pane fade in active">

                <div class="bg-white">
                  <div class="row">
                    <!-- <div style="width: 100%;text-align: left !important;position: absolute;padding-left: 50px;padding-top: 10px;padding-bottom: 10px;z-index: 1;">
                      <img class="brokerImage" *ngIf="singelProperty?.agent_info?.brokerage_firm_name == 'HomeSmart'" src="{{imagePrefix}}broker/homeSmart.png">
                      <img class="brokerImage" *ngIf="singelProperty?.agent_info?.brokerage_firm_name == 'Tru Realty'" src="{{imagePrefix}}broker/truRealty.png">
                    </div> -->
                    <div *ngIf="isShow72Hour"  style="width: 100%;text-align: left !important;position: absolute;padding-left: 50px;padding-top: 10px;padding-bottom: 10px;z-index: 1;">
                     <img class="brokerImage" src="{{imagePrefix}}OpenHouse_book3.png">
                       <span style="color: #BD3430; font-weight: 600;"> Hour Home Sale</span>
                    </div>


                    <div class="col-sm-16 property-view-padding">

                      <div class="row properties_gallery">

                        <div class="col-sm-12 width-100 mobile-ui-padding-0">
                          <div *ngIf="singelProperty?.property_file?.length == 0" id="example3" class="slider-pro">
                            <div class="sp-slides">
                              <div class="sp-slide">
                                <img class="sp-image" src="{{imagePrefix}}symbols-map-hover.png" />
                              </div>
                            </div>
                            <div class="sp-thumbnails">
                            </div>
                          </div>

                          <div *ngIf="singelProperty?.property_file?.length > 0" id="example3" class="slider-pro">
                            <div class="sp-slides">
                              <span>
                                <div *ngFor="let image of singelProperty.property_file" class="sp-slide">
                                  <img *ngIf="image.file_type == 'image'" class="sp-image" src="{{image.file_url}}">
                                  <p class="sp-layer sp-text">
                                    {{singelProperty?.basic_info?.home_price | currency:"":symbol:"1.0"}} |
                                    {{singelProperty?.basic_info?.street}} {{singelProperty?.basic_info?.unit_number}} |
                                    {{singelProperty?.basic_info?.bedroom}} bedrooms |
                                    {{singelProperty?.basic_info?.full_bath}} bathrooms |
                                    {{singelProperty?.basic_info?.lot_size}} sq ft</p>
                                  <p class="sp-layer Manage manage3"></p>
                                  <video *ngIf="image.file_type == 'video'" class="sp-video" width="100%" height="100%"
                                    controls="controls">
                                    <p class="sp-layer Manage manage3"></p>
                                    <source src="{{image.file_url}}" type="video/mp4" />
                                  </video>
                                  <!-- <p class="sp-layer sp-text">$260,000 | 1961 Fernando Street | 3 bedrooms | 2 bathrooms | 1,500 sq ft</p> -->
                                </div>
                              </span>
                            </div>
                            <div class="sp-thumbnails">
                              <span *ngFor="let images of singelProperty.property_file">
                                <img *ngIf="images.file_type == 'image'" class="sp-thumbnail"
                                  src="{{images.file_url}}" />
                                <video *ngIf="images.file_type == 'video'" class="sp-thumbnail" controls="controls">
                                  <source src="{{images.file_url}}" type="video/mp4" />
                                </video>
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="property_des  visible-xs">
                        <div class="description col-sm-16 ">
                        </div>
                      </div>

                      <div class="property_title row" *ngIf="singelProperty?.event_list.length > 0">
                        <div class="col-sm-16">
                          <h2 class="title title-bold">
                            <span> <img src="{{imagePrefix}}mobile_logo.png"
                                class="img-responsive title_pro_logo event-title-logo" alt="logo"> </span>
                            <span class="event-title-name">Want to see this house?</span>
                          </h2>
                          <p class="visit-title">Click on a house below to tour the home or to speak with a
                            knowledgeable agent.</p>
                        </div>
                      </div>

                      <div class="date_boxes" *ngIf="singelProperty?.event_list.length > 0">
                        <div class="col-xs-16 scrollmenu">
                          <div class="event-box" *ngFor="let event of singelProperty?.event_list">
                            <div class="box" (click)="eventDetail(event.event_type,event)">
                              <div class="box-header"
                                [ngClass]="{'bgcolor1' : event.event_type == 'BO', 'bgcolor2': event.event_type == 'AO', 'bgcolor3': event.event_type == 'OH'}">
                                <span *ngIf="event.event_type == 'BO'">
                                  <img src="{{imagePrefix}}OpenHouse_book2.png" alt="">
                                  <span class="event-type-msg">Broker Open</span>
                                </span>
                                <span *ngIf="event.event_type == 'AO'">
                                  <img src="{{imagePrefix}}OpenHouse_book3.png" alt="" style="width: 25px;">
                                  <span class="event-type-msg">Hour Home Sale</span>
                                </span>
                                <span *ngIf="event.event_type == 'OH'">
                                  <img src="{{imagePrefix}}OpenHouse_book.png" alt="">
                                  <span class="event-type-msg">Open House</span>
                                </span>
                              </div>
                              <div class="box-body">
                                <div class="home_date">
                                  <span class="day"
                                    [ngClass]="{'color_1' : event.event_type == 'BO', 'color_2': event.event_type == 'AO', 'color_3': event.event_type == 'OH'}">
                                    {{getDayName(event.date,event.start_time,event.is_listhub_event)}}</span>
                                  <span
                                    class="date title">{{getDay(event.date,event.start_time,event.is_listhub_event)}}</span>
                                  <span
                                    class="time">{{getTimeTypes(event.start_time, 'property-detail',event.date,event.is_listhub_event)}}
                                    -
                                    {{getTimeTypes(event.end_time, 'property-detail',event.date,event.is_listhub_event)}}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="property_des row">
                        <div class="description col-sm-16">
                          <p>{{singelProperty?.basic_info?.property_description}}</p>
                        </div>

                        <div class="features col-sm-16">
                          <div class="row">
                            <div class="col-sm-16">
                              <h2 class="title-bold">Facts & Features</h2>
                            </div>
                          </div>

                          <div class="row row-margin-right">
                            <div class="col-sm-8">
                              <div class="row facts-row" *ngIf="singelProperty?.basic_info?.property_style != ''">
                                <div class="col-sm-8 fhead">
                                  <span class="fhead-span">Style</span>
                                </div>
                                <div class="col-sm-8 fdetail">
                                  <span
                                    class="pull-right text-right">{{singelProperty?.basic_info?.property_style}}</span>
                                </div>
                              </div>
                              <div class="row facts-row" *ngIf="singelProperty?.basic_info?.year_built != 0">
                                <div class="col-sm-8 fhead">
                                  <span class="fhead-span">Year Built</span>
                                </div>
                                <div class="col-sm-8 fdetail">
                                  <span class="pull-right text-right">{{singelProperty?.basic_info?.year_built}}</span>
                                </div>
                              </div>

                              <div class="row facts-row facts-bottom-border">
                                <div class="col-sm-8 fhead">
                                  <span class="fhead-span">Days on Open Houses Direct</span>
                                </div>
                                <div class="col-sm-8 fdetail">
                                  <span
                                    class="pull-right text-right">{{dayCount(singelProperty?.basic_info?.on_opentoclose)}}
                                    Days</span>
                                </div>
                              </div>
                            </div>

                            <div class="col-sm-7 col-sm-offset-1">
                              <div class="row facts-row">
                                <div class="col-sm-8 fhead">
                                  <span class="fhead-span">Property Type</span>
                                </div>

                                <div class="col-sm-8 fdetail">
                                  <span
                                    class="pull-right text-right">{{singelProperty?.basic_info?.property_type}}</span>
                                </div>
                              </div>

                              <div class="row facts-row" *ngIf="singelProperty?.basic_info.builder != ''">
                                <div class="col-sm-8 fhead">
                                  <span class="fhead-span">Builder</span>
                                </div>
                                <div class="col-sm-8 fdetail">
                                  <span class="pull-right text-right">{{singelProperty?.basic_info?.builder}}</span>
                                </div>
                              </div>

                              <div class="row facts-row">
                                <div class="col-sm-8 fhead">
                                  <span class="fhead-span">Square Feet</span>
                                </div>
                                <div class="col-sm-8 fdetail">
                                  <span class="pull-right text-right">{{singelProperty?.basic_info?.living_area}}
                                    Sqft</span>
                                </div>
                              </div>

                              <div class="row facts-row facts-bottom-border">
                                <div class="col-sm-8 fhead">
                                  <span class="fhead-span">Lot Sizes</span>
                                </div>
                                <div class="col-sm-8 fdetail">
                                  <span class="pull-right text-right">{{singelProperty?.basic_info?.lot_size}}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="panel-group pdetails col-sm-16" id="accordion">
                          <div class="panel panel-default">
                            <div class="row">
                              <div class="col-sm-16 features">
                                <h2 class="title-bold">Details</h2>
                              </div>
                            </div>
                            <div class="row">
                              <div class="col-sm-16">
                                <h2 class="bg_title drop_down_icon"
                                  [attr.data-toggle]="isMobileScreen ? 'collapse' : ''" data-parent="#accordion"
                                  href="#collapse1">Internal Features</h2>
                              </div>
                            </div>
                            <div class="row panel-collapse collapse" id="collapse1">
                              <div class="col-sm-8">
                                <h2 class="sub_title">Bedroom Information</h2>
                                <ul class="property_detail_list">
                                  <li>{{singelProperty?.basic_info?.bedroom}}</li>
                                </ul>
                              </div>

                              <div class="col-sm-8">
                                <h2 class="sub_title">Bathroom Information</h2>
                                <ul class="property_detail_list">
                                  <li>Full bath: {{singelProperty?.basic_info?.full_bath}}</li>
                                </ul>
                              </div>
                            </div>

                            <div class="row" *ngIf="singelProperty?.external_info?.water.length > 0 || singelProperty?.external_info?.accessibility.length > 0 ||
                                          singelProperty?.external_info?.security.length > 0 || singelProperty?.external_info?.parking_total_space != '' ||
                                          singelProperty?.external_info?.garage_space != null">
                              <div class="col-sm-16">
                                <h2 class="bg_title drop_down_icon"
                                  [attr.data-toggle]="isMobileScreen ? 'collapse' : ''" data-parent="#accordion"
                                  href="#collapse2">External Features</h2>
                              </div>
                            </div>

                            <div class="row panel-collapse collapse " id="collapse2">
                              <div class="col-sm-8"
                                *ngIf="singelProperty?.external_info != null && singelProperty?.external_info?.water.length > 0">
                                <div>
                                  <h2 class="sub_title">Pool</h2>
                                </div>
                                <ul class="property_detail_list">
                                  <li *ngFor="let waterName of singelProperty?.external_info?.water; let i = index">
                                    {{waterName | propertyInfo:'water'}}
                                  </li>
                                </ul>
                              </div>

                              <div class="col-sm-8"
                                *ngIf="singelProperty?.external_info != null && singelProperty?.external_info?.accessibility.length > 0">
                                <div>
                                  <h2 class="sub_title">Accessability</h2>
                                  <ul class="property_detail_list">
                                    <li
                                      *ngFor="let accessability of singelProperty?.external_info?.accessibility; let i = index">
                                      {{accessability | propertyInfo:'accessability'}}
                                    </li>
                                  </ul>
                                </div>
                              </div>

                              <div class="col-sm-8"
                                *ngIf="singelProperty?.external_info != null && singelProperty?.external_info?.security.length > 0">
                                <div>
                                  <h2 class="sub_title">Community Features</h2>
                                  <ul class="property_detail_list">
                                    <li *ngFor="let security of singelProperty?.external_info?.security; let i = index">
                                      {{security | propertyInfo:'security'}}
                                    </li>
                                  </ul>
                                </div>
                              </div>
                              <div class="col-sm-8"
                                *ngIf="singelProperty?.external_info != null && singelProperty?.external_info?.parking_total_space != '' || singelProperty?.external_info?.garage_space != null">
                                <div>
                                  <h2 class="sub_title">Parking Information</h2>
                                  <div *ngIf="singelProperty?.external_info?.parking_total_space != ''">
                                    <ul class="property_detail_list">
                                      <li>Number of Parking Spaces:
                                        {{singelProperty?.external_info?.parking_total_space}}</li>
                                    </ul>
                                    <ul class="property_detail_list"
                                      *ngIf="singelProperty?.external_info?.parking.length > 0">
                                      <li *ngFor="let parking of singelProperty?.external_info?.parking; let i = index">
                                        {{parking | propertyInfo:'parking'}}
                                      </li>
                                    </ul>
                                  </div>
                                  <div *ngIf="singelProperty?.external_info?.garage_space != null">
                                    <ul class="property_detail_list">
                                      <li>Garage Spaces: {{singelProperty?.external_info?.garage_space}}</li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div class="row"
                              *ngIf="singelProperty?.building_info?.view_type.length > 0 || singelProperty?.building_info?.condo_floor_no != '' ||
                                          singelProperty?.building_info?.building_unit_count != '' || singelProperty?.building_info?.building_floors != '' || singelProperty?.building_info?.view_type.length > 0">
                              <div class="col-sm-16">
                                <h2 class="bg_title drop_down_icon"
                                  [attr.data-toggle]="isMobileScreen ? 'collapse' : ''" data-parent="#accordion"
                                  href="#collapse3">Building and Construction</h2>
                              </div>
                            </div>
                            <div class="row panel-collapse collapse" id="collapse3">
                              <div class="col-sm-8"
                                *ngIf="singelProperty?.building_info != null && singelProperty?.building_info?.view_type.length > 0">
                                <div>
                                  <h2 class="sub_title">View Types</h2>
                                  <ul class="property_detail_list">
                                    <li
                                      *ngFor="let viewType of singelProperty?.building_info?.view_type; let i = index">
                                      {{viewType | propertyInfo:'viewType'}}
                                    </li>
                                  </ul>
                                </div>
                              </div>

                              <div class="col-sm-8"
                                *ngIf="singelProperty?.building_info?.condo_floor_no != '' ||
                                        singelProperty?.building_info?.building_unit_count != '' || singelProperty?.building_info?.building_floors != ''">
                                <h2 class="sub_title">Building</h2>
                                <ul class="property_detail_list">
                                  <li *ngIf="singelProperty?.building_info?.condo_floor_no != ''">Condo Floor Number:
                                    {{singelProperty?.building_info?.condo_floor_no}}</li>
                                  <li *ngIf="singelProperty?.building_info?.building_unit_count != ''">Building Unit
                                    Count: {{singelProperty?.building_info?.building_unit_count}}</li>
                                  <li *ngIf="singelProperty?.building_info?.building_floors != ''">Floors:
                                    {{singelProperty?.building_info?.building_floors}}</li>
                                </ul>
                              </div>

                              <div class="col-sm-8"
                                *ngIf="singelProperty?.building_info != null && singelProperty?.building_info?.building_construction.length > 0">
                                <div>
                                  <h2 class="sub_title">Other Building and Construction</h2>
                                  <ul class="property_detail_list">
                                    <li
                                      *ngFor="let buildingCons of singelProperty?.building_info?.building_construction; let i = index">
                                      {{buildingCons | propertyInfo:'buildingConstruction'}}
                                    </li>
                                  </ul>
                                </div>
                              </div>
                            </div>


                            <div class="row" *ngIf="singelProperty?.location_info != null">
                              <div class="col-sm-16">
                                <h2 class="bg_title drop_down_icon"
                                  [attr.data-toggle]="isMobileScreen ? 'collapse' : ''" data-parent="#accordion"
                                  href="#collapse4">Location</h2>
                              </div>
                            </div>

                            <div class="row panel-collapse collapse" id="collapse4">
                              <div class="col-sm-8" *ngIf="singelProperty?.location_info?.location != null">
                                <h2 class="sub_title">Address</h2>
                                <ul class="property_detail_list">
                                  <li>{{singelProperty?.location_info.street}}
                                    {{singelProperty?.basic_info.unit_number}},
                                    {{singelProperty?.location_info.location}}</li>
                                </ul>

                                <div *ngIf="singelProperty?.location_info?.neighborhood != ''">
                                  <h2 class="sub_title">Neighborhood</h2>
                                  <ul class="property_detail_list">
                                    <li>{{singelProperty?.location_info?.neighborhood}}</li>
                                  </ul>
                                </div>

                                <div
                                  *ngIf="singelProperty?.location_info != null || singelProperty?.basic_info?.parcel_id != ''">
                                  <h2 class="sub_title">Other Location Information</h2>
                                  <ul class="property_detail_list">
                                    <li *ngIf="singelProperty?.location_info?.county != ''">County:
                                      {{singelProperty?.location_info?.county}}</li>
                                    <li *ngIf="singelProperty?.location_info?.directions != ''" class="direction-text">
                                      Directions: {{singelProperty?.location_info?.directions}}</li>
                                    <!-- <li *ngIf="singelProperty?.location_info?.elevation != ''">Elevation: {{singelProperty?.location_info?.elevation}}</li> -->
                                    <li *ngIf="singelProperty?.location_info?.latitude != 0">Latitude:
                                      {{singelProperty?.location_info?.latitude}}</li>
                                    <li *ngIf="singelProperty?.location_info?.longitude != 0">Longitude:
                                      {{singelProperty?.location_info?.longitude}}</li>
                                    <li *ngIf="singelProperty?.basic_info?.parcel_id != ''">ParcelID:
                                      {{singelProperty?.basic_info?.parcel_id}}</li>
                                  </ul>
                                </div>
                              </div>

                              <div class="col-sm-8">
                                <div
                                  *ngIf="singelProperty?.location_info?.school_1_name != '' || singelProperty?.location_info?.school_1_type != '' || singelProperty?.location_info?.school_2_name != '' || singelProperty?.location_info?.school_2_type != '' || singelProperty?.location_info?.school_3_name != '' || singelProperty?.location_info?.school_3_type != ''">
                                  <h2 class="sub_title">Schools</h2>
                                  <ul class="property_detail_list">
                                    <li *ngIf="singelProperty?.location_info?.school_1_name != ''"><span
                                        *ngIf="singelProperty?.location_info?.school_1_type != ''">{{singelProperty?.location_info?.school_1_type}}:
                                      </span>{{singelProperty?.location_info?.school_1_name}}</li>
                                    <li *ngIf="singelProperty?.location_info?.school_2_name != ''"><span
                                        *ngIf="singelProperty?.location_info?.school_2_type != ''">{{singelProperty?.location_info?.school_2_type}}:
                                      </span>{{singelProperty?.location_info?.school_2_name}}</li>
                                    <li *ngIf="singelProperty?.location_info?.school_3_name != ''"><span
                                        *ngIf="singelProperty?.location_info?.school_3_type != ''">{{singelProperty?.location_info?.school_3_type}}:
                                      </span>{{singelProperty?.location_info?.school_3_name}}</li>
                                  </ul>
                                </div>
                                <div *ngIf="singelProperty?.location_info?.subdivision != ''">
                                  <h2 class="sub_title">Subdivision</h2>
                                  <ul class="property_detail_list">
                                    <li>{{singelProperty?.location_info?.subdivision}}</li>
                                  </ul>
                                </div>
                              </div>
                            </div>

                            <div class="row"
                              *ngIf="singelProperty?.taxes_info.length > 0 || singelProperty?.expenses_info?.length > 0">
                              <div class="col-sm-16">
                                <h2 class="bg_title drop_down_icon"
                                  [attr.data-toggle]="isMobileScreen ? 'collapse' : ''" data-parent="#accordion"
                                  href="#collapse5">Taxes and Expenses</h2>
                              </div>
                            </div>

                            <div class="row panel-collapse collapse" id="collapse5">
                              <div class="col-sm-8" *ngIf="singelProperty?.taxes_info?.length != 0">
                                <h2 class="sub_title">Taxes</h2>
                                <ul class="property_detail_list">
                                  <li *ngFor="let taxes of singelProperty?.taxes_info">
                                    <span *ngIf="taxes.taxes_description !=''">{{taxes?.taxes_description}}: </span>
                                    {{taxes?.taxes_amount | currency:"":symbol:"1.0"}}
                                  </li>
                                </ul>
                              </div>

                              <div class="col-sm-8" *ngIf="singelProperty?.expenses_info?.length != 0">
                                <h2 class="sub_title">Expenses</h2>
                                <ul class="property_detail_list">
                                  <li *ngFor="let expense of singelProperty?.expenses_info">
                                    <span *ngIf="expense.expense_type != ''">{{expense?.expense_type}}:</span>
                                    {{expense?.expense_amount | currency:"":symbol:"1.0"}}
                                  </li>
                                </ul>
                              </div>
                            </div>

                            <div class="row">
                              <div class="col-sm-16">
                                <h2 class="bg_title drop_down_icon"
                                  [attr.data-toggle]="isMobileScreen ? 'collapse' : ''" data-parent="#accordion"
                                  href="#collapse6">Additional Info</h2>
                              </div>
                            </div>

                            <div class="row panel-collapse collapse" id="collapse6">
                              <div class="col-sm-8" *ngIf="singelProperty?.basic_info?.mls_name != ''">
                                <h2 class="sub_title">Source</h2>
                                <ul class="property_detail_list">
                                  <li>{{singelProperty?.basic_info?.mls_name}}</li>
                                  <div class="source-img">
                                    <img src="{{imagePrefix}}armls.png">
                                  </div>
                                </ul>
                              </div>

                              <div class="col-sm-8" *ngIf="singelProperty?.basic_info?.mls_id != ''">
                                <h2 class="sub_title">MLS ID</h2>
                                <ul class="property_detail_list">
                                  <li>{{singelProperty?.basic_info?.mls_id}}</li>
                                </ul>
                              </div>


                              <div class="col-sm-8">
                                <!-- <h2 class="sub_title" *ngIf="singelProperty?.basic_info?.mls_name == ''" >Open House Agent</h2>
                                <h2 class="sub_title" *ngIf="singelProperty?.basic_info?.mls_name != ''" >Listing Agent</h2> -->

                                <!-- <h2 class="sub_title" *ngIf="singelProperty?.basic_info?.mls_name == '' || (singelProperty?.basic_info?.mls_name != '' && singelProperty?.manually_syndicate == true)" >Open House Agent</h2> -->
                                <h2 class="sub_title" *ngIf="singelProperty?.basic_info?.mls_name != '' && singelProperty?.manually_syndicate == false"  >Listing Agent</h2>
                                <!-- <ul class="property_detail_list">
                                  <li *ngIf="singelProperty?.agent_info?.name != null" class="auto-align">
                                    {{singelProperty?.agent_info?.name}}<br>
                                    <span *ngIf="singelProperty?.agent_info?.brokerage_firm_name != ''"
                                      class="listing-agent-ml">{{singelProperty?.agent_info?.brokerage_firm_name}}</span><br>
                                  </li>
                                </ul> -->
                                <div *ngIf="singelProperty?.agent_name && singelProperty?.brokerage_name">
                                <h2 class="sub_title">Listing Agent</h2>
                                <ul class="property_detail_list">
                                  <li class="auto-align">
                                    {{singelProperty?.agent_name}}
                                    <br>
                                    {{singelProperty?.brokerage_name}}
                                  </li>
                                </ul>
                              </div>
                              </div>




                              <div class="col-sm-8" *ngIf="singelProperty?.basic_info?.is_listhub == true">
                                <div class="provided_by">
                                  <h2 class="sub_title">MLS Disclaimer</h2>
                                  <ul class="property_detail_list">
                                    <li *ngIf="singelProperty?.basic_info?.disclaimer != ''">
                                      {{singelProperty?.basic_info?.disclaimer}}</li>
                                  </ul>
                                </div>
                              </div>


                              <div class="col-sm-8" *ngIf="singelProperty?.basic_info?.listing_date != ''">
                                <h2 class="sub_title">Listing Date</h2>
                                <ul class="property_detail_list">
                                  <li>{{listingDateFormat(singelProperty?.basic_info?.listing_date)}}</li>
                                </ul>
                              </div>

                              <div class="col-sm-8" *ngIf="singelProperty?.basic_info?.mls_no != ''">
                                <h2 class="sub_title">MLS Number</h2>
                                <ul class="property_detail_list">
                                  <li>{{singelProperty?.basic_info?.mls_no}}</li>
                                </ul>
                              </div>
                            </div>

                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <span *ngIf="singelProperty?.basic_info?.is_listhub == true">
                  <h5 class="listing-date"><span
                      *ngIf="singelProperty?.basic_info?.modification_timestamp != ''">Listing last changed on
                    </span><span *ngIf="singelProperty?.basic_info?.modification_timestamp != ''"
                      class="title-bold">{{listingModificationDate(singelProperty?.basic_info?.modification_timestamp)}}</span>
                    <span *ngIf="singelProperty?.basic_info?.listing_url != ''"><a class="listing-link" target="_blank"
                        href="{{singelProperty?.basic_info?.listing_url}}">Original Listing</a></span></h5>
                </span>
              </div>

              <div id="Agent_View" class="tab-pane fade">
                <div class="bg-white">
                  <div class="row">
                    <div class="col-sm-16">
                      <h1 class="overview-title">Overview</h1>
                      <button [ngClass]="{'submit-disable': disableExportPdf == true}"
                        [disabled]="disableExportPdf == true" type="button" class="exportbtn btn exportpdf exportbtn"
                        (click)="generateAgentViewPdf()">Export to PDF</button>
                    </div>
                  </div>
                  <div class="row overviewBoth">
                    <div class="col-sm-7 overviewLeft">
                      <div class="row">
                        <div class="col-sm-4 events">
                          <p>Events</p>
                          <h2>{{propertyOverview?.total_events}}</h2>
                        </div>
                        <div class="col-sm-4 guests">
                          <p>Guests</p>
                          <h2>{{propertyOverview?.total_going}}</h2>
                        </div>
                        <div class="col-sm-4 represented">
                          <p>Represented</p>
                          <h2>{{propertyOverview?.total_represented}}/<span>{{propertyOverview?.total_going}}</span>
                          </h2>
                        </div>
                        <div class="col-sm-4 unrepresented">
                          <p>Unrepresented</p>
                          <h2>{{propertyOverview?.total_unrepresented}}/<span>{{propertyOverview?.total_going}}</span>
                          </h2>
                        </div>
                      </div>
                      <div class="row progressbar">
                        <div class="col-sm-16 mobile-ui-padding-0">
                          <div class="row">
                            <div class="col-xs-13 progressbar_text">
                              <p>
                                <img src="{{imagePrefix}}symbols-glyph-checkin.png" alt="symbols-glyph-checkin"
                                  style="width: 6%">
                                Check ins
                              </p>
                            </div>
                            <div class="col-xs-2 text-right"><span>{{ratingOverview?.ratings}}</span></div>
                          </div>
                          <div class="row">
                            <div class="col-sm-16">
                              <div class="progress checkins">
                                <div class="progress-bar progress-bar-checkins" role="progressbar"
                                  [attr.aria-valuenow]=ratingOverview?.ratings aria-valuemin="0" aria-valuemax="100"
                                  [ngStyle]="{'width':ratingOverview?.ratings + '%'}">
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-sm-16 mobile-ui-padding-0">
                          <div class="row">
                            <div class="col-xs-13 progressbar_text">
                              <p>
                                <img src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png"
                                  alt="symbols-glyph-checkin-thumbsup" style="width: 6%">
                                Positives
                              </p>
                            </div>
                            <div class="col-xs-2 text-right"><span>{{ratingOverview?.positive}}</span></div>
                          </div>
                          <div class="row">
                            <div class="col-sm-16">
                              <div class="progress positives">
                                <div class="progress-bar progress-bar-positives" role="progressbar"
                                  [attr.aria-valuenow]=ratingOverview?.positive aria-valuemin="0" aria-valuemax="100"
                                  [ngStyle]="{'width':ratingOverview?.positive + '%'}">
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-sm-16 mobile-ui-padding-0">
                          <div class="row">
                            <div class="col-xs-13 progressbar_text">
                              <p>
                                <img src="{{imagePrefix}}symbols-glyph-checkin-thumbsdown.png"
                                  alt="symbols-glyph-checkin-thumbsdown" style="width: 6%">
                                Negatives
                              </p>
                            </div>
                            <div class="col-xs-2 text-right"><span>{{ratingOverview?.negative}}</span></div>
                          </div>
                          <div class="row">
                            <div class="col-sm-16">
                              <div class="progress negatives">
                                <div class="progress-bar progress-bar-negatives" role="progressbar"
                                  [attr.aria-valuenow]=ratingOverview?.negative aria-valuemin="0" aria-valuemax="100"
                                  [ngStyle]="{'width':ratingOverview?.negative + '%'}">
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>


                    <div class="col-sm-9 overviewRight agent-overview-right">
                      <div class="row mb-24">
                        <div class="col-xs-8 col-sm-4 relative">
                          <p class="pmain">Floorplan</p>

                          <div
                            class="{{getCountProgress(ratingOverview?.floorplan_positive,ratingOverview?.floorplan_negative, 'floorplan')}} c100-width">
                            <div class="slice">
                              <div class="bar"></div>
                              <div class="fill"></div>
                            </div>
                          </div>
                          <div class="cirtext">
                            <p class="posactive"><span></span> {{ratingOverview?.floorplan_positive}}
                            </p>
                            <p class="negactive"><span></span> {{ratingOverview?.floorplan_negative}}</p>
                          </div>
                        </div>
                        <div class="col-xs-8 col-sm-4 relative">
                          <p class="pmain">Size of Bedrooms</p>
                          <div
                            class="{{getCountProgress(ratingOverview?.bedroom_positive,ratingOverview?.bedroom_negative, 'sizeofbedrooms')}} c100-width">
                            <div class="slice">
                              <div class="bar"></div>
                              <div class="fill"></div>
                            </div>
                          </div>
                          <div class="cirtext">
                            <p class="posactive"><span></span> {{ratingOverview?.bedroom_positive}}
                            </p>
                            <p class="negactive"><span></span> {{ratingOverview?.bedroom_negative}}</p>
                          </div>
                        </div>
                        <div class="col-xs-8 col-sm-4 relative">
                          <p class="pmain">Size of Bathrooms</p>
                          <div
                            class="{{getCountProgress(ratingOverview?.bathroom_positive,ratingOverview?.bathroom_negative, 'sizeofbathrooms')}}">
                            <div class="slice">
                              <div class="bar"></div>
                              <div class="fill"></div>
                            </div>
                          </div>
                          <div class="cirtext">
                            <p class="posactive"><span></span> {{ratingOverview?.bathroom_positive}}
                            </p>
                            <p class="negactive"><span></span> {{ratingOverview?.bathroom_negative}}</p>
                          </div>
                        </div>
                        <div class="col-xs-8 col-sm-4 relative">
                          <p class="pmain">Kitchen</p>
                          <div
                            class="{{getCountProgress(ratingOverview?.kitchen_positive,ratingOverview?.kitchen_negative, 'kitchen')}}">
                            <div class="slice">
                              <div class="bar"></div>
                              <div class="fill"></div>
                            </div>
                          </div>
                          <div class="cirtext">
                            <p class="posactive"><span></span> {{ratingOverview?.kitchen_positive}}
                            </p>
                            <p class="negactive"><span></span> {{ratingOverview?.kitchen_negative}}</p>
                          </div>
                        </div>
                      </div>
                      <div class="row mb-24">
                        <div class="col-xs-8 col-sm-4 relative">
                          <p class="pmain">Finishes</p>
                          <div
                            class="{{getCountProgress(ratingOverview?.finishes_positive,ratingOverview?.finishes_negative, 'finishes')}} c100-width">
                            <div class="slice">
                              <div class="bar"></div>
                              <div class="fill"></div>
                            </div>
                          </div>
                          <div class="cirtext">
                            <p class="posactive"><span></span> {{ratingOverview?.finishes_positive}}
                            </p>
                            <p class="negactive"><span></span> {{ratingOverview?.finishes_negative}}</p>
                          </div>
                        </div>
                        <div class="col-xs-8 col-sm-4 relative">
                          <p class="pmain">Landscaping</p>
                          <div
                            class="{{getCountProgress(ratingOverview?.landscaping_positive,ratingOverview?.landscaping_negative, 'landscaping')}} c100-width">
                            <div class="slice">
                              <div class="bar"></div>
                              <div class="fill"></div>
                            </div>
                          </div>
                          <div class="cirtext">
                            <p class="posactive"><span></span> {{ratingOverview?.landscaping_positive}}
                            </p>
                            <p class="negactive"><span></span> {{ratingOverview?.landscaping_negative}}</p>
                          </div>
                        </div>
                        <div class="col-xs-8 col-sm-4 relative">
                          <p class="pmain">Neighborhood</p>
                          <div
                            class="{{getCountProgress(ratingOverview?.neighbourhood_positive,ratingOverview?.neighbourhood_negative, 'neighborhood')}}">
                            <div class="slice">
                              <div class="bar"></div>
                              <div class="fill"></div>
                            </div>
                          </div>
                          <div class="cirtext">
                            <p class="posactive"><span></span> {{ratingOverview?.neighbourhood_positive}}
                            </p>
                            <p class="negactive"><span></span> {{ratingOverview?.neighbourhood_negative}}</p>
                          </div>
                        </div>
                        <div class="col-xs-8 col-sm-4 relative">
                          <p class="pmain">Price</p>
                          <div
                            class="{{getCountProgress(ratingOverview?.price_positive,ratingOverview?.price_negative, 'price')}}">
                            <div class="slice">
                              <div class="bar"></div>
                              <div class="fill"></div>
                            </div>
                          </div>
                          <div class="cirtext">
                            <p class="posactive"><span></span> {{ratingOverview?.price_positive}}
                            </p>
                            <p class="negactive"><span></span> {{ratingOverview?.price_negative}}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row events">
                  <div class="col-sm-16">
                    <h1 class="overview-title dis_inline">Events</h1>
                    <div *ngIf="showButton" class="save_notes  dis_inline pull-right add_event_mt" data-toggle="modal"
                      (click)="openEventModel()">Add Event</div>
                  </div>
                  <div class="col-sm-16">
                    <div class="my_client_table_group">
                      <!-- <div class="myclient_navbar myclient_nav_mobile visible-xs">
                                <div class="select_mate" data-mate-select="active" >
                                  <select name="" id="" class="select-nav drop_down_icon"  >
                                        <option value="menu1">Upcoming</option>
                                        <option value="menu2">Past</option>
                                  </select>
                                <p class="selecionado_opcion"  onclick="open_select(this)" ></p>
                                <span onclick="open_select(this)" class="icon_select_mate" >
                                <span class="drop_down_icon drp_soan"></span>
                                </span>
                                <div class="cont_list_select_mate">
                                    <ul class="cont_select_int">  </ul>
                                </div>
                              </div>
                            </div> -->
                      <div class="myclient_navbar">
                        <ul>
                          <li class="active" data-toggle="pill" href="#menu1">Upcoming</li>
                          <li data-toggle="pill" href="#menu2">Past</li>
                        </ul>
                      </div>
                      <div class="tab-content">
                        <div id="menu1" class="event-list tab-pane fade in active table-responsive selected_saved">
                          <div *ngIf="upcomingEventList.length == 0" class="No_matches">
                            <div class="title">No matches</div>
                            <div class="text">You don’t have any listings that meet your search criteria. Try <br>
                              running a new search or add a new listing.</div>
                          </div>
                          <div *ngIf="upcomingEventList.length != 0" class="property-list-table">
                            <table class="table">
                              <thead>
                                <tr>
                                  <th>Type <img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                  <th>Date<img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                  <th>Open House Agent<img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                  <th>Going<img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                  <th>Represented<img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                  <th colspan="4">Unrepresented<img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr *ngFor="let eventInfo of upcomingEventList">
                                  <td>
                                    <div
                                      [ngClass]="{'open_h': eventInfo?.event_type == 'OH', 'open_a': eventInfo?.event_type == 'AO', 'open_b': eventInfo?.event_type == 'BO'}">
                                     <!-- <div *ngIf="eventInfo?.event_type_msg =='72 Hour Launch'">72 Hour Home Sale</div>  -->
                                     <div *ngIf="eventInfo?.event_type_msg !='72 Hour Launch'">{{eventInfo?.event_type_msg}}</div>

                                    </div>
                                  </td>
                                  <td><span
                                      class="font_semibold">{{setEventDateFormat(eventInfo.date,eventInfo.start_time,eventInfo.is_listhub_event)}}
                                      <br></span>{{getTimeTypes(eventInfo.start_time,'',eventInfo.date,eventInfo.is_listhub_event)}}
                                    - {{getTimeTypes(eventInfo.end_time,'',eventInfo.date,eventInfo.is_listhub_event)}}
                                  </td>
                                  <td>
                                    <span *ngIf="eventInfo.open_house_agent_image != ''"><img
                                        [src]="eventInfo.open_house_agent_image"
                                        class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                    <span *ngIf="eventInfo.open_house_agent_image == ''"><img
                                        src="{{imagePrefix}}default-placeholder.png"
                                        class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                    <div class="dis_inline po_rel"><span
                                        class="dark font_semibold u-name-table">{{eventInfo?.open_house_agent_name}}
                                        <br></span>
                                    </div>
                                  </td>
                                  <td>
                                    <div class="bold_font font_semibold">{{eventInfo.going}}</div>
                                  </td>
                                  <td>
                                    <div class="bold_font font_semibold"><i
                                        class="fa fa-circle dot1"></i>{{eventInfo.total_represented}}/{{eventInfo.going}}
                                    </div>
                                  </td>
                                  <td>
                                    <div class="bold_font font_semibold"><i
                                        class="fa fa-circle dot"></i>{{eventInfo.total_unrepresented}}/{{eventInfo.going}}
                                    </div>
                                  </td>
                                  <td class="action-view">
                                    <a *ngIf="eventInfo.allow_run_event == true"
                                      (click)="runEvent(eventInfo.event_id,'newEvent')">
                                      <div class="save_notes margin_zero">Run Event</div>
                                    </a>
                                    <a *ngIf="eventInfo.is_runnig == true"
                                      (click)="runEvent(eventInfo.event_id,'runningEvent')">
                                      <div class="save_notes margin_zero">Run Event</div>
                                    </a>
                                  </td>
                                  <td class="action-option">
                                    <div class="open_click_menu" (click)="openMenu(eventInfo.event_id)">
                                      <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more "
                                        alt="">
                                      <ul id="{{eventInfo.event_id}}" class="click_menu_open events">
                                        <li class="cursor-pointer option-menu" (click)="eventInfoView(eventInfo)">Event
                                          Detail</li>
                                        <li class="cursor-pointer option-menu"
                                          (click)="cancelEvent(eventInfo.event_id, 'UP')">Cancel Event</li>
                                        <li *ngIf="currentUserId != eventInfo.open_house_agent_id"
                                          (click)="contactOpenHouseAgent(eventInfo)" class="cursor-pointer option-menu">
                                          Contact Open House Agent</li>
                                      </ul>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <div *ngIf="upcTotalCount > upcItemPerPage && upcTotalCount != upcomingEventList.length"
                              class="new_form_group load_more_btn">
                              <input type="submit" class="submit_button with_bg load_more"
                                [ngClass]="{'submit-disable': disableLoadMore == true}"
                                [disabled]="disableLoadMore == true" (click)="loadMoreEvent(upcIndex, 'UP')"
                                value="Load More">
                            </div>
                          </div>
                        </div>
                        <div id="menu2" class="event-list tab-pane fade table-responsive selected_saved">
                          <div *ngIf="pastEventList.length == 0" class="No_matches">
                            <div class="title">No matches</div>
                            <div class="text">You don’t have any listings that meet your search criteria. Try <br>
                              running a new search or add a new listing.</div>
                          </div>
                          <div *ngIf="pastEventList.length != 0" class="property-list-table">
                            <table class="table">
                              <thead>
                                <tr>
                                  <th>Type <img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                  <th>Date<img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                  <th>Open House Agent<img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                  <th colspan="3">Rating<img src="{{imagePrefix}}symbols-glyph-arrow-line.png"
                                      class="symbols-glyph-arrow-line" alt=""> </th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr *ngFor="let event of pastEventList">
                                  <td>
                                    <div
                                      [ngClass]="{'open_h': event?.event_type == 'OH', 'open_a': event?.event_type == 'AO', 'open_b': event?.event_type == 'BO'}">
                                      {{event?.event_type_msg}}</div>
                                  </td>
                                  <td><span
                                      class="font_semibold">{{setEventDateFormat(event.date,event.start_time,event.is_listhub_event)}}
                                      <br></span>{{getTimeTypes(event.start_time,'',event.date,event.is_listhub_event)}}
                                    - {{getTimeTypes(event.end_time,'',event.date,event.is_listhub_event)}}</td>
                                  <td>
                                    <span *ngIf="event.open_house_agent_image != ''"><img
                                        [src]="event.open_house_agent_image"
                                        class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                    <span *ngIf="event.open_house_agent_image == ''"><img
                                        src="{{imagePrefix}}default-placeholder.png"
                                        class="search-agent-event symbols-property-image dis_inline" alt=""> </span>
                                    <div class="dis_inline po_rel"><span
                                        class="dark font_semibold u-name-table">{{event?.open_house_agent_name}}
                                        <br></span>
                                    </div>
                                  </td>
                                  <td class="font_semibold">{{event.ratings}}<img
                                      src="{{imagePrefix}}symbols-glyph-checkin-thumbsup.png"
                                      class="symbols-glyph-checkin-thumbsup dis_inline thusup-rating" alt=""></td>
                                  <td class="action-view action-agent-view">
                                    <a (click)="runEvent(event.event_id,'runningEvent')">
                                      <div class="save_notes margin_zero">Run Event</div>
                                    </a>
                                  </td>
                                  <td class="action-option">
                                    <div class="open_click_menu" (click)="openMenu(event.event_id)">
                                      <img src="{{imagePrefix}}symbols-glyph-more.png" class="symbols-glyph-more "
                                        alt="">
                                      <ul id="{{event.event_id}}" class="click_menu_open events">
                                        <li class="cursor-pointer option-menu" (click)="eventInfoView(event, 'past')">
                                          Event Detail</li>
                                        <li *ngIf="currentUserId != event.open_house_agent_id"
                                          (click)="contactOpenHouseAgent(event)" class="cursor-pointer option-menu">
                                          Contact Open House Agent</li>
                                      </ul>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <div *ngIf="pastTotalCount > pastItemPerPage && pastTotalCount != pastEventList.length"
                              class="new_form_group load_more_btn">
                              <input type="submit" class="submit_button with_bg load_more"
                                [ngClass]="{'submit-disable': disableLoadMore == true}"
                                [disabled]="disableLoadMore == true" (click)="loadMoreEvent(pastIndex, 'PA')"
                                value="Load More">
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<event-modal (setPropertyLatestInfo)="UpdatePropertyInfo($event)"></event-modal>
<add-event (addEventResponse)="updateEventList($event)"></add-event>

<div>
  <footer></footer>
</div>
