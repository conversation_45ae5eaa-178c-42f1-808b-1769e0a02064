#map {
    height: calc(100vh - 208px);
    width: 100%;
}

.width_50 {
    width: 41% !important;
    padding-left: 2px !important;
    float: left !important;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.drop-down-sapce{
    margin-left:7px !important
}
.width_39{
    width: 35% !important;
    padding-left: 2px !important;
    float: left !important
}

.artboard6_sidebar {
    background: #FFFFFF;
    border-radius: 4px;
    width: 366px !important;
    padding: 2px 29px 10px 29px;
    margin-left: 20px;
}
.newWidth{
    width: 466px !important;
}

.inputfile {
    position: absolute;
    z-index: 1000;
    opacity: 0;
    cursor: pointer;
    right: 0;
    top: 0;
    height: 100%;
    font-size: 24px;
    width: 100%;
}

dropimage{
    width: 139% !important;
}

.save-btn-right{
    padding-right: 80px !important;
}
.plus-btn{
    font-size: 37px !important;
    color: #10B8A8 !important;
    cursor: pointer !important;
}
.minus-btn{
    font-size: 37px !important;
    color: #F25B8D !important;
    padding-left: 7px !important;
    cursor: pointer !important;
}
.spaces-btn{
    margin-bottom: 3px !important;
    margin-top: 15px !important;
}
.caption{
    width: 159px !important;
    padding-left: 0 !important;
}
.saved-img-btn{
    /* float: right !important; */
    margin-top: 0px !important;
    /* margin-right: 79px !important; */
    height: 36px !important;
}
.listing-body{
    overflow: hidden;
}
.list-paggination{
    text-align: center !important;
}
.tax-expense-err{
    color: red;
    margin: 0px 0 0px 0 !important;
    font-size: 13px !important;
    font-family: 'Source Sans Pro', sans-serif !important;
}
.title-add-bold{
    font-weight: bold;
    margin-bottom: 5px;
}
.property-img-broder{
    border: 4px solid #10B8A8;
}
.img-delete{
    float: right;
    position: absolute;
    right: 0px;
    height: 21px;
    top: -11px;
    border-radius: 30px;
    width: 21px;
}
.img-delete-sm{
    float: right;
    position: absolute;
    right: 0px;
    top: -11px;
    left: 159px;
    height: 20px;
    border-radius: 30px;
    width: 20px;
}
.delete{
    margin-top: 12px;
}
.tax-location{
    margin-bottom: 1px !important;
}
.taxes-group{
    height: 125px;
}
.expsenses-group{
    height: 125px;
}
.image-btn-text{
    margin-left: 0px !important;
    margin-right: 0px !important;
    text-align: center !important;
}
.pr-img-top{
    padding-top: 17px;
}
.first-img{
    width: 334px;
}
.image-scroll-hidden{
    overflow: hidden !important;
}
.view-listing-btn{
    font-size: 15px;
    color: #10B8A8;
    border: 1px solid #10B8A8;
    border-radius: 100px;
    padding: 8px 18px 8px 18px;
    margin-top: 5px;
    float: right;
    margin-top: 34px;
}

.share-property .modal-dialog.modal-lg{
    width: 30%;
}
.share-property .heading{
    font-size: 25px!important;
    color: #676767!important;
    line-height: 30px!important
}
.share-property .line{
    padding-top: 15px;
}
.share-property label{
    font-size: 16px;
    color: #7a7a7a;
    letter-spacing: 0;
    font-weight: 400;
    margin: 0;
    padding-top: 15px;
    padding-bottom: 15px;
}
.share-property .or_line hr{
    margin: 15px 0px 15px 0px !important;
}
.share-property i.fa.fa-facebook-square{
    color: #3B5998;
    font-size: 35px !important;
    cursor: pointer;
}
.share-property i.fa.fa-twitter{
    color: #42B6E7;
    font-size: 35px !important;
    margin-left: 30px;
    cursor: pointer;
}
.share-property .send{
    padding-top: 15px;
}
.share-property .email-input{
    width: 100%;
    height: 43px;
    padding: 6px;
    color: #8D8D8D;
    font-size: 16px;
    outline: 0 !important;
}

.property-list-table .table tr:last-child>td .open_click_menu ul.click_menu_open.events {
  top: -5em !important;
}
.property-list-table .table tr:last-child>td .open_click_menu ul {
  margin-bottom: 0px !important
}

.label-class {
    margin-bottom: 8px;
}