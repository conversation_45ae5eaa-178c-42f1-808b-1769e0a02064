<div class="marg">
  <div class="Event_manager_add small-grid">
    <div class="add_event_image_group">
      <span>
        <!-- <div class="guest-overlay"></div> -->
        <!-- <img
          src="https://staging.openhousesdirect.com/static/open-to-close-angular/dist/assets/images/logo.png"
          class="add_event_bg"
          alt=""
        /> -->
      </span>
      <span>
        <!-- <div class="guest-overlay"></div> -->
        <img src="{{imagePrefix}}image.jpg" class="add_event_bg" alt="" />
      </span>

      <img
        src="footer_logo.png"
        class="img-responsive footer_logo_event img-margin-left-7"
        alt=""
      />
      <!-- Commented content remains intact -->
    </div>

    <div class="event_agent_group event_agent_group_width_class">
      <img
        src="{{imagePrefix}}logo.png"
        class="img-responsive event_agent_img ml-0"
        alt="logo"
      />
      <div class="not_a_member" style="margin-bottom: 20px;">Create Your Real Estate Agent Account</div>

      <!-- Direct signup form without modal -->
      <div class="signup-container scrollable-form">
        <div class="new_account">
          <div *ngIf="showLenderSignUp == true">
            <div class="title">
              You have been invited to be a preferred lender on Open Houses
              Direct.
            </div>
            <div class="menu51_text">
              Set up an account to be shown as a preferred lender and start
              collecting leads
            </div>
          </div>

          <form [formGroup]="registerForm">
            <!-- First name and last name in one row -->
            <div class="form_row">
              <div class="form_group" formGroupName="profile">
                <input
                  type="text"
                  class="new_form"
                  formControlName="first_name"
                  placeholder="Enter first name*"
                  required
                />
                <div
                  *ngIf="registerForm['controls'].profile['controls']['first_name'].untouched"
                >
                  <span></span>
                </div>
                <div
                  *ngIf="registerForm['controls'].profile['controls']['first_name'].touched && registerForm['controls'].profile['controls'].first_name.errors?.required"
                >
                  <span class="form-validation">Enter first name</span>
                </div>
              </div>

              <div class="form_group" formGroupName="profile">
                <input
                  type="text"
                  class="new_form"
                  formControlName="last_name"
                  placeholder="Enter last name*"
                  required
                />
                <div
                  *ngIf="registerForm['controls'].profile['controls']['last_name'].untouched"
                >
                  <span></span>
                </div>
                <div
                  *ngIf="registerForm['controls'].profile['controls']['last_name'].touched && registerForm['controls'].profile['controls']['last_name'].errors?.required"
                >
                  <span class="form-validation">Enter last name</span>
                </div>
              </div>
            </div>

            <!-- Agent type dropdown (commented out but preserved) -->

            <div
              class="form_group"
              [ngClass]="{'has-success has-feedback': estateProfessionalRegistration && emailInPreDirectory && showResponseIcon, 'has-error has-feedback': estateProfessionalRegistration && !emailInPreDirectory && showResponseIcon}"
            >
              <input
                type="text"
                class="new_form"
                [ngClass]="{'success-border-box': estateProfessionalRegistration && emailInPreDirectory && showResponseIcon, 'error-border-box': estateProfessionalRegistration && !emailInPreDirectory && showResponseIcon}"
                formControlName="email"
                placeholder="Enter your email address*"
                required
              />

              <span
                *ngIf="emailInPreDirectory && estateProfessionalRegistration && showResponseIcon"
                class="glyphicon glyphicon-ok form-control-feedback"
              ></span>
              <span
                *ngIf="!emailInPreDirectory && estateProfessionalRegistration && showResponseIcon"
                class="glyphicon glyphicon-remove form-control-feedback"
              ></span>

              <div *ngIf="registerForm.controls['email'].untouched">
                <span></span>
              </div>
              <div
                *ngIf="registerForm.controls['email'].touched && registerForm.controls.email.errors?.email"
              >
                <span class="form-validation">Enter valid email address</span>
              </div>
              <div
                *ngIf="!emailInPreDirectory && estateProfessionalRegistration && showResponseIcon"
              >
                <span class="form-validation"
                  >Email error message goes here</span
                >
              </div>
            </div>

            <!-- Password fields in one row -->
            <div class="form_row">
              <div class="form_group" formGroupName="passwordForm">
                <input
                  type="password"
                  class="new_form"
                  formControlName="password"
                  placeholder="Choose a password*"
                  required
                />
                <label>Password must be 5-15 characters</label>
                <div
                  *ngIf="registerForm['controls'].passwordForm['controls'].password.touched"
                >
                  <p
                    class="form-validation"
                    *ngIf="registerForm['controls'].passwordForm['controls'].password.errors?.required"
                  >
                    Enter password
                  </p>
                  <p
                    class="form-validation"
                    *ngIf="registerForm['controls'].passwordForm['controls'].password.errors?.minlength"
                  >
                    Password must be 5-15 characters
                  </p>
                  <p
                    class="form-validation"
                    *ngIf="registerForm['controls'].passwordForm['controls'].password.errors?.maxlength"
                  >
                    Password must be 5-15 characters
                  </p>
                </div>
              </div>

              <div class="form_group from-md-10" formGroupName="passwordForm">
                <input
                  type="password"
                  class="new_form"
                  formControlName="confirm_password"
                  placeholder="Confirm password*"
                  required
                />
                <label>Confirm password must be 5-15 characters</label>
                <div
                  *ngIf="registerForm['controls'].passwordForm['controls'].confirm_password.touched"
                >
                  <p
                    class="form-validation"
                    *ngIf="registerForm['controls'].passwordForm['controls'].confirm_password.errors?.required &&
                              registerForm['controls'].passwordForm.hasError('mismatch') == false"
                  >
                    Enter confirm password
                  </p>
                  <p
                    class="form-validation"
                    *ngIf="registerForm['controls'].passwordForm['controls'].confirm_password.errors?.minlength"
                  >
                    Confirm password must be 5-15 characters
                  </p>
                  <p
                    class="form-validation"
                    *ngIf="registerForm['controls'].passwordForm['controls'].confirm_password.errors?.maxlength"
                  >
                    Confirm password must be 5-15 characters
                  </p>
                  <p
                    class="form-validation"
                    *ngIf="registerForm['controls'].passwordForm.errors?.mismatch"
                  >
                    Confirm password not match
                  </p>
                </div>
              </div>
            </div>

            <!-- Phone section -->
            <div class="form_row">
              <div class="form_group from-md-10" formGroupName="profile" style="margin-bottom: 17px;">
                <input
                  type="text"
                  maxlength="12"
                  #phone
                  (keyup)="validateFormat(phone.value)"
                  class="new_form"
                  formControlName="phone"
                  placeholder="Phone Number"
                />
                <div
                  *ngIf="registerForm['controls'].profile['controls']['phone'].touched"
                >
                  <p
                    class="form-validation"
                    *ngIf="registerForm.controls.profile.controls.phone.errors?.minlength"
                  >
                    phone number must be digits and 10 characters
                  </p>
                  <p
                    class="form-validation"
                    *ngIf="registerForm.controls.profile.controls.phone.errors?.maxlength"
                  >
                    phone number must be digits and 10 characters
                  </p>
                </div>
              </div>
            </div>

            <!-- For OTP verification -->
            <div
              class="form_group from-md-10"
              formGroupName="profile"
              *ngIf="showOtp"
              style="border: 1px solid red; padding: 10px !important"
            >
              <input
                type="text"
                maxlength="4"
                #otp
                class="new_form"
                formControlName="otp"
                placeholder="Enter Verification Code"
                (keyup)="validateOtpFormat(otp.value)"
              />
              <br />
              <a class="registration-link"
                ><span (click)="sendOtp(registerForm)">Resend OTP</span></a
              >
            </div>

            <!-- Agent selection section -->
            <!-- <div class="check_group" *ngIf="showLenderSignUp == false">
              <span *ngIf="isClientInviteSignUp == false">
                <div
                  *ngIf="!brokerSignup && !agentSignup && homeBuyerRegistration"
                  class="form_group"
                >
                  <input
                    type="checkbox"
                    [checked]="agent"
                    (change)="viewAgentId()"
                  />
                  <span class="checkmark"></span>
                  <label class="width_auto">I am working with an agent</label>
                </div>

                <div *ngIf="showAgent != false">
                  <div class="form_group">
                    <input
                      type="text"
                      #name
                      class="new_form"
                      placeholder="Type agent name here*"
                      (keyup)="searchAgent(name.value)"
                    />
                    <img
                      src="symbols-glyph-openhouse.png"
                      class="img-responsive search"
                      alt=""
                    />
                  </div>

                  <div *ngIf="showSearching != false">
                    <div class="agent_found">Searching...</div>
                  </div>

                  <div *ngIf="agentList != false">
                    <div class="agent_found">2 agents found</div>
                    <div class="search-agent-list">
                      <div class="remove_agent search-agent-padding">
                        <span class="remove-span">
                          <img
                            src="/api/placeholder/60/60"
                            class="search-agent-image"
                            alt=""
                          />
                        </span>
                        <div class="remove_details">
                          <div class="name">Sarah Johnson</div>
                          <div class="sub_name">Luxury Homes Realty</div>
                          <div class="remove_button select">
                            This is my agent
                          </div>
                        </div>
                      </div>
                      <div class="remove_agent search-agent-padding">
                        <span class="remove-span">
                          <img
                            src="/api/placeholder/60/60"
                            class="search-agent-image"
                            alt=""
                          />
                        </span>
                        <div class="remove_details">
                          <div class="name">Michael Brown</div>
                          <div class="sub_name">City Real Estate Group</div>
                          <div class="remove_button">This is my agent</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div> -->

               <div class="checkbox-container">
                  <label class="custom-checkbox" style="margin-top: 3px;">
                    <input type="checkbox" formControlName="agree_terms" />
                    <span class="checkmark"></span>
                  </label>
                  <span style="font-size: 13px; line-height: 1.4;">
                    I agree to the <a href="/terms-of-use" target="_blank">Terms of Use</a> and
                    <a href="/privacy-policy" target="_blank">Privacy Policy</a>, and I am opting in to receive
                    communications via email, phone calls, and SMS/MMS messages. Message frequency may vary.
                    Standard message and data rates may apply. Reply STOP to unsubscribe or HELP for assistance.
                  </span>
                </div>
            <!-- Lender company name field -->
            <div class="form_group" *ngIf="showLenderSignUp == true">
              <input
                type="text"
                #company
                class="new_form"
                placeholder="Enter Company Name*"
                (keyup)="setLenderCompanyName(company.value)"
              />
            </div>

            <!-- Submit buttons -->
            <div class="form_group cursor-pointer">
              <input
                type="button"
                *ngIf="!showOtp"
                style="width: 180px !important"
                [ngClass]="{'submit-disable':registerForm.invalid || !isFormValid}"
                [disabled]="registerForm.invalid || !isFormValid"
                class="new_form"
                value="Send Verification Code"
                (click)="sendOtp(registerForm)"
              />
              <input
                type="button"
                *ngIf="showOtp"
                style="width: 180px !important"
                [ngClass]="{'submit-disable':registerForm.invalid || !isFormValid}"
                [disabled]="registerForm.invalid || !isFormValid"
                class="new_form"
                value="Register"
                (click)="verifyOtp(registerForm)"
              />
            </div>
          </form>

          <!-- Terms and privacy policy footer -->
      <!-- <div class="modal_footer">
    <label style="display: flex; align-items: start; gap: 8px; margin-bottom: 10px;">
        <input type="checkbox" formControlName="agree_terms"  required style="margin-top: 3px;">
        <span>
            By creating an account, you agree to the Open Houses Direct
            <a href="/terms-of-use" target="_blank">Terms of Use</a> and
            <a href="/privacy-policy" target="_blank">Privacy Policy</a>.
        </span>
    </label>
</div> -->

        </div>
      </div>
    </div>
  </div>
</div>
