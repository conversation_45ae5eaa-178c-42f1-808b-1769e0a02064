<admin-header ></admin-header>

<div class="login-box">
  <div class="center-title">Welcome</div>
  <div class="center-sub-title">Log in to access the Open Houses Direct admin panel</div>

  <form [formGroup]="adminLoginForm">
      <div class="input-div">
        <input type="text" formControlName="email" placeholder="Username*" class="input-textbox">
          <div *ngIf="adminLoginForm.controls['email'].untouched" class="error-align">
            <span></span>
          </div>
          <div *ngIf="adminLoginForm.controls['email'].touched && adminLoginForm.controls.email.errors?.email" class="error-align">
              <span class="form-error">Enter valid user name</span>
          </div>
      </div>
      <div class="input-div">
        <input type="password" formControlName="password" placeholder="Password*" class="input-textbox"><br>
        <div *ngIf="adminLoginForm.controls.password.touched" class="error-align">
          <p class="form-error" *ngIf="adminLoginForm.controls.password.errors?.required">Enter password</p>
        </div>
      </div>

      <div class="input-span">
        <label (click)="routeOnUrl('admin/forgot-password')" class="forgot-pw">Forgot Password</label> 
      </div>

      <div class="input-div">
        <input type="submit" [ngClass]="{'submit-disable': adminLoginForm.invalid }" [disabled]="adminLoginForm.invalid" value="Login" (click)="loginAdmin(adminLoginForm)" class="login-btn">
      </div>
  </form>
</div>
