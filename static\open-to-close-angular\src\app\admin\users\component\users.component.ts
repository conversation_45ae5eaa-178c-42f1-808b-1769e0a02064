import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { UsersService } from '@app/admin/users/service/users-services';
import { ServiceLocator } from '@app/base/components/service-locator';

declare var $;
@Component({
  selector: 'app-users',
  templateUrl: '../views/users.component.html',
  styleUrls: ['../css/users.component.css']
})
export class UsersComponent extends BaseComponent implements OnInit {

  usersService : UsersService;

  constructor() { 
    super();
    this.usersService = ServiceLocator.injector.get(UsersService);
  }

  public currentMenuId;
  public currentMenuIndex;
  public userTypesList = ['Home Buyer','Listing Agent','Brokerage','Mortgage Lender'];
  public accountStatusList = ['Unregistered', 'Free', 'Premium']

  //de/active user dialog
  public dialogTitile = '';
  public dialogMessage = '';
  public selectedUserType = '';
  public selectedAccountStatus = '';
  public selectedUserEmail = '';
  public selectedUserStatus = '';
  public disableConfirmBtn :  boolean = false;
  public selecteduserIndex;
  
  public userList = [];
  public totalRecordsCount : number;
  public itemsPerPage : number;
  public pageCount: number = 1;

  public searchUserParams = new URLSearchParams;

  public downloadCSVSubscription: any;
  
  ngOnInit() {
    this.initUserList();
    let self = this;

    $("body").addClass("admin-body");
    $('#deactiveUser').on('hidden.bs.modal', function () {
      self.selectedUserEmail = '';
      self.selectedUserStatus = '';
      self.disableConfirmBtn = false;
      self.selecteduserIndex = undefined;
    });
    
    $(document).ready(function()
    {
      $(document).mouseup(function(e)
      {
        if(self.currentMenuId != undefined && self.currentMenuIndex != undefined){
          $("#user_"+self.currentMenuIndex+"_"+self.currentMenuId).hide();
          self.currentMenuId = undefined;
          self.currentMenuIndex = undefined;
        }
      });
    });
  }

  initUserList(){
    this.usersService.getUsersList().subscribe(res =>{
      this.userList = res['result']['records'];
      this.totalRecordsCount = res['result']['total_records_count'];
      this.itemsPerPage = res['result']['items_per_page'];
    },err=>this.errorResponse(err.json()));
  }

  openMenu(index,id){
    this.currentMenuIndex = index;
    this.currentMenuId = id;
    $("#user_"+index+"_"+id).toggle();
  }

  manageUserModal(type,userId,user){
    this.selecteduserIndex = this.userList.indexOf(user);
    this.selectedUserEmail = userId;
    $("#deactiveUser").modal("show");
    if(type == 'active'){
      this.dialogTitile = 'Activate user';
      this.dialogMessage = 'Are you sure you want to Activate this user?';
      this.selectedUserStatus = 'active';
    }
    else if(type == 'deActive'){
      this.dialogTitile = 'Deactivate user';
      this.dialogMessage = 'Are you sure you want to Deactivate this user?';
      this.selectedUserStatus = 'deActive';
    }
  }

  manageUser(){
    if(this.selectedUserEmail != '' && this.selectedUserStatus != ''){
      let manageUserParams = new URLSearchParams;
      if(this.selectedUserStatus == 'deActive'){
        manageUserParams.set('is_active','false');
      }
      else if(this.selectedUserStatus == 'active'){
        manageUserParams.set('is_active','true');
      }
      manageUserParams.set('email',this.selectedUserEmail);
      this.disableConfirmBtn = true;
      this.usersService.manageUserAccount(manageUserParams).subscribe(res =>{
        this.successResponse(res);
        this.disableConfirmBtn = false;
        $("#deactiveUser").modal("hide");
        if(this.selecteduserIndex != undefined){
          if(this.userList[this.selecteduserIndex]['is_active'] == false){
            this.userList[this.selecteduserIndex]['is_active'] = true;
          }
          else if(this.userList[this.selecteduserIndex]['is_active'] == true){
            this.userList[this.selecteduserIndex]['is_active'] = false;
          }
        }
      },err=>{
        this.errorResponse(err.json());
      });
    }
  }

  setUserType(type){
    this.selectedUserType = type;
  }

  setAccountStatus(type){
    this.selectedAccountStatus = type;
  }

  searchUser(userName,email){
    if(userName != ''){
      this.searchUserParams.set('user_name',userName);
    }
    else{
      this.searchUserParams.delete('user_name');
    }
    if(email != ''){
      this.searchUserParams.set('user_email',email.toString());
    }
    else{
      this.searchUserParams.delete('user_email');
    }

    if(this.selectedUserType != ''){
      var user_type = '';
      if(this.selectedUserType == 'Home Buyer'){
        user_type = 'HB'
      }
      else if(this.selectedUserType == 'Listing Agent'){
        user_type = 'LA'
      }
      else if(this.selectedUserType == 'Brokerage'){
        user_type = 'BR'
      }
      else if(this.selectedUserType == 'Mortgage Lender'){
        user_type = 'ML'
      }
      this.searchUserParams.set('user_role',user_type);
    }
    else{
      this.searchUserParams.delete('user_role');
    }

    if(this.selectedAccountStatus != ''){
      let account_status = this.selectedAccountStatus
      this.searchUserParams.set('account_status', account_status);
    }
    else{
      this.searchUserParams.delete('account_status');
    }
    this.getPageChange(1);
  }

  downloadCSV(userName, email){
    if(userName != ''){
      this.searchUserParams.set('user_name',userName);
    }
    else{
      this.searchUserParams.delete('user_name');
    }
    if(email != ''){
      this.searchUserParams.set('user_email',email.toString());
    }
    else{
      this.searchUserParams.delete('user_email');
    }

    if(this.selectedUserType != ''){
      var user_type = '';
      if(this.selectedUserType == 'Home Buyer'){
        user_type = 'HB'
      }
      else if(this.selectedUserType == 'Listing Agent'){
        user_type = 'LA'
      }
      else if(this.selectedUserType == 'Brokerage'){
        user_type = 'BR'
      }
      else if(this.selectedUserType == 'Mortgage Lender'){
        user_type = 'ML'
      }
      this.searchUserParams.set('user_role',user_type);
    }
    else{
      this.searchUserParams.delete('user_role');
    }

    if(this.selectedAccountStatus != ''){
      let account_status = this.selectedAccountStatus
      this.searchUserParams.set('account_status', account_status);
    }
    else{
      this.searchUserParams.delete('account_status');
    }
    this.downloadCSVFile();
  }

  downloadCSVFile() {
    if(this.downloadCSVSubscription){
      this.downloadCSVSubscription.unsubscribe();
    }
    this.downloadCSVSubscription = this.usersService.downloadCSV(this.searchUserParams).subscribe(res=>{
      this.downloadFile(res,"users.csv");
    },err => this.errorResponse(err.json()));
  }

  getPageChange(pageNo){
    this.pageCount = pageNo;
    this.searchUserParams.set('page_no',pageNo);
    this.usersService.filterUserSearch(this.searchUserParams).subscribe(res =>{
      this.userList = res['result']['records'];
      this.totalRecordsCount = res['result']['total_records_count'];
      this.itemsPerPage = res['result']['items_per_page'];
    },err=>{
      this.errorResponse(err.josn());
    });
  }


  resendUserConfirmationEmail(userEmail){
    let userParams = new URLSearchParams;
    userParams.set("email",userEmail);
    this.usersService.reSendUserConfiEmail(userParams).subscribe(res =>{
      this.successResponse(res);
    },err=>{
      this.errorResponse(err.json());
    });
  }

  resetUserPassword(userEmail){
    var email = {
      "email" : userEmail
    }

    this.usersService.sendforgotPasswordEmail(email).subscribe(res =>{
      this.successResponse(res);
    },err=>{
      this.errorResponse(err.json());
    });
  }

  impersonateUser(userEmail){
    let impersonateParams = new URLSearchParams;
    impersonateParams.set("email",userEmail)
    this.usersService.getImpersonateUserToken(impersonateParams).subscribe(res =>{
      this.setUserToken(res['result']['token']);
      let userType = this.userTypes.filter(item => item.code === res['result']['user_type']);
      localStorage.setItem('userType',userType[0]['userType']);
      window.open(window.location.origin);
    },err=>{
      this.errorResponse(err.json());
    });
  }

  viewUserHelpdeskTickets(userId){
  } 

}
