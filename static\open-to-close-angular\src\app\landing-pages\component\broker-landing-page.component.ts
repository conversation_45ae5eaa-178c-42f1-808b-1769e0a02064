import { Component, OnInit, ViewChild } from '@angular/core';

import { Subscription } from 'rxjs';

import { BaseComponent } from '@app/base/components/base.component';
import { HeaderComponent } from '@app/root/components/header.component';

import { GetPlansService } from '@app/base/services/get.plans.service';
import { ServiceLocator } from '@app/base/components/service-locator';

declare var $;

@Component({
  selector: 'broker-landing-page',
  templateUrl: '../views/broker-landing-page.component.html',
  styleUrls: ['../css/landing-page.component.css']
})
export class BrokerLandingPageComponent extends BaseComponent implements OnInit {

  @ViewChild(HeaderComponent) headerComponent: HeaderComponent;

  public getPlansService: GetPlansService;

  public plansAPISubscription: Subscription;

  public plansList = [];

  constructor() {
    super();
    this.getPlansService = ServiceLocator.injector.get(GetPlansService);
  }

  ngOnInit() {

    $(document).ready(function ($) {
      $(this).scrollTop(0);
    });

    this.getPlans();
  }

  public openSignUpModal(): void {
    this.headerComponent.brokerSignup = true;
    this.headerComponent.agentList = false;
    this.headerComponent.estateAgent = true;
    this.headerComponent.showAgent = false;
    this.headerComponent.showEstateAgent = true;
    this.headerComponent.isTitleSelected = true;
    this.headerComponent.estateProfessionalRegistration = true;
    this.headerComponent.homeBuyerRegistration = false;
    this.headerComponent.agentType = 'Broker';
    $('#authModal').modal('show');
    $('#authModal .modal-body .nav-pills a:eq(' + 1 + ')').tab('show');
  }

  getPlans(): any {
    if (this.plansList.length <= 0) {
      if (this.plansAPISubscription) {
        this.plansAPISubscription.unsubscribe();
      }
      let urlParams : URLSearchParams = new URLSearchParams();
      urlParams.set("user_type", "BR");
      this.plansAPISubscription = this.getPlansService.getPlansByUserType(urlParams).subscribe(res => {
        let tempList = [];
        this.plansList = res.result.filter(plan => {
          tempList.push(plan);
          return tempList;
        });
      });
    }
  }

}
