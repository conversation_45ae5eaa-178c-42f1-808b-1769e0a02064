import { Directive, ElementRef, Output, EventEmitter, HostListener, Input } from '@angular/core';
import { fromEvent } from 'rxjs/observable/fromEvent';
import { throttleTime, debounceTime } from 'rxjs/operators';

@Directive({
  selector: '[appAutoComplete]'
})
export class AutoCompleteDirective {

  // Execute method after event emit.
  @Output() OnTypeCompleteMethod = new EventEmitter<any>();

  constructor(public elementRef: ElementRef) {
    fromEvent(this.elementRef.nativeElement, 'keyup').pipe(debounceTime(500)).pipe(throttleTime(500))
      .subscribe(scrollEvent => {
        console.log('event executing....');
        this.OnTypeCompleteMethod.emit(null);
      });
  } 


}
