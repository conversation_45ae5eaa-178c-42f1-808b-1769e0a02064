export class Profile{
    name: string;
    contact_name: string;
    phone: string;
    brokerage_id:any;
    email_preferences: string[];
    office_phone:String;
    state: AddressId;
    city: AddressId;
    zip_code: AddressId;
    zipcode: AddressId;
    address: string;
    status: string;
    agent:Agent = new Agent();
    agent_id:any;
    agent_name:String;
    agent_brokerage_name:String;
    agent_profile_photo:String;
    agent_user_id : String;
    profile_photo: any;
    // agent_name: string;
    // agent_brokerage_name: string;   
}

export class AddressId{
    id: number;
}

export class State{
    id: number;
    name: string;
    country: number;
}

export class City{
    id: number;
    name: string;
    state: number;
}

export class ZipCode{
    id: number;
    code: string;
    city: number;
}
export class Agent{
    id:any;
    agent_id:any;
    agent_name: string;
    agent_brokerage_name: string;
}

export class ProfileResponse{
    result: Profile;
    message: String;
    status: any;
    statusCode: any;
}