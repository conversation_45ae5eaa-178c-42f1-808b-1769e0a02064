import { Component, OnInit } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { AuthService } from '@app/auth/services/auth.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { On<PERSON><PERSON>roy } from '@angular/core/src/metadata/lifecycle_hooks';

@Component({
  selector: 'verification',
  templateUrl: '../views/verification.html',
  styleUrls: ['../css/auth.component.css']
})
export class VerificationComponent extends BaseComponent  implements OnInit, OnDestroy {
    
    public authService:AuthService;
    private paramsSubscription;

    constructor(){
        super();
        this.authService=ServiceLocator.injector.get(AuthService);
    }

    ngOnInit() : void{

        this.paramsSubscription = this.route.queryParams.subscribe(params=>{                
            this.authService.verification(params['token']).subscribe(res =>{                        
                this.sucMessageResponse('Account Verified Successfully');
                this.router.navigateByUrl('/');

            },err =>{              
                this.errorResponse(err.json()); 
                this.router.navigateByUrl('/');                
            });
        });
    }

    ngOnDestroy(): void {
        this.paramsSubscription.unsubscribe();
    }
}