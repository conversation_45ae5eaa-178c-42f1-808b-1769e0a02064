import { Injectable, Input, EventEmitter } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ApiService } from '@app/base/services/api.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';

@Injectable()
export class EventManagerService {

    public baseservice: BaseComponent;
    public apiService: ApiService;
    @Input()
    public updateRunningEvent: EventEmitter<any> = new EventEmitter<any>();

    constructor() {
        this.baseservice = ServiceLocator.injector.get(BaseComponent);
        this.apiService = ServiceLocator.injector.get(ApiService);
    }

    getEventManagerList(eventTypeParams): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'], API_REFERENCE['property']['getEventManagerList'], {}, eventTypeParams.toString());
        return this.apiService.apiCall(options);
    }

    cancelEvent(eventTypeParams): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['DELETE'], API_REFERENCE['property']['cancelEvent'], eventTypeParams.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    updateEvent(eventParams): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'], API_REFERENCE['property']['updatePropertyEvent'], eventParams.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    addPropertyToFavorite(property): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'], API_REFERENCE['property']['saveToFavorite'], property.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    acceptEvent(eventTypeParams): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'], API_REFERENCE['property']['acceptRequest'], eventTypeParams.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    getRunEventDetails(eventParams): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'], API_REFERENCE['eventManager']['getRunEventDetail'], {}, eventParams.toString());
        return this.apiService.apiCall(options);
    }

    addToCheckIn(checkInParams): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'], API_REFERENCE['eventManager']['addToCheckIn'], checkInParams.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    getEventChatThread(eventParams): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'], API_REFERENCE['eventManager']['getEventChatThread'], {}, eventParams.toString());
        return this.apiService.apiCall(options);
    }

    getEventChatUserList(eventParams): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'], API_REFERENCE['eventManager']['getUserList'], {}, eventParams.toString());
        return this.apiService.apiCall(options);
    }

    checkInWithEmail(user) {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'], API_REFERENCE['eventManager']['checkInWithEmail'], user.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    endOpenHouse(openHouseParams) {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'], API_REFERENCE['eventManager']['endOpenHouse'], openHouseParams.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    addToMyLeads(propertyRatesParams) {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'], API_REFERENCE['myLeads']['leadAdd'], propertyRatesParams.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    runEvent(event): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'], API_REFERENCE['property']['runEvent'], event.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    exportLeads(event): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'], API_REFERENCE['myLeads']['exportCSV'], event.toString(), null, null, null, false, true);
        return this.apiService.downloadFile(options);
    }


    addNewNote(params): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'], API_REFERENCE['myLeads']['addNewNote'], params.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    updateNote(params): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'], API_REFERENCE['myLeads']['updateNote'], params.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    deleteNote(params): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['DELETE'], API_REFERENCE['myLeads']['deleteNote'], params.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }

    getMyNotes(id): Observable<any> {
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['GET'], API_REFERENCE['myLeads']['getNote'] + '?lead_id=' + id, {});
        return this.apiService.apiCall(options);
    }
}