<div class="header">
    <header></header>
</div>
<div  class="homeBuyer-profile">
    <div class="new_profiles">
        <div class="new_profile_title">
            <img src="{{fileUrl}}" class="new_symbols-avatar dis_inline" alt="">
             <div class="new_title dis_inline file-upload-title">
                {{profileName}}
                 <label for="file-upload" class="file-upload-label">
                   <span class="custom-file-upload">
                     <span class="file-upload-text">Add Profile Image</span>
                   </span>
                 </label>
                 <input id="file-upload" type="file" (change)="uploadProfileImage($event)"/>
              </div>
          </div>
        <!-- <div class="row"> -->
            <div class="container-fluid">
                <div class="col-md-3"></div>
                <div class="col-md-7">
                    <div class="new_profile_group_wrap tab-margin">
                            <div class="new_profile_group dis_inline">
                               <ul class="nav nav-pills">
                                  <li class="active"><a data-toggle="pill" href="#menu1">Profile</a></li>
                                  <li><a data-toggle="pill" href="#menu2">Password</a></li>
                                  <li><a data-toggle="pill" href="#menu5">My agent</a></li>
                               </ul>
                                 <div class="tab-content">               
                                  <div id="menu1" class="tab-pane fade in active">
                                 <form [formGroup]="homeBuyerProfileForm" >                    
                                     <div class="group_1 mt-20">                   
                                        <div class="title2">Basic Information</div>
                                        <div class="new_form">
                                           <div class="new_form_group " formGroupName="profile">
                                              <div class="group new_form_label">      
                                                 <input type="text" placeholder=" " class=" width_350" formControlName="name">
                                                 <span class="highlight"></span>
                                                 <span class="bar"></span>
                                                 <label>Name </label>
                                                 <!-- <div *ngIf="homeBuyerProfileForm.controls.profile.controls.name.touched">
                                                     <p class="form-validation" *ngIf="homeBuyerProfileForm.controls.profile.controls.name.errors?.required">Enter Name</p>
                                                 </div> -->
                                              </div>
                                           </div>
                                          <div class="new_form_group " formGroupName="profile">
                                             <div class="group new_form_label">
                                                <input  type="text" maxlength="12" #phone (keyup)="validateFormat(phone.value)" class="width_350" formControlName="phone" placeholder=" " required/>
                                                <span class="highlight"></span>
                                                <span class="bar"></span>
                                                <label>Phone Number*</label>
                                                <!-- <div *ngIf="homeBuyerProfileForm.controls.profile.controls.phone.touched">
                                                      <p class="form-validation" *ngIf="homeBuyerProfileForm.controls.profile.controls.phone.errors?.required">Enter phone number</p>
                                                   <p class="form-validation" *ngIf="homeBuyerProfileForm.controls.profile.controls.phone.errors?.minlength">phone number must be 10 characters</p>
                                                   <p class="form-validation" *ngIf="homeBuyerProfileForm.controls.profile.controls.phone.errors?.maxlength">phone number must be 10 characters</p>
                                                </div> -->
                                             </div>
                                          </div>
                                           <div class="new_form_group ">
                                              <div class="group new_form_label">      
                                                 <input type="text" placeholder=" " required class=" width_350"  formControlName="email"  >
                                                 <span class="highlight"></span>
                                                 <span class="bar"></span>
                                                 <label>Email Address*</label>
                                                 <div *ngIf="homeBuyerProfileForm.controls.email.untouched">
                                                     <span></span>
                                                 </div>
                                                 <!-- <div *ngIf="homeBuyerProfileForm.controls.email.touched && homeBuyerProfileForm.controls.email.errors?.email">
                                                     <span class="form-validation">Enter valid email address</span>
                                                 </div> -->
                                              </div>
                                           </div>
                                        </div>
                                     </div>
                                     <div class="group_1 mt-20">
                                         <div class="title2">Contact Preferences</div>
                                         <div class="new_form">
                                            <div class="check_group profile_checkbox width_350 ">
                                               <div class="form_group flex_none">
                                                  <input type="checkbox" (change)="CPStatusChange('msg')" [checked]="msgCP">   <span class="checkmark"></span>
                                                  <label class="width_auto">Receive Timely Open House Notifications</label>
                                               </div>
                                               <div class="form_group">
                                                  <input type="checkbox" (change)="CPStatusChange('w_oh')" [checked]="w_ohCP">   <span class="checkmark"></span>
                                                  <label class="width_auto">Weekly Open House Notifications</label>
                                               </div>
                                            </div>
                                         </div>
                                      </div>
                                      <div class="row">
                                         <div class="col-sm-16 ">
                                             <div class="new_form_group profile_save_button ml-88">
                                             <input type="button" class="submit_button with_bg dis_inline" [ngClass]="{'submit-disable':homeBuyerProfileForm.invalid}" [disabled]="homeBuyerProfileForm.invalid" value="Submit" (click)="checkEmailIsChanged(homeBuyerProfileForm)">
                                             </div>
                                         </div>
                                     </div>
                                     </form>
                                  </div>
                 
                                  <div id="menu2" class="tab-pane fade">
                                     <div class="group_1 mt-20">
                                        <div class="title2">Change your Password</div>
                                        <div class="new_form">
                                         <form [formGroup]="profilePassword" > 
                                           <div class="new_form_group ">
                                              <div class="group new_form_label">      
                                                 <input type="password" placeholder=" " required class=" width_350" formControlName="password">
                                                 <span class="highlight"></span>
                                                 <span class="bar"></span>
                                                 <label>Old Password*</label>
                                                 <div *ngIf="profilePassword.controls.password.touched">
                                                     <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.required">Enter old password</p>
                                                     <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.minlength">Password must be 5-15 characters</p>
                                                     <p class="form-validation" *ngIf="profilePassword.controls.password.errors?.maxlength">Password must be 5-15 characters</p>
                                                 </div>
                                              </div>
                                           </div>
                                           <div class="new_form_group ">
                                              <div class="group new_form_label">      
                                                 <input type="password" placeholder=" " required class=" width_350" formControlName="new_password">
                                                 <span class="highlight"></span>
                                                 <span class="bar"></span>
                                                 <label>New Password*</label>
                                                 <div *ngIf="profilePassword.controls.new_password.touched">
                                                     <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.required">Enter new password</p>
                                                     <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.minlength">Password must be 5-15 characters</p>
                                                     <p class="form-validation" *ngIf="profilePassword.controls.new_password.errors?.maxlength">Password must be 5-15 characters</p>
                                                 </div>
                                              </div>
                                           </div>
                                           <div class="new_form_group ">
                                              <div class="group new_form_label">      
                                                 <input type="password" placeholder=" " required class=" width_350" formControlName="confirm_new_password">
                                                 <span class="highlight"></span>
                                                 <span class="bar"></span>
                                                 <label>Confirm New Password*</label>
                                                 <div *ngIf="profilePassword.controls.confirm_new_password.touched">
                                                     <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.required">Enter confirm password</p>
                                                     <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.minlength">Password must be 5-15 characters</p>
                                                     <p class="form-validation" *ngIf="profilePassword.controls.confirm_new_password.errors?.maxlength">Password must be 5-15 characters</p>
                                                 </div>
                                                 <div *ngIf="profilePassword.controls.confirm_new_password.touched">
                                                     <p class="form-validation" *ngIf="profilePassword.hasError('mismatch')">Confirm password not match</p>
                                                 </div>
                                              </div>
                                           </div>
                                           <div class="new_form_group ">
                                              <input type="submit" [ngClass]="{'submit-disable':profilePassword.invalid}" [disabled]="profilePassword.invalid" class="submit_button with_bg" value="Submit" (click)="updatePassword(profilePassword)">
                                           </div>
                                           </form>
                                        </div>
                                     </div>
                                  </div>
                 
                                  <div id="menu5" class="tab-pane fade new_profile_details">
                 
                                     <div class="group_1 mt-20">
                                        <div class="title2">Are you working with an agent?</div>
                                        <div class="new_form">
                                           <div class="check_group profile_checkbox width_350 ">
                                              <div class="form_group flex_none">                                 
                                                 <input type="checkbox" (click)="changeStatus('yes')" [disabled]="status" [checked]="status">   <span class="checkmark"></span>
                                                 <label class="width_auto">Yes, I am working with an agent</label>
                                              </div>
                                              <div class="form_group">
                                                 <input type="checkbox" (click)="changeStatus('no')" [disabled]="statusFalse" [checked]="statusFalse">   <span class="checkmark"></span>
                                                 <label class="width_auto">No, I am not working with an agent</label>
                                              </div>
                                           </div>
                                        </div>
                                     </div>
                 
                                     <div class="group_1 mt-20">
                                         <div *ngIf="showMyAgent">
                                         <span *ngIf="agetID">
                                        <div  class="title2">Your agent</div>
                                        <div  class="new_form">
                                           <div class="new_form_group ">
                                              <img src="{{agentImageUrl}}" class="new_symbols-avatar searched-agent-image dis_inline" alt="">
                                              <div class="new_details dis_inline">
                                                 <div class="title3">{{agentName}}</div>
                                                 <div class="sub_title">{{brokerName}}</div>
                                                 <div class="new_form_group ">
                                                <input type="submit" (click)="contactAgent()" class="submit_button  dis_inline" value="Contact Agent">
                                            </div>
                                              </div>
                                           </div>
                                        </div>
                                     </span>
                                         </div>
                                     </div>
                                     <div class="group_1 ">
                                        <div class="title2">Change your agent</div>
                                        <div class="new_form">
                                           <div class="new_form_group ">
                                              <div class="group new_form_label ">      
                                                 <input type="text" #name placeholder=" " required class=" width_350"  (keyup)="searchAgent(name.value)">
                                                 <i class="fa fa-search search_button"></i>
                                                 <span class="highlight"></span>
                                                 <span class="bar"></span>
                                                 <label>Agent Name or Agent ID*</label>
                                              </div>
                                           </div>
                                           <div *ngIf="agentList != false">
                                             <div class="agent_found">
                                                 {{searchAgentList.length}} agent found
                                             </div>
                                             <div [ngClass]="{'search-agent-list': searchAgentList.length > 3}">
                                                <div class="remove_agent search-agent-padding" *ngFor="let agent of searchAgentList" >
                                                    <span *ngIf="agent.profile_photo == null || agent.profile_photo == '' || agent.profile_photo == undefined" [ngClass]="{ 'remove-span' : agent.profile_photo == null || agent.profile_photo == '' || agent.profile_photo == undefined}"><img src="{{imagePrefix}}testmonial-default (1).png" class="img-responsive" alt=""></span>
                                                    <span *ngIf="agent.profile_photo != null || agent.profile_photo != undefined || agent.profile_photo != ''"><img src="{{agent.profile_photo}}" width="130px" height="130px" class="search-agent-image" alt=""></span>
                                                    <div class="remove_details">
                                                        <div *ngIf="agent.name != null" class="name">{{agent.name}}</div>
                                                        <div *ngIf="agent.name == null" class="name">{{agent.mls_agent_id}}</div>
                                                        <div class="sub_name">{{agent.brokerage_name}}</div>
                                                        <div class="remove_button select" (click)="addRemoveAgent(agent.id,'add'); name.value = ''">Add Agent</div>
                                                    </div>
                                                </div>
                                             </div>
                                            </div>
                                        </div>
                                     </div>
                                  </div>
                               </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2"></div>
            </div>
        <!-- </div> -->
     </div>
</div>

<div class="modal fade sign_modal" id="confirmEmailChangePopupHB" role="dialog">
    <div class="modal-dialog modal-md">
       <div class="modal-content">
          <div class="modal-body">
             <div class="modal_content">
                <div class=" new_account">
                   <div class="title email-confirm-email-title">Confirm email change</div>
                   <div class="email-confirm-border"></div>
                   <p class="email-confirm-p">You have chosen to change your email on your account. Please verify the email below is correct before proceeding:</p>
                   <p class="email-confirm-email">{{NewEmailAddress}}</p>
                   <p class="email-confirm-p">If you confirm, you will be logged out and a we will send you a confirmation email to verify your new email address.</p>
                </div>
                <div class="form_group cursor-pointer">                                    
                    <input type="button" class="new_form" value="Confirm" (click)="onConfirmEmailClick()"/>
                    <span class="email-confirm-cancel" data-dismiss="modal">Cancel</span>
                </div>
             </div>
          </div>
       </div>
    </div>
  </div>


<div class="footer">
    <footer></footer>
</div>