.ls {
    font-size: 40px;
    color: #FFFFFF;
    position: relative;
    letter-spacing: -1px;
    line-height: 40px;
    background: #10B8A8;
    display: inline-block;
    padding: 10px 3px !important;
    border-radius: 40px;
    top: -15px;
    height: 60px;
    width: 60px;
    text-align: center !important;
}
.po_rel_1 {
    position: relative !important;
    top: 10px !important;
}

.td-width{
    width: 200px !important;
}
.profile-single-img{
    margin-top: -55px !important;
}
.my-client-up-po_rel{
    position: relative;
    top: 0px;
    left: -12px;
}
.my-client-text-width{
    width: 112%;
}
.my-client-ck-text-width{
    width: 114%;
}
.my-client-ck-po_rel {
    position: relative;
    top: 0px;
    left: -3px;
}
.my-client-fav-po_rel {
    position: relative;
    top: 0px;
    left: -36px;
}
.my-client-fav-text-width {
    width: 127%;
}
.my-client-font{
    font-size: 16px !important;
    color: #5A5A5A !important;
    font-family: 'Source Sans Pro', sans-serif !important;
}
.time-client-w{
    width: auto;
}
.new-line{
    width: 193px !important
}

.invite-block{
    display: inline-block;
    width: 55%;
    vertical-align: top;
}
.invite-block .sub-block{
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: #737373;
}
.invite-contain{
    width: 29%;
    display: inline-block;
    line-height: 0.9em;
    text-align: start;
    margin-right: 30px;
}
.invite-contain span{
    font-size: 12px;
    line-height: 0.1em;
    text-align: left;
    font-weight: 500;
}
.invite-link{
    top: -4px;
    position: relative;
    margin: auto;
    display: inline-block;
    vertical-align: top;
    width: 50%;
}
.invite-link input{
    border: 0px !important;
    background-color: #fff !important;
    box-shadow: none !important;
    z-index: 2;
    font-weight: normal;
}
@media (min-width: 768px) and (max-width: 1023px) {
    .invite-link{
    	top: 5px;
        width: 40%;
        float: right;
	}
	.invite-contain{
        width: 60%;
        margin-right: 0px;
	}

	.invite-block{
    	width: 100%;
    }
}
@media only screen and (max-width: 767px){
    .ls_group .title_group {
        margin-left: 10px;
        position: relative;
        top: -10px;
    }
    .invite-contain{
        width: 100%;
    }
    .invite-link{
        float: left;
        top: 8px;
        width: 100%
    }
    .invite-block{
        width: 100% !important;
    }
}