import { Injectable,Input,EventEmitter} from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ApiService } from '@app/base/services/api.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';
import { ApiResponse } from '@app/auth/models/api-response';

@Injectable()
export class NotificationService {
    baseService:BaseComponent;
    apiService:ApiService;
    
    @Input()
    public setMessageDotIcon: EventEmitter<any> = new EventEmitter<any>();

    @Input()
    public setNotificatioDotIcon: EventEmitter<any> = new EventEmitter<any>();

  constructor() { 
    this.baseService=ServiceLocator.injector.get(BaseComponent);
    this.apiService=ServiceLocator.injector.get(ApiService);
  }

  getNotification():Observable<any>{
    let options=this.baseService.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['notification']['getAllNotification'], {});
    return this.apiService.apiCall(options);
  }

  getMoreNotification(index):Observable<any>{
    let options=this.baseService.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['notification']['getAllNotification']+'?page_no='+index, {});
    return this.apiService.apiCall(options);
  }
  
  checkChatMessageStatus():Observable<any>{
    let options=this.baseService.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['notification']['getChatMessageStatus'], {});
    return this.apiService.apiCall(options);
  }

  checkAnyNewNotification():Observable<any>{
    let options=this.baseService.getRequestOptions(METHOD_REFERENCE['GET'],API_REFERENCE['notification']['getNewNotificationStatus'], {});
    return this.apiService.apiCall(options);
  }

  public readNotification(id):Observable<any>{
    let options=this.baseService.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['notification']['readNotification'],id.toString(), null, null, null, false, true);
    return this.apiService.apiCall(options);
  }

}
