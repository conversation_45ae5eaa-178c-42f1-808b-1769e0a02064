// import { environment } from "../../../environments/environment";

self.addEventListener('notificationclick',clickEvent=>{
  let validUrls = /openhousesdirect.com/;
  let newUrl = "/";

  if(clickEvent['notification']['data']['FCM_MSG']['data']['notification_type'] == 'update_event'){
    newUrl = '/property-detail?propertyId='+clickEvent['notification']['data']['FCM_MSG']['data']['property_id'];
  }
  else if(clickEvent['notification']['data']['FCM_MSG']['data']['notification_type'] == 'chat'){
    newUrl = 'messaging';
  }
  else if(clickEvent['notification']['data']['FCM_MSG']['data']['notification_type'] == 'create_event'){
    newUrl = '/property-detail?propertyId='+clickEvent['notification']['data']['FCM_MSG']['data']['property_id'];
  }
  else if(clickEvent['notification']['data']['FCM_MSG']['data']['notification_type'] == 'cancel_event'){
    newUrl = '/property-detail?propertyId='+clickEvent['notification']['data']['FCM_MSG']['data']['property_id'];
  }
  else if(clickEvent['notification']['data']['FCM_MSG']['data']['notification_type'] == 'event_run_tool'){
    if(clickEvent['notification']['data']['FCM_MSG']['data']['show_rate_card'] == 'true'){
      newUrl = '/my-open-houses/my-open-houses-list?event_id='+clickEvent['notification']['data']['FCM_MSG']['data']['event_id'];
    }
    else{
      newUrl = 'event-manager/run-event-manager?eventId='+clickEvent['notification']['data']['FCM_MSG']['data']['event_id'];
    }
  }
  else if(clickEvent['notification']['data']['FCM_MSG']['data']['notification_type'] == 'event_ended'){
    newUrl = '/property-detail?propertyId='+clickEvent['notification']['data']['FCM_MSG']['data']['property_id'];
  }
  else if(clickEvent['notification']['data']['FCM_MSG']['data']['notification_type'] == 'auto_checkin'){
    newUrl = '/my-open-houses/my-open-houses-list?event_id='+clickEvent['notification']['data']['FCM_MSG']['data']['event_id'];
  }
  else if(clickEvent['notification']['data']['FCM_MSG']['data']['notification_type'] == 'search_property'){
    newUrl = '/property-detail?propertyId='+clickEvent['notification']['data']['FCM_MSG']['data']['property_id'];
  }

  clickEvent.waitUntil(
    clients.matchAll({
      includeUncontrolled: true,
        type: 'window'
    })
    .then(function(windowClients) {
      if(clients.openWindow) {
        return clients.openWindow(newUrl);
      }
    })
  );
});

importScripts('https://www.gstatic.com/firebasejs/4.8.1/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/4.8.1/firebase-messaging.js');

firebase.initializeApp({
  messagingSenderId: '36165785854'
});

const messaging = firebase.messaging();
