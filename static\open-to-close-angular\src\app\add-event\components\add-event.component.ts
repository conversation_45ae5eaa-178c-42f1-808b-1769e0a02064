import { Component, OnInit, EventEmitter, Output, ViewChild, ElementRef } from "@angular/core";
import {
  Event,
  EventError,
  PropertyEvent,
} from "@app/property-detail/models/event.model";
import { BaseComponent } from "@app/base/components/base.component";
import { Agent } from "@app/profile/models/agent";
import * as moment from "moment";
import { AddEventService } from "@app/add-event/service/add-event-services";
import { ServiceLocator } from "@app/base/components/service-locator";
import { AuthService } from "@app/auth/services/auth.service";
import { PurchaseService } from "@app/purchase/service/purchase.service";
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';

declare var $;

@Component({
  selector: "add-event",
  templateUrl: "../views/add-event.component.html",
  styleUrls: ["../css/add-event.component.css"],
})
export class AddEventComponent extends BaseComponent implements OnInit {
  @Output() addEventResponse = new EventEmitter<any>();


  public event: Event = new Event();
  public eventError: EventError = new EventError();
  public selectedEventObj: PropertyEvent = new PropertyEvent();
  public authService: AuthService;
  public addEventService: AddEventService;
  public Editor = ClassicEditor;

  public timeList = [
    { key: "00:00:00", value: "12:00 AM" },
    { key: "00:30:00", value: "12:30 AM" },
    { key: "01:00:00", value: "01:00 AM" },
    { key: "01:30:00", value: "01:30 AM" },
    { key: "02:00:00", value: "02:00 AM" },
    { key: "02:30:00", value: "02:30 AM" },
    { key: "03:00:00", value: "03:00 AM" },
    { key: "03:30:00", value: "03:30 AM" },
    { key: "04:00:00", value: "04:00 AM" },
    { key: "04:30:00", value: "04:30 AM" },
    { key: "05:00:00", value: "05:00 AM" },
    { key: "05:30:00", value: "05:30 AM" },
    { key: "06:00:00", value: "06:00 AM" },
    { key: "06:30:00", value: "06:30 AM" },
    { key: "07:00:00", value: "07:00 AM" },
    { key: "07:30:00", value: "07:30 AM" },
    { key: "08:00:00", value: "08:00 AM" },
    { key: "08:30:00", value: "08:30 AM" },
    { key: "09:00:00", value: "09:00 AM" },
    { key: "09:30:00", value: "09:30 AM" },
    { key: "10:00:00", value: "10:00 AM" },
    { key: "10:30:00", value: "10:30 AM" },
    { key: "11:00:00", value: "11:00 AM" },
    { key: "11:30:00", value: "11:30 AM" },
    { key: "12:00:00", value: "12:00 PM" },
    { key: "12:30:00", value: "12:30 PM" },
    { key: "13:00:00", value: "01:00 PM" },
    { key: "13:30:00", value: "01:30 PM" },
    { key: "14:00:00", value: "02:00 PM" },
    { key: "14:30:00", value: "02:30 PM" },
    { key: "15:00:00", value: "03:00 PM" },
    { key: "15:30:00", value: "03:30 PM" },
    { key: "16:00:00", value: "04:00 PM" },
    { key: "16:30:00", value: "04:30 PM" },
    { key: "17:00:00", value: "05:00 PM" },
    { key: "17:30:00", value: "05:30 PM" },
    { key: "18:00:00", value: "06:00 PM" },
    { key: "18:30:00", value: "06:30 PM" },
    { key: "19:00:00", value: "07:00 PM" },
    { key: "19:30:00", value: "07:30 PM" },
    { key: "20:00:00", value: "08:00 PM" },
    { key: "20:30:00", value: "08:30 PM" },
    { key: "21:00:00", value: "09:00 PM" },
    { key: "21:30:00", value: "09:30 PM" },
    { key: "22:00:00", value: "10:00 PM" },
    { key: "22:30:00", value: "10:30 PM" },
    { key: "23:00:00", value: "11:00 PM" },
    { key: "23:30:00", value: "11:30 PM" },
  ];

  eventTypesList: any;

  eventType: any = [
    { key: "OH", value: "Open House" },
    { key: "BO", value: "Broker Open" },
  ];
  eventTypes: any = [
    { key: "OH", value: "Open House" },
    { key: "BO", value: "Broker Open" },
    // { key: "AO", value: "72 Hour Home Sale" },
  ];
  public plansList = [];

  public currentUserId = "";
  public meAgentType: boolean = false;
  public brAgentType: boolean = false;
  public saAgentType: boolean = false;
  public showAddEventBtn: Boolean = false;
  public searchAgentList: Agent[] = [];
  public closeBtn: boolean = false;
  public disabledEvent: boolean = false;
  public upComing: Boolean = false;
  public onlyShowSpecificAgent = false;
  public startTime: any;
  public endTime: any;
  public event_Type: any;
  public timeError: String;
  public propertyId = "";
  public todayDate: Date;
  public momentTodayDate;

  public paidLA: boolean = false;
  public isDescriptionEditable: boolean = false;

  purchaseService: PurchaseService;

  agentPlansList = this.agentPlans;
  currentUserType: any;
  public isAllowEventSharing: boolean = true;

  constructor() {
    super();
    this.addEventService = ServiceLocator.injector.get(AddEventService);
    this.authService = ServiceLocator.injector.get(AuthService);
    this.purchaseService = ServiceLocator.injector.get(PurchaseService);
  }

  async ngOnInit() {
    var data = await this.getUser();
    if (BaseComponent.user != undefined) {
      if (BaseComponent.user.user_type == "LA") {
        if (BaseComponent.user.is_paid_account) {
          this.paidLA = true;
        }
      }
    }

    //TODO: revisar que pedo con la edicion de uno pasado
    this.isDescriptionEditable = this.paidLA;

    this.todayDate = new Date();
    this.momentTodayDate = moment(this.todayDate).format("MM/DD/YYYY");

    var self = this;

    $("#manageEvent").on("hidden.bs.modal", function () {
      self.event = new Event();
      self.eventError = new EventError();
      self.timeError = "";
      self.event.updatedDate = "";
      self.disabledEvent = false;
      self.closeBtn = false;
      self.upComing = false;
      self.showAddEventBtn = false;
      self.selectedEventObj = new PropertyEvent();
      self.startTime = undefined;
      self.endTime = undefined;
      self.meAgentType = false;
      self.brAgentType = false;
      self.saAgentType = false;
      self.searchAgentList = [];
      $("#manageEventDatePicker").datepicker("setDate", self.momentTodayDate);
    });

    $("#manageEventDatePicker")
      .datepicker({
        autoclose: true,
        format: "mm/dd/yyyy",
      })
      .on("changeDate", function (e) {
        let date = e.target.value;
        if (date != "") {
          self.selectedEventObj.updatedDate = date;
          let dateFormat = moment(date, "MM/DD/YYYY");
          self.selectedEventObj.event_date = dateFormat.format("YYYY-MM-DD");
        } else {
          self.selectedEventObj.updatedDate = this.momentTodayDate;
        }
      });

    if (BaseComponent.user != undefined) {
      if (
        BaseComponent.user.user_type == "LA" ||
        BaseComponent.user.user_type == "BR"
      ) {
        this.currentUserType = BaseComponent.user.user_type;
        if (
          BaseComponent.user.user_type == "LA" &&
          BaseComponent.user.is_broker_paid_account &&
          BaseComponent.user.is_connected_with_broker
        )
          this.isAllowEventSharing = true;
      } else if (
        BaseComponent.user.user_type == "BR" &&
        BaseComponent.user.is_paid_account
      ) {
        this.isAllowEventSharing = true;
      }
    }


    if (BaseComponent.user != undefined) {
      if (BaseComponent.user.user_type == "LA") {
        if (BaseComponent.user.is_paid_account) {
          this.eventTypesList = this.eventTypes;
        } else {
          this.eventTypesList = this.eventType;
        }
      }
    }
  }

  async getUser() {
    return new Promise(resolve => {
      try{
      // check local storage userType
        if (localStorage.getItem("userType")) {
          return this.authService.getUserDetails().subscribe(async res => {
            if(res){
              BaseComponent.user = res.result;
            }
            resolve(true);
          });
        } else {
          resolve(true);
        }
      }catch(ex) {
        resolve(true);
      }
    })
  }

  manageEventDetailView(
    event: PropertyEvent = new PropertyEvent(),
    eventType: string = ""
  ) {
    this.timeError = "";

    let eventCopy: PropertyEvent = JSON.parse(JSON.stringify(event));

    let eventDateStartTimeFormat = moment(
      eventCopy.date + " " + eventCopy.start_time
    ).format("YYYY-MM-DD HH:mm:ss");
    let eventDateEndTimeFormat = moment(
      eventCopy.date + " " + eventCopy.end_time
    ).format("YYYY-MM-DD HH:mm:ss");

    if (BaseComponent.user != undefined) {
      this.currentUserId = BaseComponent.user.id;
    }
    this.showAddEventBtn = false;
    if (eventType == "past") {
      this.closeBtn = true;
      this.disabledEvent = true;
      this.upComing = true;
    } else if (eventType == "") {
      // this.currentUserId == eventCopy.open_house_agent_id
      if (eventCopy.is_running != true) {
        this.closeBtn = false;
        this.upComing = true;

        let startTime, endTime;
        if (eventCopy.is_listhub_event) {
          startTime = this.timeList.filter(
            (time) =>
              time.key == moment(eventDateStartTimeFormat).format("HH:mm:ss")
          );
          endTime = this.timeList.filter(
            (time) =>
              time.key == moment(eventDateEndTimeFormat).format("HH:mm:ss")
          );
        } else {
          startTime = this.timeList.filter(
            (time) => time.key == this.getUTCToLocalTime(eventDateEndTimeFormat)
          );
          endTime = this.timeList.filter(
            (time) => time.key == this.getUTCToLocalTime(eventDateEndTimeFormat)
          );
        }

        this.startTime = startTime[0];
        this.endTime = endTime[0];
      } else {
        this.closeBtn = true;
        this.disabledEvent = true;
        this.upComing = true;
      }
    }

    this.selectedEventObj = eventCopy;
    console.log('Envent Details: ' , eventCopy)

    // this.getStaticEventDescription(this.selectedEventObj.event_type);

    if (eventCopy.event_agent_type == "ME") {
      this.meAgentType = true;
      this.brAgentType = false;
      this.saAgentType = false;
    } else if (eventCopy.event_agent_type == "BG") {
      this.brAgentType = true;
      this.saAgentType = false;
      this.meAgentType = false;
    } else if (eventCopy.event_agent_type == "SA") {
      this.saAgentType = true;
      this.brAgentType = false;
      this.meAgentType = false;
      this.onlyShowSpecificAgent = true;
      this.selectedEventObj.agent_id = eventCopy.open_house_agent_id;
    }

    // $("#manageEvent").modal("show");
    this.showModalEvent();
    if (eventCopy.is_listhub_event) {
      this.selectedEventObj.event_date = moment(
        eventDateStartTimeFormat
      ).format("YYYY-MM-DD");
      this.selectedEventObj.updatedDate = moment(
        eventDateStartTimeFormat
      ).format("MM/DD/YYYY");
      this.selectedEventObj.start_time = moment(
        eventDateStartTimeFormat
      ).format("HH:mm:ss");
      this.selectedEventObj.end_time = moment(eventDateEndTimeFormat).format(
        "HH:mm:ss"
      );
    } else {
      this.selectedEventObj.event_date = moment
        .utc(eventDateStartTimeFormat)
        .local()
        .format("YYYY-MM-DD");
      this.selectedEventObj.updatedDate = moment
        .utc(eventDateStartTimeFormat)
        .local()
        .format("MM/DD/YYYY");
      this.selectedEventObj.start_time = this.getUTCToLocalTime(
        eventDateStartTimeFormat
      );
      this.selectedEventObj.end_time = this.getUTCToLocalTime(
        eventDateEndTimeFormat
      );
      $("#manageEventDatePicker").datepicker("setDate", this.selectedEventObj.updatedDate);
    }
  }

  setEventAgentType(eventType, value) {
    if (eventType == "ME") {
      this.meAgentType = true;
      this.brAgentType = false;
      this.saAgentType = false;
      this.searchAgentList = [];
    } else if (eventType == "BG") {
      this.meAgentType = false;
      this.brAgentType = true;
      this.saAgentType = false;
      this.searchAgentList = [];
    } else if (eventType == "SA") {
      this.meAgentType = false;
      this.brAgentType = false;
      if (this.saAgentType == false) {
        this.saAgentType = true;
        this.onlyShowSpecificAgent = false;
      } else {
        this.saAgentType = false;
        this.onlyShowSpecificAgent = true;
      }
    }
    this.selectedEventObj.event_agent_type = eventType;

    if (value.target.checked == false) {
      this.selectedEventObj.event_agent_type = undefined;
    } else {
      this.eventError.event_agent_type_error = "";
    }
  }

  updateEvent(eventAction) {
    if (eventAction == "CANCEL") {
      $("#manageEvent").modal("hide");
    } else if (eventAction == "UPDATE") {
      let dateWithStartTime = moment(
        this.selectedEventObj.event_date +
          " " +
          this.selectedEventObj.start_time
      );
      let dateWithEndTime = moment(
        this.selectedEventObj.event_date + " " + this.selectedEventObj.end_time
      );

      var utcUpdatedStartDateTime = this.getUTCTime(dateWithStartTime);
      var utcUpdatedEndDateTime = this.getUTCTime(dateWithEndTime);
      var updatedDate = this.addUpdateEventDateFormat(dateWithStartTime);

      if (this.selectedEventObj.is_listhub_event == false) {
        utcUpdatedStartDateTime = this.getUTCTime(dateWithStartTime);
        utcUpdatedEndDateTime = this.getUTCTime(dateWithEndTime);
        updatedDate = this.addUpdateEventDateFormat(dateWithStartTime);
      } else if (this.selectedEventObj.is_listhub_event == true) {
        utcUpdatedStartDateTime = moment(dateWithStartTime).format("HH:mm:ss");
        utcUpdatedEndDateTime = moment(dateWithEndTime).format("HH:mm:ss");
        updatedDate = moment(dateWithStartTime).format("YYYY-MM-DD");
      }

      if (this.validateEvent()) {
        if (this.selectedEventObj.event_agent_type == "ME") {
          this.selectedEventObj.agent_id = BaseComponent.user.id;
        }
        let eventUrlParams = new URLSearchParams();
        eventUrlParams.set("property_id", this.selectedEventObj.property_id);
        eventUrlParams.set("date", updatedDate);
        this.selectedEventObj.event_type = this.event_Type || this.selectedEventObj.event_type;
        eventUrlParams.set("event_type", this.selectedEventObj.event_type);
        // eventUrlParams.set("event_type", this.event_Type);
        this.eventTypes.map(item => {
          if(item.key === this.event_Type){
            eventUrlParams.set("event_type_msg", item.value);
          }
        })
        eventUrlParams.set("start_time", utcUpdatedStartDateTime);
        eventUrlParams.set("end_time", utcUpdatedEndDateTime);
        eventUrlParams.set("description", this.selectedEventObj.description);
        eventUrlParams.set(
          "event_agent_type",
          this.selectedEventObj.event_agent_type
        );
        eventUrlParams.set("agent_id", this.selectedEventObj.agent_id);
        eventUrlParams.set("id", this.selectedEventObj.event_id);

        this.addEventService.updateEvent(eventUrlParams).subscribe(
          (res) => {
            if (this.selectedEventObj.event_agent_type == "ME") {
              this.selectedEventObj.open_house_agent_image = BaseComponent.user.profile_photo.toString();
              this.selectedEventObj.open_house_agent_name = BaseComponent.user.name.toString();
              this.selectedEventObj.open_house_agent_id = BaseComponent.user.id;
            }
            this.selectedEventObj.event_date = updatedDate;
            this.selectedEventObj.event_type = this.event_Type;
            this.eventTypes.map(item => {
              if(item.key === this.event_Type){
                this.selectedEventObj.event_type_msg = item.value;
              }
            })
            this.selectedEventObj.start_time = utcUpdatedStartDateTime;
            this.selectedEventObj.end_time = utcUpdatedEndDateTime;
            this.selectedEventObj["is_updated"] = true;
            this.addEventResponse.emit(this.selectedEventObj);
            this.successResponse(res);
            $("#manageEvent").modal("hide");
            this.ngOnInit();
          },
          (err) => {
            this.errorResponse(err.json());
          }
        );
      }
    }
  }

  setAgentId(agent, value) {
    if (value.target.checked == true) {
      this.selectedEventObj.agent_id = agent.agent_id;
      this.selectedEventObj.open_house_agent_image = agent.profile_photo;
      this.selectedEventObj.open_house_agent_name = agent.name;
      this.selectedEventObj.open_house_agent_id = agent.agent_id;
      this.eventError.agent_id_error = "";
    } else {
      this.selectedEventObj.agent_id = undefined;
    }
  }

  searchAgent(name) {
    if (name.trim() != "") {
      let searchUrlParams = new URLSearchParams();
      searchUrlParams.set("mls_agent_id_or_name", name);
      searchUrlParams.set("show_unregister", "true");
      this.authService.searchBrokerageAgent(searchUrlParams).subscribe(
        (res) => {
          this.searchAgentList = res["result"];
        },
        (err) => {
          this.searchAgentList = [];
        }
      );
    }
  }

  isValidStartTime(time) {
    this.eventError.start_time_error = "";
    this.startTime = time;
    if (this.endTime != undefined) {
      this.isValidEndTime(this.endTime);
    }
  }

  isValidEndTime(time) {
    this.eventError.end_time_error = "";
    this.endTime = time;
    if (this.startTime != undefined && this.endTime != undefined) {
      var startTime = this.startTime.key;
      var endTime = this.endTime.key;
      var regExp = /(\d{1,2})\:(\d{1,2})\:(\d{1,2})/;
      if (
        parseInt(endTime.replace(regExp, "$1$2$3")) <=
        parseInt(startTime.replace(regExp, "$1$2$3"))
      ) {
        this.timeError = "please select valid start time and end time";
      } else {
        this.timeError = "";
      }
    }
  }

  eventModalValidation() {
    if (
      this.eventError.agent_id_error == "" &&
      this.eventError.date_error == "" &&
      this.eventError.description_error == "" &&
      this.eventError.end_time_error == "" &&
      this.eventError.event_agent_type_error == "" &&
      this.eventError.event_type_error == "" &&
      this.eventError.property_id_error == "" &&
      this.eventError.start_time_error == "" &&
      this.timeError == ""
    ) {
      return false;
    } else {
      return true;
    }
  }

  validateEvent() {
    let isValid = true;

    if (this.selectedEventObj.event_date == undefined) {
      this.eventError.date_error = "Please select event date";
      isValid = false;
    } else {
      isValid = isValid && isValid && true;
      this.eventError.date_error = "";
    }

    if (this.selectedEventObj.event_type == undefined) {
      this.eventError.event_type_error = "Please select event type";
      isValid = false;
    } else {
      isValid = isValid && true;
      this.eventError.event_type_error = "";
    }

    if (this.selectedEventObj.start_time == undefined) {
      this.eventError.start_time_error = "Please select start time";
      isValid = false;
    } else {
      isValid = isValid && true;
      this.eventError.start_time_error = "";
    }

    if (this.selectedEventObj.end_time == undefined) {
      this.eventError.end_time_error = "Please select end time";
      isValid = false;
    } else {
      isValid = isValid && true;
      this.eventError.end_time_error = "";
    }

    if (this.selectedEventObj.start_time == this.event.end_time) {
      this.timeError = "please select valid start time and end time";
      isValid = false;
    } else {
      isValid = isValid && true;
      this.timeError = "";
    }

    if (this.selectedEventObj.event_agent_type == undefined) {
      this.eventError.event_agent_type_error = "Please select event agent type";
      isValid = false;
    } else {
      isValid = isValid && true;
      this.eventError.event_agent_type_error = "";
    }

    if (
      this.selectedEventObj.description == undefined ||
      this.selectedEventObj.description == ""
    ) {
      this.eventError.description_error = "Please add description";
      isValid = false;
    } else {
      isValid = isValid && true;
      this.eventError.description_error = "";
    }

    if (this.saAgentType == true) {
      if (this.selectedEventObj.agent_id == undefined) {
        this.eventError.agent_id_error = "Please select agent";
        isValid = false;
      } else {
        isValid = isValid && true;
        this.eventError.agent_id_error = "";
      }
    }
    return isValid;
  }

  openAddEventModel(property, event) {
    // $("#manageEvent").modal("show");
    this.showModalEvent();
    this.closeBtn = false;
    this.upComing = false;
    this.disabledEvent = false;
    this.meAgentType = false;
    this.brAgentType = false;
    this.saAgentType = false;
    this.propertyId = property;
    this.showAddEventBtn = true;
    if(!event.description){
      event.description = '';
    }
    this.selectedEventObj = event;
    this.selectedEventObj.updatedDate = this.momentTodayDate;
    this.selectedEventObj.event_date = moment(new Date()).format("YYYY-MM-DD");
    // this.selectedEventObj.start_time = this.timeList[16]['key'];
  }

  addPropertyEvent() {
    if (this.timeError == "") {
      if (this.startTime != undefined && this.endTime != undefined) {
        this.selectedEventObj.start_time = this.startTime.key;
        this.selectedEventObj.end_time = this.endTime.key;
      }
    }

    if (this.validateEvent()) {
      if (this.selectedEventObj.event_agent_type == "ME") {
        this.selectedEventObj.agent_id = BaseComponent.user.id;
      }

      if (this.selectedEventObj.event_agent_type == "BG") {
        this.selectedEventObj.agent_id = "";
      }

      let dateWithStartTime = moment(
        this.selectedEventObj.event_date +
          " " +
          this.selectedEventObj.start_time
      );
      let dateWithEndTime = moment(
        this.selectedEventObj.event_date + " " + this.selectedEventObj.end_time
      );

      var utcStartTime = this.getUTCTime(dateWithStartTime);
      var utcEndTime = this.getUTCTime(dateWithEndTime);
      var selectedDateDate = this.addUpdateEventDateFormat(dateWithStartTime);

      this.selectedEventObj.property_id = this.propertyId;
      this.event.property_id = this.selectedEventObj.property_id;
      this.event.date = selectedDateDate;
      this.event.event_type = this.selectedEventObj.event_type;
      this.event.start_time = utcStartTime;
      this.event.end_time = utcEndTime;
      this.event.description = this.selectedEventObj.description;
      this.event.event_agent_type = this.selectedEventObj.event_agent_type;
      this.event.agent_id = this.selectedEventObj.agent_id;

      let eventUrlParams = new URLSearchParams();
      for (let key of Object.keys(this.event)) {
        if (key == "agent_id") {
          if (this.event[key] != "" && this.event[key] != undefined) {
            eventUrlParams.set(key, this.event[key]);
          }
        } else {
          eventUrlParams.set(key, this.event[key]);
        }
      }

      this.addEventService.addPropertyEvent(eventUrlParams).subscribe(
        (res) => {
          this.successResponse(res);
          this.addEventResponse.emit(this.event);
          $("#manageEvent").modal("hide");
        },
        (err) => {
          this.errorResponse(err.json());
        }
      );
    }
  }

  removeSpecificAgentId(value) {
    this.onlyShowSpecificAgent = false;
    this.selectedEventObj.agent_id = undefined;
  }

  onEventTypeChange(eventType) {
    this.event_Type = eventType.key;
    this.eventError.event_type_error = "";
    this.showAddEventBtn && this.getStaticEventDescription(eventType.key);
  }

  getStaticEventDescription(eventType): void {
    // POHP2-84
    if (BaseComponent.user.is_paid_account) {
      if (eventType == "OH") {
        this.selectedEventObj.description =
          "To save this open house to your viewing list, select ADD TO MY LIST below. Please contact agent below for questions, gate codes, directions or special showing times.";
      } else if (eventType == "BO") {
        this.selectedEventObj.description =
          "To save this open house to your viewing list, select ADD TO MY LIST below. Please contact agent below for questions, gate codes, directions or special showing times.";
      } else if (eventType == "AO") {
        this.selectedEventObj.description =
          "72 Hour Home Sale! Contact agent for additional details and availability.";
      }
    } else {
      if (eventType == "OH") {
        this.selectedEventObj.description =
          "To save this open house to your viewing list, select ADD TO MY LIST below. ";
      } else if (eventType == "BO") {
        this.selectedEventObj.description =
          "To save this open house to your viewing list, select ADD TO MY LIST below. ";
      } else if (eventType == "AO") {
        this.selectedEventObj.description =
          "72 Hour Home Sale! Contact agent for additional details and availability.";
      }
    }
  }

  onEventDescriptionAdd() {
    this.eventError.description_error = "";
  }

  getUTCToLocalTime(eventDate) {
    return moment.utc(eventDate).local().format("HH:mm:ss");
  }

  UTCDateTime(dateTime) {
    return moment(dateTime).utc().format("DD/MM/YYYY HH:mm:ss");
  }

  addUpdateEventDateFormat(dateTime) {
    return moment(dateTime).utc().format("YYYY-MM-DD");
  }

  getUTCTime(dateTime) {
    return moment(dateTime).utc().format("HH:mm:ss");
  }

  openModal() {
    if (this.currentUserType == "LA") {
      $("#lockedFeatureModal1").modal("show");
    }
  }

  upgradeAccount(plan) {
    this.purchaseService.setPlan(plan);
    this.routeOnUrl("/purchase");
    $("#upgradeModalEvent").modal("hide");
  }

  showModalEvent() {
    $('#manageEvent' ).modal( {
      // focus: false,
      show: true
    });
  }
}
