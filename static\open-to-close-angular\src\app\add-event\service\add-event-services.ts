import { Injectable,Input,EventEmitter } from '@angular/core';
import { BaseComponent } from '@app/base/components/base.component';
import { ApiService } from '@app/base/services/api.service';
import { ServiceLocator } from '@app/base/components/service-locator';
import { Observable } from 'rxjs/Observable';
import { METHOD_REFERENCE, API_REFERENCE } from '@app/base/components/base.constants';

@Injectable()
export class AddEventService {

    public baseservice:BaseComponent;
    public apiService:ApiService;
    
    constructor() {
        this.baseservice=ServiceLocator.injector.get(BaseComponent);
        this.apiService=ServiceLocator.injector.get(ApiService);
    }
    
    updateEvent(eventParams): Observable<any>{
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['PUT'],API_REFERENCE['property']['updatePropertyEvent'],eventParams.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }
    
    addPropertyEvent(eventUrlParams):Observable<any>{
        let options = this.baseservice.getRequestOptions(METHOD_REFERENCE['POST'],API_REFERENCE['property']['addPropertyEvent'],eventUrlParams.toString(), null, null, null, false, true);
        return this.apiService.apiCall(options);
    }
}