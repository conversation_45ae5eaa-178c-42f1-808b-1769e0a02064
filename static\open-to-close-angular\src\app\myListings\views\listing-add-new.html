<div>
  <header></header>
</div>

<div>
  <div class="property_page header_fix">
    <div *ngIf="propertyActionType == 'newProperty'" class="property_header">
      <div class="container">
        <div class="row">
          <div class="col-sm-16">
            <h1 class="prtitle prtitle-font-color">Add Listing</h1>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="propertyActionType == 'edit'" class="property_header">
      <div class="container">
        <div class="row">
          <div class="col-sm-10">
            <h1 class="prtitle prtitle-font-color">Edit Property</h1>
          </div>
          <div class="col-sm-3">
            <h1 class="view-listing-btn cursor-pointer"
              (click)="gotToPropertyDetail('my-listing/property-detail',propertyId)">View Listing</h1>
          </div>
        </div>
      </div>
    </div>

    <div class="new_profile_group_wrap add_listing">
      <div class="new_profile_group dis_inline">
        <ul class="nav nav-pills">
          <li class="active"><a data-toggle="pill" href="#BasicInformation">Basic Information</a></li>
          <!-- <li><a data-toggle="pill" href="#Internal">Internal</a></li> -->
          <li><a data-toggle="pill" href="#ImagesVideos">Images & Videos</a></li>
          <li><a data-toggle="pill" href="#External">External</a></li>
          <li><a data-toggle="pill" href="#Building">Building</a></li>
          <li><a data-toggle="pill" href="#Location">Location</a></li>
          <li><a data-toggle="pill" href="#TaxesExpenses">Taxes & Expenses </a></li>
        </ul>

        <div class="tab-content">
          <div id="BasicInformation" class="tab-pane fade in active new_profile_details">
            <form [formGroup]="addPropertyFormGroup">

              <div class="group_1 mt-20">
                <div class="title2 sub_title">Status</div>
                <div class="new_form">
                  <div class="group width_350 drop-down-label-pos">
                    <ng-select class="custom agent-dropdown" placeholder="Status*" formControlName="listing_status"
                      notFoundText="No status type found" [items]="statusTypeList" [clearable]=false bindLabel="name"
                      bindValue="value" [searchable]=false>
                    </ng-select>
                    <span class="highlight"></span>
                    <span class="bar"></span>
                    <label class="drop_down_label">Status*</label>
                  </div>
                </div>
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">Listing Agent</div>
                <div class="new_form">
                  <div class="group new_form_label">
                    <input type="text"  class=" width_350" formControlName="agent_name" placeholder=" ">
                    <span class="highlight"></span>
                    <span class="bar"></span>
                    <label>Listing Agent</label>
                  </div>
                </div>
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">Listing Brokerage</div>
                <div class="new_form">
                  <div class="group new_form_label">
                    <input type="text"  class=" width_350" formControlName="brokerage_name" placeholder=" ">
                    <span class="highlight"></span>
                    <span class="bar"></span>
                    <label>Listing Brokerage</label>
                  </div>
                </div>
              </div>


              <div class="group_1 mt-20">
                <div class="title2 sub_title">Address</div>
                <div class="new_form">
                  <div class="new_form_group ">
                    <div class="group new_form_label">
                      <input type="text" required="" class=" width_350" formControlName="street" placeholder=" ">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Street*</label>
                    </div>
                  </div>

                  <div class="new_form_group ">
                    <div class="group new_form_label">
                      <input type="text" #unitNumber (keyup)="unitNumberType(unitNumber.value)"
                        formControlName="unit_number" class=" width_350" placeholder=" ">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Unit Number</label>
                      <span *ngIf="addPropertyFormGroup.controls.unit_number.touched">
                        <span class="form-validation err-year-width"
                          *ngIf="addPropertyFormGroup.controls.unit_number.errors?.number">Please enter a number</span>
                      </span>
                    </div>
                  </div>

                  <div class="new_form_group ">
                    <div class="group new_form_label width_350">
                      <input appAutoComplete type="text" #city (focusout)="focusOutFunction('city',city.value)"
                      autocomplete="off" (OnTypeCompleteMethod)="searchCity(city.value)" formControlName="city_name" class="width_350" placeholder=" ">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>City*</label>
                      <div class="baths_group price_ul search-drop-down" *ngIf="showCitySearchResult == true">
                        <ul id="city_menu_ul" class="search-drop-down-result search-bg">
                          <li id="city_menu_li" class="search-drop-down-li" *ngFor="let value of cityList"
                            (click)="onCitySelect(value)">
                            {{value.city_name}}
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div class="new_form_group ">
                    <div class="group new_form_label width_350">
                      <ng-select class="custom agent-dropdown" [virtualScroll]="true" placeholder="State*"
                        formControlName="state" notFoundText="No State found" [items]="stateList" bindLabel="name"
                        bindValue="id" [clearable]=false [searchable]=false (change)="getCityList($event.id)">
                      </ng-select>
                      <label *ngIf="showStateLabel == true" class="drop_down_label">State*</label>
                    </div>
                  </div>

                  <div class="new_form_group">
                    <div class="group new_form_label width_350">
                      <input appAutoComplete type="text" #zipCode (focusout)="focusOutFunction('zipCode',zipCode.value)"
                      autocomplete="off" (OnTypeCompleteMethod)="searchZipCode(zipCode.value)" formControlName="zipcode_code" class="width_350"
                        placeholder=" ">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>ZipCode*</label>
                      <div class="baths_group price_ul search-drop-down" *ngIf="showZipCodeSearchResult == true">
                        <ul id="zipcode_menu_ul" class="search-drop-down-result search-bg">
                          <li id="zipcode_menu_li" class="search-drop-down-li" *ngFor="let value of zipCodeList"
                            (click)="onZipCodeSelect(value)">
                            {{value.code}}
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>



              <div class="group_1 mt-20">
                <div class="title2 sub_title">Price</div>
                <div class="new_form">
                  <div class="new_form_group ">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " required="" #homePrice (keyup)="homePriceType(homePrice.value)"
                        class=" width_350" formControlName="home_price">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Home Price*</label>
                      <span *ngIf="addPropertyFormGroup.controls.home_price.touched">
                        <span class="form-validation err-year-width"
                          *ngIf="addPropertyFormGroup.controls.home_price.errors?.number">Please enter a number</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">Space</div>
                <div class="new_form">
                  <div class="new_form_group new_form_select_css width_350 drop-down-label-pos">
                    <ng-select class="custom agent-dropdown" placeholder="Bedrooms*" formControlName="bedroom"
                      notFoundText="No Bathrooms found" [items]="BedroomsList" bindLabel="code" bindValue="id"
                      [clearable]=false [searchable]=false (change)="showLabel('Bedrooms')">
                    </ng-select>
                    <label *ngIf="showBedroomsLabel == true" class="drop_down_label">Bedrooms*</label>
                  </div>

                  <!-- <div class="new_form_group new_form_select_css width_350 drop-down-label-pos">
                                        <ng-select class="custom agent-dropdown"
                                        placeholder = "Full Baths*"
                                        notFoundText="No Full Baths found"
                                        [items]="BedroomsList"
                                        formControlName="full_bath"
                                        bindLabel="code"
                                        bindValue="id"
                                        [clearable]=false
                                        [searchable]=false
                                        (change)="showLabel('Full Baths')"
                                        >
                                        </ng-select>
                                        <label *ngIf="showFullBathsLabel == true" class="drop_down_label">Full Baths*</label>
                                    </div> -->

                  <div class="new_form_group clrboth">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " #fullBath (keyup)="fullBathValidation(fullBath.value)"
                        formControlName="full_bath" required="" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Full Baths*</label>
                      <span *ngIf="addPropertyFormGroup.controls.full_bath.touched">
                        <span class="form-validation err-year-width"
                          *ngIf="addPropertyFormGroup.controls.full_bath.errors?.number">Please enter a valid
                          number.</span>
                      </span>
                    </div>
                  </div>

                  <div class="new_form_group clrboth">
                    <div class="group new_form_label">
                      <input type="number" placeholder=" " formControlName="living_area" required="" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Approximate Square Feet*</label>
                    </div>
                  </div>

                  <div class="new_form_group ">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " #lotSize (keyup)="lotSizeType(lotSize.value)"
                        class=" width_350" formControlName="lot_size">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Lot Size</label>
                      <!-- <span *ngIf="addPropertyFormGroup.controls.lot_size.touched"> -->
                      <!-- <span class="form-validation err-year-width" *ngIf="addPropertyFormGroup.controls.lot_size.errors?.number">Please enter a number</span> -->
                      <!-- </span> -->
                    </div>
                  </div>
                  <div class="new_form_group">
                    <div class="group new_form_label width_350 drop-down-label-pos">
                      <ng-select class="custom agent-dropdown" placeholder="Property Type*"
                        formControlName="property_type" notFoundText="No Property Type found" [items]="propertyTypeList"
                        bindLabel="code" bindValue="id" [clearable]=false [searchable]=false
                        (change)="showLabel('Property Type')">
                      </ng-select>
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label *ngIf="showPropertyTypeLabel == true" class="drop_down_label">Property Type*</label>
                    </div>
                  </div>
                  <div class="new_form_group">
                    <div class="group new_form_label width_350 drop-down-label-pos">
                      <ng-select class="custom agent-dropdown" placeholder="Style" formControlName="property_style"
                        notFoundText="No Style found" [items]="styleList" bindLabel="code" bindValue="id"
                        [clearable]=false [searchable]=false (change)="showLabel('Style')">
                      </ng-select>
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label *ngIf="showPropertyStyleLabel == true" class="drop_down_label">Style</label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">History</div>
                <div class="new_form">
                  <div class="new_form_group ">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " #year (keyup)="checkBuiltingYear(year.value)"
                        class=" width_350" formControlName="year_built">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Year Built</label>
                      <span *ngIf="addPropertyFormGroup.controls.year_built.touched">
                        <span class="form-validation err-year-width"
                          *ngIf="addPropertyFormGroup.controls.year_built.errors?.incorrect">Invalid Year</span>
                        <span class="form-validation err-year-width"
                          *ngIf="addPropertyFormGroup.controls.year_built.errors?.number">Please enter a number</span>
                      </span>
                    </div>
                  </div>
                  <div class="new_form_group ">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " formControlName="builder" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Builder</label>
                    </div>
                  </div>

                  <div class="new_form_group ">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " #parcelId required="" (keyup)="validateFormat(parcelId.value)" maxlength="21" formControlName="parcel_id" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Parcel ID / Tax ID*</label>
                    </div>
                    <span class="form-validation err-year-width"
                    *ngIf="length_valid_parcel_id">Parcel ID / Tax ID max length is 20</span>
                  </div>
                </div>
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">Description</div>
                <div class="new_form">
                  <div class="new_form_group ">
                    <div class="group new_form_label">
                      <textarea rows="4" placeholder=" " formControlName="property_description"
                        class=" width_350"></textarea>
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Property Description</label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="new_form_group save-btn-right text-right">
                <input type="submit"
                  [ngClass]="{'submit-disable':addPropertyFormGroup.invalid || !isSeletedZipCode || !isSeletedCity}"
                  [disabled]="addPropertyFormGroup.invalid || !isSeletedZipCode || !isSeletedCity"
                  (click)="newProperty(addPropertyFormGroup)" class="submit_button button_with_bg" value="Save Listing">
              </div>
            </form>
          </div>

          <div id="ImagesVideos" class="tab-pane fade">
            <div class="group_1 mt-20">
              <div class="title2 sub_title">Property Images<br> & Videos</div>
              <div class="new_form">
                <div class="check_group profile_checkbox width_350 newWidth flex_container">
                  <p class="Account_text">Tap or click the photo to set its caption or select main photo. Rearrange
                    photos by dragging them.</p>
                  <p *ngIf="fileTypeError != '' " class="form-validation">You can only upload image or video files</p>
                  <p *ngIf="maxImageSelect != '' " class="form-validation">You can only upload a maximum 15 images</p>

                  <div class="row img_upload flex_container image-scroll" dnd-sortable-container
                    [sortableData]="selectedImages">
                    <div *ngFor="let image of selectedImages;let i = index"
                      [ngClass]="{'caption-img':i > 0, 'width-100': i === 0}" dnd-sortable [sortableIndex]="i">
                      <div [ngClass]="{'col-sm-16':i === 0, 'col-sm-8':i > 0, 'width-100': i === 0}"
                        class=" mb_10 pdlr_5 delete">
                        <img *ngIf="image.file_type == 'image'" src="{{image.file_url}}" alt=""
                          [ngClass]="{'upload-img': i === 0, 'upload-img-2': i>0 ,'property-img-broder': propertyImageBorderId === i,'first-img':selectedImages.length >3 && i == 0, 'width-100': i === 0}"
                         class="caption_toogle">
                        <img *ngIf="isListhubProperty == false  || isManuallySyndicate" src="{{imagePrefix}}Delete-xhdpi.png"
                          [ngClass]="{'img-delete':i === 0, 'img-delete-sm':i > 0}" (click)="removeImage(image)">
                        <video *ngIf="image.file_type == 'video'"
                          [ngClass]="{'upload-img': i === 0, 'upload-img-2': i>0}" controls>
                          <source src="{{image.file_url}}" type="video/mp4">
                        </video>
                        <div [ngClass]="{'caption':i > 0}" id="caption_{{i}}" class="new_form_group  mtb">
                          <div class="group new_form_label">
                            <span *ngIf="image.file_caption != 'null'">
                              <input placeholder=" " type="text" #txt value="{{image.file_caption}}"
                                (keyup)="setCaption(txt.value,i)" required="" class="width_100">
                              <span class="highlight"></span>
                              <label>Caption</label>
                            </span>
                            <span *ngIf="image.file_caption == 'null'">
                              <input type="text" #txt (keyup)="setCaption(txt.value,i)" required="" class="width_100">
                              <span class="highlight"></span>
                              <label>Caption</label>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>


                <div *ngIf="isImageUploading" class="" style="width: 75%">
                  <div class="loader">
                    <div class="dots" style="margin: 20px 0 0px;">
                      <div class="center"></div>
                    </div>
                  </div>
                </div>
                <div class="upload-button-area pr-img-top" *ngIf="isListhubProperty == false || isManuallySyndicate">
                  <label for="file-upload" class="file-upload-image">
                    <span class="custom-file-upload-image">
                      <span class="file-upload-text upload-image-text image-btn-text">Upload</span>
                    </span>
                  </label>
                  <input [disabled]="isImageUploading" id="file-upload" type="file"
                    accept="video/mp4,video/x-m4v,video/*,image/jpeg,image/png" multiple="multiple"
                    (change)="uploadImageVideo($event)" />
                  <label *ngIf="selectedImages.length !=0 || showImgSaveChanges == true" class="file-upload-image"
                    (click)="uploadAllImage()">
                    <span class="custom-file-upload-image">
                      <span class="file-upload-text upload-image-text image-btn-text">Save Changes</span>
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div id="External" class="tab-pane fade">
            <form [formGroup]="propertyExternalFormGroup">
              <div class="group_1 mt-20">
                <div class="title2 sub_title">Pool</div>
                <div class="new_form">
                  <div class="check_group profile_checkbox width_350 newWidth flex_container">
                    <div *ngFor="let waterObj of water | keys" class="form_group width_50">
                      <input type="checkbox" [checked]="checkIsInList(waterObj.key,waterList)"
                        (click)="addWaterKeys(waterObj.key)"> <span class="checkmark"></span>
                      <label class="width_auto">{{waterObj.value}}</label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">Parking</div>
                <div class="new_form">
                  <div class="check_group profile_checkbox width_350 newWidth flex_container">
                    <div *ngFor="let parkingObj of parking | keys" class="form_group width_50">
                      <input type="checkbox" [checked]="checkIsInList(parkingObj.key,parkingList)"
                        (click)="addParkingKeys(parkingObj.key)"> <span class="checkmark"></span>
                      <label class="width_auto">{{parkingObj.value}}</label>
                    </div>
                  </div>

                  <div class="new_form_group spaces-btn">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " #spaces (keyup)="spacesType(spaces.value)"
                        formControlName="parking_total_space" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Total Number of Spaces</label>
                      <span *ngIf="propertyExternalFormGroup.controls.parking_total_space.touched">
                        <span class="form-validation err-year-width"
                          *ngIf="propertyExternalFormGroup.controls.parking_total_space.errors?.number">Please enter a
                          number</span>
                      </span>
                    </div>
                  </div>
                  <br>
                  <div class="new_form_group spaces-btn">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " #spaces (keyup)="garageSpacesType(spaces.value)"
                        formControlName="garage_space" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>Garage Space</label>
                      <span *ngIf="propertyExternalFormGroup.controls.garage_space.touched">
                        <span class="form-validation err-year-width"
                          *ngIf="propertyExternalFormGroup.controls.garage_space.errors?.number">Please enter a
                          number</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">Community Features</div>
                <div class="new_form">
                  <div class="check_group profile_checkbox width_350 newWidth flex_container">
                    <div *ngFor="let securityObj of security | keys" class="form_group width_50">
                      <input type="checkbox" [checked]="checkIsInList(securityObj.key,securityList)"
                        (click)="addSecurityKeys(securityObj.key)"> <span class="checkmark"></span>
                      <label class="width_auto">{{securityObj.value}}</label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">Accessibility</div>
                <div class="new_form">
                  <div class="check_group profile_checkbox width_350 newWidth flex_container">
                    <div *ngFor="let accessibilityObj of accessibility | keys" class="form_group width_50">
                      <input type="checkbox" [checked]="checkIsInList(accessibilityObj.key,accessibilityList)"
                        (click)="addaccessibilityKeys(accessibilityObj.key)"> <span class="checkmark"></span>
                      <label class="width_auto">{{accessibilityObj.value}}</label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="new_form_group save-btn-right text-right">
                <input type="submit" [ngClass]="{'submit-disable': propertyExternalFormGroup.invalid }"
                  [disabled]="propertyExternalFormGroup.invalid" (click)="propertyExternal(propertyExternalFormGroup)"
                  class="submit_button button_with_bg" value="Save Changes">
              </div>
            </form>
          </div>

          <div id="Building" class="tab-pane fade">
            <form [formGroup]="propertyBuildingFormGroup">
              <div class="group_1 mt-20">
                <!-- <div class="title2 sub_title">Building</div> -->
                <!-- <div class="new_form"> -->
                <!-- <div class="new_form_group ">
                                    <div class="group new_form_label">
                                        <input type="text" placeholder=" " #floorNumber (keyup)="floorNumberType(floorNumber.value)" formControlName="condo_floor_no" class=" width_350">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Condo Floor Number</label>
                                        <span *ngIf="propertyBuildingFormGroup.controls.condo_floor_no.touched">
                                            <span class="form-validation err-year-width" *ngIf="propertyBuildingFormGroup.controls.condo_floor_no.errors?.number">Please enter a number</span>
                                        </span>
                                    </div>
                            </div>  -->
                <!-- <div class="new_form_group ">
                                    <div class="group new_form_label">
                                        <input type="text" placeholder=" " #unitCount (keyup)="unitCountType(unitCount.value)" formControlName="building_unit_count" class=" width_350">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Building Unit Count</label>
                                        <span *ngIf="propertyBuildingFormGroup.controls.building_unit_count.touched">
                                            <span class="form-validation err-year-width" *ngIf="propertyBuildingFormGroup.controls.building_unit_count.errors?.number">Please enter a number</span>
                                        </span>
                                    </div>
                            </div> -->
                <!-- <div class="new_form_group ">
                                    <div class="group new_form_label">
                                        <input type="text" placeholder=" "  #floors (keyup)="floorsType(floors.value)" formControlName="building_floors" class=" width_350">
                                        <span class="highlight"></span>
                                        <span class="bar"></span>
                                        <label>Floors</label>
                                        <span *ngIf="propertyBuildingFormGroup.controls.building_floors.touched">
                                            <span class="form-validation err-year-width" *ngIf="propertyBuildingFormGroup.controls.building_floors.errors?.number">Please enter a number</span>
                                        </span>
                                    </div>
                            </div>         -->
                <!-- </div> -->
              </div>

              <div class="group_1 mt-20">
                <div class="title2 sub_title">View Types</div>
                <div class="new_form">
                  <div class="check_group profile_checkbox width_350 newWidth flex_container">
                    <div *ngFor="let viewTypeObj of view_Types | keys" class="form_group width_50">
                      <input type="checkbox" [checked]="checkIsInList(viewTypeObj.key,addViewTypeList)"
                        (click)="addViewTypeKeys(viewTypeObj.key)"> <span class="checkmark"></span>
                      <label class="width_auto">{{viewTypeObj.value}}</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="group_1 mt-20">
                <div class="title2 sub_title">Other Building & Construction</div>
                <div class="new_form">
                  <div class="check_group profile_checkbox width_350 newWidth flex_container">
                    <div *ngFor="let OBCObj of otherBuildingCon | keys" class="form_group width_50">
                      <input type="checkbox" [checked]="checkIsInList(OBCObj.key,addOBCList)"
                        (click)="OBCListKey(OBCObj.key)"> <span class="checkmark"></span>
                      <label class="width_auto">{{OBCObj.value}}</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="new_form_group save-btn-right text-right">
                <input type="submit" [ngClass]="{'submit-disable':propertyBuildingFormGroup.invalid}"
                  [disabled]="propertyBuildingFormGroup.invalid"
                  (click)="AddPropertyBuilding(propertyBuildingFormGroup)" class="submit_button button_with_bg"
                  value="Save Changes">
              </div>
            </form>
          </div>



          <div id="Location" class="tab-pane fade">
            <div class="group_1 mt-20">
              <form [formGroup]="propertyLocationFormGroup">
                <div class="title2 sub_title">Schools</div>
                <div class="new_form">
                  <div class="location-input">
                    <div class="group new_form_label">
                      <input type="text" formControlName="district" class="floating-input width_350" placeholder=" ">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>District</label>
                    </div>
                  </div>
                  <div class="location-input">
                    <div class="group new_form_label">
                      <input placeholder=" " type="text" formControlName="school_1_name" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>School 1 Name</label>
                    </div>
                  </div>
                  <div class="location-input">
                    <div class="group width_350 drop-down-label-pos">
                      <ng-select class="custom agent-dropdown" placeholder="School 1 Type"
                        formControlName="school_1_type" notFoundText="No school type found" [items]="schoolTypeList"
                        bindLabel="code" bindValue="id" [clearable]=false [searchable]=false
                        (change)="showSchoolDropDownLabel('SchoolType1')">
                      </ng-select>
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label *ngIf="showSchool1Label == true" class="drop_down_label">School 1 Type</label>
                    </div>
                  </div>

                  <div class="location-input">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " formControlName="school_2_name" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>School 2 Name</label>
                    </div>
                  </div>
                  <div class="location-input">
                    <div class="group width_350 drop-down-label-pos">
                      <ng-select class="custom agent-dropdown" placeholder="School 2 Type"
                        formControlName="school_2_type" notFoundText="No school type found" [items]="schoolTypeList"
                        [clearable]=false [searchable]=false (change)="showSchoolDropDownLabel('SchoolType2')">
                      </ng-select>
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label *ngIf="showSchool2Label == true" class="drop_down_label">School 2 Type</label>
                    </div>
                  </div>

                  <div class="location-input">
                    <div class="group new_form_label">
                      <input type="text" placeholder=" " formControlName="school_3_name" class=" width_350">
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label>School 3 Name</label>
                    </div>
                  </div>
                  <div class="location-input">
                    <div class="group width_350 drop-down-label-pos">
                      <ng-select class="custom agent-dropdown" placeholder="School 3 Type"
                        formControlName="school_3_type" notFoundText="No school type found" [items]="schoolTypeList"
                        [clearable]=false [searchable]=false (change)="showSchoolDropDownLabel('SchoolType3')">
                      </ng-select>
                      <span class="highlight"></span>
                      <span class="bar"></span>
                      <label *ngIf="showSchool3Label == true" class="drop_down_label">School 3 Type</label>
                    </div>
                  </div>
                </div>
                <div class="group_1 mt-20">
                  <div class="title2 sub_title">Neighborhood</div>
                  <div class="new_form">
                    <div class="new_form_group ">
                      <div class="group new_form_label">
                        <input type="text" placeholder=" " formControlName="neighborhood" class=" width_350">
                        <span class="highlight"></span>
                        <span class="bar"></span>
                        <label>Neighborhood</label>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="group_1 mt-20">
                  <div class="title2 sub_title">Subdivision</div>
                  <div class="new_form">
                    <div class="new_form_group ">
                      <div class="group new_form_label">
                        <input type="text" placeholder=" " formControlName="subdivision" class=" width_350">
                        <span class="highlight"></span>
                        <span class="bar"></span>
                        <label>Subdivision</label>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="group_1 mt-20">
                  <div class="title2 sub_title">Other Location Information</div>
                  <div class="new_form">
                    <div class="location-input ">
                      <div class="group new_form_label">
                        <input type="text" placeholder=" " formControlName="county" class=" width_350">
                        <span class="highlight"></span>
                        <span class="bar"></span>
                        <label>County</label>
                      </div>
                    </div>
                    <div class="location-input ">
                      <div class="group new_form_label">
                        <input type="text" placeholder=" " formControlName="directions" class=" width_350">
                        <span class="highlight"></span>
                        <span class="bar"></span>
                        <label>Directions</label>
                      </div>
                    </div>
                    <!-- <div class="location-input ">
                                            <div class="group new_form_label">
                                                <input type="text" placeholder=" " formControlName="elevation"  class=" width_350">
                                                <span class="highlight"></span>
                                                <span class="bar"></span>
                                                <label>Elevation</label>
                                            </div>
                                        </div> -->
                    <div class="location-input ">
                      <div class="group new_form_label">
                        <input type="text" placeholder=" " formControlName="latitude" class=" width_350">
                        <span class="highlight"></span>
                        <span class="bar"></span>
                        <label>Latitude</label>
                      </div>
                    </div>
                    <div class="location-input ">
                      <div class="group new_form_label">
                        <input type="text" placeholder=" " formControlName="longitude" class=" width_350">
                        <span class="highlight"></span>
                        <span class="bar"></span>
                        <label>Longitude</label>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div class="new_form_group save-btn-right text-right">
              <input type="submit" [ngClass]="{'submit-disable':propertyLocationFormGroup.invalid}"
                [disabled]="propertyLocationFormGroup.invalid" (click)="AddPropertyLocation(propertyLocationFormGroup)"
                class="submit_button button_with_bg" value="Save Changes">
            </div>
          </div>

          <div id="TaxesExpenses" class="tab-pane fade">
            <div class="group_1 mt-20">
              <div class="title2 sub_title">Taxes</div>
              <div class="new_form">
                <form [formGroup]="taxesForm">
                  <div formArrayName="taxes" *ngFor="let item of taxesForm.get('taxes')['controls']; let i = index;"
                    class="taxes-group">
                    <div [formGroupName]="i">
                      <div class="location-input ">
                        <div class="group new_form_label">
                          <input placeholder=" " type="number" formControlName="taxes_amount" class=" width_350">
                          <span class="highlight"></span>
                          <span class="bar"></span>
                          <label>Tax {{i+1}} Amount</label>
                        </div>
                      </div>
                      <div class="tax-location">
                        <div class="group new_form_label">
                          <textarea rows="1" placeholder=" " formControlName="taxes_description" id="txt1"
                            class=" width_350"></textarea>
                          <span class="highlight"></span>
                          <span class="bar"></span>
                          <label>Tax {{i+1}} Description</label>
                        </div>
                      </div>
                      <span class="tax-expense-err" id={{i}}></span><br><br>
                    </div>
                  </div>
                  <span>
                    <!-- <i *ngIf="taxesForm.invalid == false" class="fa fa-plus-circle plus-btn" (click)="addItem()"></i> -->
                    <img *ngIf="taxesForm.invalid == false" class="tax_plus cursor-pointer"
                      src="{{imagePrefix}}ic_plus.png" (click)="addItem()">
                    <!-- <i *ngIf="removeTaxes" class="fa fa-minus-circle minus-btn" (click)="removeItem(taxesForm)"></i> -->
                    <img *ngIf="removeTaxes" class="tax_minus cursor-pointer" src="{{imagePrefix}}ic_minus.png"
                      (click)="removeItem(taxesForm)">
                  </span>
                </form>

              </div>
            </div>

            <div class="group_1 mt-20">
              <div class="title2 sub_title">Expsenses</div>
              <div class="new_form">
                <div [formGroup]="expenseForm">
                  <div formArrayName="expsenses"
                    *ngFor="let item of expenseForm.get('expsenses')['controls']; let i = index;"
                    class="expsenses-group">
                    <div [formGroupName]="i">
                      <div class="location-input ">
                        <div class="group new_form_label">
                          <input type="number" placeholder=" " formControlName="expense_amount" class=" width_350">
                          <span class="highlight"></span>
                          <span class="bar"></span>
                          <label>Expense {{i+1}} Amount</label>
                        </div>
                      </div>

                      <div class="location-input">
                        <div class="group new_form_label width_350">
                          <ng-select class="custom agent-dropdown" placeholder="Expense {{i+1}} Type"
                            formControlName="expense_type" [items]="expenseTypeList" bindLabel="code" bindValue="id"
                            [clearable]=false [searchable]=false>
                          </ng-select>
                        </div>
                      </div>
                      <span class="tax-expense-err" id="exp{{i}}"></span><br><br>
                    </div>
                  </div>
                </div>
                <div>
                  <!-- <i *ngIf="expenseForm.invalid == false" class="fa fa-plus-circle plus-btn" (click)="addEXItem()"></i>
                                <i *ngIf="Expsenses" class="fa fa-minus-circle minus-btn" (click)="removeEXItem(expenseForm)"></i> -->
                  <img *ngIf="expenseForm.invalid == false" class="tax_plus cursor-pointer"
                    src="{{imagePrefix}}ic_plus.png" (click)="addEXItem()">
                  <img *ngIf="Expsenses" class="tax_minus cursor-pointer" src="{{imagePrefix}}ic_minus.png"
                    (click)="removeEXItem(expenseForm)">
                </div>
              </div>
            </div>
            <div class="new_form_group save-btn-right text-right">
              <input type="submit" [ngClass]="{'submit-disable':expenseForm.invalid || taxesForm.invalid}"
                [disabled]="expenseForm.invalid || taxesForm.invalid"
                (click)="addPropertyTaxesExpenses(taxesForm,expenseForm)" class="submit_button button_with_bg"
                value="Save Changes">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div>
  <footer class="add-listing-footer"></footer>
</div>
