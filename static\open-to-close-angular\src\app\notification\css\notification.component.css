img.noti_image.symbols-property-image.dis_inline {
    margin-top: 23px !important;
}
.notification-time{
    float: right;
    margin-top: 29px;
    color: #9594ab;
    font-size: 14px !important;
    font-weight: 600 !important;
}
.noti_text {
    margin-left: 94px;
    width: 80%;
}
.msg-title{
    margin-top: -84px !important;
    margin-left: 96px !important;
}
.notification_div span.dark {
    font-size: 17px !important;
    color: #676767d6 !important;
    line-height: 1px;
}
.msg-title-text{
    font-size: 18px;
    font-weight: 600 !important;
    color: #727272;
    margin-left: 0px!important;
    min-height: 10%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-inline-box;
    line-height: 19px;
    max-width: 90%;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
.notification_div {
    border-top: 1px solid #C2C2C2;
}
.readed-noti{
    background: #E4F8F8;
    cursor: pointer;
}
.un-readed-noti{
    background: white;
    cursor: pointer;
}
.notification_div img.noti_image.symbols-property-image.dis_inline{
    height: 70px !important;
    vertical-align: -webkit-baseline-middle;
    margin-right: 10px;
    margin-left: 0px;
}
@media only screen and (max-width: 767px){
    .msg-title {
        margin-top: -90px !important;
        margin-left: 84px !important;
    }
    .msg-title-text{
        max-width: 77%;
    }
}
@media (min-width: 768px) and (max-width: 1023px) {
    .msg-title-text{
        max-width: 85%;
    }
}
