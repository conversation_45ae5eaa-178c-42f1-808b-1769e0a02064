.table-top{
    width: 80%;
    margin: auto;
    padding-top: 3%;
}
.table>thead>tr>th, .table>tbody>tr>th{
    border: 0px !important;
    border-left: 6px solid white !important;
}
.table>tbody+tbody{
    border-top: 0px !important;
}
.table-th{
    padding: 0px;
}
.table>tbody>tr>th{
    background: #eceff1 !important;
}
.span-th{
    background: aqua;
    padding: 7px;
    position: absolute;
}
.table>tbody>tr>td{
    padding-top: 27px;
    padding-bottom: 27px;
}
.span-sm{
    width: 10% !important;
}

table tr th:nth-child(1), table tr td:nth-child(1){
   padding-left: 8px !important;
}
.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{
    border-top: 0px !important;
}
.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{
    border-bottom: 2px solid #eceff1 !important;
}
.model-confi{
    width: 14%;
    font-size: 13px;
    font-weight: 600;
    outline: none;
}
.model-cancel{
    background: #F06292 !important;
    border: 0px !important;
    outline: none;
    width: 14%;
    font-size: 13px;
    font-weight: 600;
    outline: none;
}
.pagination-center{
    position: absolute;
    left: 41%;
}

.download-csv-button{
    width: 100% !important;
    margin-top: 3px !important;
    padding: 6px 15px 6px 15px !important;
}