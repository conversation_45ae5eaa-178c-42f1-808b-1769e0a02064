import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';

import { Subscription } from 'rxjs';

import { ServiceLocator } from '@app/base/components/service-locator';
import { PurchaseService } from '@app/purchase/service/purchase.service';
import { GetPlansService } from '@app/base/services/get.plans.service';

import { BaseComponent } from '@app/base/components/base.component';
import { AuthService } from '@app/auth/services/auth.service';

import { ProfileComponent } from '@app/profile/component/profile.component';

import { Plans } from '@app/profile/models/plan';

declare var $;

@Component({
  selector: 'agent-upgrade',
  templateUrl: '../views/agent-upgrade.component.html',
  styleUrls: ['../css/upgrade.component.css','../../landing-pages/css/landing-page.component.css']
})
export class AgentUpgradeComponent extends BaseComponent implements OnInit, OnDestroy {

  public purchaseService: PurchaseService;
  public getPlansService: GetPlansService;
  public authService: AuthService;

  public plansAPISubscription: Subscription;

  public plansList = [];

  public selectedPlan: String = "";

  public subscribed: boolean = false;

  constructor() {
    super();
    this.getPlansService = ServiceLocator.injector.get(GetPlansService);
    this.purchaseService = ServiceLocator.injector.get(PurchaseService);
    this.authService = ServiceLocator.injector.get(AuthService);
  }

  ngOnInit() {

    $(document).ready(function ($) {
      $(this).scrollTop(0);
    });

    this.getCurrentPlans();
    this.getPlans();
  }

  getCurrentPlans(): void {
    if (BaseComponent.user != undefined || Object.keys(BaseComponent.user).length != 0) {
      if (Object.keys(ProfileComponent.listingAgent).length == 0) {
        this.authService.agentDetail().subscribe(agentRes => {
          if (BaseComponent.user.is_paid_account) {
            this.selectedPlan = agentRes.result.selected_plan_id;
          }
          this.subscribed = true;
        });
      }
    }
  }

  getPlans(): any {
    if (this.plansList.length <= 0) {
      if (this.plansAPISubscription) {
        this.plansAPISubscription.unsubscribe();
      }
      this.plansAPISubscription = this.getPlansService.getPlans().subscribe(res => {
        let tempList = [];
        this.plansList = res.result.filter(plan => {
          if (plan.id.substr(0, 1) == 'A') {
            if (plan.billing_frequency === 1) {
              plan['annualPrice'] = this.Dec2(Number(12) * Number(plan.price))
            }
            if (plan.billing_frequency === 12) {
              const planPrice = Number(plan.price) - Number(0.01)
              plan['monthlyPrice'] = this.Dec2(Number(planPrice) / Number(12))
            }
            tempList.push(plan);
          }
          if (plan.id.substr(0, 2) == 'BR') {
            tempList.push(plan);
          }
          return tempList;
        });
      });
    }
  }

  upgradeAccount(plan: Plans): void {
    this.purchaseService.setPlan(plan);
    if (BaseComponent.user != undefined || Object.keys(BaseComponent.user).length != 0) {
      if (Object.keys(ProfileComponent.listingAgent).length == 0) {
        this.authService.agentDetail().subscribe(agentRes => {
          ProfileComponent.listingAgent = agentRes.result;
          ProfileComponent.listingAgent.billing_info = agentRes.result.billing_info;
          ProfileComponent.listingAgent.payment_method = agentRes.result.payment_method;
          this.routeOnUrl('/purchase');
        });
      } else {
        this.routeOnUrl('/purchase');
      }
    }
  }

  ngOnDestroy() {
    this.subscribed = false;
  }

  Dec2(num) {
    num = String(num);
    if(num.indexOf('.') !== -1) {
      var numarr = num.split(".");
      if (numarr.length == 1) {
        return Number(num);
      }
      else {
        return Number(numarr[0]+"."+numarr[1].charAt(0)+numarr[1].charAt(1));
      }
    }
    else {
      return Number(num);
    }  
  }

  redirectToApp(url){
    window.open(url);
  }

}
