import { PipeTransform, Pipe } from '@angular/core';

@Pipe({name: 'BR-agent-type-pipe'})

export class BRAgentPipe implements PipeTransform {
    transform(agentList, args:string) : any {    
      let agent = [];
      for (let i=0; i< agentList.length; i++) {
        if(args == 'U'){
          if(agentList[i]['invite_status'] == 'P'){
            agent.push(agentList[i]);
          }
        }else if(args == 'V'){
          if(agentList[i]['invite_status'] == 'A'){
            agent.push(agentList[i]);
          }
        }
      }      
      return agent;
    }
  }